'use client'

import { useEffect } from 'react'

export function useNextAuthErrorHandler() {
  useEffect(() => {
    // Override console.error to catch NextAuth errors
    const originalConsoleError = console.error

    console.error = (...args: any[]) => {
      // Check if this is a NextAuth CLIENT_FETCH_ERROR
      const errorMessage = args.join(' ')
      
      if (errorMessage.includes('[next-auth][error][CLIENT_FETCH_ERROR]')) {
        // Handle CLIENT_FETCH_ERROR more gracefully
        console.warn('NextAuth: Temporary connection issue detected. This is usually harmless and will resolve automatically.')
        
        // Don't show the full error stack to users in production
        if (process.env.NODE_ENV === 'development') {
          originalConsoleError.apply(console, args)
        }
        
        return
      }
      
      // For other NextAuth errors, log them normally
      if (errorMessage.includes('[next-auth]')) {
        console.warn('NextAuth error detected:', errorMessage)
        
        // In development, show full error details
        if (process.env.NODE_ENV === 'development') {
          originalConsoleError.apply(console, args)
        }
        
        return
      }
      
      // For non-NextAuth errors, use original console.error
      originalConsoleError.apply(console, args)
    }

    // Cleanup function to restore original console.error
    return () => {
      console.error = originalConsoleError
    }
  }, [])

  // Handle fetch errors globally
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason
      
      // Check if this is a NextAuth related fetch error
      if (error && typeof error === 'object') {
        const errorMessage = error.message || error.toString()
        
        if (errorMessage.includes('Failed to fetch') && 
            (errorMessage.includes('/api/auth/') || errorMessage.includes('next-auth'))) {
          
          console.warn('NextAuth: Network request failed. This is usually temporary.')
          
          // Prevent the error from being thrown to the user
          event.preventDefault()
          
          // In development, log more details
          if (process.env.NODE_ENV === 'development') {
            console.debug('NextAuth fetch error details:', error)
          }
          
          return
        }
      }
    }

    // Add global error handler
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    
    // Cleanup
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  // Handle window errors
  useEffect(() => {
    const handleWindowError = (event: ErrorEvent) => {
      const errorMessage = event.message || event.error?.message || ''
      
      if (errorMessage.includes('next-auth') || errorMessage.includes('CLIENT_FETCH_ERROR')) {
        console.warn('NextAuth: Window error caught and handled gracefully')
        
        // Prevent the error from bubbling up
        event.preventDefault()
        
        if (process.env.NODE_ENV === 'development') {
          console.debug('NextAuth window error details:', event.error)
        }
        
        return false
      }
    }

    window.addEventListener('error', handleWindowError)
    
    return () => {
      window.removeEventListener('error', handleWindowError)
    }
  }, [])
}

// Utility function to check if an error is NextAuth related
export function isNextAuthError(error: any): boolean {
  if (!error) return false
  
  const errorMessage = error.message || error.toString()
  
  return errorMessage.includes('next-auth') || 
         errorMessage.includes('CLIENT_FETCH_ERROR') ||
         errorMessage.includes('/api/auth/')
}

// Utility function to handle NextAuth errors gracefully
export function handleNextAuthError(error: any): boolean {
  if (!isNextAuthError(error)) {
    return false // Not a NextAuth error, let it bubble up
  }
  
  console.warn('NextAuth error handled gracefully:', error.message || error)
  
  // Return true to indicate the error was handled
  return true
}
