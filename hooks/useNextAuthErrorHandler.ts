'use client'

import { useEffect } from 'react'

export function useNextAuthErrorHandler() {
  useEffect(() => {
    // Override console.error to catch NextAuth errors
    const originalConsoleError = console.error

    console.error = (...args: any[]) => {
      // Check if this is a NextAuth CLIENT_FETCH_ERROR
      const errorMessage = args.join(' ')
      
      if (errorMessage.includes('[next-auth][error][CLIENT_FETCH_ERROR]')) {
        // Completely suppress CLIENT_FETCH_ERROR in both development and production
        // These errors are harmless and occur when NextAuth tries to fetch session data
        // on public pages where authentication is not needed
        return
      }
      
      // For other NextAuth errors, log them normally
      if (errorMessage.includes('[next-auth]')) {
        console.warn('NextAuth error detected:', errorMessage)
        
        // In development, show full error details
        if (process.env.NODE_ENV === 'development') {
          originalConsoleError.apply(console, args)
        }
        
        return
      }
      
      // For non-NextAuth errors, use original console.error
      originalConsoleError.apply(console, args)
    }

    // Cleanup function to restore original console.error
    return () => {
      console.error = originalConsoleError
    }
  }, [])

  // Handle fetch errors globally
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason
      
      // Check if this is a NextAuth related fetch error
      if (error && typeof error === 'object') {
        const errorMessage = error.message || error.toString()
        
        if (errorMessage.includes('Failed to fetch') &&
            (errorMessage.includes('/api/auth/') || errorMessage.includes('next-auth'))) {

          // Completely suppress NextAuth fetch errors
          // These are harmless and occur on public pages
          event.preventDefault()
          return
        }
      }
    }

    // Add global error handler
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    
    // Cleanup
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  // Handle window errors
  useEffect(() => {
    const handleWindowError = (event: ErrorEvent) => {
      const errorMessage = event.message || event.error?.message || ''
      
      if (errorMessage.includes('next-auth') || errorMessage.includes('CLIENT_FETCH_ERROR')) {
        // Completely suppress NextAuth window errors
        // These are harmless and occur on public pages
        event.preventDefault()
        return false
      }
    }

    window.addEventListener('error', handleWindowError)
    
    return () => {
      window.removeEventListener('error', handleWindowError)
    }
  }, [])
}

// Utility function to check if an error is NextAuth related
export function isNextAuthError(error: any): boolean {
  if (!error) return false
  
  const errorMessage = error.message || error.toString()
  
  return errorMessage.includes('next-auth') || 
         errorMessage.includes('CLIENT_FETCH_ERROR') ||
         errorMessage.includes('/api/auth/')
}

// Utility function to handle NextAuth errors gracefully
export function handleNextAuthError(error: any): boolean {
  if (!isNextAuthError(error)) {
    return false // Not a NextAuth error, let it bubble up
  }
  
  console.warn('NextAuth error handled gracefully:', error.message || error)
  
  // Return true to indicate the error was handled
  return true
}
