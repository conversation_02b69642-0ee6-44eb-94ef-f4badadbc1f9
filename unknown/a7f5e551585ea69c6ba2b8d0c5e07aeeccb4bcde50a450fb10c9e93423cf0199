'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Home, ArrowLeft } from 'lucide-react'

const BackButton = () => {
  const handleGoBack = () => {
    if (typeof window !== 'undefined' && window.history.length > 1) {
      window.history.back()
    } else {
      // Fallback to home if no history
      window.location.href = '/'
    }
  }

  return (
    <Button 
      variant="outline" 
      onClick={handleGoBack}
      className="inline-flex items-center gap-2 font-mono"
    >
      <ArrowLeft size={16} />
      Go Back
    </Button>
  )
}

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50/30 flex items-center justify-center px-4">
      <div className="max-w-lg mx-auto text-center">
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-gray-900 mb-4 font-mono">404</h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-4 font-mono">Page Not Found</h2>
          <p className="text-gray-600 leading-relaxed font-mono">
            The page you're looking for doesn't exist or has been moved.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/">
            <Button className="inline-flex items-center gap-2 font-mono">
              <Home size={16} />
              Go Home
            </Button>
          </Link>
          
          <BackButton />
        </div>
        
        <div className="mt-8 p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-3 font-mono">
            Popular Pages
          </h3>
          <div className="space-y-2">
            <Link href="/research/center" className="block text-blue-600 hover:text-blue-800 transition-colors font-mono text-sm">
              Research Center
            </Link>
            <Link href="/programs" className="block text-blue-600 hover:text-blue-800 transition-colors font-mono text-sm">
              Academic Programs
            </Link>
            <Link href="/faculty" className="block text-blue-600 hover:text-blue-800 transition-colors font-mono text-sm">
              Faculty Directory
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
} 