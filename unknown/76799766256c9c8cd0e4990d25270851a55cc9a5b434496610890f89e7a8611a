'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import * as d3 from 'd3';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Network, Info, Layers, Zap, Eye, BarChart3, ZoomIn, ZoomOut, Maximize2 } from 'lucide-react';

interface Node {
  id: string;
  group: number;
  label: string;
  radius?: number;
  description?: string;
  applications?: string;
  techniques?: string;
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
}

interface Link {
  source: string;
  target: string;
  value: number;
  type?: string;
}

interface ForceDirectedGraphProps {
  width?: number;
  height?: number;
  data: {
    nodes: Node[];
    links: Link[];
  };
}

const ForceDirectedGraph: React.FC<ForceDirectedGraphProps> = ({
  width = 1200,
  height = 700,
  data
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [hoveredNode, setHoveredNode] = useState<Node | null>(null);
  const [isSimulating, setIsSimulating] = useState(false);
  const [zoomTransform, setZoomTransform] = useState<d3.ZoomTransform | null>(null);
  const { ref: inViewRef, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  // Modern color palette
  const groupColors = {
    1: '#2563EB', // Blue - Core Methods
    2: '#059669', // Green - Physical Sciences  
    3: '#D97706', // Orange - Life Sciences
    4: '#DC2626', // Red - Social Sciences
    5: '#7C3AED', // Purple - Engineering
  };

  const groupNames = {
    1: 'Core Computational Methods',
    2: 'Physical Sciences',
    3: 'Life Sciences',
    4: 'Social Sciences & Humanities',
    5: 'Engineering & Applied Sciences'
  };

  const connectionTypes = {
    foundational: { color: '#3B82F6', label: 'Foundational Methods' },
    methodological: { color: '#10B981', label: 'Shared Methodology' },
    applied: { color: '#F59E0B', label: 'Applied Computation' },
    interdisciplinary: { color: '#EF4444', label: 'Interdisciplinary Bridge' },
    theoretical: { color: '#8B5CF6', label: 'Theoretical Foundation' },
    emerging: { color: '#EC4899', label: 'Emerging Collaboration' }
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const zoomRef = useRef<d3.ZoomBehavior<SVGSVGElement, unknown> | null>(null);

  const handleZoomIn = useCallback(() => {
    if (svgRef.current && zoomRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().duration(300).call(zoomRef.current.scaleBy, 1.5);
    }
  }, []);

  const handleZoomOut = useCallback(() => {
    if (svgRef.current && zoomRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().duration(300).call(zoomRef.current.scaleBy, 1 / 1.5);
    }
  }, []);

  const handleFitToScreen = useCallback(() => {
    if (svgRef.current && zoomRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().duration(500).call(zoomRef.current.transform, d3.zoomIdentity);
    }
  }, []);

  useEffect(() => {
    if (!isMounted || !svgRef.current || !inView || !data.nodes.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    // Create zoom behavior
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        const { transform } = event;
        setZoomTransform(transform);
        g.attr('transform', transform);
      });

    zoomRef.current = zoom;
    svg.call(zoom);

    // Create main group for all elements
    const g = svg.append('g');

    // Create definitions
    const defs = g.append("defs");
    
    // Enhanced drop shadow
    const filter = defs.append("filter")
      .attr("id", "node-shadow")
      .attr("x", "-50%")
      .attr("y", "-50%")
      .attr("width", "200%")
      .attr("height", "200%");
    
    filter.append("feDropShadow")
      .attr("dx", 0)
      .attr("dy", 2)
      .attr("stdDeviation", 3)
      .attr("flood-opacity", 0.15);

    // Create simulation with better spacing
    const simulation = d3.forceSimulation(data.nodes as d3.SimulationNodeDatum[])
      .force("link", d3.forceLink(data.links)
        .id((d: any) => d.id)
        .distance(150)
        .strength(0.6))
      .force("charge", d3.forceManyBody()
        .strength(-500)
        .distanceMax(400))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force("collision", d3.forceCollide()
        .radius((d: any) => (d.radius || 16) + 15)
        .strength(0.8));

    // Create links with better visibility
    const linkGroup = g.append("g").attr("class", "links");
    
    const link = linkGroup
      .selectAll("line")
      .data(data.links)
      .join("line")
      .attr("stroke", "#E5E7EB")
      .attr("stroke-width", 2)
      .attr("stroke-opacity", 0.5)
      .style("transition", "all 0.3s ease");

    // Create nodes with larger, more interactive circles
    const nodeGroup = g.append("g").attr("class", "nodes");
    
    const node = nodeGroup
      .selectAll(".node")
      .data(data.nodes)
      .join("g")
      .attr("class", "node")
      .style("cursor", "pointer");

    // Larger node circles for better interaction
    const nodeCircle = node.append("circle")
      .attr("r", (d: any) => (d.radius || 16) + 2)
      .attr("fill", (d: any) => groupColors[d.group as keyof typeof groupColors])
      .attr("stroke", "#ffffff")
      .attr("stroke-width", 3)
      .style("filter", "url(#node-shadow)")
      .style("transition", "all 0.3s ease");

    // Node labels with better positioning
    const labels = g.append("g")
      .attr("class", "labels")
      .selectAll("text")
      .data(data.nodes)
      .join("text")
      .text((d: any) => d.label)
      .attr("font-size", 12)
      .attr("font-weight", "600")
      .attr("font-family", "monospace")
      .attr("text-anchor", "middle")
      .attr("dy", (d: any) => (d.radius || 16) + 25)
      .attr("fill", "#374151")
      .style("pointer-events", "none")
      .style("user-select", "none")
      .style("opacity", 0.9);

    // Enhanced interactions
    node
      .on("mouseenter", function(event, d: any) {
        setHoveredNode(d);
        
        // Highlight connections with enhanced effects
        const connectedIds = new Set([d.id]);
        
        link
          .style("stroke", (l: any) => {
            if (l.source.id === d.id || l.target.id === d.id) {
              connectedIds.add(l.source.id);
              connectedIds.add(l.target.id);
              return connectionTypes[l.type as keyof typeof connectionTypes]?.color || '#6B7280';
            }
            return "#E5E7EB";
          })
          .style("stroke-opacity", (l: any) => {
            return (l.source.id === d.id || l.target.id === d.id) ? 0.9 : 0.2;
          })
          .style("stroke-width", (l: any) => {
            return (l.source.id === d.id || l.target.id === d.id) ? 4 : 2;
          });

        nodeCircle
          .style("opacity", (n: any) => connectedIds.has(n.id) ? 1 : 0.4)
          .style("transform", (n: any) => n.id === d.id ? "scale(1.2)" : "scale(1)")
          .style("stroke-width", (n: any) => n.id === d.id ? 4 : 3);

        labels
          .style("opacity", (n: any) => connectedIds.has(n.id) ? 1 : 0.4)
          .style("font-weight", (n: any) => n.id === d.id ? "700" : "600");
      })
      .on("mouseleave", function() {
        setHoveredNode(null);
        
        link
          .style("stroke", "#E5E7EB")
          .style("stroke-opacity", 0.5)
          .style("stroke-width", 2);

        nodeCircle
          .style("opacity", 1)
          .style("transform", "scale(1)")
          .style("stroke-width", 3);

        labels
          .style("opacity", 0.9)
          .style("font-weight", "600");
      })
      .on("click", function(event, d: any) {
        setSelectedNode(selectedNode?.id === d.id ? null : d);
        
        // Add click effect
        d3.select(this).select("circle")
          .transition()
          .duration(200)
          .style("transform", "scale(1.3)")
          .transition()
          .duration(200)
          .style("transform", "scale(1)");
      });

    // Enhanced drag behavior that works with zoom
    node.call(d3.drag<SVGGElement, any>()
      .on("start", (event, d: any) => {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
        setIsSimulating(true);
      })
      .on("drag", (event, d: any) => {
        d.fx = event.x;
        d.fy = event.y;
      })
      .on("end", (event, d: any) => {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
        setTimeout(() => setIsSimulating(false), 800);
      }) as any);

    // Update positions
    simulation.on("tick", () => {
      link
        .attr("x1", (d: any) => d.source.x)
        .attr("y1", (d: any) => d.source.y)
        .attr("x2", (d: any) => d.target.x)
        .attr("y2", (d: any) => d.target.y);

      node
        .attr("transform", (d: any) => `translate(${d.x},${d.y})`);

      labels
        .attr("x", (d: any) => d.x)
        .attr("y", (d: any) => d.y);
    });

    // Auto-stabilize
    setTimeout(() => {
      simulation.stop();
      setIsSimulating(false);
    }, 3000);
    
    return () => {
      simulation.stop();
    };
  }, [data, width, height, isMounted, inView]);

  const currentNode = selectedNode || hoveredNode;
  
  // Calculate most connected field based on actual connections
  const connectionCounts = data.links.reduce((counts, link) => {
    counts[link.source] = (counts[link.source] || 0) + 1;
    counts[link.target] = (counts[link.target] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);
  
  const mostConnectedId = Object.entries(connectionCounts).sort(([,a], [,b]) => b - a)[0]?.[0];
  const mostConnectedField = data.nodes.find(node => node.id === mostConnectedId)?.label || 'Data Science';

  return (
    <div className="w-full" ref={inViewRef}>
      {/* Header Section */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-50 border border-blue-100 text-blue-700 text-sm font-medium mb-4">
          <Network size={16} />
          <span className="font-mono">Interdisciplinary Research Network</span>
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-4 font-mono">
          Interdisciplinary Computational Network
        </h2>
        <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed font-mono">
          Discover how computation transforms research across all disciplines—from physics and biology to economics and linguistics. 
          Each connection represents an opportunity for groundbreaking interdisciplinary collaboration.
        </p>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto">
        
        {/* Stats Bar */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white rounded-xl border border-gray-100 p-4 text-center shadow-sm">
            <div className="text-2xl font-bold text-blue-600 font-mono">{data.nodes.length}</div>
            <div className="text-sm text-gray-600 font-mono">Research Fields</div>
          </div>
          <div className="bg-white rounded-xl border border-gray-100 p-4 text-center shadow-sm">
            <div className="text-2xl font-bold text-green-600 font-mono">{data.links.length}</div>
            <div className="text-sm text-gray-600 font-mono">Connections</div>
          </div>
          <div className="bg-white rounded-xl border border-gray-100 p-4 text-center shadow-sm">
            <div className="text-2xl font-bold text-orange-600 font-mono">{Object.keys(groupNames).length}</div>
            <div className="text-sm text-gray-600 font-mono">Disciplines</div>
          </div>
          <div className="bg-white rounded-xl border border-gray-100 p-4 text-center shadow-sm">
            <div className="text-2xl font-bold text-purple-600 font-mono">
              {isSimulating ? '⚡' : '🔗'}
            </div>
            <div className="text-sm text-gray-600 font-mono">
              {isSimulating ? 'Stabilizing' : 'Interactive'}
            </div>
          </div>
        </div>

        {/* Main Graph Section */}
        <div className="bg-white rounded-2xl border border-gray-100 shadow-lg overflow-hidden mb-8">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900 font-mono">Interactive Research Network</h3>
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2 text-gray-600">
                  <Eye size={16} />
                  <span className="font-mono">Hover to explore connections</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <Zap size={16} />
                  <span className="font-mono">Click for detailed information</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="relative bg-gradient-to-br from-gray-50 via-white to-gray-50">
            {isMounted && (
              <svg
                ref={svgRef}
                width={width}
                height={height}
                viewBox={`0 0 ${width} ${height}`}
                className="w-full h-auto border border-gray-100"
                style={{ minHeight: '600px', maxHeight: '700px' }}
              />
            )}
            
            {/* Zoom Controls */}
            <div className="absolute top-4 right-4 flex flex-col gap-2">
              <button
                onClick={handleZoomIn}
                className="p-2 bg-white/95 hover:bg-white rounded-lg shadow-lg border border-gray-200 transition-colors group"
                title="Zoom In"
              >
                <ZoomIn size={18} className="text-gray-600 group-hover:text-gray-800" />
              </button>
              <button
                onClick={handleZoomOut}
                className="p-2 bg-white/95 hover:bg-white rounded-lg shadow-lg border border-gray-200 transition-colors group"
                title="Zoom Out"
              >
                <ZoomOut size={18} className="text-gray-600 group-hover:text-gray-800" />
              </button>
              <button
                onClick={handleFitToScreen}
                className="p-2 bg-white/95 hover:bg-white rounded-lg shadow-lg border border-gray-200 transition-colors group"
                title="Fit to Screen"
              >
                <Maximize2 size={18} className="text-gray-600 group-hover:text-gray-800" />
              </button>
            </div>

            {/* Enhanced Interaction Guide */}
            <div className="absolute bottom-6 left-6 bg-white/95 backdrop-blur-sm rounded-xl px-4 py-3 shadow-lg border border-gray-200">
              <div className="text-xs font-semibold text-gray-700 mb-2 font-mono">Controls</div>
              <div className="space-y-1 text-xs text-gray-600">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="font-mono">Scroll: Zoom in/out</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="font-mono">Drag background: Pan view</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="font-mono">Drag nodes: Reposition</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="font-mono">Click nodes: View details</span>
                </div>
              </div>
            </div>

            {/* Zoom Level Indicator */}
            {zoomTransform && (
              <div className="absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg px-3 py-2 shadow-sm border border-gray-200">
                <div className="text-xs text-gray-600 font-mono">
                  Zoom: {Math.round(zoomTransform.k * 100)}%
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Bottom Section */}
        <div className="grid lg:grid-cols-3 gap-8">
          
          {/* Legend */}
          <div className="bg-white rounded-2xl border border-gray-100 p-6 shadow-sm">
            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2 font-mono">
              <Layers size={18} />
              Research Disciplines
            </h3>
            <div className="space-y-3">
              {Object.entries(groupNames).map(([group, name]) => (
                <div key={group} className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div
                    className="w-5 h-5 rounded-full border-2 border-white shadow-sm"
                    style={{ backgroundColor: groupColors[parseInt(group) as keyof typeof groupColors] }}
                  />
                  <span className="text-sm font-medium text-gray-700 font-mono">{name}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Selected Node Details */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              {currentNode ? (
                <motion.div
                  key={currentNode.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden"
                >
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center gap-4 mb-4">
                      <div
                        className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                        style={{ backgroundColor: groupColors[currentNode.group as keyof typeof groupColors] }}
                      />
                      <h3 className="text-xl font-bold text-gray-900 font-mono">{currentNode.label}</h3>
                      <span className="text-sm px-2 py-1 bg-gray-100 rounded-full text-gray-600 font-mono">
                        {groupNames[currentNode.group as keyof typeof groupNames]}
                      </span>
                    </div>
                    <p className="text-gray-600 leading-relaxed font-mono">
                      {currentNode.description}
                    </p>
                  </div>
                  
                  <div className="p-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      {currentNode.applications && (
                        <div>
                          <h4 className="text-sm font-bold text-gray-700 mb-3 flex items-center gap-2 font-mono">
                            <BarChart3 size={16} />
                            Key Applications
                          </h4>
                          <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                            <p className="text-sm text-blue-800 leading-relaxed font-mono">{currentNode.applications}</p>
                          </div>
                        </div>
                      )}
                      
                      {currentNode.techniques && (
                        <div>
                          <h4 className="text-sm font-bold text-gray-700 mb-3 flex items-center gap-2 font-mono">
                            <Info size={16} />
                            Core Techniques
                          </h4>
                          <div className="bg-green-50 rounded-lg p-4 border border-green-100">
                            <p className="text-sm text-green-800 leading-relaxed font-mono">{currentNode.techniques}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="bg-gradient-to-br from-gray-50 to-white rounded-2xl border-2 border-dashed border-gray-300 p-12 text-center"
                >
                  <Network size={64} className="text-gray-300 mx-auto mb-6" />
                  <h3 className="text-xl font-semibold text-gray-500 mb-3 font-mono">Explore Research Fields</h3>
                  <p className="text-gray-400 leading-relaxed max-w-md mx-auto font-mono">
                    Hover over any field in the network above to see its connections, or click to learn more about 
                    specific research areas and their computational applications.
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Insights Section */}
        <div className="mt-12 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-100 p-8">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-xl font-bold text-blue-900 mb-6 font-mono">Interdisciplinary Insights</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white/80 rounded-xl p-6 shadow-sm">
                <div className="text-3xl mb-3">🧮</div>
                <div className="text-sm text-blue-800 font-medium font-mono">Computational methods transform every discipline</div>
              </div>
              <div className="bg-white/80 rounded-xl p-6 shadow-sm">
                <div className="text-3xl mb-3">🌍</div>
                <div className="text-sm text-green-800 font-medium font-mono">Climate science bridges physics and policy</div>
              </div>
              <div className="bg-white/80 rounded-xl p-6 shadow-sm">
                <div className="text-3xl mb-3">🧬</div>
                <div className="text-sm text-orange-800 font-medium font-mono">Biology meets computing for new discoveries</div>
              </div>
              <div className="bg-white/80 rounded-xl p-6 shadow-sm">
                <div className="text-3xl mb-3">🤝</div>
                <div className="text-sm text-purple-800 font-medium font-mono">Social sciences gain computational power</div>
              </div>
            </div>
            <div className="mt-8 p-4 bg-white/60 rounded-xl">
              <p className="text-blue-700 font-medium italic font-mono">
                "True innovation happens at the intersection of computational methods and domain expertise"
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForceDirectedGraph; 