'use client'

import Link from "next/link"
import { useState, useEffect } from "react"
import { ArrowRight, CalendarDays, UserCircle, Tag, Share2, Facebook, Twitter, Linkedin, Copy, Check, Clock, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"

interface BlogPostContentProps {
  post: any;
  relatedPosts: any[];
}

export default function BlogPostContent({ post, relatedPosts }: BlogPostContentProps) {
  const [showShareTooltip, setShowShareTooltip] = useState(false);
  
  // Progress state for scroll tracking
  const [progress, setProgress] = useState(0);
  
  // Track scroll progress
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      setProgress(scrollPercent);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // Handle share link copy
  const handleCopyLink = () => {
    navigator.clipboard.writeText(window.location.href);
    setShowShareTooltip(true);
    toast({
      title: "Link copied to clipboard",
      description: "You can now share this article with others",
      duration: 3000,
    });
    setTimeout(() => setShowShareTooltip(false), 2000);
  };

  // Process HTML content to add classes to elements
  const processedContent = post.content ? enhanceHtmlContent(post.content) : "";

  return (
    <>
      {/* Reading progress bar */}
      <div className="fixed top-0 left-0 w-full h-1 z-50">
        <div 
          className="h-full bg-primary transition-all duration-150 ease-out"
          style={{ width: `${progress}%` }}
        />
      </div>
      
      <article className="container mx-auto px-4 md:px-6 py-8">
        {/* Article header */}
        <header className="mb-10 max-w-3xl mx-auto">
          <div className="space-y-4 text-center">
            <Badge className="mb-2">{post.category}</Badge>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight text-foreground">
              {post.title}
            </h1>
            
            <div className="flex flex-wrap justify-center items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <CalendarDays className="mr-1.5 h-4 w-4" />
                <span>{post.date}</span>
              </div>
              <div className="flex items-center">
                <Clock className="mr-1.5 h-4 w-4" />
                <span>{post.readingTime}</span>
              </div>
              <div className="flex items-center">
                <Eye className="mr-1.5 h-4 w-4" />
                <span>{post.viewCount} views</span>
              </div>
            </div>
          </div>
          
          {/* Featured image */}
          {post.imageUrl && (
            <div className="mt-8">
              <img 
                src={post.imageUrl} 
                alt={post.imageAlt || post.title} 
                className="w-full rounded-lg object-cover aspect-video shadow-lg"
              />
            </div>
          )}
        </header>
        
        {/* Article content in a nice readable format */}
        <div className="flex flex-col lg:flex-row gap-8 lg:gap-12">
          {/* Sidebar with sharing options (for larger screens) */}
          <aside className="hidden lg:block lg:w-16 h-fit sticky top-24">
            <div className="flex flex-col items-center gap-4">
              <p className="text-xs font-medium text-muted-foreground">SHARE</p>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button size="icon" variant="outline" className="rounded-full h-10 w-10">
                      <Facebook className="h-4 w-4" />
                      <span className="sr-only">Share on Facebook</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Share on Facebook</TooltipContent>
                </Tooltip>
                
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button size="icon" variant="outline" className="rounded-full h-10 w-10">
                      <Twitter className="h-4 w-4" />
                      <span className="sr-only">Share on Twitter</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Share on Twitter</TooltipContent>
                </Tooltip>
                
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button size="icon" variant="outline" className="rounded-full h-10 w-10">
                      <Linkedin className="h-4 w-4" />
                      <span className="sr-only">Share on LinkedIn</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Share on LinkedIn</TooltipContent>
                </Tooltip>
                
                <Tooltip open={showShareTooltip} onOpenChange={setShowShareTooltip}>
                  <TooltipTrigger asChild>
                    <Button 
                      size="icon" 
                      variant="outline" 
                      className="rounded-full h-10 w-10"
                      onClick={handleCopyLink}
                    >
                      {showShareTooltip ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      <span className="sr-only">Copy link</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{showShareTooltip ? "Copied!" : "Copy link"}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </aside>
          
          {/* Main content */}
          <div className="max-w-3xl mx-auto lg:mx-0 flex-1">
            {/* Post content */}
            <div 
              className="prose prose-lg dark:prose-invert max-w-none article-content"
              dangerouslySetInnerHTML={{ __html: processedContent }}
            />
            
            {/* Mobile sharing options */}
            <div className="mt-8 lg:hidden">
              <p className="text-sm font-medium text-muted-foreground mb-4">Share this article</p>
              <div className="flex gap-2">
                <Button size="icon" variant="outline" className="rounded-full">
                  <Facebook className="h-4 w-4" />
                  <span className="sr-only">Share on Facebook</span>
                </Button>
                <Button size="icon" variant="outline" className="rounded-full">
                  <Twitter className="h-4 w-4" />
                  <span className="sr-only">Share on Twitter</span>
                </Button>
                <Button size="icon" variant="outline" className="rounded-full">
                  <Linkedin className="h-4 w-4" />
                  <span className="sr-only">Share on LinkedIn</span>
                </Button>
                <Button 
                  size="icon" 
                  variant="outline" 
                  className="rounded-full"
                  onClick={handleCopyLink}
                >
                  {showShareTooltip ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  <span className="sr-only">Copy link</span>
                </Button>
              </div>
            </div>
            
            {/* Tags */}
            {post.tags && post.tags.length > 0 && (
              <div className="mt-10 pt-6 border-t">
                <h3 className="text-sm font-semibold uppercase tracking-wider text-muted-foreground mb-3 inline-flex items-center">
                  <Tag className="mr-2 h-4 w-4" /> Tags
                </h3>
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag: string) => (
                    <Badge key={tag} variant="secondary" className="text-sm">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {/* Author info */}
            <div className="mt-10 pt-6 border-t">
              <div className="flex items-center">
                <Avatar className="h-12 w-12 mr-4">
                  <AvatarImage src={post.authorAvatar} alt={post.author} />
                  <AvatarFallback>{post.author.split(' ').map((n: string) => n[0]).join('')}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{post.author}</p>
                  <p className="text-sm text-muted-foreground">{post.authorBio}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Custom styles for article content */}
        <style jsx global>{`
          .article-content {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.8;
            color: hsl(var(--foreground));
          }
          
          .article-content h2 {
            font-size: 1.75rem;
            font-weight: 700;
            margin-top: 2.5rem;
            margin-bottom: 1.25rem;
            color: hsl(var(--primary));
            border-bottom: 1px solid hsl(var(--border) / 0.5);
            padding-bottom: 0.5rem;
          }
          
          .article-content h3 {
            font-size: 1.4rem;
            font-weight: 600;
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: hsl(var(--primary));
          }
          
          .article-content p {
            margin-bottom: 1.5rem;
            font-size: 1.125rem;
          }
          
          .article-content ul,
          .article-content ol {
            margin-bottom: 1.5rem;
            padding-left: 1.5rem;
          }
          
          .article-content li {
            margin-bottom: 0.5rem;
          }
          
          .article-content blockquote {
            border-left: 4px solid hsl(var(--primary) / 0.5);
            padding-left: 1.5rem;
            font-style: italic;
            margin: 2rem 0;
            color: hsl(var(--muted-foreground));
            background-color: hsl(var(--muted) / 0.3);
            padding: 1.5rem;
            border-radius: 0.5rem;
          }
          
          .article-content blockquote p {
            margin-bottom: 0;
          }
          
          .article-content a {
            color: hsl(var(--primary));
            text-decoration: underline;
            text-decoration-thickness: 1px;
            text-underline-offset: 2px;
            transition: text-decoration-color 0.2s ease;
          }
          
          .article-content a:hover {
            text-decoration-color: hsl(var(--primary));
          }
          
          .article-content code {
            background-color: hsl(var(--muted));
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-family: monospace;
          }
          
          .article-content pre {
            background-color: hsl(var(--muted));
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1.5rem 0;
          }
          
          .article-content pre code {
            background-color: transparent;
            padding: 0;
            font-size: 0.875rem;
            color: hsl(var(--foreground));
          }
          
          .article-content img {
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 2rem 0;
          }
          
          .article-content hr {
            border: 0;
            border-top: 1px solid hsl(var(--border));
            margin: 2rem 0;
          }
          
          .article-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            font-size: 0.875rem;
          }
          
          .article-content th {
            background-color: hsl(var(--muted));
            font-weight: 600;
            text-align: left;
            padding: 0.75rem;
            border: 1px solid hsl(var(--border));
          }
          
          .article-content td {
            padding: 0.75rem;
            border: 1px solid hsl(var(--border));
          }
          
          .article-content tr:nth-child(even) {
            background-color: hsl(var(--muted) / 0.3);
          }
          
          @media (max-width: 640px) {
            .article-content {
              font-size: 1rem;
              line-height: 1.7;
            }
            
            .article-content h2 {
              font-size: 1.5rem;
            }
            
            .article-content h3 {
              font-size: 1.25rem;
            }
            
            .article-content p {
              font-size: 1rem;
            }
          }
        `}</style>
      </article>
      
      {/* Related posts */}
      {relatedPosts.length > 0 && (
        <section className="bg-muted/50 py-16 mt-16">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-2xl font-bold mb-8">You might also like</h2>
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {relatedPosts.map(relatedPost => (
                <Card key={relatedPost.id} className="flex flex-col h-full overflow-hidden">
                  <div className="h-48 overflow-hidden">
                    <img 
                      src={relatedPost.imageUrl || "/placeholder.svg?height=400&width=600"} 
                      alt={relatedPost.title}
                      className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                    />
                  </div>
                  <CardContent className="flex-grow p-6">
                    <Badge variant="outline" className="mb-2">{relatedPost.category}</Badge>
                    <h3 className="text-xl font-semibold mb-2 line-clamp-2">
                      <Link href={`/posts/${relatedPost.slug}`} className="hover:text-primary transition-colors">
                        {relatedPost.title}
                      </Link>
                    </h3>
                    <p className="text-muted-foreground line-clamp-3">{relatedPost.excerpt}</p>
                  </CardContent>
                  <CardFooter className="border-t p-6 pt-4">
                    <div className="flex justify-between items-center w-full">
                      <span className="text-sm text-muted-foreground">{relatedPost.date}</span>
                      <Link 
                        href={`/posts/${relatedPost.slug}`} 
                        className="text-sm font-medium text-primary hover:underline flex items-center"
                      >
                        Read More <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}
      
      {/* Newsletter subscription */}
      <section className="bg-primary text-primary-foreground py-16">
        <div className="container mx-auto px-4 md:px-6 text-center">
          <div className="max-w-md mx-auto">
            <h2 className="text-2xl font-bold mb-2">Subscribe to our newsletter</h2>
            <p className="mb-6">
              Get the latest posts and updates delivered straight to your inbox.
            </p>
            <form className="flex flex-col sm:flex-row gap-3">
              <input 
                type="email" 
                placeholder="Enter your email" 
                className="flex-grow px-4 py-2 rounded-md bg-primary-foreground/10 text-primary-foreground border border-primary-foreground/20 focus:outline-none focus:ring-2 focus:ring-primary-foreground/50"
                required
              />
              <Button variant="secondary">Subscribe</Button>
            </form>
          </div>
        </div>
      </section>
      
      <Toaster />
    </>
  )
}

// Helper function to process HTML content
function enhanceHtmlContent(html: string): string {
  // This is a simple example. In a real app, you'd use a proper HTML parser.
  // For security reasons, you should use a sanitizer like DOMPurify
  
  // Add a class to first paragraph for a nice intro effect
  html = html.replace(/<p>(.*?)<\/p>/, '<p class="text-lg font-medium leading-relaxed text-muted-foreground">$1</p>');
  
  // Add a transition class to all images
  html = html.replace(/<img/g, '<img class="transition-all duration-300 hover:shadow-lg"');
  
  // Add a special style to blockquotes
  html = html.replace(/<blockquote>/g, '<blockquote class="italic text-muted-foreground">');
  
  return html;
} 