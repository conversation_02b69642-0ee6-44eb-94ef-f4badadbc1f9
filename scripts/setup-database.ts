import { PrismaClient, UserRole, UserStatus } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function createDefaultUsers() {
  console.log('🔧 Creating default users...')

  // Create System Admin
  const hashedPassword = await bcrypt.hash('defaultpassword123', 12)
  
  const sysAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'System Administrator',
      password: hashedPassword,
      role: UserRole.SYS_ADMIN,
      status: UserStatus.ACTIVE,
      profile: {
        create: {
          firstName: 'System',
          lastName: 'Administrator',
          bio: 'System administrator with full access to all features.'
        }
      }
    }
  })

  // Create College Admin
  const collegeAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'College Administrator',
      password: hashedPassword,
      role: UserRole.COLLEGE_ADMIN,
      status: UserStatus.ACTIVE,
      profile: {
        create: {
          firstName: 'College',
          lastName: 'Administrator',
          bio: 'College administrator managing content and faculty.'
        }
      }
    }
  })

  // Create Sample Faculty User
  const faculty = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Dr. Sample Faculty',
      password: hashedPassword,
      role: UserRole.FACULTY,
      status: UserStatus.ACTIVE,
      profile: {
        create: {
          firstName: 'Sample',
          lastName: 'Faculty',
          bio: 'Sample faculty member for testing purposes.'
        }
      }
    }
  })

  // Create Sample Student
  const student = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Sample Student',
      password: hashedPassword,
      role: UserRole.STUDENT,
      status: UserStatus.ACTIVE,
      profile: {
        create: {
          firstName: 'Sample',
          lastName: 'Student',
          bio: 'Sample student for testing purposes.'
        }
      }
    }
  })

  console.log('✅ Default users created:')
  console.log(`   - System Admin: <EMAIL>`)
  console.log(`   - College Admin: <EMAIL>`)
  console.log(`   - Faculty: <EMAIL>`)
  console.log(`   - Student: <EMAIL>`)
  console.log(`   - Password for all: defaultpassword123`)
}

async function createDefaultCategories() {
  console.log('🔧 Creating default categories...')

  const categories = [
    {
      name: 'College News',
      slug: 'college-news',
      description: 'Official college announcements and news',
      color: '#3B82F6'
    },
    {
      name: 'Events',
      slug: 'events',
      description: 'Campus events and activities',
      color: '#10B981'
    },
    {
      name: 'Faculty Spotlight',
      slug: 'faculty-spotlight',
      description: 'Highlighting our amazing faculty members',
      color: '#8B5CF6'
    },
    {
      name: 'Student Achievements',
      slug: 'student-achievements',
      description: 'Celebrating student accomplishments',
      color: '#F59E0B'
    },
    {
      name: 'Research',
      slug: 'research',
      description: 'Research updates and breakthroughs',
      color: '#EF4444'
    }
  ]

  for (const category of categories) {
    await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: category
    })
  }

  console.log('✅ Default categories created')
}

async function createDefaultTags() {
  console.log('🔧 Creating default tags...')

  const tags = [
    'announcement', 'blog', 'community', 'event', 'technology', 
    'students', 'innovation', 'symposium', 'ai', 'ethics', 
    'faculty', 'computer science', 'research', 'sustainability',
    'environment', 'robotics', 'competition', 'achievements'
  ]

  for (const tagName of tags) {
    await prisma.tag.upsert({
      where: { slug: tagName.toLowerCase().replace(/\s+/g, '-') },
      update: {},
      create: {
        name: tagName,
        slug: tagName.toLowerCase().replace(/\s+/g, '-')
      }
    })
  }

  console.log('✅ Default tags created')
}

async function main() {
  try {
    console.log('🚀 Setting up database with default data...')
    
    await createDefaultUsers()
    await createDefaultCategories()
    await createDefaultTags()
    
    console.log('✅ Database setup completed successfully!')
    console.log('')
    console.log('🔑 You can now sign in with:')
    console.log('   - <EMAIL> (System Admin)')
    console.log('   - <EMAIL> (College Admin)')
    console.log('   - <EMAIL> (Faculty)')
    console.log('   - <EMAIL> (Student)')
    console.log('   - Password: defaultpassword123')
    console.log('')
    console.log('🌐 Access the admin panel at: http://localhost:3000/admin')
    console.log('🎓 Access the faculty portal at: http://localhost:3000/faculty-portal')
    
  } catch (error) {
    console.error('❌ Setup failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

export { main as setupDatabase }
