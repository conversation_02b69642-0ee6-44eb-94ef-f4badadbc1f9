import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testPostPermissions() {
  console.log('🔒 Testing post permissions and security...\n')

  try {
    // Find <PERSON><PERSON>'s test post
    const monilPost = await prisma.post.findFirst({
      where: {
        slug: 'machine-learning-applications-computer-vision'
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    })

    if (!monilPost) {
      console.error('❌ Monil\'s test post not found!')
      return
    }

    console.log(`📝 Found test post: "${monilPost.title}"`)
    console.log(`👤 Author: ${monilPost.author.name} (${monilPost.author.email})`)
    console.log(`🔑 Author Role: ${monilPost.author.role}`)
    console.log(`📅 Created: ${monilPost.createdAt.toISOString()}`)
    console.log(`🆔 Post ID: ${monilPost.id}\n`)

    // Get different user types
    const users = await prisma.user.findMany({
      where: {
        email: {
          in: [
            '<EMAIL>', // Faculty (author)
            '<EMAIL>',       // SysAdmin
            '<EMAIL>',            // College Admin
            '<EMAIL>',                  // Another Faculty
            '<EMAIL>'                   // Student
          ]
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true
      }
    })

    console.log('👥 Available users for testing:')
    users.forEach(user => {
      console.log(`   - ${user.name} (${user.email}) - ${user.role}`)
    })
    console.log()

    // Test permissions for each user
    for (const user of users) {
      console.log(`🧪 Testing permissions for: ${user.name} (${user.role})`)
      
      // Test 1: Can view post (should be true for all)
      const canView = true // Posts are public when published
      console.log(`   📖 Can view post: ${canView ? '✅ YES' : '❌ NO'}`)

      // Test 2: Can edit post
      let canEdit = false
      if (user.role === 'SYS_ADMIN') {
        // SysAdmin can edit all posts
        canEdit = true
      } else if (user.role === 'COLLEGE_ADMIN') {
        // College Admin can edit all posts
        canEdit = true
      } else if (user.role === 'FACULTY' && user.id === monilPost.authorId) {
        // Faculty can only edit their own posts
        canEdit = true
      } else {
        // Students and other faculty cannot edit
        canEdit = false
      }
      console.log(`   ✏️  Can edit post: ${canEdit ? '✅ YES' : '❌ NO'}`)

      // Test 3: Can delete post
      let canDelete = false
      if (user.role === 'SYS_ADMIN') {
        // SysAdmin can delete all posts
        canDelete = true
      } else if (user.role === 'COLLEGE_ADMIN') {
        // College Admin can delete all posts
        canDelete = true
      } else if (user.role === 'FACULTY' && user.id === monilPost.authorId) {
        // Faculty can only delete their own posts
        canDelete = true
      } else {
        // Students and other faculty cannot delete
        canDelete = false
      }
      console.log(`   🗑️  Can delete post: ${canDelete ? '✅ YES' : '❌ NO'}`)

      // Test 4: Can change post status to ARCHIVED
      let canArchive = false
      if (user.role === 'SYS_ADMIN') {
        // Only SysAdmin can archive posts
        canArchive = true
      } else if (user.role === 'COLLEGE_ADMIN') {
        // College Admin can archive posts
        canArchive = true
      } else {
        // Faculty cannot archive posts (as per business rules)
        canArchive = false
      }
      console.log(`   📦 Can archive post: ${canArchive ? '✅ YES' : '❌ NO'}`)

      console.log()
    }

    // Test API endpoint access patterns
    console.log('🌐 API Endpoint Access Patterns:')
    console.log()
    
    console.log('📍 Faculty API Endpoints (/api/faculty/posts):')
    console.log('   - GET /api/faculty/posts')
    console.log('     ✅ Monil (author): Can list own posts')
    console.log('     ❌ Other Faculty: Cannot see Monil\'s posts')
    console.log('     ❌ Students: No access')
    console.log('     ❌ Admins: No access (use admin endpoints)')
    console.log()
    
    console.log('   - GET /api/faculty/posts/[id]')
    console.log('     ✅ Monil (author): Can view own post details')
    console.log('     ❌ Other Faculty: Cannot access Monil\'s post')
    console.log('     ❌ Students: No access')
    console.log('     ❌ Admins: No access (use admin endpoints)')
    console.log()
    
    console.log('   - PUT /api/faculty/posts/[id]')
    console.log('     ✅ Monil (author): Can edit own post')
    console.log('     ❌ Other Faculty: Cannot edit Monil\'s post')
    console.log('     ❌ Students: No access')
    console.log('     ❌ Admins: No access (use admin endpoints)')
    console.log()
    
    console.log('   - DELETE /api/faculty/posts/[id]')
    console.log('     ✅ Monil (author): Can delete own post')
    console.log('     ❌ Other Faculty: Cannot delete Monil\'s post')
    console.log('     ❌ Students: No access')
    console.log('     ❌ Admins: No access (use admin endpoints)')
    console.log()

    console.log('📍 Admin API Endpoints (/api/admin/posts):')
    console.log('   - GET /api/admin/posts')
    console.log('     ❌ Monil (faculty): No access')
    console.log('     ❌ Other Faculty: No access')
    console.log('     ❌ Students: No access')
    console.log('     ✅ SysAdmin: Can list all posts')
    console.log('     ✅ College Admin: Can list all posts')
    console.log()
    
    console.log('   - PUT /api/admin/posts/[id]')
    console.log('     ❌ Monil (faculty): No access')
    console.log('     ❌ Other Faculty: No access')
    console.log('     ❌ Students: No access')
    console.log('     ✅ SysAdmin: Can edit any post (including Monil\'s)')
    console.log('     ✅ College Admin: Can edit any post (including Monil\'s)')
    console.log()

    console.log('📍 Public API Endpoints (/api/posts):')
    console.log('   - GET /api/posts')
    console.log('     ✅ Everyone: Can view published posts')
    console.log()
    
    console.log('   - GET /api/posts/[slug]')
    console.log('     ✅ Everyone: Can view published post details')
    console.log()

    // Security summary
    console.log('🛡️  SECURITY SUMMARY:')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log()
    console.log('✅ CORRECT PERMISSIONS:')
    console.log('   • Monil can edit/delete ONLY his own posts')
    console.log('   • SysAdmin can edit/delete ANY post (including Monil\'s)')
    console.log('   • College Admin can edit/delete ANY post (including Monil\'s)')
    console.log('   • Other Faculty CANNOT edit/delete Monil\'s posts')
    console.log('   • Students CANNOT edit/delete any posts')
    console.log('   • Published posts are visible to everyone')
    console.log()
    console.log('🔒 SECURITY FEATURES:')
    console.log('   • Role-based access control (RBAC)')
    console.log('   • Author-based ownership for faculty')
    console.log('   • Separate API endpoints for faculty vs admin')
    console.log('   • Database-level user ID validation')
    console.log('   • Session-based authentication')
    console.log()
    console.log('⚠️  RESTRICTIONS:')
    console.log('   • Faculty cannot archive posts (admin-only)')
    console.log('   • Faculty cannot access admin endpoints')
    console.log('   • Cross-faculty editing is prohibited')
    console.log('   • All operations require valid authentication')

  } catch (error) {
    console.error('❌ Error testing permissions:', error)
    throw error
  }
}

testPostPermissions()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
