import { PrismaClient, PostStatus } from '@prisma/client'

const prisma = new PrismaClient()

async function createMonilTestPost() {
  console.log('Creating test post by <PERSON><PERSON>...')

  try {
    // Find <PERSON><PERSON>hikari's user account
    const monilUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        facultyProfile: {
          include: {
            department: true
          }
        }
      }
    })

    if (!monilUser) {
      console.error('Mon<PERSON> Ad<PERSON>kari user not found!')
      return
    }

    console.log(`Found user: ${monilUser.name} (${monilUser.email})`)
    console.log(`Department: ${monilUser.facultyProfile?.department?.name || 'Not assigned'}`)

    // Find a suitable category
    const category = await prisma.category.findFirst({
      where: { 
        OR: [
          { slug: 'academic-research' },
          { slug: 'college-news' },
          { slug: 'faculty-insights' }
        ]
      }
    })

    if (!category) {
      console.error('No suitable category found!')
      return
    }

    console.log(`Using category: ${category.name}`)

    // Check if a test post by Monil already exists
    const existingPost = await prisma.post.findFirst({
      where: {
        authorId: monilUser.id,
        slug: 'machine-learning-applications-computer-vision'
      }
    })

    if (existingPost) {
      console.log('Test post by Monil already exists!')
      console.log(`Post ID: ${existingPost.id}`)
      console.log(`Post Title: ${existingPost.title}`)
      return existingPost
    }

    // Create the test post
    const testPost = await prisma.post.create({
      data: {
        title: 'Machine Learning Applications in Computer Vision',
        slug: 'machine-learning-applications-computer-vision',
        content: `# Machine Learning Applications in Computer Vision

## Introduction

Computer vision has revolutionized how machines interpret and understand visual information. In this comprehensive overview, I'll explore the cutting-edge applications of machine learning in computer vision, drawing from my research and industry experience.

## Mathematical Foundations

The core of computer vision lies in mathematical transformations. Consider the fundamental convolution operation:

$$
(f * g)(t) = \\int_{-\\infty}^{\\infty} f(\\tau) g(t - \\tau) d\\tau
$$

For discrete images, this becomes:

$$
(I * K)(i,j) = \\sum_{m} \\sum_{n} I(m,n) \\cdot K(i-m, j-n)
$$

Where $I$ is the input image and $K$ is the convolution kernel.

## Deep Learning Architectures

### Convolutional Neural Networks (CNNs)

CNNs have become the backbone of modern computer vision. The architecture typically consists of:

1. **Convolutional Layers**: Extract features using learnable filters
2. **Pooling Layers**: Reduce spatial dimensions
3. **Fully Connected Layers**: Perform final classification

\`\`\`python
import torch
import torch.nn as nn
import torch.nn.functional as F

class SimpleCNN(nn.Module):
    def __init__(self, num_classes=10):
        super(SimpleCNN, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        
        self.pool = nn.MaxPool2d(2, 2)
        self.dropout = nn.Dropout(0.5)
        
        self.fc1 = nn.Linear(128 * 4 * 4, 512)
        self.fc2 = nn.Linear(512, num_classes)
    
    def forward(self, x):
        # Feature extraction
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = self.pool(F.relu(self.conv3(x)))
        
        # Flatten and classify
        x = x.view(-1, 128 * 4 * 4)
        x = self.dropout(F.relu(self.fc1(x)))
        x = self.fc2(x)
        
        return x

# Model instantiation
model = SimpleCNN(num_classes=1000)
print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
\`\`\`

### Attention Mechanisms

The attention mechanism has transformed computer vision, particularly with Vision Transformers (ViTs):

$$
\\text{Attention}(Q, K, V) = \\text{softmax}\\left(\\frac{QK^T}{\\sqrt{d_k}}\\right)V
$$

## Real-World Applications

### 1. Medical Image Analysis

Computer vision is revolutionizing healthcare through:

- **Diagnostic Imaging**: Automated detection of tumors in MRI scans
- **Pathology**: Digital slide analysis for cancer detection
- **Surgical Assistance**: Real-time guidance during procedures

### 2. Autonomous Vehicles

Key components include:

- **Object Detection**: Identifying pedestrians, vehicles, and obstacles
- **Lane Detection**: Maintaining proper vehicle positioning
- **Depth Estimation**: Understanding 3D scene geometry

### 3. Industrial Quality Control

Applications in manufacturing:

- **Defect Detection**: Automated inspection of products
- **Assembly Verification**: Ensuring correct component placement
- **Predictive Maintenance**: Visual monitoring of equipment condition

## Performance Metrics

When evaluating computer vision models, we use several key metrics:

| Metric | Formula | Use Case |
|--------|---------|----------|
| Accuracy | $\\frac{TP + TN}{TP + TN + FP + FN}$ | Overall performance |
| Precision | $\\frac{TP}{TP + FP}$ | False positive control |
| Recall | $\\frac{TP}{TP + FN}$ | False negative control |
| F1-Score | $\\frac{2 \\cdot \\text{Precision} \\cdot \\text{Recall}}{\\text{Precision} + \\text{Recall}}$ | Balanced evaluation |
| IoU | $\\frac{|A \\cap B|}{|A \\cup B|}$ | Object detection |

## Current Research Directions

### Self-Supervised Learning

Recent advances in self-supervised learning are reducing the dependency on labeled data:

\`\`\`python
# Example: SimCLR-style contrastive learning
def contrastive_loss(z_i, z_j, temperature=0.5):
    """
    Compute contrastive loss for self-supervised learning
    """
    batch_size = z_i.shape[0]
    
    # Normalize representations
    z_i = F.normalize(z_i, dim=1)
    z_j = F.normalize(z_j, dim=1)
    
    # Compute similarity matrix
    representations = torch.cat([z_i, z_j], dim=0)
    similarity_matrix = torch.matmul(representations, representations.T)
    
    # Create labels for positive pairs
    labels = torch.cat([torch.arange(batch_size) for _ in range(2)], dim=0)
    labels = (labels.unsqueeze(0) == labels.unsqueeze(1)).float()
    
    # Remove self-similarity
    mask = torch.eye(labels.shape[0], dtype=torch.bool)
    labels = labels[~mask].view(labels.shape[0], -1)
    similarity_matrix = similarity_matrix[~mask].view(similarity_matrix.shape[0], -1)
    
    # Compute loss
    positives = similarity_matrix[labels.bool()].view(labels.shape[0], -1)
    negatives = similarity_matrix[~labels.bool()].view(similarity_matrix.shape[0], -1)
    
    logits = torch.cat([positives, negatives], dim=1)
    labels = torch.zeros(logits.shape[0], dtype=torch.long)
    
    return F.cross_entropy(logits / temperature, labels)
\`\`\`

### Multimodal Learning

Combining vision with other modalities:

- **Vision-Language Models**: CLIP, DALL-E, GPT-4V
- **Vision-Audio**: Scene understanding through multiple senses
- **Robotics**: Integrating visual perception with motor control

## Challenges and Future Directions

### 1. Robustness and Generalization

Current challenges include:

- **Domain Adaptation**: Models trained on one dataset may fail on another
- **Adversarial Attacks**: Vulnerability to carefully crafted perturbations
- **Out-of-Distribution Detection**: Identifying when inputs differ from training data

### 2. Efficiency and Deployment

Key considerations:

- **Model Compression**: Reducing model size for mobile deployment
- **Edge Computing**: Running inference on resource-constrained devices
- **Real-time Processing**: Meeting latency requirements for critical applications

### 3. Ethical Considerations

Important aspects:

- **Bias and Fairness**: Ensuring equitable performance across demographics
- **Privacy**: Protecting individual privacy in surveillance applications
- **Transparency**: Making AI decisions interpretable and explainable

## Conclusion

Machine learning in computer vision continues to evolve rapidly, with new architectures and applications emerging regularly. As we advance, the focus is shifting from pure performance improvements to creating more robust, efficient, and ethical systems.

The integration of computer vision with other AI domains promises exciting developments in the coming years. From healthcare to autonomous systems, the impact of these technologies will continue to grow, making it essential for researchers and practitioners to stay current with the latest developments.

## References

1. LeCun, Y., Bengio, Y., & Hinton, G. (2015). Deep learning. Nature, 521(7553), 436-444.
2. Dosovitskiy, A., et al. (2020). An image is worth 16x16 words: Transformers for image recognition at scale. ICLR.
3. He, K., et al. (2016). Deep residual learning for image recognition. CVPR.
4. Chen, T., et al. (2020). A simple framework for contrastive learning of visual representations. ICML.

---

*Dr. Monil Adhikari is a faculty member specializing in machine learning and computer vision. His research focuses on developing robust and efficient algorithms for real-world applications.*`,
        excerpt: 'A comprehensive exploration of machine learning applications in computer vision, covering mathematical foundations, deep learning architectures, real-world applications, and current research directions.',
        authorId: monilUser.id,
        categoryId: category.id,
        status: PostStatus.PUBLISHED,
        featured: true,
        publishedAt: new Date(),
        readingTime: '12 min read',
        imageUrl: '/images/computer-vision-ml.jpg',
        imageAlt: 'Machine learning and computer vision concept illustration'
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        }
      }
    })

    // Add some tags
    const tags = ['machine-learning', 'computer-vision', 'deep-learning', 'ai', 'research']
    
    for (const tagName of tags) {
      const tagSlug = tagName.toLowerCase().replace(/\s+/g, '-')
      
      // Create tag if it doesn't exist
      const tag = await prisma.tag.upsert({
        where: { slug: tagSlug },
        update: {},
        create: {
          name: tagName.charAt(0).toUpperCase() + tagName.slice(1).replace(/-/g, ' '),
          slug: tagSlug
        }
      })

      // Link tag to post
      await prisma.postTag.create({
        data: {
          postId: testPost.id,
          tagId: tag.id
        }
      })
    }

    console.log('✅ Test post created successfully!')
    console.log(`Post ID: ${testPost.id}`)
    console.log(`Post Title: ${testPost.title}`)
    console.log(`Post Slug: ${testPost.slug}`)
    console.log(`Author: ${testPost.author.name} (${testPost.author.email})`)
    console.log(`Category: ${testPost.category.name}`)
    console.log(`Status: ${testPost.status}`)
    console.log(`Featured: ${testPost.featured}`)
    console.log(`Tags: ${tags.join(', ')}`)
    
    return testPost

  } catch (error) {
    console.error('Error creating test post:', error)
    throw error
  }
}

createMonilTestPost()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
