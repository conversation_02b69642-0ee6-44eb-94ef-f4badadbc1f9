const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  // Departments to add
  const departments = [
    {
      name: 'Agriculture and Climate Science',
      slug: 'agriculture-climate-science',
      description: 'Department of Agriculture and Climate Science'
    },
    {
      name: 'Business',
      slug: 'business',
      description: 'Department of Business'
    },
    {
      name: 'Education',
      slug: 'education',
      description: 'Department of Education'
    }
  ];

  console.log('Adding departments...');

  try {
    // Add each department
    for (const dept of departments) {
      // Check if department already exists
      const existing = await prisma.department.findFirst({
        where: {
          OR: [
            { name: dept.name },
            { slug: dept.slug }
          ]
        }
      });

      if (existing) {
        console.log(`Department "${dept.name}" already exists, skipping.`);
      } else {
        const created = await prisma.department.create({
          data: dept
        });
        console.log(`Created department: ${created.name} (ID: ${created.id})`);
      }
    }

    console.log('Department creation completed!');

    // List all departments
    const allDepartments = await prisma.department.findMany({
      orderBy: { name: 'asc' }
    });

    console.log('\nAll departments:');
    allDepartments.forEach(dept => {
      console.log(`- ${dept.name}`);
    });
  } catch (error) {
    console.error('Error adding departments:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  }); 