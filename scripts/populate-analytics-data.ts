import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function populateAnalyticsData() {
  console.log('🚀 Starting analytics data population...')

  try {
    // Get existing users
    const users = await prisma.user.findMany({
      take: 10
    })

    if (users.length === 0) {
      console.log('❌ No users found. Please create some users first.')
      return
    }

    console.log(`📊 Found ${users.length} users to work with`)

    // Create user sessions
    const sessions = []
    const countries = ['United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Japan', 'India']
    const cities = {
      'United States': ['New York', 'Los Angeles', 'Chicago', 'Houston'],
      'United Kingdom': ['London', 'Manchester', 'Birmingham', 'Liverpool'],
      'Canada': ['Toronto', 'Vancouver', 'Montreal', 'Calgary'],
      'Australia': ['Sydney', 'Melbourne', 'Brisbane', 'Perth'],
      'Germany': ['Berlin', 'Munich', 'Hamburg', 'Frankfurt'],
      'France': ['Paris', 'Lyon', 'Marseille', 'Toulouse'],
      'Japan': ['Tokyo', 'Osaka', 'Kyoto', 'Yokohama'],
      'India': ['Mumbai', 'Delhi', 'Bangalore', 'Chennai']
    }

    // Create sessions for the last 30 days
    for (let i = 0; i < 30; i++) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      
      // Create 5-15 sessions per day
      const sessionsPerDay = Math.floor(Math.random() * 10) + 5
      
      for (let j = 0; j < sessionsPerDay; j++) {
        const user = users[Math.floor(Math.random() * users.length)]
        const country = countries[Math.floor(Math.random() * countries.length)]
        const cityList = cities[country as keyof typeof cities]
        const city = cityList[Math.floor(Math.random() * cityList.length)]
        
        const sessionStart = new Date(date)
        sessionStart.setHours(Math.floor(Math.random() * 24))
        sessionStart.setMinutes(Math.floor(Math.random() * 60))
        
        const duration = Math.floor(Math.random() * 3600) + 60 // 1 minute to 1 hour
        const sessionEnd = new Date(sessionStart.getTime() + duration * 1000)
        
        const session = await prisma.userSession.create({
          data: {
            userId: user.id,
            sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            country,
            city,
            region: city,
            startTime: sessionStart,
            endTime: sessionEnd,
            duration,
            pageViewCount: Math.floor(Math.random() * 20) + 1,
            createdAt: sessionStart,
            updatedAt: sessionEnd
          }
        })
        
        sessions.push(session)
      }
    }

    console.log(`✅ Created ${sessions.length} user sessions`)

    // Create user events
    const eventTypes = ['registration', 'login', 'logout', 'profile_update', 'content_view', 'search', 'download']
    const events = []

    for (const session of sessions.slice(0, 100)) { // Limit to first 100 sessions
      const eventsPerSession = Math.floor(Math.random() * 5) + 1
      
      for (let i = 0; i < eventsPerSession; i++) {
        const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)]
        const eventTime = new Date(session.startTime.getTime() + Math.random() * (session.duration || 300) * 1000)
        
        const event = await prisma.userEvent.create({
          data: {
            userId: session.userId,
            sessionId: session.sessionId,
            eventType,
            eventData: {
              page: `/page-${Math.floor(Math.random() * 10)}`,
              action: eventType,
              timestamp: eventTime.toISOString()
            },
            timestamp: eventTime,
            ipAddress: session.ipAddress,
            userAgent: session.userAgent
          }
        })
        
        events.push(event)
      }
    }

    console.log(`✅ Created ${events.length} user events`)

    // Create content engagement data
    const posts = await prisma.post.findMany({ take: 10 })
    const faculty = await prisma.facultyProfile.findMany({ take: 5 })
    const departments = await prisma.department.findMany({ take: 3 })
    
    const engagements = []
    const engagementTypes = ['view', 'like', 'share', 'download', 'contact', 'bookmark']

    // Post engagements
    for (const post of posts) {
      const engagementsPerPost = Math.floor(Math.random() * 50) + 10
      
      for (let i = 0; i < engagementsPerPost; i++) {
        const session = sessions[Math.floor(Math.random() * sessions.length)]
        const engagementType = engagementTypes[Math.floor(Math.random() * engagementTypes.length)]
        const duration = engagementType === 'view' ? Math.floor(Math.random() * 600) + 30 : undefined
        
        const engagement = await prisma.contentEngagement.create({
          data: {
            contentId: post.id,
            contentType: 'post',
            userId: session.userId,
            sessionId: session.sessionId,
            engagementType,
            duration,
            timestamp: new Date(session.startTime.getTime() + Math.random() * (session.duration || 300) * 1000),
            metadata: {
              title: post.title,
              category: 'blog'
            }
          }
        })
        
        engagements.push(engagement)
      }
    }

    // Faculty profile engagements
    for (const facultyProfile of faculty) {
      const engagementsPerProfile = Math.floor(Math.random() * 30) + 5
      
      for (let i = 0; i < engagementsPerProfile; i++) {
        const session = sessions[Math.floor(Math.random() * sessions.length)]
        const engagementType = engagementTypes[Math.floor(Math.random() * engagementTypes.length)]
        const duration = engagementType === 'view' ? Math.floor(Math.random() * 400) + 20 : undefined
        
        const engagement = await prisma.contentEngagement.create({
          data: {
            contentId: facultyProfile.id,
            contentType: 'faculty_profile',
            userId: session.userId,
            sessionId: session.sessionId,
            engagementType,
            duration,
            timestamp: new Date(session.startTime.getTime() + Math.random() * (session.duration || 300) * 1000),
            metadata: {
              title: facultyProfile.title,
              department: 'faculty'
            }
          }
        })
        
        engagements.push(engagement)
      }
    }

    // Department engagements
    for (const department of departments) {
      const engagementsPerDept = Math.floor(Math.random() * 20) + 3
      
      for (let i = 0; i < engagementsPerDept; i++) {
        const session = sessions[Math.floor(Math.random() * sessions.length)]
        const engagementType = engagementTypes[Math.floor(Math.random() * engagementTypes.length)]
        const duration = engagementType === 'view' ? Math.floor(Math.random() * 300) + 15 : undefined
        
        const engagement = await prisma.contentEngagement.create({
          data: {
            contentId: department.id,
            contentType: 'department',
            userId: session.userId,
            sessionId: session.sessionId,
            engagementType,
            duration,
            timestamp: new Date(session.startTime.getTime() + Math.random() * (session.duration || 300) * 1000),
            metadata: {
              name: department.name,
              type: 'department'
            }
          }
        })
        
        engagements.push(engagement)
      }
    }

    console.log(`✅ Created ${engagements.length} content engagements`)

    // Create sample A/B test
    const abTest = await prisma.aBTest.create({
      data: {
        name: 'Homepage Hero Section Test',
        description: 'Testing different hero section designs to improve conversion rates',
        status: 'active',
        startDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 14 days ago
        variants: {
          control: {
            name: 'Original Design',
            description: 'Current hero section with blue background'
          },
          variant_a: {
            name: 'Green Background',
            description: 'Hero section with green background and updated copy'
          }
        },
        metrics: {
          primary: 'conversion_rate',
          secondary: ['time_on_page', 'bounce_rate']
        }
      }
    })

    // Create A/B test participants
    const participants = []
    for (let i = 0; i < 200; i++) {
      const session = sessions[Math.floor(Math.random() * sessions.length)]
      const variant = Math.random() > 0.5 ? 'control' : 'variant_a'
      const converted = Math.random() > 0.85 // 15% conversion rate
      
      const participant = await prisma.aBTestParticipant.create({
        data: {
          testId: abTest.id,
          userId: session.userId,
          sessionId: session.sessionId,
          variant,
          assignedAt: session.startTime,
          convertedAt: converted ? new Date(session.startTime.getTime() + Math.random() * (session.duration || 300) * 1000) : null,
          conversionData: converted ? {
            action: 'signup',
            value: Math.floor(Math.random() * 100) + 50
          } : null
        }
      })
      
      participants.push(participant)
    }

    console.log(`✅ Created A/B test with ${participants.length} participants`)

    console.log('🎉 Analytics data population completed successfully!')
    console.log(`
📊 Summary:
- ${sessions.length} user sessions
- ${events.length} user events  
- ${engagements.length} content engagements
- 1 A/B test with ${participants.length} participants
    `)

  } catch (error) {
    console.error('❌ Error populating analytics data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
populateAnalyticsData()
