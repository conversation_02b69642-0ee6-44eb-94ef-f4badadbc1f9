import { prisma } from '../lib/prisma'

async function cleanupOrphanedFaculty() {
  console.log('🔍 Checking for orphaned faculty users...')
  
  try {
    // Find faculty users with no corresponding faculty profile
    const orphanedFaculty = await prisma.user.findMany({
      where: {
        role: 'FACULTY',
        facultyProfile: null
      },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true
      }
    })

    console.log(`Found ${orphanedFaculty.length} orphaned faculty users:`)
    orphanedFaculty.forEach(user => {
      console.log(`- ${user.name} (${user.email}) - ID: ${user.id}`)
    })

    if (orphanedFaculty.length === 0) {
      console.log('✅ No orphaned faculty users found.')
      return
    }

    console.log('\n🧹 Cleaning up orphaned faculty users...')
    
    // Delete orphaned faculty users
    const deleted = await prisma.user.deleteMany({
      where: {
        role: 'FACULTY',
        facultyProfile: null
      }
    })

    console.log(`✅ Successfully deleted ${deleted.count} orphaned faculty users.`)
    console.log('🎉 Cleanup completed!')

  } catch (error) {
    console.error('❌ Error during cleanup:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the cleanup
cleanupOrphanedFaculty()
  .then(() => {
    console.log('Script completed successfully.')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Script failed:', error)
    process.exit(1)
  }) 