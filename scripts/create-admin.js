const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  // Change these values as needed
  const adminEmail = '<EMAIL>';
  const adminPassword = 'Admin123!';
  const adminName = 'System Administrator';

  // Hash the password
  const hashedPassword = await bcrypt.hash(adminPassword, 10);

  try {
    // Create the admin user
    const admin = await prisma.user.create({
      data: {
        email: adminEmail,
        name: adminName,
        password: hashedPassword,
        role: 'SYS_ADMIN',  // SYS_ADMIN or COLLEGE_ADMIN
        status: 'ACTIVE',
        profile: {
          create: {
            firstName: 'System',
            lastName: 'Administrator',
          }
        }
      },
    });

    console.log(`Admin user created with ID: ${admin.id}`);
    console.log(`Email: ${adminEmail}`);
    console.log(`Password: ${adminPassword}`);
    console.log('Login to the admin dashboard with these credentials.');
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  }); 