const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  // New admin credentials
  const adminEmail = '<EMAIL>';
  const adminPassword = 'Admin123!';
  const adminName = 'System Administrator';

  // Hash the password
  const hashedPassword = await bcrypt.hash(adminPassword, 10);

  try {
    // Find existing admin
    const existingAdmin = await prisma.user.findFirst({
      where: {
        role: 'SYS_ADMIN'
      }
    });

    if (existingAdmin) {
      // Update the existing admin
      const updatedAdmin = await prisma.user.update({
        where: {
          id: existingAdmin.id
        },
        data: {
          email: adminEmail,
          password: hashedPassword,
          name: adminName,
          status: 'ACTIVE'
        }
      });

      console.log(`Admin user updated successfully!`);
      console.log(`Email: ${updatedAdmin.email}`);
      console.log(`Role: ${updatedAdmin.role}`);
    } else {
      // Create a new admin if none exists
      const admin = await prisma.user.create({
        data: {
          email: adminEmail,
          name: adminName,
          password: hashedPassword,
          role: 'SYS_ADMIN',
          status: 'ACTIVE',
          profile: {
            create: {
              firstName: 'System',
              lastName: 'Administrator',
            }
          }
        },
      });

      console.log(`Admin user created successfully!`);
      console.log(`Email: ${admin.email}`);
      console.log(`Role: ${admin.role}`);
    }
  } catch (error) {
    console.error('Error updating admin:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  }); 