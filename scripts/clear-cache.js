#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Clear Next.js and Node.js cache directories
 * This script helps resolve webpack cache corruption issues
 */

const cacheDirectories = [
  '.next',
  'node_modules/.cache',
  '.next/cache',
  '.next/static',
  '.next/server',
  'tsconfig.tsbuildinfo'
];

function deleteFolderRecursive(folderPath) {
  try {
    if (fs.existsSync(folderPath)) {
      const stats = fs.statSync(folderPath);
      if (stats.isDirectory()) {
        fs.rmSync(folderPath, { recursive: true, force: true });
        console.log(`✓ Deleted directory: ${folderPath}`);
      } else {
        fs.unlinkSync(folderPath);
        console.log(`✓ Deleted file: ${folderPath}`);
      }
    } else {
      console.log(`- Not found: ${folderPath}`);
    }
  } catch (error) {
    console.log(`⚠ Error deleting ${folderPath}: ${error.message}`);
  }
}

function clearCaches() {
  console.log('🧹 Clearing Next.js and Node.js caches...\n');

  cacheDirectories.forEach(dir => {
    const fullPath = path.join(process.cwd(), dir);
    deleteFolderRecursive(fullPath);
  });

  // Also try to clear any webpack temp files
  try {
    const nextDir = path.join(process.cwd(), '.next');
    if (fs.existsSync(nextDir)) {
      const files = fs.readdirSync(nextDir, { withFileTypes: true });
      files.forEach(file => {
        if (file.name.includes('webpack') || file.name.includes('cache')) {
          const filePath = path.join(nextDir, file.name);
          deleteFolderRecursive(filePath);
        }
      });
    }
  } catch (error) {
    console.log(`⚠ Error clearing webpack temp files: ${error.message}`);
  }

  console.log('\n✅ Cache clearing complete!');
  console.log('💡 You can now run "npm run dev" to start a fresh development server.');
}

// Run the cache clearing
clearCaches();
