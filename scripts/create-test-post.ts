import { PrismaClient, PostStatus } from '@prisma/client'

const prisma = new PrismaClient()

async function createTestPost() {
  console.log('Creating test post...')

  // Find a sysadmin user
  const sysAdmin = await prisma.user.findFirst({
    where: { role: 'SYS_ADMIN' }
  })

  if (!sysAdmin) {
    console.log('No SYS_ADMIN user found. Creating one...')
    
    const newSysAdmin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'System Administrator',
        role: 'SYS_ADMIN',
        status: 'ACTIVE'
      }
    })
    
    console.log('Created SYS_ADMIN user:', newSysAdmin.email)
  }

  const adminUser = sysAdmin || await prisma.user.findFirst({
    where: { role: 'SYS_ADMIN' }
  })

  // Find a category
  const category = await prisma.category.findFirst({
    where: { slug: 'college-news' }
  })

  if (!category) {
    console.error('No category found. Please run the seed-categories script first.')
    return
  }

  // Create a test post
  const testPost = await prisma.post.create({
    data: {
      title: 'Welcome to Our New Blog System!',
      slug: 'welcome-to-our-new-blog-system',
      content: `# Welcome to Our New Blog System!

We're excited to announce the launch of our new blog and news system! This platform allows both administrators and faculty members to share important updates, insights, and stories with our college community.

## Features

- **Easy Content Creation**: Simple and intuitive post creation interface
- **Category Organization**: Posts are organized by categories for better navigation
- **Faculty Contributions**: Faculty members can now share their expertise and insights
- **Rich Content Support**: Support for formatted text, images, and more

## Getting Started

Faculty members can access the posts management system through their faculty portal. Simply navigate to the "Manage Posts" section to start creating and managing your content.

We look forward to seeing the valuable content our community will create!`,
      excerpt: 'Announcing the launch of our new blog and news system for administrators and faculty members.',
      authorId: adminUser!.id,
      categoryId: category.id,
      status: PostStatus.PUBLISHED,
      featured: true,
      publishedAt: new Date(),
      readingTime: '2 min read'
    }
  })

  console.log('Created test post:', testPost.title)
  console.log('Post slug:', testPost.slug)
  console.log('Post ID:', testPost.id)
}

createTestPost()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
