import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const markdownContent = `# Welcome to Our New Blog System!

We're excited to announce the launch of our new blog and news system! This platform allows both administrators and faculty members to share important updates, insights, and stories with our college community.

## Features

Our new system includes many powerful features:

- **Easy Content Creation**: Simple and intuitive post creation interface
- **Category Organization**: Posts are organized by categories for better navigation  
- **Faculty Contributions**: Faculty members can now share their expertise and insights
- **Rich Content Support**: Support for formatted text, images, code, and mathematical expressions
- **LaTeX Support**: Full support for mathematical notation and equations

### Mathematical Expressions

You can now include beautiful mathematical expressions using LaTeX notation:

**Inline math**: The quadratic formula is $x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$

**Block equations**:

$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$

**Complex equations**:

$$
\\frac{\\partial^2 u}{\\partial t^2} = c^2 \\nabla^2 u
$$

### Code Examples

The system also supports syntax-highlighted code blocks:

\`\`\`javascript
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10)); // Output: 55
\`\`\`

\`\`\`python
import numpy as np
import matplotlib.pyplot as plt

# Generate sample data
x = np.linspace(0, 2*np.pi, 100)
y = np.sin(x)

# Create plot
plt.figure(figsize=(10, 6))
plt.plot(x, y, 'b-', linewidth=2)
plt.title('Sine Wave')
plt.xlabel('x')
plt.ylabel('sin(x)')
plt.grid(True)
plt.show()
\`\`\`

## Getting Started

Faculty members can access the posts management system through their faculty portal. Simply navigate to the **"Manage Posts"** section to start creating and managing your content.

### For Faculty

1. **Login** to your faculty portal
2. **Navigate** to "Manage Posts" 
3. **Create** your first post
4. **Publish** when ready

### For Administrators

Administrators have full access to all posts and can:

- ✅ Create and edit any post
- ✅ Manage categories and tags
- ✅ Archive or feature posts
- ✅ Monitor post analytics

## Advanced Features

### Tables

| Feature | Faculty | Admin | Public |
|---------|---------|-------|--------|
| Create Posts | ✅ | ✅ | ❌ |
| Edit Own Posts | ✅ | ✅ | ❌ |
| Edit All Posts | ❌ | ✅ | ❌ |
| View Published | ✅ | ✅ | ✅ |

### Blockquotes

> "The best way to predict the future is to create it." - Peter Drucker

This new platform represents our commitment to fostering communication and knowledge sharing within our academic community.

### Task Lists

- [x] Design new blog system
- [x] Implement markdown support
- [x] Add LaTeX rendering
- [x] Create faculty interface
- [ ] Add comment system
- [ ] Implement newsletter integration
- [ ] Add social media sharing

## Conclusion

We look forward to seeing the valuable content our community will create! This system will serve as a hub for academic discourse, research sharing, and community engagement.

**Happy blogging!** 🎉

---

*For technical support or questions about the blog system, please contact the IT department.*`

async function updatePostContent() {
  console.log('Updating post content with markdown...')

  try {
    const post = await prisma.post.findFirst({
      where: { slug: 'welcome-to-our-new-blog-system' }
    })

    if (!post) {
      console.error('Post not found')
      return
    }

    await prisma.post.update({
      where: { id: post.id },
      data: {
        content: markdownContent,
        excerpt: "Announcing the launch of our new blog and news system with markdown support, LaTeX rendering, and enhanced features for administrators and faculty members."
      }
    })

    console.log('Post content updated successfully!')
  } catch (error) {
    console.error('Error updating post:', error)
  }
}

updatePostContent()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
