import { PrismaClient, PostStatus } from '@prisma/client'

const prisma = new PrismaClient()

const samplePosts = [
  {
    title: "Advanced Mathematics in Computer Science",
    slug: "advanced-mathematics-computer-science",
    content: `# Advanced Mathematics in Computer Science

Mathematics forms the foundation of computer science, providing the theoretical framework for algorithms, data structures, and computational complexity.

## Linear Algebra Applications

Linear algebra is crucial in many CS applications:

### Matrix Operations

The transformation matrix for 2D rotation:

$$
R(\\theta) = \\begin{pmatrix}
\\cos\\theta & -\\sin\\theta \\\\
\\sin\\theta & \\cos\\theta
\\end{pmatrix}
$$

### Eigenvalues and Eigenvectors

For a matrix $A$, eigenvalues $\\lambda$ and eigenvectors $v$ satisfy:

$$Av = \\lambda v$$

This is fundamental in:
- Principal Component Analysis (PCA)
- Google's PageRank algorithm
- Machine learning dimensionality reduction

## Discrete Mathematics

### Graph Theory

The number of edges in a complete graph with $n$ vertices:

$$|E| = \\binom{n}{2} = \\frac{n(n-1)}{2}$$

### Complexity Analysis

Big O notation describes algorithmic complexity:

- **Constant time**: $O(1)$
- **Logarithmic**: $O(\\log n)$
- **Linear**: $O(n)$
- **Quadratic**: $O(n^2)$

## Probability and Statistics

### Bayes' Theorem

$$P(A|B) = \\frac{P(B|A) \\cdot P(A)}{P(B)}$$

This is essential for:
- Machine learning classification
- Spam filtering algorithms
- Medical diagnosis systems

## Code Example: Matrix Multiplication

\`\`\`python
import numpy as np

def matrix_multiply(A, B):
    """
    Multiply two matrices using NumPy
    """
    if A.shape[1] != B.shape[0]:
        raise ValueError("Incompatible matrix dimensions")
    
    return np.dot(A, B)

# Example usage
A = np.array([[1, 2], [3, 4]])
B = np.array([[5, 6], [7, 8]])
result = matrix_multiply(A, B)
print(f"Result:\\n{result}")
\`\`\`

Mathematics continues to drive innovation in computer science, from quantum computing to artificial intelligence.`,
    excerpt: "Exploring the fundamental role of mathematics in computer science, from linear algebra to discrete mathematics and their practical applications.",
    categorySlug: "faculty-spotlight",
    featured: false
  },
  {
    title: "Research Methodology in Data Science",
    slug: "research-methodology-data-science",
    content: `# Research Methodology in Data Science

Effective research methodology is crucial for producing reliable and reproducible results in data science.

## The Data Science Research Process

### 1. Problem Definition
- Clearly define the research question
- Identify stakeholders and success metrics
- Establish project scope and constraints

### 2. Data Collection and Preparation

\`\`\`python
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

# Load and explore data
df = pd.read_csv('dataset.csv')
print(f"Dataset shape: {df.shape}")
print(f"Missing values:\\n{df.isnull().sum()}")

# Data cleaning
df_clean = df.dropna()
df_clean = df_clean[df_clean['value'] > 0]

# Feature scaling
scaler = StandardScaler()
scaled_features = scaler.fit_transform(df_clean[['feature1', 'feature2']])
\`\`\`

### 3. Exploratory Data Analysis (EDA)

Key statistical measures:

- **Mean**: $\\bar{x} = \\frac{1}{n}\\sum_{i=1}^{n} x_i$
- **Variance**: $\\sigma^2 = \\frac{1}{n}\\sum_{i=1}^{n} (x_i - \\bar{x})^2$
- **Standard Deviation**: $\\sigma = \\sqrt{\\sigma^2}$

## Statistical Testing

### Hypothesis Testing

For a null hypothesis $H_0$ and alternative $H_1$:

$$t = \\frac{\\bar{x} - \\mu_0}{s/\\sqrt{n}}$$

Where:
- $\\bar{x}$ is the sample mean
- $\\mu_0$ is the hypothesized population mean
- $s$ is the sample standard deviation
- $n$ is the sample size

### Confidence Intervals

The 95% confidence interval for the mean:

$$\\bar{x} \\pm t_{\\alpha/2} \\cdot \\frac{s}{\\sqrt{n}}$$

## Machine Learning Validation

### Cross-Validation

K-fold cross-validation helps assess model performance:

\`\`\`python
from sklearn.model_selection import cross_val_score
from sklearn.ensemble import RandomForestClassifier

# Initialize model
model = RandomForestClassifier(n_estimators=100, random_state=42)

# Perform 5-fold cross-validation
cv_scores = cross_val_score(model, X, y, cv=5, scoring='accuracy')

print(f"CV Accuracy: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
\`\`\`

## Best Practices

| Practice | Description | Importance |
|----------|-------------|------------|
| **Reproducibility** | Document all steps and use version control | High |
| **Data Validation** | Verify data quality and consistency | High |
| **Bias Detection** | Check for sampling and selection bias | Medium |
| **Ethical Considerations** | Ensure privacy and fairness | High |

## Conclusion

> "In God we trust, all others must bring data." - W. Edwards Deming

Rigorous methodology ensures that data science research contributes meaningful insights to the scientific community.`,
    excerpt: "A comprehensive guide to research methodology in data science, covering statistical testing, validation techniques, and best practices for reproducible research.",
    categorySlug: "research-innovation",
    featured: true
  }
]

async function createSamplePosts() {
  console.log('Creating sample posts...')

  try {
    // Find a faculty user to be the author
    const facultyUser = await prisma.user.findFirst({
      where: { role: 'FACULTY' }
    })

    if (!facultyUser) {
      console.log('No faculty user found. Creating sample posts with sysadmin...')
    }

    const author = facultyUser || await prisma.user.findFirst({
      where: { role: 'SYS_ADMIN' }
    })

    if (!author) {
      console.error('No suitable author found')
      return
    }

    for (const postData of samplePosts) {
      // Find the category
      const category = await prisma.category.findFirst({
        where: { slug: postData.categorySlug }
      })

      if (!category) {
        console.error(`Category not found: ${postData.categorySlug}`)
        continue
      }

      // Check if post already exists
      const existingPost = await prisma.post.findUnique({
        where: { slug: postData.slug }
      })

      if (existingPost) {
        console.log(`Post already exists: ${postData.title}`)
        continue
      }

      // Create the post
      await prisma.post.create({
        data: {
          title: postData.title,
          slug: postData.slug,
          content: postData.content,
          excerpt: postData.excerpt,
          authorId: author.id,
          categoryId: category.id,
          status: PostStatus.PUBLISHED,
          featured: postData.featured,
          publishedAt: new Date(),
          readingTime: '8 min read'
        }
      })

      console.log(`Created post: ${postData.title}`)
    }

    console.log('Sample posts created successfully!')
  } catch (error) {
    console.error('Error creating sample posts:', error)
  }
}

createSamplePosts()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
