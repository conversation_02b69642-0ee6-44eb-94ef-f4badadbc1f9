import { PrismaClient, UserRole, UserStatus } from '@prisma/client'
import bcrypt from 'bcryptjs'
import { facultyData } from '../lib/data/faculty'

const prisma = new PrismaClient()

async function createDepartments() {
  const departments = [
    {
      name: "Computer Science",
      slug: "computer-science",
      description: "Department of Computer Science and Engineering"
    },
    {
      name: "Business",
      slug: "business",
      description: "School of Business Administration"
    },
    {
      name: "Agriculture and Climate Science",
      slug: "agriculture-climate",
      description: "Department of Agriculture and Climate Science"
    },
    {
      name: "Education",
      slug: "education",
      description: "School of Education"
    }
  ]

  for (const dept of departments) {
    await prisma.department.upsert({
      where: { slug: dept.slug },
      update: {},
      create: dept
    })
  }

  console.log('✅ Departments created')
}

async function migrateFacultyData() {
  // Get departments for mapping
  const departments = await prisma.department.findMany()
  const deptMap = new Map(departments.map(d => [d.name, d.id]))

  for (const faculty of facultyData) {
    try {
      // Map department name to ID
      const departmentId = deptMap.get(faculty.department)
      if (!departmentId) {
        console.warn(`⚠️  Department not found for ${faculty.name}: ${faculty.department}`)
        continue
      }

      // Create user with faculty profile
      const hashedPassword = await bcrypt.hash('defaultpassword123', 12)

      const user = await prisma.user.upsert({
        where: { email: faculty.email },
        update: {
          name: faculty.name,
          role: UserRole.FACULTY,
          status: UserStatus.ACTIVE,
          image: faculty.imageUrl,
        },
        create: {
          email: faculty.email,
          name: faculty.name,
          password: hashedPassword,
          role: UserRole.FACULTY,
          status: UserStatus.ACTIVE,
          image: faculty.imageUrl,
          profile: {
            create: {
              firstName: faculty.name.split(' ')[1] || faculty.name,
              lastName: faculty.name.split(' ').slice(2).join(' ') || '',
              bio: faculty.bio,
              avatarUrl: faculty.imageUrl
            }
          },
          facultyProfile: {
            create: {
              title: faculty.title,
              departmentId: departmentId,
              officeLocation: faculty.office,
              websiteUrl: faculty.website,
              scholarId: faculty.scholarId,
              bio: faculty.bio,
            }
          }
        },
        include: {
          facultyProfile: true
        }
      })

      // If user exists but doesn't have faculty profile, create it
      if (!user.facultyProfile) {
        await prisma.facultyProfile.create({
          data: {
            userId: user.id,
            title: faculty.title,
            departmentId: departmentId,
            officeLocation: faculty.office,
            websiteUrl: faculty.website,
            scholarId: faculty.scholarId,
            bio: faculty.bio,
          }
        })

        // Refetch user with faculty profile
        const updatedUser = await prisma.user.findUnique({
          where: { id: user.id },
          include: { facultyProfile: true }
        })
        if (updatedUser?.facultyProfile) {
          user.facultyProfile = updatedUser.facultyProfile
        }
      }

      // Add education records
      if (faculty.education) {
        for (const edu of faculty.education) {
          await prisma.facultyEducation.create({
            data: {
              facultyId: user.facultyProfile!.id,
              degree: edu.split(',')[0] || edu,
              institution: edu.split(',')[1]?.trim() || 'Unknown',
              year: extractYear(edu)
            }
          })
        }
      }

      // Add research areas
      if (faculty.research) {
        for (const area of faculty.research) {
          await prisma.facultyResearchArea.create({
            data: {
              facultyId: user.facultyProfile!.id,
              areaName: area,
              description: `Research focus in ${area}`
            }
          })
        }
      }

      // Add publications
      if (faculty.publications) {
        for (const pub of faculty.publications) {
          const pubData = parsePublication(pub)
          await prisma.facultyPublication.create({
            data: {
              facultyId: user.facultyProfile!.id,
              title: pubData.title,
              authors: pubData.authors,
              journal: pubData.journal,
              year: pubData.year,
              citationCount: 0,
              tags: []
            }
          })
        }
      }

      // Add timeline events if available
      if (faculty.timeline) {
        for (const event of faculty.timeline) {
          await prisma.facultyTimeline.create({
            data: {
              facultyId: user.facultyProfile!.id,
              year: event.year,
              title: event.title,
              description: event.description,
              type: event.type.toUpperCase() as any
            }
          })
        }
      }

      console.log(`✅ Migrated faculty: ${faculty.name}`)

    } catch (error) {
      console.error(`❌ Error migrating ${faculty.name}:`, error)
    }
  }
}

function extractYear(education: string): number | null {
  const yearMatch = education.match(/\b(19|20)\d{2}\b/)
  return yearMatch ? parseInt(yearMatch[0]) : null
}

function parsePublication(pub: string) {
  // Simple parsing - in production you'd want more sophisticated parsing
  const parts = pub.split('.')
  const title = parts[1]?.trim() || pub
  const journal = parts[2]?.trim() || 'Unknown Journal'
  const yearMatch = pub.match(/\((\d{4})\)/)
  const year = yearMatch ? parseInt(yearMatch[1]) : new Date().getFullYear()

  // Extract authors (simplified)
  const authorsPart = parts[0]?.trim() || ''
  const authors = authorsPart.split(',').map(a => a.trim()).filter(a => a.length > 0)

  return {
    title,
    authors: authors.length > 0 ? authors : ['Unknown'],
    journal,
    year
  }
}

async function main() {
  try {
    console.log('🚀 Starting faculty data migration...')

    await createDepartments()
    await migrateFacultyData()

    console.log('✅ Faculty data migration completed successfully!')
  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

export { main as migrateFacultyData }
