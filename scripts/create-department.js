const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    // Create a default department
    const department = await prisma.department.create({
      data: {
        name: 'Computer Science',
        slug: 'computer-science',
        description: 'Department of Computer Science and Engineering',
      },
    });

    console.log(`Department created with ID: ${department.id}`);
    console.log(`Name: ${department.name}`);
    console.log(`Slug: ${department.slug}`);
  } catch (error) {
    console.error('Error creating department:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  }); 