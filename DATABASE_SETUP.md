# College Website Database Setup

This guide will help you set up the database system for your college website with PostgreSQL, Prisma, and NextAuth.js.

## Prerequisites

1. **PostgreSQL Database**: You need a PostgreSQL database. You can use:
   - Local PostgreSQL installation
   - Cloud services like Supabase, Railway, or Neon
   - Docker container

2. **Node.js**: Make sure you have Node.js 18+ installed

## Quick Setup

### 1. Database Configuration

Update your `.env` file with your database credentials:

```env
# Replace with your actual database URL
DATABASE_URL="postgresql://username:password@localhost:5432/college_website_db?schema=public"

# NextAuth configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-this-in-production"
```

### 2. Database Migration

Run the following commands to set up your database:

```bash
# Generate Prisma client
npx prisma generate

# Create and apply database migrations
npx prisma db push

# Set up default data (users, categories, etc.)
npx tsx scripts/setup-database.ts

# Optional: Migrate existing faculty data
npx tsx scripts/migrate-faculty-data.ts
```

### 3. Start the Development Server

```bash
npm run dev
```

## Default User Accounts

After running the setup script, you'll have these default accounts:

| Role | Email | Password | Access |
|------|-------|----------|---------|
| System Admin | <EMAIL> | defaultpassword123 | Full system access |
| College Admin | <EMAIL> | defaultpassword123 | Content & faculty management |
| Faculty | <EMAIL> | defaultpassword123 | Faculty portal |
| Student | <EMAIL> | defaultpassword123 | Student features |

## Access Points

- **Admin Panel**: http://localhost:3000/admin
- **Faculty Portal**: http://localhost:3000/faculty-portal
- **Sign In**: http://localhost:3000/auth/signin
- **Main Website**: http://localhost:3000

## Database Schema Overview

### User Management
- `users` - Core user accounts with roles
- `user_profiles` - Extended user information
- `accounts` & `sessions` - NextAuth.js authentication

### Faculty System
- `faculty_profiles` - Faculty-specific information
- `faculty_education` - Education history
- `faculty_research_areas` - Research interests
- `faculty_publications` - Academic publications
- `faculty_timeline` - Career timeline
- `faculty_cv_documents` - CV and document uploads

### Academic Structure
- `departments` - Academic departments
- `programs` - Degree programs
- `courses` - Course catalog
- `course_classes` - Specific course instances

### Content Management
- `posts` - Blog posts and news articles
- `categories` - Content categories
- `tags` - Content tags
- `post_tags` - Many-to-many relationship

### Scheduling
- `office_hours` - Faculty office hours
- `office_hour_bookings` - Student bookings
- `research_projects` - Research opportunities

## User Roles & Permissions

### System Administrator (SYS_ADMIN)
- Full access to all features
- User management
- System configuration
- All admin functions

### College Administrator (COLLEGE_ADMIN)
- Faculty management
- Content management (posts, news)
- Department and program management
- Analytics and reporting

### Faculty (FACULTY)
- Personal profile management
- Research project management
- Publication management
- Office hours scheduling
- Course management

### Student (STUDENT)
- Profile management
- Office hour booking
- Access to student features

## Customization

### Adding New User Roles
1. Update the `UserRole` enum in `prisma/schema.prisma`
2. Run `npx prisma db push`
3. Update role checks in `lib/auth-utils.ts`
4. Update navigation in admin/faculty components

### Adding New Features
1. Update the database schema in `prisma/schema.prisma`
2. Generate new Prisma client: `npx prisma generate`
3. Push changes: `npx prisma db push`
4. Create service functions in `lib/services/`
5. Build UI components and pages

## Production Deployment

### Environment Variables
```env
DATABASE_URL="your-production-database-url"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="a-very-secure-random-string"
```

### Security Considerations
1. Change all default passwords
2. Use strong, unique NEXTAUTH_SECRET
3. Enable SSL for database connections
4. Set up proper backup strategies
5. Configure rate limiting
6. Enable audit logging

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check your DATABASE_URL format
   - Ensure PostgreSQL is running
   - Verify credentials and database exists

2. **Prisma Client Not Found**
   ```bash
   npx prisma generate
   ```

3. **Migration Errors**
   ```bash
   npx prisma db push --force-reset
   npx tsx scripts/setup-database.ts
   ```

4. **Authentication Issues**
   - Check NEXTAUTH_SECRET is set
   - Verify NEXTAUTH_URL matches your domain
   - Clear browser cookies and try again

### Getting Help

1. Check the Prisma documentation: https://www.prisma.io/docs
2. NextAuth.js documentation: https://next-auth.js.org
3. Review the database schema in `prisma/schema.prisma`
4. Check service functions in `lib/services/`

## Next Steps

1. **Customize the UI**: Update components to match your college branding
2. **Add More Features**: Implement additional functionality as needed
3. **Set Up Email**: Configure email notifications for bookings, etc.
4. **Add File Uploads**: Implement file upload for CVs, images, etc.
5. **Analytics**: Set up analytics and reporting features
6. **Mobile App**: Consider building a mobile app using the same database

## Migration from Static Data

If you have existing faculty data in TypeScript files, run:

```bash
npx tsx scripts/migrate-faculty-data.ts
```

This will transfer your existing faculty data to the database while preserving all information.
