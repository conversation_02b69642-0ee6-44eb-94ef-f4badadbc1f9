# NextAuth Troubleshooting Guide

## Common NextAuth Errors and Solutions

### 1. CLIENT_FETCH_ERROR

**Error**: `[next-auth][error][CLIENT_FETCH_ERROR] "Failed to fetch"`

**Causes & Solutions**:

#### A. Environment Variables
```bash
# Check your .env file has these set:
NEXTAUTH_URL="http://localhost:3000"  # Must match your dev server port
NEXTAUTH_SECRET="your-secret-here"    # Required for JWT signing
```

#### B. Middleware Configuration
- Ensure middleware doesn't block `/api/auth/*` routes
- Check `middleware.ts` matcher excludes NextAuth API routes

#### C. Server Issues
```bash
# Restart development server
npm run dev

# Clear Next.js cache if needed
npm run clear-cache && npm run dev
```

### 2. Session Issues

**Error**: Session not persisting or undefined

**Solutions**:
```typescript
// Check SessionProvider is wrapping your app
// In app/layout.tsx:
<SessionProvider>
  {children}
</SessionProvider>
```

### 3. Database Connection Errors

**Error**: Prisma/Database connection issues

**Solutions**:
```bash
# Check database is running
# Verify DATABASE_URL in .env
# Regenerate Prisma client
npm run db:generate
```

### 4. Credential Provider Issues

**Error**: Authentication fails with valid credentials

**Check**:
- Password hashing matches (bcrypt)
- User exists in database
- User status is ACTIVE
- Email format is correct

### 5. Middleware Conflicts

**Error**: Infinite redirects or blocked requests

**Fix middleware.ts**:
```typescript
export const config = {
  matcher: [
    // Exclude NextAuth API routes
    '/((?!api/auth|_next/static|_next/image|favicon.ico).*)',
  ],
}
```

## Debugging Steps

### 1. Enable Debug Mode
```env
# In .env
NODE_ENV=development
```

### 2. Check Network Tab
- Look for failed requests to `/api/auth/*`
- Check response status codes
- Verify request/response headers

### 3. Server Logs
```bash
# Watch server terminal for:
# - NextAuth warnings
# - Database query errors
# - Middleware logs
```

### 4. Test API Endpoints
```bash
# Test session endpoint
curl http://localhost:3000/api/auth/session

# Test providers endpoint
curl http://localhost:3000/api/auth/providers
```

## Security Considerations

### 1. Production Environment
```env
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="secure-random-string-32-chars-min"
```

### 2. HTTPS Required
- NextAuth requires HTTPS in production
- Use secure cookies
- Set proper CORS headers

### 3. Database Security
- Use connection pooling
- Enable SSL for database connections
- Regular security updates

## Quick Fixes

### Reset Everything
```bash
# Stop server
# Clear all caches
rm -rf .next node_modules/.cache
npm install
npm run db:generate
npm run dev
```

### Check Configuration
```typescript
// Verify authOptions in lib/auth.ts
export const authOptions: NextAuthOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  providers: [...],
  callbacks: {...},
  pages: {
    signIn: "/auth/signin",
  },
}
```

### Verify Middleware
```typescript
// middleware.ts should not block auth routes
export const config = {
  matcher: [
    '/((?!api/auth|_next/static|_next/image|favicon.ico).*)',
  ],
}
```

## Testing Authentication

### 1. Manual Testing
1. Open browser dev tools
2. Navigate to signin page
3. Check Network tab for API calls
4. Verify no console errors
5. Test login with valid credentials

### 2. API Testing
```bash
# Test session
curl -X GET http://localhost:3000/api/auth/session

# Test signin (replace with actual credentials)
curl -X POST http://localhost:3000/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

### 3. Database Verification
```bash
# Check users exist
npm run db:studio
# Navigate to users table
# Verify user data and password hashes
```

## Common Gotchas

1. **Port Mismatch**: NEXTAUTH_URL must match dev server port
2. **Missing Secret**: NEXTAUTH_SECRET required for JWT
3. **Middleware Blocking**: Don't block `/api/auth/*` routes
4. **Database Issues**: Ensure Prisma client is generated
5. **HTTPS in Production**: NextAuth requires HTTPS for production

## Getting Help

If issues persist:
1. Check NextAuth.js documentation
2. Verify all environment variables
3. Test with minimal configuration
4. Check GitHub issues for similar problems
5. Enable debug mode for detailed logs
