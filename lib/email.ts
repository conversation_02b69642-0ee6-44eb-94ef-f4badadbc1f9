import nodemailer from 'nodemailer'

interface EmailOptions {
  to: string
  subject: string
  text?: string
  html?: string
}

/**
 * Send an email using the configured email service
 */
export async function sendEmail({ to, subject, text, html }: EmailOptions): Promise<void> {
  // Check for required environment variables
  const emailHost = process.env.EMAIL_SERVER_HOST
  const emailPort = process.env.EMAIL_SERVER_PORT
  const emailUser = process.env.EMAIL_SERVER_USER
  const emailPassword = process.env.EMAIL_SERVER_PASSWORD
  const emailFrom = process.env.EMAIL_FROM
  
  // Validate email configuration is present
  if (!emailHost || !emailPort || !emailUser || !emailPassword || !emailFrom) {
    console.warn('Email service not fully configured. Check environment variables.')
    // For now we'll mock the email sending in development
    if (process.env.NODE_ENV === 'development') {
      console.log('==== DEVELOPMENT MODE: Email would be sent ====')
      console.log(`To: ${to}`)
      console.log(`Subject: ${subject}`)
      console.log(`Content: ${text || html}`)
      console.log('=============================================')
      return
    }
    throw new Error('Email service not configured')
  }

  // Create a nodemailer transporter
  const transporter = nodemailer.createTransport({
    host: emailHost,
    port: parseInt(emailPort),
    secure: parseInt(emailPort) === 465, // true for 465, false for other ports
    auth: {
      user: emailUser,
      pass: emailPassword,
    },
  })

  // Send the email
  await transporter.sendMail({
    from: `"College Website" <${emailFrom}>`,
    to,
    subject,
    text,
    html,
  })
} 