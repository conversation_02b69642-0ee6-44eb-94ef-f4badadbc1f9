import { NextRequest } from 'next/server'
import { trackUserEvent, trackContentEngagement } from '@/lib/services/analytics'

// Extract IP address from request
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}

// Extract user agent
export function getUserAgent(request: NextRequest): string {
  return request.headers.get('user-agent') || 'unknown'
}

// Track page view
export async function trackPageView(
  request: NextRequest,
  userId?: string,
  sessionId?: string
) {
  try {
    const path = request.nextUrl.pathname
    const title = extractPageTitle(path)
    const referrer = request.headers.get('referer') || undefined
    const ipAddress = getClientIP(request)
    const userAgent = getUserAgent(request)

    // Track as user event
    await trackUserEvent(
      'page_view',
      userId,
      sessionId,
      {
        path,
        title,
        referrer,
        timestamp: new Date().toISOString()
      },
      ipAddress,
      userAgent
    )

    // If it's content, also track as content engagement
    const contentMatch = extractContentInfo(path)
    if (contentMatch) {
      await trackContentEngagement(
        contentMatch.id,
        contentMatch.type,
        'view',
        userId,
        sessionId,
        undefined,
        {
          path,
          referrer,
          timestamp: new Date().toISOString()
        }
      )
    }
  } catch (error) {
    console.error('Error tracking page view:', error)
  }
}

// Extract page title from path
function extractPageTitle(path: string): string {
  const segments = path.split('/').filter(Boolean)
  
  if (segments.length === 0) return 'Home'
  if (segments[0] === 'admin') return 'Admin Dashboard'
  if (segments[0] === 'faculty-portal') return 'Faculty Portal'
  if (segments[0] === 'faculty' && segments[1]) return 'Faculty Profile'
  if (segments[0] === 'posts' && segments[1]) return 'Blog Post'
  if (segments[0] === 'departments' && segments[1]) return 'Department'
  if (segments[0] === 'programs' && segments[1]) return 'Program'
  
  return segments[segments.length - 1]?.charAt(0).toUpperCase() + 
         segments[segments.length - 1]?.slice(1) || 'Page'
}

// Extract content information from path
function extractContentInfo(path: string): { id: string; type: string } | null {
  const segments = path.split('/').filter(Boolean)
  
  // Faculty profile: /faculty/[id]
  if (segments[0] === 'faculty' && segments[1] && segments[1] !== 'portal') {
    return { id: segments[1], type: 'faculty_profile' }
  }
  
  // Blog post: /posts/[slug] or /blog/[slug]
  if ((segments[0] === 'posts' || segments[0] === 'blog') && segments[1]) {
    return { id: segments[1], type: 'post' }
  }
  
  // Department: /departments/[slug]
  if (segments[0] === 'departments' && segments[1]) {
    return { id: segments[1], type: 'department' }
  }
  
  // Program: /programs/[slug]
  if (segments[0] === 'programs' && segments[1]) {
    return { id: segments[1], type: 'program' }
  }
  
  // Course: /courses/[id]
  if (segments[0] === 'courses' && segments[1]) {
    return { id: segments[1], type: 'course' }
  }
  
  return null
}

// Track user action (login, registration, etc.)
export async function trackUserAction(
  action: string,
  userId?: string,
  sessionId?: string,
  additionalData?: any,
  request?: NextRequest
) {
  try {
    const ipAddress = request ? getClientIP(request) : undefined
    const userAgent = request ? getUserAgent(request) : undefined

    await trackUserEvent(
      action,
      userId,
      sessionId,
      {
        ...additionalData,
        timestamp: new Date().toISOString()
      },
      ipAddress,
      userAgent
    )
  } catch (error) {
    console.error('Error tracking user action:', error)
  }
}

// Track content interaction (like, share, download, etc.)
export async function trackContentInteraction(
  contentId: string,
  contentType: string,
  interactionType: string,
  userId?: string,
  sessionId?: string,
  duration?: number,
  additionalData?: any
) {
  try {
    await trackContentEngagement(
      contentId,
      contentType,
      interactionType,
      userId,
      sessionId,
      duration,
      {
        ...additionalData,
        timestamp: new Date().toISOString()
      }
    )
  } catch (error) {
    console.error('Error tracking content interaction:', error)
  }
}

// Generate session ID (simple implementation)
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Mock geographic data (in production, use a service like MaxMind)
export function getGeographicData(ipAddress: string): { country?: string; city?: string; region?: string } {
  // This is a mock implementation
  // In production, you would use a service like MaxMind GeoIP2 or similar
  const mockData = [
    { country: 'United States', city: 'New York', region: 'NY' },
    { country: 'United Kingdom', city: 'London', region: 'England' },
    { country: 'Canada', city: 'Toronto', region: 'ON' },
    { country: 'Australia', city: 'Sydney', region: 'NSW' },
    { country: 'Germany', city: 'Berlin', region: 'Berlin' },
    { country: 'France', city: 'Paris', region: 'Île-de-France' },
    { country: 'Japan', city: 'Tokyo', region: 'Tokyo' },
    { country: 'India', city: 'Mumbai', region: 'Maharashtra' }
  ]
  
  // Return random data for demo purposes
  return mockData[Math.floor(Math.random() * mockData.length)]
}
