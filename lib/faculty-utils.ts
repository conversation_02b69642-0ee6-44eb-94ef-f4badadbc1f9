'use client'

import { FacultyMember } from "./data/faculty"

// Type definitions for local storage data
export interface StoredFaculty {
  id: string
  name: string
  title: string
  department: string
  imageUrl: string
  timestamp: number
}

// Constants for localStorage keys
const RECENTLY_VIEWED_KEY = 'recently-viewed-faculty'
const MAX_RECENT = 10
const DEFAULT_AVATAR = '/images/faculty/default-avatar.svg'

// Helper function to clean up placeholder image URLs
function cleanupImageUrl(imageUrl: string): string {
  if (imageUrl === '/placeholder.jpg' || imageUrl.includes('placeholder')) {
    return DEFAULT_AVATAR
  }
  return imageUrl
}

// Helper function to clean up stored faculty data
function cleanupStoredFaculty(faculty: StoredFaculty[]): StoredFaculty[] {
  return faculty.map(f => ({
    ...f,
    imageUrl: cleanupImageUrl(f.imageUrl)
  }))
}

// One-time cleanup function to fix existing localStorage data
export function cleanupLocalStorageData(): void {
  if (typeof window === 'undefined') return

  try {
    // Clean up recently viewed
    const recentStored = localStorage.getItem(RECENTLY_VIEWED_KEY)
    if (recentStored) {
      const recent = JSON.parse(recentStored)
      const cleanedRecent = cleanupStoredFaculty(recent)
      localStorage.setItem(RECENTLY_VIEWED_KEY, JSON.stringify(cleanedRecent))
    }
  } catch (error) {
    console.error('Error cleaning up localStorage data:', error)
  }
}



// Get recently viewed faculty
export function getRecentlyViewedFaculty(): StoredFaculty[] {
  if (typeof window === 'undefined') return []

  try {
    const stored = localStorage.getItem(RECENTLY_VIEWED_KEY)
    const recentlyViewed = stored ? JSON.parse(stored) : []
    return cleanupStoredFaculty(recentlyViewed)
  } catch (error) {
    console.error('Error getting recently viewed faculty:', error)
    return []
  }
}

// Add faculty to recently viewed
export function addFacultyToRecentlyViewed(faculty: FacultyMember): void {
  if (typeof window === 'undefined') return

  try {
    const recentlyViewed = getRecentlyViewedFaculty()

    // Create the faculty item to store
    const storedFaculty: StoredFaculty = {
      id: faculty.id,
      name: faculty.name,
      title: faculty.title,
      department: faculty.department,
      imageUrl: cleanupImageUrl(faculty.imageUrl),
      timestamp: Date.now()
    }

    // Remove if already in list to avoid duplicates
    const newList = recentlyViewed.filter(fac => fac.id !== faculty.id)

    // Add to front of list
    newList.unshift(storedFaculty)

    // Limit to MAX_RECENT items
    if (newList.length > MAX_RECENT) {
      newList.pop()
    }

    localStorage.setItem(RECENTLY_VIEWED_KEY, JSON.stringify(newList))
  } catch (error) {
    console.error('Error adding faculty to recently viewed:', error)
  }
}

// Calculate profile completeness as a percentage
export function calculateProfileCompleteness(faculty: FacultyMember): number {
  const requiredFields = [
    'id', 'name', 'title', 'department', 'email',
    'website', 'bio', 'imageUrl'
  ]

  const optionalFields = [
    'education', 'research', 'publications', 'courses',
    'officeHours', 'office', 'timeline', 'cvDocuments',
    'scholarlyPublications', 'upcomingClasses',
    'scheduledOfficeHours', 'researchProjects'
  ]

  // Check required fields (each worth 10%)
  const requiredScore = requiredFields.reduce((score, field) => {
    return score + (faculty[field as keyof FacultyMember] ? 10 : 0)
  }, 0)

  // Check array fields (each worth 5% if has at least one item)
  const arrayScore = optionalFields.reduce((score, field) => {
    const value = faculty[field as keyof FacultyMember]
    if (Array.isArray(value) && value.length > 0) {
      return score + 5
    }
    return score + (value ? 5 : 0)
  }, 0)

  // Calculate total (cap at 100%)
  const total = Math.min(100, requiredScore + arrayScore)
  return total
}