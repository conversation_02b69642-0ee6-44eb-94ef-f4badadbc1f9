import { prisma } from '@/lib/prisma'

// Geographic Analytics
export async function getGeographicAnalytics() {
  try {
    const usersByCountry = await prisma.userSession.groupBy({
      by: ['country'],
      where: {
        country: {
          not: null
        }
      },
      _count: {
        userId: true
      },
      orderBy: {
        _count: {
          userId: 'desc'
        }
      }
    })

    const usersByCity = await prisma.userSession.groupBy({
      by: ['city', 'country'],
      where: {
        city: {
          not: null
        },
        country: {
          not: null
        }
      },
      _count: {
        userId: true
      },
      orderBy: {
        _count: {
          userId: 'desc'
        }
      },
      take: 20
    })

    return {
      countries: usersByCountry.map(item => ({
        country: item.country,
        users: item._count.userId
      })),
      cities: usersByCity.map(item => ({
        city: item.city,
        country: item.country,
        users: item._count.userId
      }))
    }
  } catch (error) {
    console.error('Error fetching geographic analytics:', error)
    return { countries: [], cities: [] }
  }
}

// Content Engagement Heatmap
export async function getContentEngagementHeatmap(days: number = 30) {
  try {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const engagementData = await prisma.contentEngagement.findMany({
      where: {
        timestamp: {
          gte: startDate
        }
      },
      select: {
        contentId: true,
        contentType: true,
        engagementType: true,
        duration: true,
        timestamp: true
      }
    })

    // Group by content and engagement type
    const heatmapData = engagementData.reduce((acc, engagement) => {
      const key = `${engagement.contentType}:${engagement.contentId}`
      if (!acc[key]) {
        acc[key] = {
          contentId: engagement.contentId,
          contentType: engagement.contentType,
          views: 0,
          totalDuration: 0,
          engagements: {}
        }
      }

      if (engagement.engagementType === 'view') {
        acc[key].views++
      }

      if (engagement.duration) {
        acc[key].totalDuration += engagement.duration
      }

      acc[key].engagements[engagement.engagementType] = 
        (acc[key].engagements[engagement.engagementType] || 0) + 1

      return acc
    }, {} as Record<string, any>)

    return Object.values(heatmapData)
  } catch (error) {
    console.error('Error fetching engagement heatmap:', error)
    return []
  }
}

// User Journey Funnel Analysis
export async function getUserJourneyFunnel() {
  try {
    const totalUsers = await prisma.user.count()
    
    const registeredUsers = await prisma.userEvent.groupBy({
      by: ['userId'],
      where: {
        eventType: 'registration',
        userId: {
          not: null
        }
      }
    }).then(results => results.length)

    const activatedUsers = await prisma.userEvent.groupBy({
      by: ['userId'],
      where: {
        eventType: 'profile_update',
        userId: {
          not: null
        }
      }
    }).then(results => results.length)

    const engagedUsers = await prisma.contentEngagement.groupBy({
      by: ['userId'],
      where: {
        userId: {
          not: null
        }
      }
    }).then(results => results.length)

    const retainedUsers = await prisma.userSession.groupBy({
      by: ['userId'],
      where: {
        startTime: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      }
    }).then(results => results.length)

    return {
      stages: [
        { name: 'Total Users', count: totalUsers, percentage: 100 },
        { name: 'Registered', count: registeredUsers, percentage: (registeredUsers / totalUsers) * 100 },
        { name: 'Activated', count: activatedUsers, percentage: (activatedUsers / totalUsers) * 100 },
        { name: 'Engaged', count: engagedUsers, percentage: (engagedUsers / totalUsers) * 100 },
        { name: 'Retained (7d)', count: retainedUsers, percentage: (retainedUsers / totalUsers) * 100 }
      ]
    }
  } catch (error) {
    console.error('Error fetching user journey funnel:', error)
    return { stages: [] }
  }
}

// Cohort Analysis
export async function getCohortAnalysis() {
  try {
    // Get users grouped by registration month
    const userCohorts = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('month', "createdAt") as cohort_month,
        COUNT(*) as users_count
      FROM users 
      WHERE "createdAt" >= NOW() - INTERVAL '12 months'
      GROUP BY DATE_TRUNC('month', "createdAt")
      ORDER BY cohort_month
    ` as Array<{ cohort_month: Date; users_count: bigint }>

    // Get retention data for each cohort
    const retentionData = await Promise.all(
      userCohorts.map(async (cohort) => {
        const cohortUsers = await prisma.user.findMany({
          where: {
            createdAt: {
              gte: cohort.cohort_month,
              lt: new Date(cohort.cohort_month.getTime() + 30 * 24 * 60 * 60 * 1000) // Next month
            }
          },
          select: { id: true }
        })

        const userIds = cohortUsers.map(u => u.id)

        // Check retention for different periods
        const retention = await Promise.all([
          // Week 1
          prisma.userSession.groupBy({
            by: ['userId'],
            where: {
              userId: { in: userIds },
              startTime: {
                gte: new Date(cohort.cohort_month.getTime() + 7 * 24 * 60 * 60 * 1000),
                lt: new Date(cohort.cohort_month.getTime() + 14 * 24 * 60 * 60 * 1000)
              }
            }
          }),
          // Month 1
          prisma.userSession.groupBy({
            by: ['userId'],
            where: {
              userId: { in: userIds },
              startTime: {
                gte: new Date(cohort.cohort_month.getTime() + 30 * 24 * 60 * 60 * 1000),
                lt: new Date(cohort.cohort_month.getTime() + 60 * 24 * 60 * 60 * 1000)
              }
            }
          }),
          // Month 3
          prisma.userSession.groupBy({
            by: ['userId'],
            where: {
              userId: { in: userIds },
              startTime: {
                gte: new Date(cohort.cohort_month.getTime() + 90 * 24 * 60 * 60 * 1000),
                lt: new Date(cohort.cohort_month.getTime() + 120 * 24 * 60 * 60 * 1000)
              }
            }
          })
        ])

        return {
          cohort: cohort.cohort_month.toISOString().slice(0, 7), // YYYY-MM format
          totalUsers: Number(cohort.users_count),
          retention: {
            week1: retention[0].length,
            month1: retention[1].length,
            month3: retention[2].length
          }
        }
      })
    )

    return retentionData
  } catch (error) {
    console.error('Error fetching cohort analysis:', error)
    return []
  }
}

// A/B Test Analytics
export async function getABTestAnalytics(testId?: string) {
  try {
    if (testId) {
      // Get specific test results
      const test = await prisma.aBTest.findUnique({
        where: { id: testId },
        include: {
          participants: true
        }
      })

      if (!test) return null

      const participantsByVariant = test.participants.reduce((acc, participant) => {
        if (!acc[participant.variant]) {
          acc[participant.variant] = {
            total: 0,
            converted: 0
          }
        }
        acc[participant.variant].total++
        if (participant.convertedAt) {
          acc[participant.variant].converted++
        }
        return acc
      }, {} as Record<string, { total: number; converted: number }>)

      return {
        test,
        results: Object.entries(participantsByVariant).map(([variant, data]) => ({
          variant,
          participants: data.total,
          conversions: data.converted,
          conversionRate: data.total > 0 ? (data.converted / data.total) * 100 : 0
        }))
      }
    } else {
      // Get all active tests summary
      const activeTests = await prisma.aBTest.findMany({
        where: {
          status: 'active'
        },
        include: {
          _count: {
            select: {
              participants: true
            }
          }
        }
      })

      return activeTests.map(test => ({
        id: test.id,
        name: test.name,
        status: test.status,
        participants: test._count.participants,
        startDate: test.startDate,
        endDate: test.endDate
      }))
    }
  } catch (error) {
    console.error('Error fetching A/B test analytics:', error)
    return testId ? null : []
  }
}

// Track user event
export async function trackUserEvent(
  eventType: string,
  userId?: string,
  sessionId?: string,
  eventData?: any,
  ipAddress?: string,
  userAgent?: string
) {
  try {
    await prisma.userEvent.create({
      data: {
        eventType,
        userId,
        sessionId,
        eventData,
        ipAddress,
        userAgent
      }
    })
  } catch (error) {
    console.error('Error tracking user event:', error)
  }
}

// Track content engagement
export async function trackContentEngagement(
  contentId: string,
  contentType: string,
  engagementType: string,
  userId?: string,
  sessionId?: string,
  duration?: number,
  metadata?: any
) {
  try {
    await prisma.contentEngagement.create({
      data: {
        contentId,
        contentType,
        engagementType,
        userId,
        sessionId,
        duration,
        metadata
      }
    })
  } catch (error) {
    console.error('Error tracking content engagement:', error)
  }
}
