import { prisma } from "@/lib/prisma"

export interface CreateDepartmentData {
  name: string
  slug: string
  description?: string
  headFacultyId?: string
}

export interface UpdateDepartmentData {
  name?: string
  slug?: string
  description?: string
  headFacultyId?: string
}

export async function createDepartment(data: CreateDepartmentData) {
  return prisma.department.create({
    data,
    include: {
      faculty: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      },
      programs: true,
      courses: true
    }
  })
}

export async function getAllDepartments() {
  return prisma.department.findMany({
    include: {
      faculty: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      },
      programs: true,
      courses: true,
      _count: {
        select: {
          faculty: true,
          programs: true,
          courses: true
        }
      }
    },
    orderBy: {
      name: 'asc'
    }
  })
}

export async function getDepartmentById(id: string) {
  return prisma.department.findUnique({
    where: { id },
    include: {
      faculty: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          researchAreas: true,
          publications: true
        }
      },
      programs: true,
      courses: true
    }
  })
}

export async function getDepartmentBySlug(slug: string) {
  return prisma.department.findUnique({
    where: { slug },
    include: {
      faculty: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          researchAreas: true,
          publications: true
        }
      },
      programs: true,
      courses: true
    }
  })
}

export async function updateDepartment(id: string, data: UpdateDepartmentData) {
  return prisma.department.update({
    where: { id },
    data,
    include: {
      faculty: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      },
      programs: true,
      courses: true
    }
  })
}

export async function deleteDepartment(id: string) {
  return prisma.department.delete({
    where: { id }
  })
}

export async function getDepartmentOptions() {
  return prisma.department.findMany({
    select: {
      id: true,
      name: true,
      slug: true
    },
    orderBy: {
      name: 'asc'
    }
  })
}
