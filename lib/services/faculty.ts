import { prisma } from "@/lib/prisma"
import { UserRole, UserStatus } from "@prisma/client"
import bcrypt from "bcryptjs"

export interface CreateFacultyData {
  email: string
  name: string
  password: string
  title: string
  departmentId: string
  officeLocation?: string
  websiteUrl?: string
  scholarId?: string
  bio?: string
  profile?: {
    firstName?: string
    lastName?: string
    phone?: string
    avatarUrl?: string
  }
}

export interface UpdateFacultyData {
  name?: string
  title?: string
  departmentId?: string
  officeLocation?: string
  websiteUrl?: string
  scholarId?: string
  bio?: string
  profile?: {
    firstName?: string
    lastName?: string
    phone?: string
    avatarUrl?: string
    bio?: string
  }
}

export async function createFaculty(data: CreateFacultyData) {
  const hashedPassword = await bcrypt.hash(data.password, 12)

  return prisma.user.create({
    data: {
      email: data.email,
      name: data.name,
      password: hashedPassword,
      role: UserRole.FACULTY,
      status: UserStatus.ACTIVE,
      profile: data.profile ? {
        create: data.profile
      } : undefined,
      facultyProfile: {
        create: {
          title: data.title,
          departmentId: data.departmentId,
          officeLocation: data.officeLocation,
          websiteUrl: data.websiteUrl,
          scholarId: data.scholarId,
          bio: data.bio,
        }
      }
    },
    include: {
      profile: true,
      facultyProfile: {
        include: {
          department: true,
          education: true,
          researchAreas: true,
          publications: true,
          timeline: true,
          cvDocuments: true,
        }
      }
    }
  })
}

export async function getFacultyById(id: string) {
  return prisma.user.findUnique({
    where: { id },
    include: {
      profile: true,
      facultyProfile: {
        include: {
          department: true,
          education: true,
          researchAreas: true,
          publications: true,
          timeline: true,
          cvDocuments: true,
          classes: {
            include: {
              course: true
            }
          },
          officeHours: true,
          researchProjects: {
            include: {
              requirements: true,
              tags: {
                include: {
                  tag: true
                }
              }
            }
          }
        }
      }
    }
  })
}

export async function getAllFaculty() {
  return prisma.user.findMany({
    where: {
      role: UserRole.FACULTY,
      status: UserStatus.ACTIVE
    },
    include: {
      profile: true,
      facultyProfile: {
        include: {
          department: true,
          researchAreas: true,
          publications: true,
        }
      }
    },
    orderBy: {
      name: 'asc'
    }
  })
}

export async function updateFaculty(id: string, data: UpdateFacultyData) {
  return prisma.user.update({
    where: { id },
    data: {
      name: data.name,
      profile: data.profile ? {
        upsert: {
          create: data.profile,
          update: data.profile
        }
      } : undefined,
      facultyProfile: {
        update: {
          title: data.title,
          departmentId: data.departmentId,
          officeLocation: data.officeLocation,
          websiteUrl: data.websiteUrl,
          scholarId: data.scholarId,
          bio: data.bio,
        }
      }
    },
    include: {
      profile: true,
      facultyProfile: {
        include: {
          department: true,
          education: true,
          researchAreas: true,
          publications: true,
          timeline: true,
          cvDocuments: true,
        }
      }
    }
  })
}

export async function deleteFaculty(id: string) {
  return prisma.user.update({
    where: { id },
    data: {
      status: UserStatus.INACTIVE
    }
  })
}

export async function searchFaculty(query: string) {
  return prisma.user.findMany({
    where: {
      role: UserRole.FACULTY,
      status: UserStatus.ACTIVE,
      OR: [
        { name: { contains: query, mode: 'insensitive' } },
        { email: { contains: query, mode: 'insensitive' } },
        { facultyProfile: { 
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { bio: { contains: query, mode: 'insensitive' } },
            { department: { name: { contains: query, mode: 'insensitive' } } },
            { researchAreas: { some: { areaName: { contains: query, mode: 'insensitive' } } } }
          ]
        }}
      ]
    },
    include: {
      profile: true,
      facultyProfile: {
        include: {
          department: true,
          researchAreas: true,
          publications: true,
        }
      }
    }
  })
}
