import { Book<PERSON>pen, Building, Leaf, Lock, Users, BarChart3, FileText, UsersRound, Briefcase } from 'lucide-react';
import React from 'react';

export interface ProgramFaculty {
  id: string;
  name: string;
  title: string;
  imageUrl?: string;
  bioLink?: string; // Link to full faculty profile page if available
}

export interface Program {
  id: string; // e.g., 'btech-cybersecurity'
  slug: string; // for the URL
  departmentId: string; // e.g., 'computer-science'
  departmentName: string;
  name: string; // Full program name, e.g., "B.Tech in Cybersecurity"
  shortName?: string; // Shorter name for cards, e.g., "Cybersecurity"
  degree: string; // e.g., "B.Tech", "B.Sc.", "M.Sc.", "B.Ed."
  tagline: string;
  description: string; // Detailed description of the program
  icon?: React.ReactElement; // Corrected type for icon
  imageUrl?: string; // Main image for the program page
  imageAlt?: string;
  durationYears?: number;
  admissionRequirements: string[]; // List of requirements
  curriculumHighlights: {
    title: string; // e.g., "Year 1", "Core Modules", "Specializations"
    courses: string[]; // List of course names or brief descriptions
  }[];
  careerProspects: string[];
  featuredFaculty?: ProgramFaculty[]; // A few key faculty members
  relatedPrograms?: { id: string; name: string; slug: string }[]; // Links to related programs
}

export const programsData: Program[] = [
  // Computer Science Programs
  {
    id: 'btech-cybersecurity',
    slug: 'btech-cybersecurity',
    departmentId: 'computer-science',
    departmentName: 'School of Computer Science',
    name: 'B.Tech in Cybersecurity',
    shortName: 'Cybersecurity',
    degree: 'B.Tech',
    tagline: 'Defend the Digital Frontier.',
    description: 'Our comprehensive Cybersecurity program equips students with the skills to protect digital assets and infrastructure from evolving threats. Learn from industry experts and gain hands-on experience in our state-of-the-art security labs.',
    icon: <Lock className="h-5 w-5" />,
    imageUrl: '/placeholder.svg?height=500&width=800',
    imageAlt: 'Cybersecurity concept image',
    durationYears: 4,
    admissionRequirements: [
      'Completed 10+2 or equivalent with Physics, Chemistry, and Mathematics.',
      'Minimum 60% aggregate score.',
      'Entrance exam qualification (if applicable).',
    ],
    curriculumHighlights: [
      { 
        title: 'Core Cybersecurity Modules',
        courses: [
          'Network Security & Cryptography',
          'Ethical Hacking & Penetration Testing',
          'Digital Forensics & Incident Response',
          'Security Operations & Threat Intelligence',
          'Web Application Security',
          'Cloud Security',
        ]
      },
      {
        title: 'Foundational Computer Science',
        courses: [
          'Data Structures & Algorithms',
          'Operating Systems',
          'Database Management Systems',
          'Computer Networks',
        ]
      },
      {
        title: 'Electives & Specializations',
        courses: [
          'Advanced Malware Analysis',
          'IoT Security',
          'Blockchain Security',
          'AI in Cybersecurity',
        ]
      },
    ],
    careerProspects: [
      'Cybersecurity Analyst',
      'Security Engineer',
      'Penetration Tester',
      'Digital Forensics Investigator',
      'Security Consultant',
      'Chief Information Security Officer (CISO)',
    ],
    featuredFaculty: [
      {
        id: 'faculty-cs-1',
        name: 'Dr. Alan Turing Jr.',
        title: 'Professor, Cybersecurity Lead',
        imageUrl: '/placeholder.svg?height=100&width=100',
      },
      {
        id: 'faculty-cs-2',
        name: 'Ms. Ada Lovelace II',
        title: 'Associate Professor, Network Security Expert',
        imageUrl: '/placeholder.svg?height=100&width=100',
      },
    ],
  },
  {
    id: 'btech-ai-ml',
    slug: 'btech-ai-ml',
    departmentId: 'computer-science',
    departmentName: 'School of Computer Science',
    name: 'B.Tech in Artificial Intelligence & Machine Learning',
    shortName: 'AI & Machine Learning',
    degree: 'B.Tech',
    tagline: 'Engineer the Future of Intelligence.',
    description: 'This program focuses on the theory and application of AI and ML, preparing students to develop intelligent systems and solve complex problems across various domains.',
    icon: <Users className="h-5 w-5" />,
    imageUrl: '/placeholder.svg?height=500&width=800',
    imageAlt: 'AI and Machine Learning concept',
    durationYears: 4,
    admissionRequirements: [
      'Completed 10+2 or equivalent with Physics, Chemistry, and Mathematics.',
      'Minimum 60% aggregate score in PCM.',
      'Strong aptitude for programming and logical reasoning.',
    ],
    curriculumHighlights: [
      { 
        title: 'Core AI/ML Modules',
        courses: [
          'Introduction to Artificial Intelligence',
          'Machine Learning Algorithms',
          'Deep Learning & Neural Networks',
          'Natural Language Processing',
          'Computer Vision',
          'Reinforcement Learning',
        ]
      },
      {
        title: 'Supporting Technical Skills',
        courses: [
          'Advanced Python Programming',
          'Big Data Analytics',
          'Robotics Process Automation',
          'AI Ethics and Governance',
        ]
      },
    ],
    careerProspects: [
      'Machine Learning Engineer',
      'Data Scientist',
      'AI Researcher',
      'NLP Engineer',
      'Computer Vision Engineer',
      'Robotics Engineer',
    ],
  },
  // Add B.Sc. Sustainable Agriculture
  {
    id: 'bsc-sustainable-agriculture',
    slug: 'bsc-sustainable-agriculture',
    departmentId: 'agriculture',
    departmentName: 'School of Agriculture and Climate Science',
    name: 'B.Sc. in Sustainable Agriculture',
    shortName: 'Sustainable Agriculture',
    degree: 'B.Sc.',
    tagline: 'Cultivating a Greener Future.',
    description: 'This program focuses on environmentally sound agricultural practices, resource management, and food security to address global challenges.',
    icon: <Leaf className="h-5 w-5" />,
    imageUrl: '/placeholder.svg?height=500&width=800',
    imageAlt: 'Sustainable farming practices',
    durationYears: 3,
    admissionRequirements: [
      'Completed 10+2 or equivalent with Biology/Agriculture stream.',
      'Minimum 55% aggregate score.',
    ],
    curriculumHighlights: [
      {
        title: 'Core Agricultural Sciences',
        courses: [
          'Soil Science & Management',
          'Agronomy & Crop Production',
          'Organic Farming Principles',
          'Integrated Pest Management',
          'Water Resource Management',
        ]
      },
      {
        title: 'Sustainability & Environment',
        courses: [
          'Agroecology',
          'Climate Change & Agriculture',
          'Renewable Energy in Agriculture',
          'Food Policy & Security',
        ]
      },
    ],
    careerProspects: [
      'Agricultural Consultant',
      'Farm Manager (Sustainable Practices)',
      'Organic Certification Inspector',
      'Agricultural Policy Analyst',
      'Researcher in Sustainable Agriculture',
    ],
  },
  // Add BBA
  {
    id: 'bba-general',
    slug: 'bba-general',
    departmentId: 'business',
    departmentName: 'School of Business',
    name: 'Bachelor of Business Administration (BBA)',
    shortName: 'BBA',
    degree: 'BBA',
    tagline: 'Lead with Vision and Strategy.',
    description: 'Our BBA program provides a broad understanding of business principles and practices, preparing students for diverse roles in the corporate world.',
    icon: <Building className="h-5 w-5" />,
    imageUrl: '/placeholder.svg?height=500&width=800',
    imageAlt: 'Modern business environment',
    durationYears: 3,
    admissionRequirements: [
      'Completed 10+2 or equivalent in any stream.',
      'Minimum 55% aggregate score.',
    ],
    curriculumHighlights: [
      {
        title: 'Foundational Business Courses',
        courses: [
          'Principles of Management',
          'Financial Accounting',
          'Marketing Management',
          'Business Economics',
          'Human Resource Management',
        ]
      },
      {
        title: 'Advanced Business Topics',
        courses: [
          'Business Law & Ethics',
          'Operations Management',
          'Strategic Management',
          'International Business',
          'Entrepreneurship Development',
        ]
      },
    ],
    careerProspects: [
      'Business Analyst',
      'Management Trainee',
      'Marketing Executive',
      'HR Specialist',
      'Entrepreneur',
      'Operations Manager',
    ],
  },
  // Add B.Ed. Elementary
  {
    id: 'bed-elementary',
    slug: 'bed-elementary',
    departmentId: 'education',
    departmentName: 'School of Education',
    name: 'B.Ed. in Elementary Education',
    shortName: 'Elementary Education',
    degree: 'B.Ed.',
    tagline: 'Shaping Young Minds for Tomorrow.',
    description: 'This program prepares aspiring teachers with the knowledge, skills, and values needed to effectively educate elementary school students.',
    icon: <BookOpen className="h-5 w-5" />,
    imageUrl: '/placeholder.svg?height=500&width=800',
    imageAlt: 'Elementary classroom setting',
    durationYears: 2,
    admissionRequirements: [
      'Bachelor\'s degree with minimum 50% marks.',
      'Qualifying score in entrance test.',
    ],
    curriculumHighlights: [
      {
        title: 'Educational Theory',
        courses: [
          'Child Development & Learning',
          'Curriculum Design & Development',
          'Educational Psychology',
          'Inclusive Education Practices',
        ]
      },
      {
        title: 'Teaching Methodology',
        courses: [
          'Language Teaching Methods',
          'Mathematics Teaching',
          'Environmental Studies',
          'Arts & Physical Education',
          'Educational Technology',
        ]
      },
    ],
    careerProspects: [
      'Elementary School Teacher',
      'Educational Content Developer',
      'School Administrator',
      'Educational Consultant',
      'Academic Coordinator',
    ],
  },
];

// Helper function to get a program by its slug
export const getProgramBySlug = (slug: string): Program | undefined => {
  return programsData.find(program => program.slug === slug);
};

// Helper function to get programs by department
export const getProgramsByDepartment = (departmentId: string): Program[] => {
  return programsData.filter(program => program.departmentId === departmentId);
}; 