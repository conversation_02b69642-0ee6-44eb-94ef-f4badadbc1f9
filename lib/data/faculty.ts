// Define types for faculty data
export interface Publication {
  id: string
  title: string
  authors: string[]
  journal: string
  year: number
  citationCount: number
  link: string
  abstract?: string
  tags?: string[]
}

export interface TimelineEvent {
  id: string
  year: string
  title: string
  description: string
  type: 'education' | 'position' | 'award'
}

export interface CVDocument {
  id: string
  title: string
  fileType: 'pdf' | 'docx' | 'txt'
  fileSize: string
  lastUpdated: string
  url: string
}

export interface CourseClass {
  id: string
  courseCode: string
  courseName: string
  semester: string
  schedule: {
    days: string[]
    time: string
    location: string
  }
  enrollmentCount: number
  maxEnrollment: number
  syllabus?: string
  description?: string
  status: 'upcoming' | 'current' | 'past'
}

export interface TimeSlot {
  id: string
  startTime: string
  endTime: string
  isAvailable: boolean
  isBooked?: boolean
  studentName?: string
  studentEmail?: string
  topic?: string
}

export interface OfficeHour {
  id: string
  day: string
  timeSlots: TimeSlot[]
  location: string
  notes?: string
}

export interface ResearchProject {
  id: string
  title: string
  description: string
  requirements?: string[]
  timeline: string
  positions: number
  commitment: string
  isPaid: boolean
  isCredited: boolean
  tags: string[]
  status: 'recruiting' | 'ongoing' | 'completed'
  imageUrl?: string
}

export interface FacultyMember {
  id: string
  imageUrl: string
  altText: string
  name: string
  title: string
  department: string
  email: string
  website: string
  bio: string
  education: string[]
  research: string[]
  publications: string[]
  courses?: string[]
  officeHours?: string
  office?: string
  // Extended data
  scholarId?: string
  timeline?: TimelineEvent[]
  cvDocuments?: CVDocument[]
  scholarlyPublications?: Publication[]
  upcomingClasses?: CourseClass[]
  scheduledOfficeHours?: OfficeHour[]
  researchProjects?: ResearchProject[]
}

export interface ResearchArea {
  name: string
  facultyIds: string[]
  color: string
}

// Expanded faculty data with unique IDs and more details
export const facultyData: FacultyMember[] = [
  {
    id: "shishir-adhikari",
    imageUrl: "/images/team/shishir-adhikari.jpg",
    altText: "Shishir Raj Adhikari, PhD",
    name: "Shishir Raj Adhikari, PhD",
    title: "Academic Director",
    department: "Ullens College",
    email: "<EMAIL>",
    website: "https://faculty.ullens.edu.np/sadhikari",
    bio: "Dr. Shishir Adhikari brings a unique blend of rigorous scientific training and practical research experience to his role as Academic Director at Ullens College. With a PhD in Physics from Case Western Reserve University and postdoctoral experience at Harvard Medical School and Dana-Farber Cancer Institute, he is passionate about bridging the gap between advanced research methodologies and undergraduate education. Currently serving as a Research Scientist at the Nepal Applied Mathematics and Informatics Institute for Research (NAAMII), Shishir specializes in statistical mechanics, biophysics, and machine learning. His research journey has taken him from studying polymer conformations to unraveling cellular adhesion mechanisms, always with an eye toward practical applications. What drives Shishir in his academic leadership role is the belief that students learn best when they engage with real problems. Having mentored students from high school to graduate levels across institutions like Yale, CalTech, and Case Western Reserve University, he understands that meaningful learning happens when theoretical knowledge meets hands-on discovery. At Ullens College, Shishir is committed to creating an environment where students don't just consume knowledge but actively participate in the research process. He believes that every student, regardless of their background, has the potential to contribute meaningfully to solving complex problems when given the right guidance and opportunities. His approach to education is simple: start with curiosity, build with rigor, and always remember that the best research questions often come from the most unexpected places. When not thinking about academic programs or research problems, Shishir enjoys photography, rock climbing, and cooking—activities that, much like good research, require patience, precision, and a willingness to experiment.",
    education: [
      "Ph.D. Physics, Case Western Reserve University",
      "M.S. Physics Entrepreneurship Program (PEP), Case Western Reserve University", 
      "B.S. Physics with Math & Computer Science Minor, Hiram College"
    ],
    research: [
      "Statistical Mechanics",
      "Biophysics", 
      "Machine Learning",
      "Polymer Conformations",
      "Cell Adhesion Mechanisms",
      "Theory & Computation"
    ],
    publications: [
      "Adhikari, S. et al. (2022). 'Impact of Solvent Density and Temperature on the Conformation of a Lennard-Jones Polymer Chain in Solution'. Journal of Statistical Physics.",
      "Adhikari, S. & Chen, M. (2021). 'Plexar Imaging: A Startup Determined to Solve Dose Variability Problem'. Entrepreneurship in Science Journal.",
      "Adhikari, S. (2020). 'Statistical Physics of Cell Adhesion Complexes and Machine Learning'. Biophysical Journal."
    ],
    courses: [
      "PHYS-4010: Advanced Statistical Mechanics",
      "PHYS-3050: Computational Physics",
      "MATH-2020: Mathematical Methods in Physics"
    ],
    officeHours: "Tuesdays and Thursdays: 2:00 PM - 4:00 PM",
    office: "Academic Affairs Building, Room 201",
    // Extended data
    scholarId: "sadhikari2024",
    timeline: [
      {
        id: "timeline-1", 
        year: "2024-Present",
        title: "Academic Director, Ullens College",
        description: "Leading academic programs and fostering research-based learning at Ullens College.",
        type: "position"
      },
      {
        id: "timeline-2",
        year: "2020-2024", 
        title: "Postdoctoral Researcher",
        description: "Conducted research in circadian rhythm in stem cells, graph neural nets, and bilevel optimization at Harvard Medical School and Dana-Farber Cancer Institute.",
        type: "position"
      },
      {
        id: "timeline-3",
        year: "2015-2020",
        title: "PhD Student",
        description: "Focused on theoretical physics research at Case Western Reserve University.",
        type: "education"
      },
      {
        id: "timeline-4", 
        year: "2020",
        title: "Ph.D. Theoretical Physics",
        description: "Dissertation on 'Statistical Physics of Cell Adhesion Complexes and Machine Learning'",
        type: "education"
      }
    ],
    cvDocuments: [
      {
        id: "cv-1",
        title: "Academic CV",
        fileType: "pdf", 
        fileSize: "1.1 MB",
        lastUpdated: "January 10, 2024",
        url: "#"
      },
      {
        id: "cv-2",
        title: "Research Statement",
        fileType: "pdf",
        fileSize: "520 KB", 
        lastUpdated: "February 15, 2024",
        url: "#"
      }
    ],
    scholarlyPublications: [
      {
        id: "pub-1",
        title: "Impact of Solvent Density and Temperature on the Conformation of a Lennard-Jones Polymer Chain in Solution",
        authors: ["Shishir Adhikari", "Research Team"],
        journal: "Journal of Statistical Physics",
        year: 2022,
        citationCount: 15,
        link: "#",
        abstract: "This research investigates how solvent density and temperature affect polymer chain conformations using statistical mechanics principles."
      },
      {
        id: "pub-2", 
        title: "Plexar Imaging: A Startup Determined to Solve Dose Variability Problem",
        authors: ["Shishir Adhikari", "Business Partners"],
        journal: "Entrepreneurship in Science Journal",
        year: 2021,
        citationCount: 8,
        link: "#",
        abstract: "A comprehensive overview of the Plexar Imaging startup and its innovative approach to solving medical dose variability challenges."
      },
      {
        id: "pub-3",
        title: "Statistical Physics of Cell Adhesion Complexes and Machine Learning", 
        authors: ["Shishir Adhikari"],
        journal: "Biophysical Journal",
        year: 2020,
        citationCount: 25,
        link: "#",
        abstract: "This paper explores the application of statistical physics principles and machine learning to understand cell adhesion mechanisms."
      }
    ],
    upcomingClasses: [
      {
        id: "class-1",
        courseCode: "PHYS-4010",
        courseName: "Advanced Statistical Mechanics",
        semester: "Fall 2024",
        schedule: {
          days: ["M", "W"],
          time: "14:00-15:30", 
          location: "Physics Building, Room 201"
        },
        enrollmentCount: 18,
        maxEnrollment: 25,
        syllabus: "#",
        description: "Advanced topics in statistical mechanics with applications to biological systems and polymers.",
        status: "current"
      },
      {
        id: "class-2",
        courseCode: "PHYS-3050",
        courseName: "Computational Physics",
        semester: "Fall 2024",
        schedule: {
          days: ["T", "Th"], 
          time: "10:00-11:30",
          location: "Computer Lab, Room 105"
        },
        enrollmentCount: 22,
        maxEnrollment: 30,
        syllabus: "#",
        description: "Introduction to computational methods in physics and data analysis.",
        status: "current"
      }
    ],
    scheduledOfficeHours: [
      {
        id: "office-1",
        day: "Tuesday",
        timeSlots: [
          {
            id: "slot-1",
            startTime: "14:00",
            endTime: "14:30",
            isAvailable: true
          },
          {
            id: "slot-2", 
            startTime: "14:30",
            endTime: "15:00",
            isAvailable: true
          },
          {
            id: "slot-3",
            startTime: "15:00", 
            endTime: "15:30",
            isAvailable: true
          },
          {
            id: "slot-4",
            startTime: "15:30",
            endTime: "16:00",
            isAvailable: true
          }
        ],
        location: "Physics Building, Room 201"
      }
    ],
    researchProjects: [
      {
        id: "project-1",
        title: "Neural Networks for Medical Image Analysis",
        description: "Developing advanced neural networks for accurate diagnosis of medical conditions from imaging data.",
        requirements: [
          "Strong programming skills in Python",
          "Experience with deep learning frameworks (PyTorch or TensorFlow)",
          "Interest in healthcare applications"
        ],
        timeline: "Fall 2023 - Spring 2024",
        positions: 2,
        commitment: "10 hours/week",
        isPaid: true,
        isCredited: true,
        tags: ["Deep Learning", "Computer Vision", "Healthcare", "Neural Networks"],
        status: "recruiting"
      },
      {
        id: "project-2",
        title: "Reinforcement Learning for Robotics",
        description: "Exploring reinforcement learning techniques to improve robot navigation and decision-making.",
        requirements: [
          "Coursework in AI/ML",
          "Programming experience",
          "Interest in robotics"
        ],
        timeline: "Fall 2023 - Summer 2024",
        positions: 1,
        commitment: "8-12 hours/week",
        isPaid: false,
        isCredited: true,
        tags: ["Reinforcement Learning", "Robotics", "AI"],
        status: "recruiting"
      },
      {
        id: "project-3",
        title: "Natural Language Understanding",
        description: "Researching improved methods for natural language understanding in conversational AI systems.",
        timeline: "Fall 2022 - Spring 2023",
        positions: 3,
        commitment: "10 hours/week",
        isPaid: true,
        isCredited: false,
        tags: ["NLP", "Conversational AI", "Machine Learning"],
        status: "ongoing",
        imageUrl: "/images/faculty/default-avatar.svg"
      }
    ]
  },
  {
    id: "michael-chen",
    imageUrl: "/images/faculty/default-avatar.svg",
    altText: "Prof. Michael Chen",
    name: "Prof. Michael Chen",
    title: "Chair, Business School",
    department: "Business",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/mchen",
    bio: "MBA from Harvard Business School with extensive experience in corporate leadership and entrepreneurship.",
    education: [
      "MBA, Harvard Business School",
      "B.S. Economics, University of Pennsylvania"
    ],
    research: [
      "Strategic Management",
      "Entrepreneurship and Innovation",
      "Corporate Finance"
    ],
    publications: [
      "Chen, M. (2021). 'Strategic Innovation in Emerging Markets'. Journal of Business Strategy.",
      "Chen, M. & Williams, J. (2019). 'Entrepreneurial Ecosystems in Digital Age'. Business Innovation Review.",
      "Chen, M. (2017). 'Corporate Leadership in Times of Change'. Leadership Quarterly."
    ],
    courses: [
      "BUS-4100: Strategic Management",
      "BUS-3200: Entrepreneurship",
      "BUS-2100: Principles of Management"
    ],
    officeHours: "Tuesdays and Thursdays: 1:00 PM - 3:00 PM",
    office: "Business Building, Room 210"
  },
  {
    id: "emily-rodriguez",
    imageUrl: "/images/faculty/default-avatar.svg",
    altText: "Dr. Emily Rodriguez",
    name: "Dr. Emily Rodriguez",
    title: "Professor, Agriculture",
    department: "Agriculture and Climate Science",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/erodriguez",
    bio: "Ph.D. in Environmental Science with research focus on sustainable farming practices and climate adaptation.",
    education: [
      "Ph.D. Environmental Science, University of California, Davis",
      "M.S. Agricultural Science, Cornell University",
      "B.S. Environmental Studies, University of Washington"
    ],
    research: [
      "Sustainable Agriculture",
      "Climate Adaptation for Farming",
      "Agroecology and Biodiversity"
    ],
    publications: [
      "Rodriguez, E. et al. (2022). 'Climate-Resilient Agricultural Practices'. Journal of Sustainable Agriculture.",
      "Rodriguez, E. & Kumar, A. (2020). 'Biodiversity in Sustainable Farming'. Agroecology Journal.",
      "Rodriguez, E. (2018). 'Water Conservation Techniques in Agriculture'. Environmental Management Review."
    ],
    courses: [
      "AGR-4010: Sustainable Farming Systems",
      "AGR-3050: Climate Adaptation in Agriculture",
      "AGR-2020: Introduction to Agroecology"
    ],
    officeHours: "Wednesdays and Fridays: 10:00 AM - 12:00 PM",
    office: "Agriculture Building, Room 120"
  },
  {
    id: "james-wilson",
    imageUrl: "/images/faculty/default-avatar.svg",
    altText: "Dr. James Wilson",
    name: "Dr. James Wilson",
    title: "Director, Education",
    department: "Education",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/jwilson",
    bio: "Ed.D. in Educational Leadership with 20+ years of experience in curriculum development and teacher training.",
    education: [
      "Ed.D. Educational Leadership, Columbia University",
      "M.Ed. Curriculum and Instruction, University of Michigan",
      "B.A. Education, Ohio State University"
    ],
    research: [
      "Educational Technology",
      "Teacher Professional Development",
      "Inclusive Education Practices"
    ],
    publications: [
      "Wilson, J. (2022). 'Digital Transformation in Education'. Educational Technology Journal.",
      "Wilson, J. & Martinez, L. (2020). 'Inclusive Teaching Strategies'. Journal of Teacher Education.",
      "Wilson, J. (2018). 'Professional Development Models for Educators'. Educational Leadership Review."
    ],
    courses: [
      "EDU-4100: Educational Leadership",
      "EDU-3200: Technology in Education",
      "EDU-2100: Curriculum Design"
    ],
    officeHours: "Mondays and Thursdays: 11:00 AM - 1:00 PM",
    office: "Education Building, Room 405"
  },
  {
    id: "amira-patel",
    imageUrl: "/images/faculty/default-avatar.svg",
    altText: "Dr. Amira Patel",
    name: "Dr. Amira Patel",
    title: "Associate Professor, Computer Science",
    department: "Computer Science",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/apatel",
    bio: "Ph.D. in Computer Science specializing in cybersecurity and network systems, with industry experience at major tech companies.",
    education: [
      "Ph.D. Computer Science, Carnegie Mellon University",
      "M.S. Information Security, Georgia Tech",
      "B.S. Computer Science, University of Illinois"
    ],
    research: [
      "Cybersecurity",
      "Network Security",
      "Privacy-Preserving Computing"
    ],
    publications: [
      "Patel, A. et al. (2021). 'Advanced Threat Detection Systems'. Journal of Cybersecurity.",
      "Patel, A. & Garcia, N. (2019). 'Privacy in Distributed Systems'. Network Security Journal.",
      "Patel, A. (2017). 'Encryption Methods for Cloud Storage'. Cloud Computing Review."
    ],
    courses: [
      "CS-4200: Advanced Network Security",
      "CS-3300: Cryptography",
      "CS-2300: Introduction to Cybersecurity"
    ],
    officeHours: "Tuesdays and Fridays: 3:00 PM - 5:00 PM",
    office: "Computer Science Building, Room 210"
  },
  {
    id: "david-kim",
    imageUrl: "/images/faculty/default-avatar.svg",
    altText: "Prof. David Kim",
    name: "Prof. David Kim",
    title: "Assistant Professor, Business",
    department: "Business",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/dkim",
    bio: "Finance expert with experience in investment banking and public policy, focusing on sustainable investment strategies.",
    education: [
      "Ph.D. Finance, London School of Economics",
      "MBA, INSEAD",
      "B.A. Economics, Seoul National University"
    ],
    research: [
      "Sustainable Finance",
      "ESG Investing",
      "Financial Markets and Policy"
    ],
    publications: [
      "Kim, D. (2022). 'ESG Factors in Investment Performance'. Journal of Sustainable Finance.",
      "Kim, D. & Brown, T. (2020). 'Green Bonds and Climate Finance'. Financial Markets Journal.",
      "Kim, D. (2018). 'Policy Implications for Sustainable Investment'. Journal of Financial Policy."
    ],
    courses: [
      "BUS-4300: Sustainable Finance",
      "BUS-3400: Investment Analysis",
      "BUS-2400: Financial Markets"
    ],
    officeHours: "Mondays and Wednesdays: 9:00 AM - 11:00 AM",
    office: "Business Building, Room 320"
  },
  {
    id: "maria-santos",
    imageUrl: "/images/faculty/default-avatar.svg",
    altText: "Dr. Maria Santos",
    name: "Dr. Maria Santos",
    title: "Associate Professor, Agriculture",
    department: "Agriculture and Climate Science",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/msantos",
    bio: "Soil scientist with expertise in regenerative agriculture and carbon sequestration techniques for climate change mitigation.",
    education: [
      "Ph.D. Soil Science, University of São Paulo",
      "M.S. Agricultural Engineering, Texas A&M University",
      "B.S. Agronomy, Federal University of Rio de Janeiro"
    ],
    research: [
      "Soil Carbon Sequestration",
      "Regenerative Agriculture",
      "Climate-Smart Farming Practices"
    ],
    publications: [
      "Santos, M. et al. (2022). 'Carbon Sequestration Methods in Agriculture'. Climate Change Biology.",
      "Santos, M. & Lee, J. (2020). 'Soil Health Indicators for Sustainable Farming'. Soil Science Journal.",
      "Santos, M. (2018). 'Regenerative Practices for Tropical Soils'. Journal of Tropical Agriculture."
    ],
    courses: [
      "AGR-4200: Soil Science for Climate Mitigation",
      "AGR-3300: Regenerative Agriculture",
      "AGR-2200: Soil Biology"
    ],
    officeHours: "Tuesdays and Thursdays: 2:00 PM - 4:00 PM",
    office: "Agriculture Building, Room 230"
  },
  {
    id: "robert-taylor",
    imageUrl: "/images/faculty/default-avatar.svg",
    altText: "Prof. Robert Taylor",
    name: "Prof. Robert Taylor",
    title: "Associate Professor, Education",
    department: "Education",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/rtaylor",
    bio: "Specialist in educational psychology and learning technologies with focus on creating accessible learning environments for diverse student populations.",
    education: [
      "Ph.D. Educational Psychology, University of Toronto",
      "M.Ed. Learning Design and Technology, Harvard University",
      "B.Ed. Secondary Education, University of British Columbia"
    ],
    research: [
      "Accessibility in Education",
      "Learning Technologies",
      "Universal Design for Learning"
    ],
    publications: [
      "Taylor, R. (2022). 'Universal Design Principles in Online Learning'. Educational Technology Research.",
      "Taylor, R. & Wong, S. (2020). 'Assistive Technologies in Classrooms'. Inclusive Education Journal.",
      "Taylor, R. (2018). 'Cognitive Load in Digital Learning Environments'. Learning Sciences Review."
    ],
    courses: [
      "EDU-4300: Accessible Learning Design",
      "EDU-3400: Educational Technology",
      "EDU-2300: Foundations of Learning Psychology"
    ],
    officeHours: "Wednesdays and Fridays: 1:00 PM - 3:00 PM",
    office: "Education Building, Room 315"
  }
];

// Define research areas with color coding
export const researchAreas: ResearchArea[] = [
  {
    name: "Statistical Mechanics",
    facultyIds: ["shishir-adhikari"],
    color: "#4C51BF" // Indigo
  },
  {
    name: "Biophysics",
    facultyIds: ["shishir-adhikari"],
    color: "#667EEA" // Indigo lighter
  },
  {
    name: "Machine Learning",
    facultyIds: ["shishir-adhikari"],
    color: "#9F7AEA" // Purple
  },
  {
    name: "Cybersecurity",
    facultyIds: ["amira-patel"],
    color: "#ED64A6" // Pink
  },
  {
    name: "Network Security",
    facultyIds: ["amira-patel"],
    color: "#F687B3" // Pink lighter
  },
  {
    name: "Privacy-Preserving Computing",
    facultyIds: ["amira-patel"],
    color: "#FC8181" // Red lighter
  },
  {
    name: "Strategic Management",
    facultyIds: ["michael-chen"],
    color: "#F6AD55" // Orange
  },
  {
    name: "Entrepreneurship and Innovation",
    facultyIds: ["michael-chen"],
    color: "#F6E05E" // Yellow
  },
  {
    name: "Corporate Finance",
    facultyIds: ["michael-chen", "david-kim"],
    color: "#68D391" // Green
  },
  {
    name: "Sustainable Agriculture",
    facultyIds: ["emily-rodriguez"],
    color: "#38B2AC" // Teal
  },
  {
    name: "Climate Adaptation for Farming",
    facultyIds: ["emily-rodriguez"],
    color: "#4FD1C5" // Teal lighter
  },
  {
    name: "Agroecology and Biodiversity",
    facultyIds: ["emily-rodriguez"],
    color: "#81E6D9" // Teal lightest
  },
  {
    name: "Educational Technology",
    facultyIds: ["james-wilson", "robert-taylor"],
    color: "#63B3ED" // Blue
  },
  {
    name: "Teacher Professional Development",
    facultyIds: ["james-wilson"],
    color: "#7F9CF5" // Indigo lighter
  },
  {
    name: "Inclusive Education Practices",
    facultyIds: ["james-wilson", "robert-taylor"],
    color: "#B794F4" // Purple lighter
  },
  {
    name: "Sustainable Finance",
    facultyIds: ["david-kim"],
    color: "#48BB78" // Green
  },
  {
    name: "ESG Investing",
    facultyIds: ["david-kim"],
    color: "#68D391" // Green lighter
  },
  {
    name: "Financial Markets and Policy",
    facultyIds: ["david-kim"],
    color: "#9AE6B4" // Green lightest
  },
  {
    name: "Soil Carbon Sequestration",
    facultyIds: ["maria-santos"],
    color: "#38B2AC" // Teal
  },
  {
    name: "Regenerative Agriculture",
    facultyIds: ["maria-santos", "emily-rodriguez"],
    color: "#4FD1C5" // Teal lighter
  },
  {
    name: "Climate-Smart Farming Practices",
    facultyIds: ["maria-santos"],
    color: "#81E6D9" // Teal lightest
  },
  {
    name: "Accessibility in Education",
    facultyIds: ["robert-taylor"],
    color: "#7F9CF5" // Indigo lighter
  },
  {
    name: "Learning Technologies",
    facultyIds: ["robert-taylor"],
    color: "#B794F4" // Purple lighter
  },
  {
    name: "Universal Design for Learning",
    facultyIds: ["robert-taylor"],
    color: "#D6BCFA" // Purple lightest
  }
];

// Helper function to get faculty by ID from database
export async function getFacultyById(id: string): Promise<FacultyMember | null> {
  try {
    // Import prisma here to avoid circular dependencies
    const { prisma } = await import('@/lib/prisma')

    const user = await prisma.user.findUnique({
      where: {
        id: id,
        role: 'FACULTY',
        status: 'ACTIVE'
      },
      include: {
        profile: true,
        facultyProfile: {
          include: {
            department: true,
            publications: {
              orderBy: { year: 'desc' }
            },
            researchAreas: true,
            education: {
              orderBy: { year: 'desc' }
            },
            timeline: {
              orderBy: { year: 'desc' }
            }
          }
        }
      }
    })

    if (!user || !user.facultyProfile) {
      return null
    }

    // Transform database data to FacultyMember format
    const faculty: FacultyMember = {
      id: user.id,
      imageUrl: user.profile?.avatarUrl || user.image || "/images/faculty/default-avatar.svg",
      altText: user.name || "Faculty Member",
      name: user.name || "Faculty Member",
      title: user.facultyProfile.title || "Faculty Member",
      department: user.facultyProfile.department.name,
      email: user.email,
      website: user.facultyProfile.websiteUrl || "#",
      bio: user.facultyProfile.bio || "No bio available.",
      education: user.facultyProfile.education.map(edu =>
        `${edu.degree}, ${edu.institution}${edu.year ? ` (${edu.year})` : ''}`
      ),
      research: user.facultyProfile.researchAreas.map(area => area.areaName),
      publications: user.facultyProfile.publications.map(pub =>
        `${pub.authors.join(', ')} (${pub.year}). "${pub.title}". ${pub.journal}.`
      ),
      courses: [], // TODO: Add courses if needed
      officeHours: user.facultyProfile.officeHours || undefined,
      office: user.facultyProfile.office || undefined,
      // Extended data
      scholarlyPublications: user.facultyProfile.publications.map(pub => ({
        id: pub.id,
        title: pub.title,
        authors: pub.authors,
        journal: pub.journal,
        year: pub.year,
        citationCount: pub.citationCount,
        link: pub.link || "#",
        abstract: pub.abstract || undefined,
        tags: pub.tags
      })),
      timeline: user.facultyProfile.timeline.map(event => ({
        id: event.id,
        year: event.year,
        title: event.title,
        description: event.description,
        type: event.type.toLowerCase() as 'education' | 'position' | 'award'
      }))
    }

    return faculty
  } catch (error) {
    console.error('Error fetching faculty by ID:', error)
    return null
  }
}

// Legacy function for static data (keep for backward compatibility)
export function getFacultyByIdStatic(id: string): FacultyMember | null {
  return facultyData.find(faculty => faculty.id === id) || null;
}

// Helper function to get all research areas for a specific faculty member
export function getResearchAreasForFaculty(facultyId: string): ResearchArea[] {
  return researchAreas.filter(area => area.facultyIds.includes(facultyId));
}

// Helper function to search faculty by keyword
export function searchFaculty(query: string): FacultyMember[] {
  const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);

  if (searchTerms.length === 0) return facultyData;

  return facultyData.filter(faculty => {
    // Search in various faculty properties
    const searchableText = [
      faculty.name,
      faculty.title,
      faculty.department,
      faculty.bio,
      ...faculty.education,
      ...faculty.research,
      ...faculty.publications,
      ...(faculty.courses || [])
    ].join(' ').toLowerCase();

    // Check if all search terms are found
    return searchTerms.every(term => searchableText.includes(term));
  });
}

// Helper function to filter faculty by department
export function filterFacultyByDepartment(department: string): FacultyMember[] {
  if (!department) return facultyData;
  return facultyData.filter(faculty => faculty.department === department);
}

// Helper function to filter faculty by research area
export function filterFacultyByResearchArea(areaName: string): FacultyMember[] {
  if (!areaName) return facultyData;

  const area = researchAreas.find(a => a.name === areaName);
  if (!area) return [];

  return facultyData.filter(faculty => area.facultyIds.includes(faculty.id));
}

// Get unique departments
export function getUniqueDepartments(): string[] {
  return [...new Set(facultyData.map(f => f.department))];
}

// Get unique research area names
export function getUniqueResearchAreas(): string[] {
  return [...new Set(researchAreas.map(area => area.name))];
}