import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Generates a strong random password with:
 * - At least one uppercase letter
 * - At least one lowercase letter
 * - At least one number
 * - At least one special character
 * - Minimum length of 10 characters
 */
export function generateStrongPassword(length = 12): string {
  const uppercase = 'ABCDEFGHJKLMNPQRSTUVWXYZ'  // Excluding I and O to avoid confusion
  const lowercase = 'abcdefghijkmnopqrstuvwxyz'  // Excluding l to avoid confusion
  const numbers = '23456789'                     // Excluding 0 and 1 to avoid confusion
  const special = '!@#$%^&*-_=+?'

  // Ensure at least one of each type
  let password = ''
  password += uppercase.charAt(Math.floor(Math.random() * uppercase.length))
  password += lowercase.charAt(Math.floor(Math.random() * lowercase.length))
  password += numbers.charAt(Math.floor(Math.random() * numbers.length))
  password += special.charAt(Math.floor(Math.random() * special.length))

  // Fill the rest randomly
  const allChars = uppercase + lowercase + numbers + special
  for (let i = password.length; i < length; i++) {
    password += allChars.charAt(Math.floor(Math.random() * allChars.length))
  }

  // Shuffle the password characters
  password = password
    .split('')
    .sort(() => Math.random() - 0.5)
    .join('')

  return password
}
