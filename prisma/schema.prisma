// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  STUDENT
  FACULTY
  COLLEGE_ADMIN
  SYS_ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum ProjectStatus {
  RECRUITING
  ONGOING
  COMPLETED
  CANCELLED
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}

enum TimelineEventType {
  EDUCATION
  POSITION
  AWARD
  PUBLICATION
}

// Core User Management
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  emailVerified DateTime?
  name          String?
  image         String?
  password      String?   // For credential-based authentication
  role          UserRole  @default(STUDENT)
  status        UserStatus @default(PENDING)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  profile       UserProfile?
  facultyProfile FacultyProfile?
  posts         Post[]
  bookings      OfficeHourBooking[]
  userSessions  UserSession[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model UserProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  firstName   String?
  lastName    String?
  phone       String?
  avatarUrl   String?
  bio         String?  @db.Text
  dateOfBirth DateTime?
  address     String?
  city        String?
  state       String?
  zipCode     String?
  country     String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

// Academic Structure
model Department {
  id          String   @id @default(cuid())
  name        String   @unique
  slug        String   @unique
  description String?  @db.Text
  headFacultyId String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  faculty     FacultyProfile[]
  programs    Program[]
  courses     Course[]

  @@map("departments")
}

model Program {
  id           String   @id @default(cuid())
  name         String
  slug         String   @unique
  departmentId String
  degreeType   String
  duration     Int      // in years
  description  String?  @db.Text
  imageUrl     String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  department Department @relation(fields: [departmentId], references: [id])

  @@map("programs")
}

model Course {
  id           String   @id @default(cuid())
  code         String   @unique
  name         String
  description  String?  @db.Text
  credits      Int
  departmentId String
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  department Department @relation(fields: [departmentId], references: [id])
  classes    CourseClass[]

  @@map("courses")
}

// Faculty Models
model FacultyProfile {
  id            String   @id @default(cuid())
  userId        String   @unique
  title         String   // Dr., Prof., etc.
  departmentId  String
  officeLocation String?
  websiteUrl    String?
  scholarId     String?
  bio           String?  @db.Text
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  department        Department          @relation(fields: [departmentId], references: [id])
  education         FacultyEducation[]
  researchAreas     FacultyResearchArea[]
  publications      FacultyPublication[]
  timeline          FacultyTimeline[]
  cvDocuments       FacultyCVDocument[]
  classes           CourseClass[]
  officeHours       OfficeHour[]
  researchProjects  ResearchProject[]
  industryExperience FacultyIndustryExperience[]
  skills            FacultySkill[]

  @@map("faculty_profiles")
}

model FacultyEducation {
  id        String   @id @default(cuid())
  facultyId String
  degree    String
  institution String
  year      Int?
  field     String?
  createdAt DateTime @default(now())

  faculty FacultyProfile @relation(fields: [facultyId], references: [id], onDelete: Cascade)

  @@map("faculty_education")
}

model FacultyResearchArea {
  id          String   @id @default(cuid())
  facultyId   String
  areaName    String
  description String?  @db.Text
  createdAt   DateTime @default(now())

  faculty FacultyProfile @relation(fields: [facultyId], references: [id], onDelete: Cascade)

  @@map("faculty_research_areas")
}

model FacultyPublication {
  id            String   @id @default(cuid())
  facultyId     String
  title         String
  authors       String[] // Array of author names
  journal       String
  year          Int
  citationCount Int      @default(0)
  link          String?
  abstract      String?  @db.Text
  tags          String[]
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  faculty FacultyProfile @relation(fields: [facultyId], references: [id], onDelete: Cascade)

  @@map("faculty_publications")
}

model FacultyTimeline {
  id          String            @id @default(cuid())
  facultyId   String
  year        String
  title       String
  description String            @db.Text
  type        TimelineEventType
  createdAt   DateTime          @default(now())

  faculty FacultyProfile @relation(fields: [facultyId], references: [id], onDelete: Cascade)

  @@map("faculty_timeline")
}

model FacultyCVDocument {
  id          String   @id @default(cuid())
  facultyId   String
  title       String
  fileType    String   // pdf, docx, txt
  fileSize    String
  fileName    String
  fileUrl     String
  lastUpdated DateTime @default(now())
  createdAt   DateTime @default(now())

  faculty FacultyProfile @relation(fields: [facultyId], references: [id], onDelete: Cascade)

  @@map("faculty_cv_documents")
}

// Course Management
model CourseClass {
  id              String   @id @default(cuid())
  courseId        String
  facultyId       String
  semester        String
  year            Int
  section         String?
  enrollmentCount Int      @default(0)
  maxEnrollment   Int
  schedule        Json     // {days: [], time: "", location: ""}
  syllabusUrl     String?
  description     String?  @db.Text
  status          String   @default("upcoming") // upcoming, current, past
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  course  Course         @relation(fields: [courseId], references: [id])
  faculty FacultyProfile @relation(fields: [facultyId], references: [id])

  @@map("course_classes")
}

// Office Hours & Scheduling
model OfficeHour {
  id         String   @id @default(cuid())
  facultyId  String
  dayOfWeek  Int      // 0 = Sunday, 1 = Monday, etc.
  startTime  String   // HH:MM format
  endTime    String   // HH:MM format
  location   String
  notes      String?  @db.Text
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  faculty  FacultyProfile      @relation(fields: [facultyId], references: [id], onDelete: Cascade)
  bookings OfficeHourBooking[]

  @@map("office_hours")
}

model OfficeHourBooking {
  id           String        @id @default(cuid())
  officeHourId String
  studentId    String
  date         DateTime      // Specific date for the booking
  startTime    String        // HH:MM format
  endTime      String        // HH:MM format
  topic        String?
  notes        String?       @db.Text
  status       BookingStatus @default(PENDING)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  // Relations
  officeHour OfficeHour @relation(fields: [officeHourId], references: [id], onDelete: Cascade)
  student    User       @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("office_hour_bookings")
}

// Research Projects
model ResearchProject {
  id          String        @id @default(cuid())
  facultyId   String
  title       String
  description String        @db.Text
  timeline    String
  positions   Int           @default(1)
  commitment  String        // e.g., "10 hours/week"
  isPaid      Boolean       @default(false)
  isCredited  Boolean       @default(false)
  status      ProjectStatus @default(RECRUITING)
  imageUrl    String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  faculty      FacultyProfile           @relation(fields: [facultyId], references: [id], onDelete: Cascade)
  requirements ProjectRequirement[]
  tags         ProjectTag[]

  @@map("research_projects")
}

model ProjectRequirement {
  id        String   @id @default(cuid())
  projectId String
  requirement String
  createdAt DateTime @default(now())

  project ResearchProject @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("project_requirements")
}

// Content Management
model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  slug        String   @unique
  description String?  @db.Text
  color       String?  // Hex color for UI
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  posts Post[]

  @@map("categories")
}

model Tag {
  id        String   @id @default(cuid())
  name      String   @unique
  slug      String   @unique
  createdAt DateTime @default(now())

  // Relations
  posts    PostTag[]
  projects ProjectTag[]

  @@map("tags")
}

model Post {
  id          String     @id @default(cuid())
  title       String
  slug        String     @unique
  content     String     @db.Text
  excerpt     String?    @db.Text
  authorId    String
  categoryId  String
  status      PostStatus @default(DRAFT)
  featured    Boolean    @default(false)
  imageUrl    String?
  imageAlt    String?
  viewCount   Int        @default(0)
  readingTime String?    // e.g., "5 min read"
  publishedAt DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  author   User      @relation(fields: [authorId], references: [id])
  category Category  @relation(fields: [categoryId], references: [id])
  tags     PostTag[]

  @@map("posts")
}

model PostTag {
  postId String
  tagId  String

  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)
  tag  Tag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([postId, tagId])
  @@map("post_tags")
}

model ProjectTag {
  projectId String
  tagId     String

  project ResearchProject @relation(fields: [projectId], references: [id], onDelete: Cascade)
  tag     Tag             @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([projectId, tagId])
  @@map("project_tags")
}

model FacultyIndustryExperience {
  id          String   @id @default(cuid())
  facultyId   String
  company     String
  position    String
  startDate   String
  endDate     String?  // null if "Present" or ongoing
  description String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  faculty FacultyProfile @relation(fields: [facultyId], references: [id], onDelete: Cascade)

  @@map("faculty_industry_experience")
}

model FacultySkill {
  id          String   @id @default(cuid())
  facultyId   String
  skillName   String
  proficiency String?  // e.g., "Beginner", "Intermediate", "Advanced", "Expert"
  category    String?  // e.g., "Technical", "Soft Skills", "Languages"
  createdAt   DateTime @default(now())

  faculty FacultyProfile @relation(fields: [facultyId], references: [id], onDelete: Cascade)

  @@map("faculty_skills")
}

// Analytics and Tracking Models
model UserSession {
  id        String   @id @default(cuid())
  userId    String
  sessionId String   @unique
  ipAddress String?
  userAgent String?
  country   String?
  city      String?
  region    String?
  startTime DateTime @default(now())
  endTime   DateTime?
  duration  Int?     // in seconds
  pageViewCount Int  @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  pageViews UserPageView[]

  @@map("user_sessions")
}

model UserPageView {
  id        String   @id @default(cuid())
  sessionId String
  userId    String?
  path      String
  title     String?
  referrer  String?
  timestamp DateTime @default(now())
  timeOnPage Int?    // in seconds

  session UserSession? @relation(fields: [sessionId], references: [sessionId], onDelete: Cascade)

  @@map("user_page_views")
}

model UserEvent {
  id         String   @id @default(cuid())
  userId     String?
  sessionId  String?
  eventType  String   // 'registration', 'login', 'logout', 'profile_update', 'content_view', etc.
  eventData  Json?    // Additional event-specific data
  timestamp  DateTime @default(now())
  ipAddress  String?
  userAgent  String?

  @@map("user_events")
}

model ContentEngagement {
  id         String   @id @default(cuid())
  contentId  String   // Can be post ID, faculty profile ID, etc.
  contentType String  // 'post', 'faculty_profile', 'course', etc.
  userId     String?
  sessionId  String?
  engagementType String // 'view', 'like', 'share', 'download', 'contact', etc.
  duration   Int?     // Time spent on content in seconds
  timestamp  DateTime @default(now())
  metadata   Json?    // Additional engagement data

  @@map("content_engagement")
}

model ABTest {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?  @db.Text
  status      String   @default("draft") // draft, active, paused, completed
  startDate   DateTime?
  endDate     DateTime?
  variants    Json     // Array of variant configurations
  targetAudience Json? // Targeting criteria
  metrics     Json?    // Success metrics to track
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  participants ABTestParticipant[]

  @@map("ab_tests")
}

model ABTestParticipant {
  id        String   @id @default(cuid())
  testId    String
  userId    String?
  sessionId String?
  variant   String   // Which variant the user was assigned
  assignedAt DateTime @default(now())
  convertedAt DateTime?
  conversionData Json?

  test ABTest @relation(fields: [testId], references: [id], onDelete: Cascade)

  @@unique([testId, userId])
  @@unique([testId, sessionId])
  @@map("ab_test_participants")
}
