// Test script to verify admin API functionality
// Using axios which works better with CommonJS
const axios = require('axios');

async function testAdminAPI() {
  console.log('Testing admin API endpoints...');
  
  try {
    // Test the server status first
    console.log('Testing server health...');
    const healthResponse = await axios.get('http://localhost:3000/api/health', {
      validateStatus: () => true // Accept all status codes
    });
    
    if (healthResponse.status >= 200 && healthResponse.status < 300) {
      console.log('✅ Server is running');
      console.log(healthResponse.data);
    } else {
      console.error('❌ Server is not responding properly');
      console.error(`Status: ${healthResponse.status}`);
      process.exit(1);
    }
    
    // Test auth endpoints without credentials (should fail)
    console.log('\nTesting admin faculty endpoint without auth (should fail)...');
    const unauthResponse = await axios.get('http://localhost:3000/api/admin/faculty', {
      validateStatus: () => true // Accept all status codes
    });
    
    console.log(`Status: ${unauthResponse.status}`);
    console.log(`Response:`, unauthResponse.data);
    
    if (unauthResponse.status === 401) {
      console.log('✅ Correctly blocked unauthorized access');
    } else {
      console.log('❌ Failed to properly authenticate requests');
    }
    
    console.log('\nAPI tests completed. To test with authentication, use test-faculty-update-with-cookie.js with a valid session cookie.');
    
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Run the tests
testAdminAPI(); 