'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  Calendar, 
  Lightbulb, 
  ArrowRight, 
  Users, 
  Layers, 
  TrendingUp,
  Globe,
  Code,
  Brain,
  Sparkles,
  ChevronDown,
  ChevronUp,
  Info,
  ExternalLink,
  BookOpen,
  Clock,
  Award,
  Zap,
  Target,
  GitBranch,
  Link2
} from 'lucide-react';

interface TimelineEvent {
  year: number;
  title: string;
  description: string;
  relevance: string;
  color?: string;
  image?: string;
  link?: string;
  keyFigures?: string[];
  impact?: string;
  interdisciplinaryConnection?: string;
  computationalAdvance?: string;
  modernLegacy?: string;
  funFact?: string;
  source?: string;
  sourceUrl?: string;
}

interface ComputationalTimelineProps {
  events: TimelineEvent[];
}

// Enhanced loading spinner with scientific theme
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="relative">
      <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
      <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-purple-400 rounded-full animate-spin animation-delay-300"></div>
      <div className="absolute inset-2 w-12 h-12 border-4 border-transparent border-t-green-400 rounded-full animate-spin animation-delay-600"></div>
      <motion.div 
        className="absolute inset-6 w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
        animate={{ scale: [1, 1.2, 1], opacity: [0.8, 1, 0.8] }}
        transition={{ duration: 2, repeat: Infinity }}
      />
    </div>
  </div>
);

// Scientific badge component
const ScientificBadge: React.FC<{ 
  children: React.ReactNode; 
  color: string; 
  icon?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
}> = ({ children, color, icon, size = 'md' }) => {
  const sizes = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  return (
    <motion.div
      className={`${color} text-white rounded-full font-mono font-bold ${sizes[size]} flex items-center gap-1.5 shadow-lg border-2 border-white/30 backdrop-blur-sm`}
      whileHover={{ scale: 1.05, y: -1 }}
      transition={{ type: "spring", stiffness: 400 }}
    >
      {icon}
      <span>{children}</span>
    </motion.div>
  );
};

// Enhanced event card component with scientific styling
const EventCard: React.FC<{
  event: TimelineEvent;
  index: number;
  isActive: boolean;
  isExpanded: boolean;
  onClick: () => void;
  onToggleExpanded: () => void;
}> = ({ event, index, isActive, isExpanded, onClick, onToggleExpanded }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Generate scientific styling based on decade
  const decade = Math.floor(event.year / 10) * 10;
  const decadeColors = {
    1940: 'from-gray-600 to-gray-700',
    1950: 'from-amber-600 to-orange-600',
    1960: 'from-emerald-600 to-green-600',
    1970: 'from-purple-600 to-violet-600',
    1980: 'from-blue-600 to-indigo-600',
    1990: 'from-cyan-600 to-teal-600',
    2000: 'from-rose-600 to-red-600',
    2010: 'from-lime-600 to-green-600',
    2020: 'from-yellow-600 to-amber-600'
  };

  const gradientClass = decadeColors[decade as keyof typeof decadeColors] || 'from-gray-600 to-gray-700';

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, rotateX: -10 }}
      animate={{ 
        opacity: 1, 
        y: 0, 
        rotateX: 0,
        scale: isActive ? 1.02 : 1
      }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      className="timeline-event relative group cursor-pointer transition-all duration-500"
      onClick={onClick}
      style={{ 
        minWidth: '300px',
        maxWidth: '320px',
        scrollSnapAlign: 'center',
        perspective: '1000px'
      }}
    >
      {/* Scientific grid background */}
      <div className="absolute inset-0 bg-white/95 backdrop-blur-sm rounded-3xl border border-gray-200/50 shadow-2xl overflow-hidden">
        <div className="absolute inset-0 bg-[linear-gradient(to_right,rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(to_bottom,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:1rem_1rem]"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-50"></div>
      </div>

      {/* Enhanced year marker */}
      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 z-30">
        <motion.div 
          className={`w-16 h-16 rounded-full border-4 border-white shadow-2xl flex items-center justify-center text-white font-bold text-lg bg-gradient-to-br ${gradientClass} relative overflow-hidden`}
          whileHover={{ scale: 1.1, rotate: 5 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.2)_0%,transparent_70%)]"></div>
          <Calendar size={24} className="relative z-10" />
        </motion.div>
        
        {/* Animated year label */}
        <motion.div 
          className="absolute -bottom-10 left-1/2 transform -translate-x-1/2 whitespace-nowrap"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 + 0.3 }}
        >
          <span className={`px-4 py-2 rounded-full text-lg font-bold font-mono border-4 border-white shadow-xl bg-gradient-to-r ${gradientClass} text-white relative overflow-hidden`}>
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-1000"></div>
            <span className="relative z-10">{event.year}</span>
          </span>
        </motion.div>
      </div>

      {/* Connection line with particle effect */}
      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 -translate-y-10 w-1 h-10 bg-gradient-to-t from-gray-300 via-blue-400 to-transparent relative">
        <motion.div 
          className="absolute inset-0 w-1 bg-gradient-to-t from-blue-500 to-purple-500 opacity-0 group-hover:opacity-100"
          transition={{ duration: 0.3 }}
        />
      </div>

      {/* Enhanced image section */}
      <div className="relative h-40 overflow-hidden rounded-t-3xl">
        {!imageError && event.image ? (
          <>
            <img 
              src={event.image} 
              alt={event.title}
              className={`w-full h-full object-cover transition-all duration-700 ${
                imageLoaded ? 'scale-100 blur-0' : 'scale-110 blur-sm'
              } group-hover:scale-105`}
              onLoad={() => setImageLoaded(true)}
              onError={() => setImageError(true)}
            />
            
            {/* Scientific overlay effects */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(59,130,246,0.1)_0%,transparent_50%)]"></div>
            
            {/* Decade badge */}
            <div className="absolute top-4 right-4">
              <ScientificBadge color={`bg-gradient-to-r ${gradientClass}`} size="sm">
                {decade}s
              </ScientificBadge>
            </div>
            
            {/* Scientific metrics overlay */}
            <div className="absolute bottom-2 left-2 flex gap-1">
              <ScientificBadge 
                color="bg-black/60" 
                icon={<Clock size={10} />}
                size="sm"
              >
                {event.year}
              </ScientificBadge>
              {event.keyFigures && event.keyFigures.length > 0 && (
                <ScientificBadge 
                  color="bg-black/60" 
                  icon={<Users size={10} />}
                  size="sm"
                >
                  {event.keyFigures.length}
                </ScientificBadge>
              )}
            </div>

            {!imageLoaded && !imageError && (
              <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 animate-pulse flex items-center justify-center">
                <div className="w-12 h-12 border-4 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </>
        ) : (
          // Fallback scientific pattern when image fails
          <div className={`w-full h-full bg-gradient-to-br ${gradientClass} flex items-center justify-center relative overflow-hidden`}>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.1)_0%,transparent_70%)]"></div>
            <div className="absolute inset-0 bg-[linear-gradient(45deg,rgba(255,255,255,0.05)_25%,transparent_25%,transparent_75%,rgba(255,255,255,0.05)_75%)] bg-[size:20px_20px]"></div>
            <Brain size={48} className="text-white/70" />
            <div className="absolute top-4 right-4">
              <ScientificBadge color="bg-white/20" size="sm">
                {decade}s
              </ScientificBadge>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced content section */}
      <div className="relative p-4 pt-6">
        {/* Title with scientific styling - Reverted to h3, adjusted classes */}
        <h3 className="text-md font-bold mb-2 text-gray-800 group-hover:text-blue-600 transition-colors duration-300 leading-tight">
            {event.title}
        </h3>

        {/* Enhanced description */}
        <p className="text-gray-600 text-xs leading-snug mb-3 font-mono">
          {event.description}
        </p>

        {/* Action buttons row - Placed before expandable content for immediate interaction */} 
        <div className="flex gap-2 mb-3"> 
          {/* Expand button */}
          <motion.button
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpanded();
            }}
            className="flex-1 flex items-center justify-center gap-1 py-2 px-3 bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 border border-blue-200 rounded-lg transition-all text-blue-700 text-xs font-bold font-mono"
            whileHover={{ scale: 1.02, y: -1 }}
            whileTap={{ scale: 0.98 }}
          >
            <span>{isExpanded ? 'Less' : 'More'}</span>
            {isExpanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />} 
          </motion.button>

          {/* Source link button */}
          <motion.a
            href={event.sourceUrl || `https://scholar.google.com/scholar?q=${encodeURIComponent(event.title)}`}
            target="_blank"
            rel="noopener noreferrer"
            onClick={(e) => e.stopPropagation()}
            className="flex items-center gap-1 py-2 px-3 bg-gradient-to-r from-amber-50 to-orange-50 hover:from-amber-100 hover:to-orange-100 border border-amber-200 rounded-lg transition-all text-amber-700 text-xs font-bold font-mono"
            whileHover={{ scale: 1.02, y: -1 }}
            whileTap={{ scale: 0.98 }}
          >
            <BookOpen size={14} />
            <span>Source</span>
            <ExternalLink size={12} />
          </motion.a>
        </div>

        {/* Expanded content with enhanced styling */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0, y: -10 }}
              animate={{ opacity: 1, height: 'auto', y: 0 }}
              exit={{ opacity: 0, height: 0, y: -10 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="mt-3 pt-3 border-t border-gray-200 space-y-2 text-xs" /* Adjusted border, spacing */
            >
              {/* Relevance moved inside expandable section */}
              <DetailItem icon={<Target size={14} />} label="Relevance" value={event.relevance} />
              {event.impact && <DetailItem icon={<Zap size={14} />} label="Impact" value={event.impact} />}
              {event.keyFigures && event.keyFigures.length > 0 && (
                <DetailItem icon={<Users size={14} />} label="Key Figures" value={event.keyFigures.join(', ')} />
              )}
              {event.interdisciplinaryConnection && <DetailItem icon={<GitBranch size={14} />} label="Interdisciplinary Link" value={event.interdisciplinaryConnection} />}
              {event.computationalAdvance && <DetailItem icon={<Code size={14} />} label="Computational Advance" value={event.computationalAdvance} />}
              {event.modernLegacy && <DetailItem icon={<Layers size={14} />} label="Modern Legacy" value={event.modernLegacy} />}
              {event.funFact && <DetailItem icon={<Sparkles size={14} />} label="Fun Fact" value={event.funFact} />}
              
              {/* Enhanced source section - simplified and consistent */}
              {(event.source || event.sourceUrl) && (
                 <div className="mt-2 pt-2 border-t border-gray-100"> {/* Added a separator for source if other details are present*/}
                    <DetailItem 
                        icon={<Link2 size={14} />} 
                        label="Reference"  /* Changed label for clarity */
                        value={ 
                            event.sourceUrl ? 
                            <a href={event.sourceUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">{event.source || event.title}</a> 
                            : 
                            event.source || 'N/A'
                        }
                    />
                 </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

const DetailItem: React.FC<{ icon: React.ReactNode; label: string; value: string | React.ReactNode }> = ({ icon, label, value }) => (
  <div className="flex items-start space-x-1.5 text-gray-700"> 
    <div className="flex-shrink-0 w-3.5 h-3.5 mt-0.5 text-blue-500">{icon}</div> 
    <div>
      <strong className="font-semibold text-gray-800 text-xs">{label}:</strong> 
      <span className="ml-1 text-gray-600 text-xs leading-snug">{value}</span> {/* Added leading-snug */}
    </div>
  </div>
);

const ComputationalTimeline: React.FC<ComputationalTimelineProps> = ({ events }) => {
  const [isMounted, setIsMounted] = useState(false);
  const [activeEvent, setActiveEvent] = useState<number | null>(null);
  const [expandedEvents, setExpandedEvents] = useState<Set<number>>(new Set());
  const [isScrolling, setIsScrolling] = useState(false);
  const [showIntro, setShowIntro] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const { ref: inViewRef, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  // Sort events by year
  const sortedEvents = [...events].sort((a, b) => a.year - b.year);

  useEffect(() => {
    setIsMounted(true);
    if (sortedEvents.length > 0) {
      setActiveEvent(0);
    }
  }, [sortedEvents.length]);

  // Scroll handling
  const handleScroll = useCallback(() => {
    if (!containerRef.current || isScrolling) return;
    
    const containerRect = containerRef.current.getBoundingClientRect();
    const containerCenter = containerRect.left + containerRect.width / 2;
    
    const eventElements = containerRef.current.querySelectorAll('.timeline-event');
    let closestEventIndex = 0;
    let minDistance = Infinity;
    
    eventElements.forEach((element, index) => {
      const elementRect = element.getBoundingClientRect();
      const elementCenter = elementRect.left + elementRect.width / 2;
      const distance = Math.abs(elementCenter - containerCenter);
      
      if (distance < minDistance) {
        minDistance = distance;
        closestEventIndex = index;
      }
    });
    
    setActiveEvent(closestEventIndex);
  }, [isScrolling]);

  // Scroll to event
  const scrollToEvent = useCallback((index: number) => {
    if (!containerRef.current || index < 0 || index >= sortedEvents.length) return;
    
    setIsScrolling(true);
    setActiveEvent(index);
    
    const container = containerRef.current;
    const targetCard = container.querySelector(`.timeline-event:nth-child(${index + 1})`) as HTMLElement;
    
    if (targetCard) {
      const containerRect = container.getBoundingClientRect();
      const targetRect = targetCard.getBoundingClientRect();
      
      const containerCenter = containerRect.width / 2;
      const cardOffsetLeft = targetRect.left - containerRect.left;
      const cardCenter = cardOffsetLeft + (targetRect.width / 2);
      const scrollOffset = cardCenter - containerCenter;
      
      container.scrollTo({
        left: container.scrollLeft + scrollOffset,
        behavior: 'smooth'
      });
      
      setTimeout(() => setIsScrolling(false), 500);
    }
  }, [sortedEvents.length]);

  // Toggle expanded state
  const toggleExpanded = useCallback((index: number) => {
    setExpandedEvents(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (activeEvent === null) return;
      
      if (e.key === 'ArrowRight' && activeEvent < sortedEvents.length - 1) {
        scrollToEvent(activeEvent + 1);
      } else if (e.key === 'ArrowLeft' && activeEvent > 0) {
        scrollToEvent(activeEvent - 1);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [activeEvent, scrollToEvent, sortedEvents.length]);

  // Scroll event listener
  useEffect(() => {
    if (!containerRef.current || !isMounted) return;
    
    let scrollTimeout: NodeJS.Timeout;
    const throttleScroll = () => {
      if (!scrollTimeout) {
        scrollTimeout = setTimeout(() => {
          handleScroll();
          scrollTimeout = undefined as unknown as NodeJS.Timeout;
        }, 100);
      }
    };
    
    containerRef.current.addEventListener('scroll', throttleScroll, { passive: true });
    
    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener('scroll', throttleScroll);
      }
      if (scrollTimeout) clearTimeout(scrollTimeout);
    };
  }, [isMounted, handleScroll]);

  if (!isMounted) {
    return <LoadingSpinner />;
  }

  const timeSpan = sortedEvents.length > 0 ? sortedEvents[sortedEvents.length - 1].year - sortedEvents[0].year : 0;

  return (
    <div className="w-full my-20 relative" ref={inViewRef}>
      {/* Enhanced scientific background with dynamic patterns */}
      <div className="absolute inset-0 overflow-hidden rounded-3xl">
        {/* Base gradient with scientific theme */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50/90 via-blue-50/80 via-indigo-50/70 to-purple-50/60"></div>
        
        {/* Animated scientific particles */}
        <motion.div 
          className="absolute -bottom-1/3 -right-1/4 w-full h-full bg-gradient-to-tl from-blue-500/6 via-purple-500/4 to-transparent rounded-full blur-3xl"
          animate={{ 
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.5, 0.3],
            rotate: [0, 360]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        />
        <motion.div 
          className="absolute -top-1/3 -left-1/4 w-3/4 h-3/4 bg-gradient-to-br from-emerald-500/6 via-cyan-500/4 to-transparent rounded-full blur-3xl"
          animate={{ 
            scale: [1.1, 1, 1.1],
            opacity: [0.4, 0.2, 0.4],
            rotate: [360, 0]
          }}
          transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
        />
        
        {/* Scientific grid overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,rgba(59,130,246,0.02)_1px,transparent_1px),linear-gradient(to_bottom,rgba(59,130,246,0.02)_1px,transparent_1px)] bg-[size:4rem_4rem]"></div>
        
        {/* Hexagonal scientific pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(99,102,241,0.02)_2px,transparent_2px)] bg-[size:6rem_6rem]"></div>
        
        {/* Subtle molecular structure pattern */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 opacity-5">
          <div className="absolute inset-0 border border-blue-300 rounded-full"></div>
          <div className="absolute inset-8 border border-purple-300 rounded-full"></div>
          <div className="absolute inset-16 border border-emerald-300 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 w-4 h-4 bg-blue-400 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10 p-8">
        {/* Enhanced scientific header */}
        <div className="text-center mb-12">
          {/* Animated timeline badge */}
          <motion.div 
            className="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-blue-100/80 to-indigo-100/80 backdrop-blur-sm text-blue-700 text-lg font-medium mb-6 border border-blue-200/50 shadow-lg"
            animate={{ 
              scale: [1, 1.02, 1],
              boxShadow: ['0 4px 20px rgba(59, 130, 246, 0.1)', '0 8px 30px rgba(59, 130, 246, 0.2)', '0 4px 20px rgba(59, 130, 246, 0.1)']
            }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            >
              <Calendar size={24} />
            </motion.div>
            <span className="font-mono font-bold">
              {sortedEvents.length > 0 && `${sortedEvents[0].year} — ${sortedEvents[sortedEvents.length - 1].year}`}
            </span>
            <motion.div
              animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-2 h-2 bg-blue-500 rounded-full"
            />
          </motion.div>
          
          {/* Main title with gradient text */}
          <motion.h2 
            className="text-5xl md:text-6xl font-bold tracking-tight font-mono mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600 bg-clip-text text-transparent leading-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            Evolution of Computational Methods
          </motion.h2>
          
          {/* Enhanced description */}
          <motion.p 
            className="text-xl text-gray-600 max-w-5xl mx-auto leading-relaxed font-mono mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            Journey through <span className="font-bold text-blue-600">{timeSpan} years</span> of computational breakthroughs that transformed how we understand and interact with the world. Each milestone represents a quantum leap in our ability to solve complex problems across all disciplines.
          </motion.p>

          {/* Scientific metrics overview */}
          <motion.div 
            className="flex items-center justify-center gap-6 text-sm text-gray-600 font-mono mb-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <div className="flex items-center gap-2">
              <Zap size={16} className="text-blue-500" />
              <span><strong>{sortedEvents.length}</strong> Breakthroughs</span>
            </div>
            <div className="flex items-center gap-2">
              <GitBranch size={16} className="text-purple-500" />
              <span><strong>∞</strong> Possibilities</span>
            </div>
            <div className="flex items-center gap-2">
              <Target size={16} className="text-emerald-500" />
              <span><strong>All</strong> Disciplines</span>
            </div>
          </motion.div>

          {/* Navigation hint */}
          <motion.div 
            className="flex items-center justify-center gap-2 text-sm text-gray-500 font-mono"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <Info size={16} />
            <span>Use ← → keys or scroll • Click cards to explore • Tap "Source" for research links</span>
          </motion.div>
        </div>

        {/* Enhanced statistics grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          <motion.div 
            className="bg-white/90 backdrop-blur-sm rounded-2xl border border-blue-100 p-6 text-center relative overflow-hidden group"
            whileHover={{ y: -4, boxShadow: '0 20px 40px rgba(59, 130, 246, 0.15)' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-transparent group-hover:from-blue-100/50 transition-colors"></div>
            <div className="relative z-10">
              <div className="text-3xl font-bold text-blue-600 font-mono mb-2">{sortedEvents.length}</div>
              <div className="text-sm text-gray-600 font-mono">Major Milestones</div>
              <Lightbulb className="w-5 h-5 text-blue-400 mx-auto mt-2" />
            </div>
          </motion.div>
          
          <motion.div 
            className="bg-white/90 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6 text-center relative overflow-hidden group"
            whileHover={{ y: -4, boxShadow: '0 20px 40px rgba(16, 185, 129, 0.15)' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 to-transparent group-hover:from-emerald-100/50 transition-colors"></div>
            <div className="relative z-10">
              <div className="text-3xl font-bold text-emerald-600 font-mono mb-2">{timeSpan}</div>
              <div className="text-sm text-gray-600 font-mono">Years of Progress</div>
              <Clock className="w-5 h-5 text-emerald-400 mx-auto mt-2" />
            </div>
          </motion.div>
          
          <motion.div 
            className="bg-white/90 backdrop-blur-sm rounded-2xl border border-purple-100 p-6 text-center relative overflow-hidden group"
            whileHover={{ y: -4, boxShadow: '0 20px 40px rgba(139, 92, 246, 0.15)' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-transparent group-hover:from-purple-100/50 transition-colors"></div>
            <div className="relative z-10">
              <div className="text-3xl font-bold text-purple-600 font-mono mb-2">∞</div>
              <div className="text-sm text-gray-600 font-mono">Possibilities Created</div>
              <Brain className="w-5 h-5 text-purple-400 mx-auto mt-2" />
            </div>
          </motion.div>
          
          <motion.div 
            className="bg-white/90 backdrop-blur-sm rounded-2xl border border-amber-100 p-6 text-center relative overflow-hidden group"
            whileHover={{ y: -4, boxShadow: '0 20px 40px rgba(245, 158, 11, 0.15)' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-amber-50/50 to-transparent group-hover:from-amber-100/50 transition-colors"></div>
            <div className="relative z-10">
              <div className="text-3xl font-bold text-amber-600 font-mono mb-2">🔬</div>
              <div className="text-sm text-gray-600 font-mono">Interactive Research</div>
              <Link2 className="w-5 h-5 text-amber-400 mx-auto mt-2" />
            </div>
          </motion.div>
        </div>

        {/* Enhanced introduction card */}
        <AnimatePresence>
          {showIntro && (
            <motion.div 
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -30, scale: 0.95 }}
              transition={{ duration: 0.5 }}
              className="bg-white/95 backdrop-blur-sm rounded-3xl border border-gray-200/50 shadow-xl p-8 mb-12 relative overflow-hidden"
            >
              {/* Scientific background pattern */}
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_left,rgba(99,102,241,0.05)_0%,transparent_50%),radial-gradient(circle_at_bottom_right,rgba(16,185,129,0.05)_0%,transparent_50%)]"></div>
              
              <button 
                onClick={() => setShowIntro(false)}
                className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 text-gray-500 transition-colors z-10"
              >
                ✕
              </button>

              <div className="flex items-start gap-6 relative z-10">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-emerald-500 rounded-2xl flex items-center justify-center relative overflow-hidden">
                    <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.2)_0%,transparent_70%)]"></div>
                    <Brain size={32} className="text-white relative z-10" />
                  </div>
                </div>
                
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold font-mono mb-3 flex items-center gap-3">
                    <Lightbulb size={24} className="text-amber-500" />
                    The Computational Revolution
                  </h3>
                  <p className="text-gray-600 leading-relaxed font-mono text-base mb-6">
                    From breaking enemy codes in WWII to predicting protein structures, computational methods have fundamentally transformed how we approach complex problems. Each breakthrough opened entire new frontiers of possibility, creating an exponential growth in our ability to understand and shape the world.
                  </p>
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 text-center relative overflow-hidden">
                      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-blue-600"></div>
                      <div className="text-lg font-bold text-blue-700 font-mono">{Math.floor(timeSpan / 10)}</div>
                      <div className="text-xs text-blue-600 font-mono font-medium">Decades</div>
                    </div>
                    <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-xl p-4 border border-emerald-200 text-center relative overflow-hidden">
                      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-400 to-emerald-600"></div>
                      <div className="text-lg font-bold text-emerald-700 font-mono">All</div>
                      <div className="text-xs text-emerald-600 font-mono font-medium">Fields</div>
                    </div>
                    <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200 text-center relative overflow-hidden">
                      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-400 to-purple-600"></div>
                      <div className="text-lg font-bold text-purple-700 font-mono">∞</div>
                      <div className="text-xs text-purple-600 font-mono font-medium">Growth</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main timeline section */}
        <div className="relative">
          {/* Enhanced timeline background line */}
          <div className="hidden md:block absolute top-24 left-8 right-8 h-2 bg-gradient-to-r from-blue-500 via-purple-500 via-emerald-500 to-amber-500 rounded-full opacity-20 shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-400 via-emerald-400 to-amber-400 rounded-full animate-pulse"></div>
          </div>
          
          {/* Scrollable timeline container */}
          <div
            ref={containerRef}
            className="flex overflow-x-auto pb-12 scrollbar-hide space-x-8 px-6 relative z-10"
            style={{ scrollSnapType: 'x mandatory' }}
          >
            {sortedEvents.map((event, index) => (
              <EventCard
                key={index}
                event={event}
                index={index}
                isActive={activeEvent === index}
                isExpanded={expandedEvents.has(index)}
                onClick={() => scrollToEvent(index)}
                onToggleExpanded={() => toggleExpanded(index)}
              />
            ))}
          </div>

          {/* Enhanced navigation controls */}
          <div className="flex justify-between items-center mt-8">
            <motion.button
              onClick={() => activeEvent !== null && activeEvent > 0 && scrollToEvent(activeEvent - 1)}
              disabled={activeEvent === 0 || activeEvent === null}
              className="p-4 rounded-2xl bg-white/90 backdrop-blur-sm shadow-xl border border-gray-200 disabled:opacity-30 disabled:cursor-not-allowed text-gray-700 font-bold text-lg relative overflow-hidden group"
              whileHover={activeEvent !== 0 ? { scale: 1.05, boxShadow: '0 10px 30px rgba(0,0,0,0.1)' } : {}}
              whileTap={activeEvent !== 0 ? { scale: 0.95 } : {}}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1 }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-100/50 to-purple-100/50 opacity-0 group-hover:opacity-100 transition-opacity"></div>
              <span className="relative z-10">←</span>
            </motion.button>

            {/* Enhanced timeline dots */}
            <div className="flex items-center gap-2 overflow-x-auto max-w-2xl mx-8 p-4 bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 shadow-lg">
              {sortedEvents.map((event, index) => {
                const decade = Math.floor(event.year / 10) * 10;
                const isActive = activeEvent === index;
                return (
                  <motion.button
                    key={index}
                    onClick={() => scrollToEvent(index)}
                    className={`relative h-4 w-4 rounded-full transition-all duration-300 ${
                      isActive 
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg scale-125' 
                        : 'bg-gray-300 hover:bg-gray-400 hover:scale-110'
                    }`}
                    whileHover={{ scale: isActive ? 1.25 : 1.2 }}
                    whileTap={{ scale: 0.9 }}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: isActive ? 1.25 : 1 }}
                    transition={{ delay: index * 0.02 + 1.2 }}
                  >
                    {isActive && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
                        animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    )}
                  </motion.button>
                );
              })}
            </div>

            <motion.button
              onClick={() => activeEvent !== null && activeEvent < sortedEvents.length - 1 && scrollToEvent(activeEvent + 1)}
              disabled={activeEvent === sortedEvents.length - 1 || activeEvent === null}
              className="p-4 rounded-2xl bg-white/90 backdrop-blur-sm shadow-xl border border-gray-200 disabled:opacity-30 disabled:cursor-not-allowed text-gray-700 font-bold text-lg relative overflow-hidden group"
              whileHover={activeEvent !== sortedEvents.length - 1 ? { scale: 1.05, boxShadow: '0 10px 30px rgba(0,0,0,0.1)' } : {}}
              whileTap={activeEvent !== sortedEvents.length - 1 ? { scale: 0.95 } : {}}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1 }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-100/50 to-emerald-100/50 opacity-0 group-hover:opacity-100 transition-opacity"></div>
              <span className="relative z-10">→</span>
            </motion.button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComputationalTimeline; 