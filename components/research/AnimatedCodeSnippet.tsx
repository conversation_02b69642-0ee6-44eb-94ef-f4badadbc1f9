'use client';

import React, { useState } from 'react';
import { TypeAnimation } from 'react-type-animation';
import { motion } from 'framer-motion';

interface AnimatedCodeSnippetProps {
  title: string;
  code: string;
  language?: string;
  typingSpeed?: number;
  startDelay?: number;
  tabs?: { 
    label: string; 
    code: string;
    language?: string;
  }[];
}

const AnimatedCodeSnippet: React.FC<AnimatedCodeSnippetProps> = ({
  title,
  code,
  language = 'javascript',
  typingSpeed = 2,
  startDelay = 100,
  tabs = []
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [isTypingComplete, setIsTypingComplete] = useState(false);
  const [showInstantly, setShowInstantly] = useState(false);
  const [copied, setCopied] = useState(false);
  
  const activeCode = tabs.length > 0 ? tabs[activeTab].code : code;
  const activeLanguage = tabs.length > 0 ? (tabs[activeTab].language || language) : language;

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(activeCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const getLanguageClass = (lang: string) => {
    const languageMap: Record<string, string> = {
      'javascript': 'text-yellow-600',
      'python': 'text-blue-600',
      'java': 'text-orange-600',
      'cpp': 'text-blue-800',
      'csharp': 'text-purple-600',
      'ruby': 'text-red-600',
      'typescript': 'text-blue-600',
      'r': 'text-blue-500',
      'julia': 'text-purple-500',
      'pseudocode': 'text-gray-700'
    };
    
    return languageMap[lang] || 'text-gray-700';
  };

  return (
    <div className="w-full rounded-lg border shadow-sm bg-white/80 backdrop-blur-sm overflow-hidden">
      <div className="border-b px-4 py-3 flex justify-between items-center bg-gray-50">
        <span className="font-mono text-sm font-medium">{title}</span>
        <div className="flex items-center space-x-4">
          {!showInstantly && !isTypingComplete && (
            <button
              onClick={() => {
                setShowInstantly(true);
                setIsTypingComplete(true);
              }}
              className="text-xs px-2 py-1 bg-primary/10 hover:bg-primary/20 text-primary rounded transition-colors"
            >
              Skip Animation
            </button>
          )}
          <div className="flex space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-400"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
            <div className="w-3 h-3 rounded-full bg-green-400"></div>
          </div>
        </div>
      </div>
      
      {tabs.length > 0 && (
        <div className="flex border-b">
          {tabs.map((tab, index) => (
            <button
              key={index}
              className={`px-4 py-2 text-sm font-mono transition-colors ${
                activeTab === index 
                  ? 'border-b-2 border-primary text-primary font-medium' 
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => {
                setActiveTab(index);
                setIsTypingComplete(false);
                setShowInstantly(false);
                setCopied(false);
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>
      )}
      
      <div className="p-4 relative font-mono text-sm overflow-x-auto" style={{ minHeight: '200px' }}>
        <div className={`absolute top-0 right-3 py-1 px-2 text-xs font-medium ${getLanguageClass(activeLanguage)}`}>
          {activeLanguage.toUpperCase()}
        </div>
        
        {showInstantly ? (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
            style={{ 
              whiteSpace: 'pre-wrap', 
              fontFamily: 'monospace', 
              fontSize: '0.875rem',
              lineHeight: '1.5',
              maxWidth: '100%',
              overflowX: 'auto',
              padding: '1rem',
              marginTop: '0.5rem'
            }}
            className="bg-gray-50 rounded-md"
            onAnimationComplete={() => setIsTypingComplete(true)}
          >
            {activeCode}
          </motion.div>
        ) : (
          <TypeAnimation
            key={`${activeTab}-${activeCode.slice(0, 50)}`} // Key to force re-render on tab change
            sequence={[
              startDelay,
              activeCode,
              () => setIsTypingComplete(true)
            ]}
            wrapper="div"
            cursor={true}
            repeat={0}
            speed={{ type: 'keyStrokeDelayInMs', value: typingSpeed }}
            style={{ 
              whiteSpace: 'pre-wrap', 
              fontFamily: 'monospace', 
              fontSize: '0.875rem',
              lineHeight: '1.5',
              maxWidth: '100%',
              overflowX: 'auto',
              padding: '1rem',
              marginTop: '0.5rem'
            }}
            className="bg-gray-50 rounded-md"
          />
        )}
        
        {isTypingComplete && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="mt-4 flex justify-end"
          >
            <button 
              onClick={copyToClipboard}
              className="text-xs text-primary underline hover:text-primary/80 transition-colors"
            >
              {copied ? 'Copied!' : 'Copy to clipboard'}
            </button>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default AnimatedCodeSnippet; 