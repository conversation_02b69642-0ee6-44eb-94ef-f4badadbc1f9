'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

interface Author {
  name: string;
  affiliation?: string;
  isPI?: boolean;
}

interface Publication {
  id: string;
  title: string;
  authors: Author[];
  journal?: string;
  conference?: string;
  year: number;
  doi?: string;
  abstract: string;
  keywords: string[];
  image?: string;
  preprint?: string;
}

interface PublicationFeedProps {
  publications: Publication[];
}

type CitationFormat = 'apa' | 'mla' | 'chicago' | 'harvard' | 'bibtex';

const PublicationFeed: React.FC<PublicationFeedProps> = ({ publications }) => {
  const [citationFormat, setCitationFormat] = useState<CitationFormat>('apa');
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  const toggleExpand = (id: string) => {
    setExpandedId(expandedId === id ? null : id);
  };

  const formatCitation = (pub: Publication, format: CitationFormat): string => {
    const authorString = pub.authors.map(author => author.name).join(', ');
    
    switch (format) {
      case 'apa':
        return `${authorString}. (${pub.year}). ${pub.title}. ${pub.journal || pub.conference}. ${pub.doi ? `https://doi.org/${pub.doi}` : ''}`;
      
      case 'mla':
        return `${authorString}. "${pub.title}." ${pub.journal || pub.conference}, ${pub.year}. ${pub.doi ? `DOI: ${pub.doi}` : ''}`;
      
      case 'chicago':
        return `${authorString}. "${pub.title}." ${pub.journal || pub.conference} (${pub.year}). ${pub.doi ? `https://doi.org/${pub.doi}` : ''}`;
      
      case 'harvard':
        return `${authorString} (${pub.year}). ${pub.title}. ${pub.journal || pub.conference}. ${pub.doi ? `Available at: https://doi.org/${pub.doi}` : ''}`;
      
      case 'bibtex':
        return `@article{${pub.id},
  title={${pub.title}},
  author={${pub.authors.map(author => author.name).join(' and ')}},
  journal={${pub.journal || pub.conference || ''}},
  year={${pub.year}},
  doi={${pub.doi || ''}}
}`;
      
      default:
        return `${pub.authors[0]?.name} et al. (${pub.year}). ${pub.title}`;
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add toast notification here
  };

  return (
    <div ref={ref} className="w-full mb-12">
      <h3 className="text-xl font-mono mb-4 text-center">Recent Publications</h3>
      <p className="text-muted-foreground text-sm text-center mb-6 max-w-2xl mx-auto">
        Explore our latest research publications and computational findings
      </p>
      
      {/* Citation format selector */}
      <div className="flex justify-center mb-8">
        <div className="inline-flex rounded-md bg-white/80 backdrop-blur-sm border shadow-sm overflow-hidden">
          {(['apa', 'mla', 'chicago', 'harvard', 'bibtex'] as CitationFormat[]).map((format) => (
            <button
              key={format}
              onClick={() => setCitationFormat(format)}
              className={`px-3 py-2 text-xs font-mono uppercase transition-colors ${
                citationFormat === format
                  ? 'bg-primary text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              {format}
            </button>
          ))}
        </div>
      </div>
      
      <motion.div
        className="space-y-6"
        variants={container}
        initial="hidden"
        animate={inView ? "show" : "hidden"}
      >
        {publications.map((pub) => (
          <motion.div
            key={pub.id}
            className="bg-white/80 backdrop-blur-sm rounded-lg border shadow-sm overflow-hidden"
            variants={item}
          >
            <div className="flex flex-col md:flex-row">
              {/* Publication image/preview (if available) */}
              {pub.image && (
                <div className="md:w-1/4 bg-gray-100 relative">
                  <img
                    src={pub.image}
                    alt={`Visual abstract for ${pub.title}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              
              {/* Publication details */}
              <div className={`p-6 flex-1 ${pub.image ? 'md:w-3/4' : 'w-full'}`}>
                <div className="mb-2 flex flex-wrap gap-2">
                  {pub.keywords.map((keyword, idx) => (
                    <span
                      key={idx}
                      className="text-xs px-2 py-1 rounded-full bg-primary/10 text-primary font-medium"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
                
                <h4 className="text-lg font-mono font-medium mb-2">{pub.title}</h4>
                
                <div className="text-sm text-muted-foreground mb-4">
                  <span className="font-medium">Authors: <AUTHORS>
                  {pub.authors.map((author, idx) => (
                    <span key={idx}>
                      {author.name}
                      {author.isPI && <sup className="text-primary">*</sup>}
                      {idx < pub.authors.length - 1 && ', '}
                    </span>
                  ))}
                </div>
                
                <div className="text-sm text-muted-foreground mb-4">
                  <span className="font-medium">Published in:</span>{' '}
                  {pub.journal || pub.conference}, {pub.year}
                </div>
                
                {/* Abstract - shown when expanded */}
                <div className={`overflow-hidden transition-all duration-300 ${
                  expandedId === pub.id ? 'max-h-96' : 'max-h-0'
                }`}>
                  <div className="bg-gray-50 p-4 rounded-md mb-4 text-sm">
                    <h5 className="font-medium mb-2">Abstract</h5>
                    <p>{pub.abstract}</p>
                  </div>
                </div>
                
                <div className="flex flex-wrap items-center gap-3 mt-4">
                  <button
                    onClick={() => toggleExpand(pub.id)}
                    className="text-xs inline-flex items-center font-mono text-primary hover:text-primary/80"
                  >
                    {expandedId === pub.id ? 'Hide' : 'Show'} Abstract
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className={`ml-1 transition-transform ${expandedId === pub.id ? 'rotate-180' : ''}`}
                    >
                      <path d="m6 9 6 6 6-6" />
                    </svg>
                  </button>
                  
                  {pub.doi && (
                    <a
                      href={`https://doi.org/${pub.doi}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs inline-flex items-center font-mono text-primary hover:text-primary/80"
                    >
                      DOI
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="ml-1"
                      >
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                        <polyline points="15 3 21 3 21 9" />
                        <line x1="10" y1="14" x2="21" y2="3" />
                      </svg>
                    </a>
                  )}
                  
                  {pub.preprint && (
                    <a
                      href={pub.preprint}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs inline-flex items-center font-mono text-primary hover:text-primary/80"
                    >
                      Preprint
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="ml-1"
                      >
                        <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" />
                        <polyline points="16 6 12 2 8 6" />
                        <line x1="12" y1="2" x2="12" y2="15" />
                      </svg>
                    </a>
                  )}
                </div>
              </div>
            </div>
            
            {/* Citation footer */}
            <div className="px-6 py-3 bg-gray-50 border-t">
              <div className="flex flex-wrap items-center justify-between">
                <div className="text-xs text-muted-foreground font-mono">
                  <span className="font-medium">Cite as:</span>
                </div>
                <button
                  onClick={() => copyToClipboard(formatCitation(pub, citationFormat))}
                  className="text-xs inline-flex items-center font-mono text-primary hover:text-primary/80"
                >
                  Copy Citation
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="ml-1"
                  >
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" />
                  </svg>
                </button>
              </div>
              <div className="mt-2 text-xs p-2 bg-gray-100 rounded font-mono overflow-x-auto whitespace-pre-wrap">
                {formatCitation(pub, citationFormat)}
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default PublicationFeed; 