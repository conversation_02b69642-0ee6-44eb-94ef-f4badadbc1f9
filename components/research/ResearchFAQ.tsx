'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface FAQItem {
  question: string;
  answer: React.ReactNode;
  category?: string;
}

interface ResearchFAQProps {
  items: FAQItem[];
}

const ResearchFAQ: React.FC<ResearchFAQProps> = ({ items }) => {
  const [openItems, setOpenItems] = useState<string[]>([]);
  const [filter, setFilter] = useState<string | null>(null);
  
  const toggleItem = (question: string) => {
    setOpenItems(prev => 
      prev.includes(question)
        ? prev.filter(item => item !== question)
        : [...prev, question]
    );
  };
  
  // Get unique categories
  const categories = Array.from(
    new Set(items.map(item => item.category || 'General'))
  );
  
  // Filter items
  const filteredItems = filter
    ? items.filter(item => (item.category || 'General') === filter)
    : items;

  return (
    <div className="w-full mb-12">
      <h3 className="text-xl font-mono mb-4 text-center">Computational Research FAQ</h3>
      <p className="text-muted-foreground text-sm text-center mb-6 max-w-2xl mx-auto">
        Common questions about computational approaches in interdisciplinary research
      </p>
      
      {/* Category filters */}
      <div className="flex flex-wrap justify-center gap-2 mb-6">
        <button
          className={`px-3 py-1 text-sm rounded-full font-mono transition-colors ${
            filter === null
              ? 'bg-primary text-white'
              : 'bg-gray-100 hover:bg-gray-200'
          }`}
          onClick={() => setFilter(null)}
        >
          All
        </button>
        {categories.map((category) => (
          <button
            key={category}
            className={`px-3 py-1 text-sm rounded-full font-mono transition-colors ${
              filter === category
                ? 'bg-primary text-white'
                : 'bg-gray-100 hover:bg-gray-200'
            }`}
            onClick={() => setFilter(category)}
          >
            {category}
          </button>
        ))}
      </div>
      
      <div className="space-y-4 max-w-4xl mx-auto">
        {filteredItems.map((item, index) => (
          <div
            key={index}
            className="bg-white/80 backdrop-blur-sm rounded-lg border shadow-sm overflow-hidden"
          >
            <button
              className="w-full px-6 py-4 text-left font-mono flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary/20 transition-colors"
              onClick={() => toggleItem(item.question)}
            >
              <span className="font-medium">{item.question}</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={`transform transition-transform ${openItems.includes(item.question) ? 'rotate-180' : ''}`}
              >
                <path d="m6 9 6 6 6-6" />
              </svg>
            </button>
            
            <AnimatePresence>
              {openItems.includes(item.question) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="overflow-hidden"
                >
                  <div className="px-6 pb-4 bg-gray-50/80 border-t">
                    {typeof item.answer === 'string' ? (
                      <p className="py-4 text-muted-foreground">{item.answer}</p>
                    ) : (
                      <div className="py-4">{item.answer}</div>
                    )}
                    
                    {item.category && (
                      <div className="mt-2 flex justify-end">
                        <span className="text-xs px-2 py-1 rounded bg-primary/10 text-primary">
                          {item.category}
                        </span>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ResearchFAQ; 