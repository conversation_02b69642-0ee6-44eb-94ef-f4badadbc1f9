'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

interface ResearchProject {
  id: string;
  title: string;
  description: string;
  image: string;
  tags: string[];
  researchAreas: string[];
  investigators?: string[];
}

interface ResearchShowcaseProps {
  projects: ResearchProject[];
}

const ResearchShowcase: React.FC<ResearchShowcaseProps> = ({ projects }) => {
  const [current, setCurrent] = useState(0);
  const [direction, setDirection] = useState(0);
  const [isMounted, setIsMounted] = useState(false);
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: false
  });

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!inView) return;
    
    // Auto-advance carousel
    const interval = setInterval(() => {
      setCurrent((prev) => {
        setDirection(1);
        return (prev + 1) % projects.length;
      });
    }, 5000);
    
    return () => clearInterval(interval);
  }, [projects.length, inView]);

  const navigate = useCallback((newIndex: number) => {
    setDirection(newIndex > current ? 1 : -1);
    setCurrent(newIndex);
  }, [current]);

  const next = useCallback(() => {
    setDirection(1);
    setCurrent((prev) => (prev + 1) % projects.length);
  }, [projects.length]);

  const prev = useCallback(() => {
    setDirection(-1);
    setCurrent((prev) => (prev - 1 + projects.length) % projects.length);
  }, [projects.length]);

  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 1000 : -1000,
      opacity: 0
    })
  };

  if (!isMounted || projects.length === 0) {
    return (
      <div className="h-64 flex justify-center items-center bg-white/80 backdrop-blur-sm border rounded-lg shadow-sm">
        <p className="text-muted-foreground">Loading research projects...</p>
      </div>
    );
  }

  return (
    <div ref={ref} className="w-full relative mb-12">
      <h3 className="text-xl font-mono mb-4 text-center">Featured Research Projects</h3>
      <p className="text-muted-foreground text-sm text-center mb-6 max-w-2xl mx-auto">
        Explore our innovative research projects applying computational methods to solve complex problems.
      </p>
      
      <div className="relative h-[500px] w-full overflow-hidden rounded-lg border shadow-sm bg-white/80 backdrop-blur-sm">
        <AnimatePresence initial={false} custom={direction} mode="wait">
          <motion.div
            key={current}
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              x: { type: "spring", stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 }
            }}
            className="absolute top-0 left-0 w-full h-full"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 h-full">
              <div className="p-6 flex flex-col justify-center">
                <div className="mb-4">
                  {projects[current].tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-block mr-2 mb-2 px-2 py-1 text-xs font-medium rounded bg-primary/10 text-primary"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                <h4 className="text-xl font-mono mb-4">{projects[current].title}</h4>
                <p className="text-muted-foreground mb-6">{projects[current].description}</p>
                
                {projects[current].investigators && (
                  <div className="mb-4">
                    <h5 className="text-sm font-medium mb-2">Lead Investigators</h5>
                    <div className="flex flex-wrap gap-2">
                      {projects[current].investigators.map((name, index) => (
                        <span
                          key={index}
                          className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100"
                        >
                          {name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                <button className="mt-auto px-4 py-2 bg-primary text-white rounded-md font-mono hover:bg-primary/90 transition-colors w-fit">
                  Learn More
                </button>
              </div>
              
              <div className="relative h-full bg-gray-100 overflow-hidden">
                <img
                  src={projects[current].image}
                  alt={projects[current].title}
                  className="absolute inset-0 w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="flex flex-wrap gap-2">
                    {projects[current].researchAreas.map((area, index) => (
                      <span
                        key={index}
                        className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-white/80 backdrop-blur-sm"
                      >
                        {area}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
        
        {/* Navigation arrows */}
        <button
          className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-white/80 backdrop-blur-sm shadow-sm hover:bg-white z-10"
          onClick={prev}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="m15 18-6-6 6-6"/>
          </svg>
        </button>
        
        <button
          className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-white/80 backdrop-blur-sm shadow-sm hover:bg-white z-10"
          onClick={next}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="m9 18 6-6-6-6"/>
          </svg>
        </button>
        
        {/* Dots indicator */}
        <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-10">
          {projects.map((_, index) => (
            <button
              key={index}
              className={`w-2 h-2 rounded-full transition-all ${
                current === index ? "bg-primary w-4" : "bg-gray-300 hover:bg-gray-400"
              }`}
              onClick={() => navigate(index)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ResearchShowcase; 