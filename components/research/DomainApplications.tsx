'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { ExternalLink, ChevronDown, ChevronUp, Users, Cpu, Award, Link as LinkIcon } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface Example {
  title: string;
  description: string;
  achievement: string;
  source: string;
  link: string;
}

interface Application {
  id: string;
  domain: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  examples: Example[];
  technologies: string[];
  institutions: string[];
}

interface DomainApplicationsProps {
  applications: Application[];
}

const DomainApplications: React.FC<DomainApplicationsProps> = ({ applications }) => {
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());

  const toggleCard = (id: string) => {
    const newExpanded = new Set(expandedCards);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedCards(newExpanded);
  };

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <div ref={ref} className="w-full mb-16">
      <div className="text-center mb-12">
        <Badge variant="outline" className="inline-flex items-center gap-2 px-4 py-2 mb-6 bg-white/80 backdrop-blur-sm border-primary/20">
          <Cpu className="h-4 w-4 text-primary" />
          Real-World Impact
        </Badge>
        <h3 className="text-3xl font-bold text-gray-900 mb-4 font-mono">Domain-Specific Applications</h3>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Discover how computational methods are revolutionizing research and solving complex challenges across diverse fields. 
          Each domain showcases breakthrough achievements with verifiable sources and measurable impact.
        </p>
      </div>
      
      <motion.div 
        className="grid md:grid-cols-2 xl:grid-cols-3 gap-8"
        variants={container}
        initial="hidden"
        animate={inView ? "show" : "hidden"}
      >
        {applications.map((app) => {
          const isExpanded = expandedCards.has(app.id);
          
          return (
            <motion.div
              key={app.id}
              variants={item}
              className="group"
            >
              <Card className="h-full hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden border-l-4" 
                    style={{ borderLeftColor: app.color.replace('bg-', '') === 'red-500' ? '#ef4444' : 
                                              app.color.replace('bg-', '') === 'green-500' ? '#22c55e' :
                                              app.color.replace('bg-', '') === 'blue-500' ? '#3b82f6' :
                                              app.color.replace('bg-', '') === 'yellow-500' ? '#eab308' :
                                              app.color.replace('bg-', '') === 'purple-500' ? '#a855f7' :
                                              app.color.replace('bg-', '') === 'indigo-500' ? '#6366f1' :
                                              app.color.replace('bg-', '') === 'amber-500' ? '#f59e0b' :
                                              app.color.replace('bg-', '') === 'cyan-500' ? '#06b6d4' : '#6b7280' }}>
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${app.color} shadow-lg`}>
                        <div className="text-white">
                          {app.icon}
                        </div>
                      </div>
                      <div>
                        <Badge variant="secondary" className="text-xs mb-2">{app.domain}</Badge>
                        <CardTitle className="text-lg font-semibold leading-tight">{app.title}</CardTitle>
                      </div>
                    </div>
                  </div>
                  <CardDescription className="text-sm text-muted-foreground mt-3">
                    {app.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0">
                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-primary">{app.examples.length}</div>
                      <div className="text-xs text-muted-foreground">Case Studies</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-primary">{app.institutions.length}+</div>
                      <div className="text-xs text-muted-foreground">Leading Orgs</div>
                    </div>
                  </div>

                  {/* Technologies Preview */}
                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Cpu className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Key Technologies</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {app.technologies.slice(0, 3).map((tech, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                      {app.technologies.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{app.technologies.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Expand/Collapse Button */}
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => toggleCard(app.id)}
                    className="w-full justify-between hover:bg-gray-50"
                  >
                    <span className="text-sm font-medium">
                      {isExpanded ? 'Show Less' : 'View Examples & Sources'}
                    </span>
                    {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>

                  {/* Expanded Content */}
                  <AnimatePresence>
                    {isExpanded && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="pt-4 space-y-6">
                          {/* Real-World Examples */}
                          <div>
                            <h4 className="font-semibold text-sm mb-3 flex items-center gap-2">
                              <Award className="h-4 w-4 text-primary" />
                              Breakthrough Examples
                            </h4>
                            <div className="space-y-4">
                              {app.examples.map((example, index) => (
                                <div key={index} className="border-l-2 border-gray-200 pl-4">
                                  <h5 className="font-medium text-sm text-gray-900 mb-1">
                                    {example.title}
                                  </h5>
                                  <p className="text-xs text-muted-foreground mb-2">
                                    {example.description}
                                  </p>
                                  <div className="flex items-center justify-between">
                                    <div className="text-xs text-green-600 font-medium">
                                      ✓ {example.achievement}
                                    </div>
                                    <a 
                                      href={example.link} 
                                      target="_blank" 
                                      rel="noopener noreferrer"
                                      className="text-xs text-primary hover:underline flex items-center gap-1"
                                    >
                                      {example.source}
                                      <ExternalLink className="h-3 w-3" />
                                    </a>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* All Technologies */}
                          <div>
                            <h4 className="font-semibold text-sm mb-3 flex items-center gap-2">
                              <Cpu className="h-4 w-4 text-primary" />
                              Technologies & Methods
                            </h4>
                            <div className="flex flex-wrap gap-1">
                              {app.technologies.map((tech, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {tech}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* Leading Institutions */}
                          <div>
                            <h4 className="font-semibold text-sm mb-3 flex items-center gap-2">
                              <Users className="h-4 w-4 text-primary" />
                              Leading Organizations
                            </h4>
                            <div className="grid grid-cols-1 gap-1">
                              {app.institutions.map((institution, index) => (
                                <div key={index} className="text-xs text-muted-foreground">
                                  • {institution}
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </motion.div>

      {/* Summary Statistics */}
      <motion.div 
        className="mt-12 text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
          <div className="p-4">
            <div className="text-3xl font-bold text-primary mb-2">{applications.length}</div>
            <div className="text-sm text-muted-foreground">Application Domains</div>
          </div>
          <div className="p-4">
            <div className="text-3xl font-bold text-primary mb-2">
              {applications.reduce((acc, app) => acc + app.examples.length, 0)}
            </div>
            <div className="text-sm text-muted-foreground">Real-World Examples</div>
          </div>
          <div className="p-4">
            <div className="text-3xl font-bold text-primary mb-2">
              {new Set(applications.flatMap(app => app.technologies)).size}
            </div>
            <div className="text-sm text-muted-foreground">Unique Technologies</div>
          </div>
          <div className="p-4">
            <div className="text-3xl font-bold text-primary mb-2">
              {new Set(applications.flatMap(app => app.institutions)).size}
            </div>
            <div className="text-sm text-muted-foreground">Partner Organizations</div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default DomainApplications; 