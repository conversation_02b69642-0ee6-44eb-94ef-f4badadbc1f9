'use client'

import { useEffect } from 'react'

interface PrintStylesProps {
  selector?: string
}

export function PrintStyles({ selector = '#print-content' }: PrintStylesProps) {
  useEffect(() => {
    // Add the print styles when the component mounts
    const style = document.createElement('style')
    style.id = 'print-styles'
    style.innerHTML = `
      @media print {
        /* Hide non-essential elements */
        header, footer, nav, button, .print-hide {
          display: none !important;
        }
        
        /* Ensure the content takes the full page */
        ${selector} {
          width: 100% !important;
          margin: 0 !important;
          padding: 0 !important;
          color: black !important;
          background: white !important;
        }
        
        /* Typography for print */
        ${selector} h1 {
          font-size: 24pt !important;
          margin-bottom: 0.5cm !important;
        }
        
        ${selector} h2 {
          font-size: 18pt !important;
          margin-top: 0.5cm !important;
          margin-bottom: 0.3cm !important;
        }
        
        ${selector} p, ${selector} li {
          font-size: 12pt !important;
          line-height: 1.5 !important;
        }
        
        /* Links display with their URLs */
        ${selector} a::after {
          content: " (" attr(href) ")" !important;
          font-size: 10pt !important;
          font-style: italic !important;
        }
        
        /* Force page breaks where needed */
        .page-break-before {
          page-break-before: always !important;
        }
        
        .page-break-after {
          page-break-after: always !important;
        }
        
        /* Improve table display */
        table {
          border-collapse: collapse !important;
        }
        
        table, th, td {
          border: 1px solid #ddd !important;
        }
        
        th, td {
          padding: 8px !important;
        }
        
        /* Remove background colors and shadows */
        * {
          box-shadow: none !important;
          background-image: none !important;
        }
        
        /* Ensure proper image sizing */
        img {
          max-width: 100% !important;
          page-break-inside: avoid !important;
        }
      }
    `
    document.head.appendChild(style)
    
    // Clean up function to remove the styles when the component unmounts
    return () => {
      const styleElement = document.getElementById('print-styles')
      if (styleElement) {
        document.head.removeChild(styleElement)
      }
    }
  }, [selector])
  
  return null
} 