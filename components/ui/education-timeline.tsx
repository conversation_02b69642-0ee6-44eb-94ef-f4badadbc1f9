'use client'

import React, { useState } from 'react'
import { 
  GraduationCap, 
  Building, 
  Clock, 
  ChevronDown, 
  ChevronRight,
  CalendarDays,
  MapPin
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface Education {
  id: string
  degree: string
  institution: string
  year: number
  field?: string | null
}

interface EducationTimelineProps {
  education: Education[]
}

export function EducationTimeline({ education }: EducationTimelineProps) {
  const [expandedId, setExpandedId] = useState<string | null>(null)
  
  // Sort education by year (most recent first)
  const sortedEducation = [...education].sort((a, b) => b.year - a.year)
  
  // Generate random gradient for each institution to make it visually distinctive
  const getInstitutionGradient = (institution: string) => {
    const hash = institution.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
    
    // Create a set of predefined gradients
    const gradients = [
      'from-crimson/20 to-gold/20',
      'from-blue-300/20 to-indigo-400/20',
      'from-green-300/20 to-emerald-400/20',
      'from-amber-300/20 to-orange-400/20',
      'from-purple-300/20 to-fuchsia-400/20',
      'from-sky-300/20 to-cyan-400/20',
    ]
    
    return gradients[hash % gradients.length]
  }
  
  return (
    <div className="space-y-6">
      <div className="relative pl-6 border-l-2 border-gray-200">
        {sortedEducation.map((edu, index) => (
          <div 
            key={edu.id} 
            className={`mb-8 ${index !== sortedEducation.length - 1 ? 'relative' : ''}`}
          >
            {/* Timeline node */}
            <div className="absolute -left-[11px] h-5 w-5 rounded-full bg-white border-2 border-crimson flex items-center justify-center">
              <div className="h-2 w-2 rounded-full bg-crimson"></div>
            </div>
            
            {/* Timeline card */}
            <div 
              className={`
                relative ml-4 bg-white rounded-xl shadow-sm border 
                border-gray-100 overflow-hidden cursor-pointer transition-all duration-300
                hover:shadow-md hover:border-crimson/20
              `}
              onClick={() => setExpandedId(expandedId === edu.id ? null : edu.id)}
            >
              {/* Decorative top gradient bar */}
              <div className={`h-1 w-full bg-gradient-to-r ${getInstitutionGradient(edu.institution)}`}></div>
              
              <div className="p-5">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center mb-2">
                      <GraduationCap className="h-5 w-5 text-crimson mr-2" />
                      <h3 className="font-medium text-lg">{edu.degree}</h3>
                    </div>
                    
                    <div className="flex items-center text-gray-700 mb-1 text-sm">
                      <Building className="h-4 w-4 text-gray-400 mr-1.5" />
                      <span>{edu.institution}</span>
                    </div>
                    
                    <div className="flex items-center text-gray-600 text-sm">
                      <CalendarDays className="h-4 w-4 text-gray-400 mr-1.5" />
                      <span>Class of {edu.year}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Badge 
                      variant="outline" 
                      className="bg-crimson/5 text-crimson border-crimson/10"
                    >
                      {edu.year}
                    </Badge>
                    
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 ml-1 text-gray-400"
                      onClick={(e) => {
                        e.stopPropagation()
                        setExpandedId(expandedId === edu.id ? null : edu.id)
                      }}
                    >
                      {expandedId === edu.id ? (
                        <ChevronDown className="h-5 w-5" />
                      ) : (
                        <ChevronRight className="h-5 w-5" />
                      )}
                    </Button>
                  </div>
                </div>
                
                {/* Expandable content */}
                <AnimatePresence>
                  {expandedId === edu.id && edu.field && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <div className="pl-1">
                          <div className={`
                            p-3 rounded-lg bg-gradient-to-r ${getInstitutionGradient(edu.institution)}
                          `}>
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Field of Study:</h4>
                            <p className="text-gray-800">{edu.field}</p>
                          </div>
                          
                          {/* We could add more details here, like achievements, GPA, thesis, etc. */}
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>
        ))}
        
        {/* Decorative start and end elements */}
        <div className="absolute -top-4 -left-[7px] h-4 w-4 rounded-full bg-gradient-to-br from-crimson to-gold opacity-30"></div>
        <div className="absolute -bottom-4 -left-[7px] h-4 w-4 rounded-full bg-gradient-to-br from-crimson to-gold opacity-30"></div>
      </div>
    </div>
  )
} 