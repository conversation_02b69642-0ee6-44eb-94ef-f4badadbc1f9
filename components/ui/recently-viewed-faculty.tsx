'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { getRecentlyViewedFaculty, StoredFaculty } from "@/lib/faculty-utils"
import { Card, CardContent } from "@/components/ui/card"
import { History, Clock } from "lucide-react"
import { LazyImage } from "@/components/ui/lazy-image"
import { formatDistanceToNow } from 'date-fns'

interface RecentlyViewedFacultyProps {
  className?: string
  maxItems?: number
}

export function RecentlyViewedFaculty({
  className,
  maxItems = 4
}: RecentlyViewedFacultyProps) {
  const [recentFaculty, setRecentFaculty] = useState<StoredFaculty[]>([])
  
  useEffect(() => {
    // Get recently viewed faculty on client side
    const faculty = getRecentlyViewedFaculty()
    setRecentFaculty(faculty.slice(0, maxItems))
    
    // Setup event listener to update when favorites change
    const handleStorageChange = () => {
      const updatedFaculty = getRecentlyViewedFaculty()
      setRecentFaculty(updatedFaculty.slice(0, maxItems))
    }
    
    window.addEventListener('storage', handleStorageChange)
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [maxItems])
  
  if (recentFaculty.length === 0) {
    return null
  }
  
  return (
    <div className={className}>
      <div className="flex items-center gap-2 mb-3">
        <History className="h-4 w-4 text-primary" />
        <h2 className="text-base font-medium">Recently Viewed</h2>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
        {recentFaculty.map((faculty) => (
          <Link key={faculty.id} href={`/faculty/${faculty.id}`}>
            <Card className="h-full hover:shadow-sm transition-shadow">
              <div className="aspect-square w-full overflow-hidden bg-muted/50">
                <LazyImage
                  src={faculty.imageUrl}
                  alt={`${faculty.name}`}
                  aspectRatio="aspect-square"
                />
              </div>
              <CardContent className="p-2">
                <h3 className="text-xs font-medium truncate">{faculty.name}</h3>
                <p className="text-xs text-muted-foreground truncate mt-0.5">{faculty.title}</p>
                <div className="mt-1 flex items-center text-[10px] text-muted-foreground">
                  <Clock className="h-2.5 w-2.5 mr-1 inline" />
                  <span className="inline-block">
                    {formatDistanceToNow(new Date(faculty.timestamp), { addSuffix: true })}
                  </span>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  )
} 