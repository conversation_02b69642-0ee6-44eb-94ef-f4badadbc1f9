'use client'

import React from 'react'

interface SkipLinkProps {
  targetId?: string
  className?: string
  text?: string
}

export function SkipLink({
  targetId = 'main-content',
  className = '',
  text = 'Skip to main content'
}: SkipLinkProps) {
  return (
    <a
      href={`#${targetId}`}
      className={`
        sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 
        focus:px-4 focus:py-2 focus:bg-background focus:text-foreground 
        focus:outline-none focus:ring-2 focus:ring-crimson focus:ring-offset-2
        dark:focus:ring-gold dark:focus:bg-dark dark:focus:text-light
        ${className}
      `}
    >
      {text}
    </a>
  )
}
