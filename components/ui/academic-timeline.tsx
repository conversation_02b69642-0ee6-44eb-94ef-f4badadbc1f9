'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from "@/components/ui/card"
import { Briefcase, GraduationCap, Award } from "lucide-react"

export interface TimelineEvent {
  id: string
  year: string
  title: string
  description: string
  type: 'education' | 'position' | 'award'
}

interface AcademicTimelineProps {
  events: TimelineEvent[]
}

export function AcademicTimeline({ events }: AcademicTimelineProps) {
  // Sort events by year in descending order (most recent first)
  const sortedEvents = [...events].sort((a, b) => {
    // Extract starting year if there's a range (e.g., "2018-2020" -> "2018")
    const yearA = parseInt(a.year.split('-')[0]);
    const yearB = parseInt(b.year.split('-')[0]);
    return yearB - yearA;
  });

  const getIconForType = (type: string) => {
    switch (type) {
      case 'education':
        return <GraduationCap className="h-5 w-5" />;
      case 'position':
        return <Briefcase className="h-5 w-5" />;
      case 'award':
        return <Award className="h-5 w-5" />;
      default:
        return <Briefcase className="h-5 w-5" />;
    }
  };

  const getColorForType = (type: string) => {
    switch (type) {
      case 'education':
        return 'bg-blue-500';
      case 'position':
        return 'bg-green-500';
      case 'award':
        return 'bg-amber-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold mb-6">Academic Career Timeline</h2>
      
      <div className="relative">
        {/* Vertical timeline line */}
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-muted z-0" />
        
        {/* Timeline events */}
        <div className="space-y-8">
          {sortedEvents.map((event, index) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative pl-14"
            >
              {/* Year badge */}
              <div className="absolute left-0 rounded-full w-16 h-16 bg-background border flex items-center justify-center z-10 shadow-sm">
                <span className="text-sm font-semibold">{event.year}</span>
              </div>
              
              {/* Timeline dot */}
              <div className={`absolute left-7.5 w-5 h-5 rounded-full ${getColorForType(event.type)} z-10 border-2 border-background`} style={{ top: '22px' }} />
              
              {/* Content */}
              <Card className="w-full overflow-hidden">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className={`p-1.5 rounded-md ${getColorForType(event.type)} bg-opacity-10 text-${getColorForType(event.type).replace('bg-', '')}`}>
                      {getIconForType(event.type)}
                    </div>
                    <h3 className="font-medium text-lg">{event.title}</h3>
                  </div>
                  <p className="text-muted-foreground">{event.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
} 