'use client'

import React from 'react'

type SpinnerSize = 'xs' | 'sm' | 'md' | 'lg'

interface LoadingSpinnerProps {
  size?: SpinnerSize
  className?: string
  showText?: boolean
  text?: string
}

const sizeMap: Record<SpinnerSize, string> = {
  xs: 'h-4 w-4 border-2',
  sm: 'h-6 w-6 border-2',
  md: 'h-8 w-8 border-3',
  lg: 'h-12 w-12 border-4',
}

export function LoadingSpinner({
  size = 'md',
  className = '',
  showText = false,
  text = 'Loading...'
}: LoadingSpinnerProps) {
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className="relative">
        {/* Outer spinner - crimson color */}
        <div 
          className={`${sizeMap[size]} rounded-full border-solid border-crimson/30 border-t-crimson animate-spin`}
          role="status"
          aria-label="Loading"
        />
        
        {/* Inner accent - gold color */}
        <div className={`absolute inset-0 flex items-center justify-center`}>
          <div className={`${size === 'xs' ? 'h-1.5 w-1.5' : size === 'sm' ? 'h-2 w-2' : size === 'md' ? 'h-3 w-3' : 'h-4 w-4'} rounded-full bg-gold/50`} />
        </div>
      </div>
      
      {showText && (
        <p className="mt-2 text-sm text-muted-foreground">{text}</p>
      )}
    </div>
  )
}

export function SkeletonLoader({ 
  className = '',
  width = 'w-full',
  height = 'h-4'
}: { 
  className?: string
  width?: string
  height?: string
}) {
  return (
    <div 
      className={`animate-pulse rounded-md bg-muted/50 ${width} ${height} ${className}`}
      role="status"
      aria-label="Loading"
    >
      <span className="sr-only">Loading...</span>
    </div>
  )
}

export function CardSkeleton() {
  return (
    <div className="rounded-xl border bg-card p-4 shadow-sm">
      <div className="space-y-3">
        <SkeletonLoader className="h-4 w-1/4" />
        <SkeletonLoader className="h-8 w-3/4" />
        <SkeletonLoader className="h-4 w-full" />
        <SkeletonLoader className="h-4 w-full" />
        <SkeletonLoader className="h-4 w-2/3" />
      </div>
    </div>
  )
}

export function ImageSkeleton({ 
  aspectRatio = 'aspect-video',
  className = ''
}: { 
  aspectRatio?: string
  className?: string
}) {
  return (
    <div className={`rounded-md bg-muted/50 ${aspectRatio} animate-pulse ${className}`} />
  )
}

export function TextBlockSkeleton({ lines = 3 }: { lines?: number }) {
  return (
    <div className="space-y-2">
      {Array.from({ length: lines }).map((_, i) => (
        <SkeletonLoader 
          key={i} 
          className="h-4" 
          width={i === lines - 1 && lines > 1 ? 'w-4/5' : 'w-full'} 
        />
      ))}
    </div>
  )
}
