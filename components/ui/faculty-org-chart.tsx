'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent } from "@/components/ui/card"
import { ChevronDown, ChevronRight, Users } from "lucide-react"
import { motion, AnimatePresence } from 'framer-motion'
import { LazyImage } from "@/components/ui/lazy-image"

interface FacultyMember {
  id: string
  name: string
  title: string
  department: string
  imageUrl: string
  altText: string
  bio: string
  research?: string[]
}

interface Department {
  name: string
  head?: FacultyMember
  faculty: FacultyMember[]
}

interface FacultyOrgChartProps {
  departments: Department[]
  selectedDepartment?: string
}

export function FacultyOrgChart({ departments, selectedDepartment }: FacultyOrgChartProps) {
  const [expandedDepts, setExpandedDepts] = useState<string[]>([])
  
  // Filter departments based on selectedDepartment prop
  const filteredDepartments = selectedDepartment 
    ? departments.filter(dept => dept.name === selectedDepartment)
    : departments

  // Initialize expanded departments list
  useEffect(() => {
    // If a department is selected, auto-expand it
    if (selectedDepartment) {
      setExpandedDepts([selectedDepartment])
    } else {
      // Otherwise expand all departments by default
      setExpandedDepts(departments.map(d => d.name))
    }
  }, [selectedDepartment, departments])

  const toggleDepartment = (deptName: string) => {
    setExpandedDepts(prev => 
      prev.includes(deptName)
        ? prev.filter(d => d !== deptName)
        : [...prev, deptName]
    )
  }

  return (
    <div className="space-y-6">
      {filteredDepartments.length === 0 ? (
        <div className="text-center py-12 border rounded-lg bg-muted/20">
          <p className="text-muted-foreground">No departments match your criteria.</p>
        </div>
      ) : (
        filteredDepartments.map((dept) => (
          <div key={dept.name} className="border rounded-lg overflow-hidden">
            <div 
              className="bg-muted p-4 flex items-center justify-between cursor-pointer"
              onClick={() => toggleDepartment(dept.name)}
              aria-expanded={expandedDepts.includes(dept.name)}
              role="button"
              aria-controls={`dept-${dept.name.toLowerCase().replace(/\s+/g, '-')}`}
            >
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold">{dept.name}</h3>
                <div className="text-sm text-muted-foreground">
                  ({dept.faculty.length} faculty {dept.faculty.length === 1 ? 'member' : 'members'})
                </div>
              </div>
              {expandedDepts.includes(dept.name) ? (
                <ChevronDown className="h-5 w-5" />
              ) : (
                <ChevronRight className="h-5 w-5" />
              )}
            </div>
            
            <AnimatePresence>
              {expandedDepts.includes(dept.name) && (
                <motion.div
                  id={`dept-${dept.name.toLowerCase().replace(/\s+/g, '-')}`}
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="p-4">
                    {dept.head && (
                      <div className="mb-6">
                        <div className="text-sm text-muted-foreground mb-2">Department Chair/Head:</div>
                        <Link href={`/faculty/${dept.head.id}`} className="block">
                          <Card className="border-primary/30 hover:border-primary transition-colors">
                            <CardContent className="p-4 flex items-center gap-4">
                              <div className="h-16 w-16 rounded-full overflow-hidden bg-muted flex-shrink-0">
                                <LazyImage 
                                  src={dept.head.imageUrl} 
                                  alt={dept.head.altText} 
                                  aspectRatio="aspect-square"
                                  className="h-full w-full object-cover"
                                />
                              </div>
                              <div>
                                <h4 className="font-semibold">{dept.head.name}</h4>
                                <p className="text-sm text-muted-foreground">{dept.head.title}</p>
                              </div>
                            </CardContent>
                          </Card>
                        </Link>
                      </div>
                    )}
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {dept.faculty
                        .filter(f => !dept.head || f.id !== dept.head.id)
                        .map((faculty) => (
                          <Link key={faculty.id} href={`/faculty/${faculty.id}`} className="block">
                            <Card className="h-full hover:border-primary/50 hover:shadow-sm transition-all">
                              <CardContent className="p-4 flex items-center gap-3">
                                <div className="h-12 w-12 rounded-full overflow-hidden bg-muted flex-shrink-0">
                                  <LazyImage 
                                    src={faculty.imageUrl} 
                                    alt={faculty.altText} 
                                    aspectRatio="aspect-square"
                                    className="h-full w-full object-cover"
                                  />
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm">{faculty.name}</h4>
                                  <p className="text-xs text-muted-foreground">{faculty.title}</p>
                                </div>
                              </CardContent>
                            </Card>
                          </Link>
                        ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))
      )}
    </div>
  )
} 