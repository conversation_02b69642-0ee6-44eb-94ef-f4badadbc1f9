'use client'

import React, { useState } from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Bookmark, Search, ArrowRight, ChevronRight, Hash } from "lucide-react"
import { Input } from "@/components/ui/input"

interface ResearchArea {
  id: string
  areaName: string
  description?: string | null
}

interface ResearchAreasProps {
  areas: ResearchArea[]
}

export function ResearchAreas({ areas }: ResearchAreasProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [activeArea, setActiveArea] = useState<string | null>(null)
  
  const filteredAreas = areas.filter(area => 
    area.areaName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (area.description && area.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )
  
  const selectedArea = activeArea 
    ? areas.find(area => area.id === activeArea) 
    : filteredAreas[0]
    
  // Extract keywords from area descriptions
  const extractKeywords = (text: string | null | undefined): string[] => {
    if (!text) return []
    
    // Basic keyword extraction - real implementation would use NLP
    const commonWords = ['and', 'the', 'of', 'in', 'to', 'a', 'an', 'for', 'with', 'on', 'at', 'by', 'is', 'are', 'that', 'this', 'from']
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3 && !commonWords.includes(word))
    
    return Array.from(new Set(words)).slice(0, 8) // Return unique words, limited to 8
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold flex items-center">
          <Bookmark className="h-5 w-5 mr-2 text-crimson" />
          Research Areas
        </h2>
      </div>
      
      {areas.length > 3 && (
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search research areas..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 border-gray-200 focus:border-crimson/30 focus:ring-crimson/20"
          />
        </div>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left side: Area list */}
        <div className="lg:col-span-1">
          <div className="space-y-2">
            {filteredAreas.length > 0 ? (
              filteredAreas.map(area => (
                <div
                  key={area.id}
                  className={`p-4 rounded-xl cursor-pointer transition-all duration-300 border ${
                    activeArea === area.id 
                      ? 'bg-crimson/5 border-crimson/20 shadow-sm' 
                      : 'bg-white border-gray-100 hover:bg-crimson/5 hover:border-crimson/10'
                  }`}
                  onClick={() => setActiveArea(area.id)}
                >
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900">{area.areaName}</h3>
                    <ChevronRight className={`h-4 w-4 transition-transform duration-300 ${
                      activeArea === area.id ? 'text-crimson' : 'text-gray-400'
                    }`} />
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 bg-gray-50 rounded-xl">
                <p className="text-gray-500">No research areas found matching your search.</p>
              </div>
            )}
          </div>
        </div>
        
        {/* Right side: Selected area details */}
        <div className="lg:col-span-2">
          {selectedArea ? (
            <Card className="overflow-hidden border-none shadow-md bg-white h-full">
              <div className="h-1 bg-gradient-to-r from-crimson to-gold"></div>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold text-gray-900">{selectedArea.areaName}</h3>
                  <Badge className="bg-crimson/10 text-crimson border-none">Research Focus</Badge>
                </div>
                
                {selectedArea.description ? (
                  <>
                    <div className="relative mt-4 mb-6">
                      <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-crimson/20 to-gold/20 rounded-full"></div>
                      <p className="text-gray-700 pl-4 leading-relaxed">
                        {selectedArea.description}
                      </p>
                    </div>
                    
                    {/* Keywords */}
                    <div className="mt-6">
                      <div className="flex items-center gap-1 mb-2">
                        <Hash className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-500">Related keywords:</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {extractKeywords(selectedArea.description).map((keyword, idx) => (
                          <Badge 
                            key={idx} 
                            variant="outline" 
                            className="bg-gray-50 text-gray-700"
                          >
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </>
                ) : (
                  <p className="text-muted-foreground italic">No detailed description available for this research area.</p>
                )}
                
                {/* Visual decoration elements */}
                <div className="absolute -bottom-8 -right-8 w-32 h-32 rounded-full bg-gradient-to-br from-crimson/5 to-gold/5 blur-xl"></div>
                <div className="absolute top-12 -right-10 w-20 h-20 rounded-full border border-crimson/10"></div>
              </CardContent>
            </Card>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-muted-foreground">Select a research area to view details</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 