'use client'

import { FacultyMember } from "@/lib/data/faculty"
import { calculateProfileCompleteness } from "@/lib/faculty-utils"
import { Check, AlertCircle, Info } from "lucide-react"

interface ProfileCompletenessProps {
  faculty: FacultyMember
  className?: string
  showText?: boolean
}

export function ProfileCompleteness({
  faculty,
  className = "",
  showText = true
}: ProfileCompletenessProps) {
  const completeness = calculateProfileCompleteness(faculty)
  
  // Determine color based on completeness
  const getColor = () => {
    if (completeness >= 75) return "bg-green-500"
    if (completeness >= 50) return "bg-yellow-500"
    return "bg-red-500"
  }
  
  // Determine icon based on completeness
  const getIcon = () => {
    if (completeness >= 75) return <Check className="h-4 w-4 text-green-500" />
    if (completeness >= 50) return <Info className="h-4 w-4 text-yellow-500" />
    return <AlertCircle className="h-4 w-4 text-red-500" />
  }
  
  return (
    <div className={`flex flex-col ${className}`}>
      <div className="flex items-center">
        {getIcon()}
        {showText && (
          <span className="ml-2 text-sm font-medium">
            Profile {completeness >= 75 ? 'Complete' : 'Incomplete'}
          </span>
        )}
      </div>
      
      <div className="mt-2">
        <p className="text-xs font-semibold mb-1">Profile Completeness: {completeness}%</p>
        <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
          <div 
            className={`h-full ${getColor()}`} 
            style={{ width: `${completeness}%` }}
            role="progressbar"
            aria-valuenow={completeness}
            aria-valuemin={0}
            aria-valuemax={100}
          ></div>
        </div>
      </div>
    </div>
  )
} 