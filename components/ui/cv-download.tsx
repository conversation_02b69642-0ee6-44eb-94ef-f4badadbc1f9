'use client'

import React from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { FileDown, FileText, Calendar, Clock } from "lucide-react"

export interface CVDocument {
  id: string
  title: string
  fileType: 'pdf' | 'docx' | 'txt'
  fileSize: string
  lastUpdated: string
  url: string
}

interface CVDownloadProps {
  documents: CVDocument[]
  facultyName: string
}

export function CVDownload({ documents, facultyName }: CVDownloadProps) {
  const getFileTypeIcon = (fileType: string) => {
    switch (fileType) {
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-500" />;
      case 'docx':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'txt':
        return <FileText className="h-5 w-5 text-gray-500" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-semibold mb-4">CV & Resume</h2>
      
      <div className="grid gap-4 md:grid-cols-2">
        {documents.map((doc) => (
          <Card key={doc.id} className="overflow-hidden">
            <CardContent className="p-5">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-md bg-primary/10">
                  {getFileTypeIcon(doc.fileType)}
                </div>
                
                <div className="flex-1">
                  <h3 className="font-medium text-lg mb-1">{doc.title}</h3>
                  
                  <div className="flex flex-wrap text-sm text-muted-foreground gap-3 mb-3">
                    <span className="flex items-center gap-1">
                      <FileText className="h-3.5 w-3.5" />
                      {doc.fileType.toUpperCase()} • {doc.fileSize}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3.5 w-3.5" />
                      Last updated: {doc.lastUpdated}
                    </span>
                  </div>
                  
                  <a href={doc.url} download={`${facultyName.replace(/\s+/g, '_')}_${doc.title.replace(/\s+/g, '_')}.${doc.fileType}`}>
                    <Button size="sm" className="w-full md:w-auto">
                      <FileDown className="mr-2 h-4 w-4" />
                      Download
                    </Button>
                  </a>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {documents.length === 0 && (
        <div className="text-center py-8 border rounded-lg bg-muted/20">
          <p className="text-muted-foreground">No documents available for download.</p>
        </div>
      )}
    </div>
  )
} 