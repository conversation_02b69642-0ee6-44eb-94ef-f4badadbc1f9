'use client'

import React, { useState, useEffect } from 'react'
import { LoadingSpinner } from './loading-spinner'

interface PageTransitionProps {
  children: React.ReactNode
  className?: string
  transitionType?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right'
  duration?: number
  showSpinner?: boolean
}

export function PageTransition({
  children,
  className = '',
  transitionType = 'fade',
  duration = 300,
  showSpinner = true,
}: PageTransitionProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [isVisible, setIsVisible] = useState(false)

  // Animation classes based on transition type
  const getAnimationClass = () => {
    switch (transitionType) {
      case 'slide-up':
        return 'translate-y-4 opacity-0'
      case 'slide-down':
        return '-translate-y-4 opacity-0'
      case 'slide-left':
        return 'translate-x-4 opacity-0'
      case 'slide-right':
        return '-translate-x-4 opacity-0'
      case 'fade':
      default:
        return 'opacity-0'
    }
  }

  useEffect(() => {
    // Simulate initial loading
    const timer = setTimeout(() => {
      setIsLoading(false)
      
      // Start animation after loading is complete
      setTimeout(() => {
        setIsVisible(true)
      }, 50) // Small delay to ensure DOM is ready
    }, duration)

    return () => clearTimeout(timer)
  }, [duration])

  // Handle reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    if (mediaQuery.matches) {
      // Skip loading animation for users who prefer reduced motion
      setIsLoading(false)
      setIsVisible(true)
    }
  }, [])

  if (isLoading && showSpinner) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-background z-50">
        <LoadingSpinner size="lg" showText text="Loading..." />
      </div>
    )
  }

  return (
    <div
      className={`transition-all duration-${duration} ease-out ${
        isVisible ? '' : getAnimationClass()
      } ${className}`}
      style={{ 
        transitionDuration: `${duration}ms`,
      }}
    >
      {children}
    </div>
  )
}
