'use client'

import React, { useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'

interface FacultyMember {
  id: string
  name: string
  title: string
  department: string
  imageUrl: string
  research: string[]
}

interface ResearchArea {
  name: string
  facultyIds: string[]
  color: string
}

interface FacultyResearchNetworkProps {
  faculty: FacultyMember[]
  researchAreas: ResearchArea[]
  selectedDepartment?: string
  selectedResearchArea?: string
}

export function FacultyResearchNetwork({
  faculty,
  researchAreas,
  selectedDepartment,
  selectedResearchArea
}: FacultyResearchNetworkProps) {
  const router = useRouter()
  const [activeNode, setActiveNode] = useState<string | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [containerDimensions, setContainerDimensions] = useState({ width: 800, height: 600 })
  
  // Update container dimensions on resize
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        setContainerDimensions({
          width: containerRef.current.offsetWidth,
          height: Math.max(500, window.innerHeight * 0.6),
        })
      }
    }
    
    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  // Filter faculty based on selected filters
  const filteredFaculty = faculty.filter(f => {
    if (selectedDepartment && f.department !== selectedDepartment) return false
    if (selectedResearchArea && !f.research.includes(selectedResearchArea)) return false
    return true
  })
  
  // Filter research areas based on filtered faculty
  const filteredResearchAreas = researchAreas.filter(area => {
    const hasFaculty = area.facultyIds.some(id => 
      filteredFaculty.some(f => f.id === id)
    )
    return hasFaculty || area.name === selectedResearchArea
  })
  
  // Handle faculty card click
  const handleFacultyClick = (id: string) => {
    router.push(`/faculty/${id}`)
  }
  
  // Calculate positions for research areas and faculty
  const centerX = containerDimensions.width / 2
  const centerY = containerDimensions.height / 2
  const researchRadius = Math.min(centerX, centerY) * 0.8
  
  // Position research areas in a circle
  const researchAreaPositions = filteredResearchAreas.map((area, index) => {
    const angle = (index / filteredResearchAreas.length) * 2 * Math.PI
    const x = centerX + researchRadius * Math.cos(angle)
    const y = centerY + researchRadius * Math.sin(angle)
    return { area, x, y }
  })
  
  // Connect faculty to their research areas
  const facultyConnections = filteredFaculty.flatMap(f => {
    const matchingAreas = researchAreaPositions.filter(({ area }) => 
      area.facultyIds.includes(f.id)
    )
    return matchingAreas.map(({ area }) => ({
      facultyId: f.id,
      areaName: area.name,
      color: area.color
    }))
  })
  
  // Calculate positions for faculty members
  const facultyPositions = filteredFaculty.map((f, index) => {
    const totalFaculty = filteredFaculty.length
    const innerRadius = researchRadius * 0.4
    const angle = (index / totalFaculty) * 2 * Math.PI
    const x = centerX + innerRadius * Math.cos(angle)
    const y = centerY + innerRadius * Math.sin(angle)
    return { faculty: f, x, y }
  })
  
  // Highlight connections on hover
  const getConnectionOpacity = (facultyId: string, areaName: string) => {
    if (!activeNode) return 0.3
    return activeNode === facultyId || activeNode === areaName ? 0.7 : 0.1
  }
  
  // Get faculty by ID
  const getFacultyById = (id: string) => faculty.find(f => f.id === id)
  
  return (
    <div ref={containerRef} className="w-full border rounded-xl overflow-hidden bg-muted/30 relative">
      <div className="p-4 border-b bg-muted">
        <h3 className="text-lg font-semibold">Research Collaboration Network</h3>
        <p className="text-sm text-muted-foreground">
          Visualizing faculty research areas and collaborations
        </p>
      </div>
      
      <div style={{ height: containerDimensions.height }} className="relative">
        {/* Connections between faculty and research areas */}
        <svg width="100%" height="100%" className="absolute inset-0">
          {facultyConnections.map(({ facultyId, areaName, color }, idx) => {
            const facultyPos = facultyPositions.find(p => p.faculty.id === facultyId)
            const areaPos = researchAreaPositions.find(p => p.area.name === areaName)
            
            if (!facultyPos || !areaPos) return null
            
            return (
              <line
                key={`connection-${facultyId}-${areaName}-${idx}`}
                x1={facultyPos.x}
                y1={facultyPos.y}
                x2={areaPos.x}
                y2={areaPos.y}
                stroke={color}
                strokeWidth={2}
                strokeOpacity={getConnectionOpacity(facultyId, areaName)}
                strokeDasharray="4 2"
              />
            )
          })}
        </svg>
        
        {/* Faculty nodes */}
        {facultyPositions.map(({ faculty, x, y }) => (
          <motion.div
            key={`faculty-${faculty.id}`}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
            style={{ left: x, top: y }}
            whileHover={{ scale: 1.1 }}
            onMouseEnter={() => setActiveNode(faculty.id)}
            onMouseLeave={() => setActiveNode(null)}
            onClick={() => handleFacultyClick(faculty.id)}
          >
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white shadow-md">
                <img 
                  src={faculty.imageUrl} 
                  alt={faculty.name} 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="mt-1 text-center bg-background rounded-md px-2 py-1 shadow-sm max-w-[120px]">
                <p className="text-xs font-medium truncate">{faculty.name}</p>
              </div>
            </div>
          </motion.div>
        ))}
        
        {/* Research area nodes */}
        {researchAreaPositions.map(({ area, x, y }) => (
          <motion.div
            key={`area-${area.name}`}
            className="absolute transform -translate-x-1/2 -translate-y-1/2"
            style={{ left: x, top: y }}
            whileHover={{ scale: 1.05 }}
            onMouseEnter={() => setActiveNode(area.name)}
            onMouseLeave={() => setActiveNode(null)}
          >
            <div
              className="p-3 rounded-lg shadow-md text-white text-center min-w-[120px]"
              style={{ backgroundColor: area.color }}
            >
              <p className="text-sm font-medium">{area.name}</p>
              <p className="text-xs mt-1">
                {area.facultyIds.filter(id => filteredFaculty.some(f => f.id === id)).length} Faculty
              </p>
            </div>
          </motion.div>
        ))}
      </div>
      
      {/* Legend */}
      <div className="border-t p-4 bg-background">
        <div className="text-sm font-medium mb-2">Research Areas:</div>
        <div className="flex flex-wrap gap-2">
          {filteredResearchAreas.map(area => (
            <div 
              key={area.name}
              className="flex items-center gap-1 px-2 py-1 rounded-full text-xs"
              style={{ backgroundColor: `${area.color}20`, color: area.color }}
            >
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: area.color }}
              />
              <span>{area.name}</span>
            </div>
          ))}
        </div>
      </div>
      
      {/* Details card for active node */}
      {activeNode && getFacultyById(activeNode) && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute right-4 bottom-20 w-64"
        >
          <Card className="shadow-lg">
            <CardContent className="p-3">
              <h4 className="font-medium text-sm">
                {getFacultyById(activeNode)?.name}
              </h4>
              <p className="text-xs text-muted-foreground mb-2">
                {getFacultyById(activeNode)?.title}
              </p>
              <div className="text-xs space-y-1">
                <div className="font-medium">Research Interests:</div>
                <div className="flex flex-wrap gap-1">
                  {getFacultyById(activeNode)?.research.map(area => (
                    <span key={area} className="bg-muted px-1.5 py-0.5 rounded text-xs">
                      {area}
                    </span>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
} 