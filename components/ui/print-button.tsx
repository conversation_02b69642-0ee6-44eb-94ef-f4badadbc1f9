'use client'

import { But<PERSON> } from "@/components/ui/button"
import { Printer } from "lucide-react"

interface PrintButtonProps {
  className?: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  tooltip?: string
}

export function PrintButton({
  className,
  variant = "outline",
  size = "sm",
  tooltip = "Print this page"
}: PrintButtonProps) {
  const handlePrint = () => {
    window.print()
  }
  
  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handlePrint}
      aria-label={tooltip}
      title={tooltip}
    >
      <Printer className="h-4 w-4 mr-2" />
      <span>Print</span>
    </Button>
  )
} 