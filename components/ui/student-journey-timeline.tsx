'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { 
  ClipboardList, 
  PenTool, 
  Users, 
  BookOpen, 
  Award, 
  GraduationCap,
  ChevronDown,
  ChevronUp,
  ArrowRight,
  ExternalLink,
  CheckCircle2,
  LucideIcon,
  Clock
} from 'lucide-react'

interface Resource {
  title: string
  link: string
}

interface TimelineStage {
  id: string
  title: string
  description: string
  icon: LucideIcon
  color: string
  accentColor: string
  bgGradient: string
  tips: string[]
  resources: Resource[]
  duration?: string
}

export function StudentJourneyTimeline() {
  const [expandedStage, setExpandedStage] = useState<string | null>("application")
  const [activeIndex, setActiveIndex] = useState<number>(0)
  const [isInView, setIsInView] = useState<boolean>(false)
  
  useEffect(() => {
    // Check if the timeline is in view when component mounts and on scroll
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting)
      },
      { threshold: 0.1 }
    )
    
    const timeline = document.getElementById('journey-timeline')
    if (timeline) {
      observer.observe(timeline)
    }
    
    return () => {
      if (timeline) {
        observer.unobserve(timeline)
      }
    }
  }, [])

  // Auto-cycle through the stages when not interacting
  useEffect(() => {
    if (!isInView) return
    
    const interval = setInterval(() => {
      if (!expandedStage) { // Only auto-cycle if user hasn't manually expanded a stage
        const nextIndex = (activeIndex + 1) % journeyStages.length
        setActiveIndex(nextIndex)
      }
    }, 5000)
    
    return () => clearInterval(interval)
  }, [activeIndex, expandedStage, isInView])
  
  const toggleExpand = (id: string, index: number) => {
    setActiveIndex(index)
    setExpandedStage(expandedStage === id ? null : id)
  }
  
  const journeyStages: TimelineStage[] = [
    {
      id: 'application',
      title: 'Application',
      description: 'Submit your application with all required documents and meet our admissions criteria.',
      icon: ClipboardList,
      color: 'bg-blue-500',
      accentColor: 'text-blue-600',
      bgGradient: 'from-blue-50/80 via-blue-50/30 to-transparent',
      duration: '1-2 months',
      tips: [
        'Research programs thoroughly',
        'Prepare personal statement',
        'Gather recommendation letters',
        'Check application deadlines'
      ],
      resources: [
        { title: 'Application Guide', link: '#' },
        { title: 'Program Finder', link: '/programs/compare' }
      ]
    },
    {
      id: 'admission',
      title: 'Admission',
      description: 'Receive your admission offer and complete all enrollment requirements.',
      icon: PenTool,
      color: 'bg-indigo-500',
      accentColor: 'text-indigo-600',
      bgGradient: 'from-indigo-50/80 via-indigo-50/30 to-transparent',
      duration: '2-4 weeks',
      tips: [
        'Review financial aid options',
        'Complete enrollment forms',
        'Submit final transcripts',
        'Register for orientation'
      ],
      resources: [
        { title: 'Financial Aid', link: '#' },
        { title: 'Enrollment Checklist', link: '#' }
      ]
    },
    {
      id: 'orientation',
      title: 'Orientation',
      description: 'Attend orientation sessions to get familiar with campus resources and meet fellow students.',
      icon: Users,
      color: 'bg-purple-500',
      accentColor: 'text-purple-600',
      bgGradient: 'from-purple-50/80 via-purple-50/30 to-transparent',
      duration: '1 week',
      tips: [
        'Tour the campus',
        'Meet academic advisors',
        'Learn about student services',
        'Join student organizations'
      ],
      resources: [
        { title: 'Campus Map', link: '#' },
        { title: 'Student Orgs', link: '#' }
      ]
    },
    {
      id: 'year1',
      title: 'First Year',
      description: 'Begin your academic journey with foundational courses and exploration of your interests.',
      icon: BookOpen,
      color: 'bg-pink-500',
      accentColor: 'text-pink-600',
      bgGradient: 'from-pink-50/80 via-pink-50/30 to-transparent',
      duration: '2 semesters',
      tips: [
        'Establish study routines',
        'Connect with professors',
        'Utilize academic support services',
        'Balance academics and social life'
      ],
      resources: [
        { title: 'Academic Support', link: '#' },
        { title: 'First-Year Programs', link: '#' }
      ]
    },
    {
      id: 'midProgram',
      title: 'Mid-Program',
      description: 'Advance to specialized courses, engage in research, and gain practical experience.',
      icon: Users,
      color: 'bg-orange-500',
      accentColor: 'text-orange-600',
      bgGradient: 'from-orange-50/80 via-orange-50/30 to-transparent',
      duration: '2-3 years',
      tips: [
        'Declare major/minor',
        'Explore internship opportunities',
        'Consider study abroad',
        'Build professional network'
      ],
      resources: [
        { title: 'Internship Database', link: '#' },
        { title: 'Study Abroad', link: '#' }
      ]
    },
    {
      id: 'finalYear',
      title: 'Final Year',
      description: 'Complete capstone projects, prepare for graduation, and plan for your future career.',
      icon: Award,
      color: 'bg-amber-500',
      accentColor: 'text-amber-600',
      bgGradient: 'from-amber-50/80 via-amber-50/30 to-transparent',
      duration: '2 semesters',
      tips: [
        'Finalize graduation requirements',
        'Update resume and portfolio',
        'Apply for jobs/graduate school',
        'Attend career fairs'
      ],
      resources: [
        { title: 'Career Services', link: '#' },
        { title: 'Graduate Programs', link: '#' }
      ]
    },
    {
      id: 'graduation',
      title: 'Graduation',
      description: 'Celebrate your achievements and transition to the next phase of your journey.',
      icon: GraduationCap,
      color: 'bg-crimson',
      accentColor: 'text-crimson',
      bgGradient: 'from-red-50/80 via-red-50/30 to-transparent',
      duration: 'Ceremony & Beyond',
      tips: [
        'Complete exit interviews',
        'Join alumni network',
        'Attend commencement ceremony',
        'Set post-graduation goals'
      ],
      resources: [
        { title: 'Alumni Association', link: '#' },
        { title: 'Graduation Checklist', link: '#' }
      ]
    }
  ]

  return (
    <div id="journey-timeline" className="w-full">
      {/* Timeline Navigation */}
      <div className="mb-12 px-4 overflow-x-auto scrollbar-hide">
        <div className="flex items-center space-x-3 md:space-x-6 min-w-max md:justify-center">
          {journeyStages.map((stage, index) => {
            const Icon = stage.icon;
            const isActive = activeIndex === index;
            const isExpanded = expandedStage === stage.id;
            
            return (
              <button 
                key={stage.id}
                onClick={() => toggleExpand(stage.id, index)}
                className={`flex flex-col items-center transition-all duration-300 rounded-lg px-3 py-2 ${
                  isActive ? 'bg-background shadow-sm' : 'hover:bg-background/50'
                }`}
                aria-pressed={isActive}
              >
                <div 
                  className={`relative w-10 h-10 rounded-full ${stage.color} flex items-center justify-center mb-2 shadow-md transition-transform duration-300 ${
                    isActive ? 'scale-110 ring-2 ring-offset-2 ring-background' : 'opacity-80'
                  }`}
                >
                  <Icon className="h-5 w-5 text-white" />
                  {isExpanded && (
                    <span className="absolute -right-1 -top-1 w-5 h-5 rounded-full bg-green-500 shadow flex items-center justify-center">
                      <CheckCircle2 className="h-3 w-3 text-white" />
                    </span>
                  )}
                </div>
                <span className={`text-xs font-medium transition-colors ${
                  isActive ? `${stage.accentColor} font-semibold` : 'text-muted-foreground'
                }`}>
                  {stage.title}
                </span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Current Stage Focus */}
      {journeyStages.map((stage, index) => {
        if (index !== activeIndex) return null;
        
        const Icon = stage.icon;
        const isExpanded = expandedStage === stage.id;
        
        return (
          <div 
            key={stage.id}
            className="px-4 relative animate-fade-in-up mb-16"
          >
            <Card className={`overflow-hidden border-0 shadow-lg transition-all duration-500 ${
              isExpanded ? 'scale-105' : ''
            }`}>
              <div className={`h-2 ${stage.color} w-full`} />
              <div className={`bg-gradient-to-br ${stage.bgGradient}`}>
                <CardContent className="p-6 md:p-8">
                  <div className="flex flex-col md:flex-row gap-6 md:items-start">
                    <div className={`flex-shrink-0 h-16 w-16 rounded-2xl ${stage.color} flex items-center justify-center shadow-md`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    
                    <div className="flex-grow">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
                        <div>
                          <h3 className={`text-2xl font-bold ${stage.accentColor}`}>
                            {stage.title}
                          </h3>
                          {stage.duration && (
                            <div className="flex items-center text-sm text-muted-foreground mt-1">
                              <Clock className="mr-1 h-3.5 w-3.5" />
                              <span>{stage.duration}</span>
                            </div>
                          )}
                        </div>
                        
                        <Button 
                          variant={isExpanded ? "secondary" : "outline"} 
                          size="sm" 
                          onClick={() => toggleExpand(stage.id, index)}
                          className="transition-all duration-300"
                          aria-label={isExpanded ? "Show less" : "Show more"}
                          aria-expanded={isExpanded}
                        >
                          {isExpanded ? "Show less" : "Show more"}
                          {isExpanded ? (
                            <ChevronUp className="ml-1.5 h-4 w-4" />
                          ) : (
                            <ChevronDown className="ml-1.5 h-4 w-4" />
                          )}
                        </Button>
                      </div>
                      
                      <p className="text-muted-foreground text-lg leading-relaxed">
                        {stage.description}
                      </p>
                      
                      {isExpanded && (
                        <div className="mt-8 animate-fade-in">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <h4 className={`text-sm uppercase tracking-wider font-semibold mb-3 ${stage.accentColor}`}>
                                Tips for Success
                              </h4>
                              <ul className="space-y-3">
                                {stage.tips.map((tip, idx) => (
                                  <li key={idx} className="flex items-start gap-3 text-sm bg-background/60 p-3 rounded-lg shadow-sm">
                                    <div className={`mt-0.5 h-5 w-5 rounded-full ${stage.color} flex-shrink-0 flex items-center justify-center`}>
                                      <CheckCircle2 className="h-3 w-3 text-white" />
                                    </div>
                                    <span>{tip}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                            
                            <div>
                              <h4 className={`text-sm uppercase tracking-wider font-semibold mb-3 ${stage.accentColor}`}>
                                Resources
                              </h4>
                              <div className="bg-background/60 p-4 rounded-lg shadow-sm">
                                <ul className="space-y-2">
                                  {stage.resources.map((resource, idx) => (
                                    <li key={idx}>
                                      <Button variant="link" asChild className={`p-0 h-auto ${stage.accentColor} group`}>
                                        <a href={resource.link} className="flex items-center">
                                          <ExternalLink className="mr-2 h-4 w-4" />
                                          {resource.title}
                                          <ArrowRight className="ml-1.5 h-3.5 w-3.5 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all" />
                                        </a>
                                      </Button>
                                    </li>
                                  ))}
                                </ul>
                                
                                {stage.id === "application" && (
                                  <div className="mt-4 pt-4 border-t">
                                    <Button size="sm" className={stage.color} asChild>
                                      <a href="/apply" className="flex items-center">
                                        Apply Now <ArrowRight className="ml-1.5 h-4 w-4" />
                                      </a>
                                    </Button>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </div>
            </Card>
          </div>
        );
      })}

      {/* Progress Timeline */}
      <div className="mt-8 px-4">
        <div className="relative h-2 bg-muted/50 rounded-full overflow-hidden">
          <div 
            className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 via-purple-500 to-crimson"
            style={{ width: `${(activeIndex + 1) * (100 / journeyStages.length)}%` }}
          ></div>
        </div>
        <div className="mt-3 flex justify-between text-xs text-muted-foreground">
          <span>Application</span>
          <span className="hidden sm:block">Year 1</span>
          <span className="hidden md:block">Mid-Program</span>
          <span className="hidden sm:block">Final Year</span>
          <span>Graduation</span>
        </div>
      </div>

      {/* Journey Navigation */}
      <div className="mt-12 flex justify-center gap-4">
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => {
            const prevIndex = activeIndex === 0 ? journeyStages.length - 1 : activeIndex - 1
            setActiveIndex(prevIndex)
            setExpandedStage(journeyStages[prevIndex].id)
          }}
          className="h-9 w-9 p-0 rounded-full"
          aria-label="Previous stage"
        >
          <ChevronUp className="h-5 w-5 -rotate-90" />
        </Button>
        
        <div className="flex items-center gap-1">
          {journeyStages.map((_, index) => (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              onClick={() => {
                setActiveIndex(index)
                setExpandedStage(journeyStages[index].id)
              }}
              className={`h-2 min-w-[16px] p-0 rounded-full ${
                activeIndex === index ? 'bg-primary' : 'bg-muted'
              }`}
              aria-label={`Go to stage ${index + 1}`}
              aria-current={activeIndex === index ? 'step' : undefined}
            />
          ))}
        </div>
        
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => {
            const nextIndex = activeIndex === journeyStages.length - 1 ? 0 : activeIndex + 1
            setActiveIndex(nextIndex)
            setExpandedStage(journeyStages[nextIndex].id)
          }}
          className="h-9 w-9 p-0 rounded-full"
          aria-label="Next stage"
        >
          <ChevronUp className="h-5 w-5 rotate-90" />
        </Button>
      </div>
    </div>
  );
} 