'use client'

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Clock, Info, Check, X } from "lucide-react"

export interface OfficeHour {
  id: string
  day: string
  timeSlots: TimeSlot[]
  location: string
  notes?: string
}

export interface TimeSlot {
  id: string
  startTime: string
  endTime: string
  isAvailable: boolean
  isBooked?: boolean
  studentName?: string
  studentEmail?: string
  topic?: string
}

interface OfficeHoursSchedulerProps {
  officeHours: OfficeHour[] | string
  facultyName: string
  facultyEmail: string
}

// Mock office hours data for when a string is provided instead of proper data
const mockOfficeHours: OfficeHour[] = [
  {
    id: "monday",
    day: "Monday",
    timeSlots: [
      { id: "mon-1", startTime: "10:00", endTime: "11:00", isAvailable: true },
      { id: "mon-2", startTime: "11:00", endTime: "12:00", isAvailable: true }
    ],
    location: "Office 301"
  },
  {
    id: "wednesday",
    day: "Wednesday",
    timeSlots: [
      { id: "wed-1", startTime: "14:00", endTime: "15:00", isAvailable: true },
      { id: "wed-2", startTime: "15:00", endTime: "16:00", isAvailable: true }
    ],
    location: "Office 301"
  }
];

export function OfficeHoursScheduler({ 
  officeHours,
  facultyName,
  facultyEmail
}: OfficeHoursSchedulerProps) {
  // Handle the case where officeHours is a string instead of an array
  const officeHoursData = Array.isArray(officeHours) ? officeHours : mockOfficeHours;
  
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlot | null>(null)
  const [bookingDialogOpen, setBookingDialogOpen] = useState(false)
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false)
  const [bookingForm, setBookingForm] = useState({
    studentName: '',
    studentEmail: '',
    topic: ''
  })

  // Filter office hours based on selected date
  const getOfficeHoursForDate = (date: Date | undefined) => {
    if (!date) return null
    
    const dayName = format(date, 'EEEE')
    return officeHoursData.find(oh => oh.day === dayName) || null
  }
  
  const selectedDayOfficeHours = getOfficeHoursForDate(selectedDate)
  
  // Handle booking form submission
  const handleBookingSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setBookingDialogOpen(false)
    setConfirmationDialogOpen(true)
    
    // In a real application, this would send the booking data to an API
    console.log('Booking submitted:', {
      date: selectedDate,
      timeSlot: selectedTimeSlot,
      ...bookingForm
    })
  }
  
  // Format time for display (e.g., "3:00 PM")
  const formatTime = (time: string) => {
    // Example simple format if time is already in "HH:MM" format
    const [hour, minute] = time.split(':')
    const hourNum = parseInt(hour)
    const ampm = hourNum >= 12 ? 'PM' : 'AM'
    const hour12 = hourNum % 12 || 12
    return `${hour12}:${minute} ${ampm}`
  }
  
  // Check if a date should be disabled in the calendar
  const isDateDisabled = (date: Date) => {
    const dayName = format(date, 'EEEE')
    const hasOfficeHours = officeHoursData.some(oh => oh.day === dayName)
    const isPast = date < new Date(new Date().setHours(0, 0, 0, 0))
    return !hasOfficeHours || isPast
  }
  
  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-semibold mb-4">Office Hours</h2>
      
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Select a Day</CardTitle>
          </CardHeader>
          <CardContent>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, 'PPP') : "Select a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  disabled={isDateDisabled}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            
            {/* Office hours information */}
            <div className="mt-4">
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Regular Office Hours:</h3>
              <ul className="space-y-2">
                {officeHoursData.map((oh) => (
                  <li key={oh.id} className="flex items-start gap-2 text-sm">
                    <div className="font-medium min-w-24">{oh.day}:</div>
                    <div>
                      {oh.timeSlots.length > 0 ? (
                        <div>
                          {oh.timeSlots[0].startTime} - {oh.timeSlots[oh.timeSlots.length - 1].endTime}
                          <div className="text-xs text-muted-foreground mt-1">
                            Location: {oh.location}
                          </div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">No office hours</span>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
              
              {/* Additional notes */}
              <div className="mt-4 p-3 bg-muted rounded-md text-sm">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <p>Please book at least 24 hours in advance.</p>
                    <p className="mt-1">If you need to cancel, please email {facultyEmail}.</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Available Time Slots</CardTitle>
          </CardHeader>
          <CardContent>
            {selectedDate ? (
              <>
                {selectedDayOfficeHours ? (
                  <div className="space-y-4">
                    <div className="flex items-center text-sm">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>
                        {format(selectedDate, 'EEEE, MMMM d, yyyy')} at {selectedDayOfficeHours.location}
                      </span>
                    </div>
                    
                    <div className="grid gap-2">
                      {selectedDayOfficeHours.timeSlots.map((slot) => (
                        <Button
                          key={slot.id}
                          variant={slot.isBooked ? "outline" : "default"}
                          className={`justify-between ${!slot.isAvailable || slot.isBooked ? 'opacity-60 cursor-not-allowed' : ''}`}
                          disabled={!slot.isAvailable || slot.isBooked}
                          onClick={() => {
                            setSelectedTimeSlot(slot);
                            setBookingDialogOpen(true);
                          }}
                        >
                          <span>
                            {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
                          </span>
                          {slot.isBooked ? (
                            <span className="text-xs bg-muted px-2 py-0.5 rounded">Booked</span>
                          ) : !slot.isAvailable ? (
                            <span className="text-xs bg-muted px-2 py-0.5 rounded">Unavailable</span>
                          ) : (
                            <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 px-2 py-0.5 rounded">Available</span>
                          )}
                        </Button>
                      ))}
                    </div>
                    
                    {selectedDayOfficeHours.notes && (
                      <div className="mt-2 text-sm text-muted-foreground">
                        <p><strong>Note:</strong> {selectedDayOfficeHours.notes}</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">No office hours available on this day.</p>
                    <p className="text-sm text-muted-foreground mt-1">Please select a different date.</p>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">Please select a date to view available time slots.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Booking Dialog */}
      <Dialog open={bookingDialogOpen} onOpenChange={setBookingDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Book Office Hours Appointment</DialogTitle>
            <DialogDescription>
              Schedule time with {facultyName} on {selectedDate && format(selectedDate, 'EEEE, MMMM d, yyyy')}
              {" "} at {selectedTimeSlot && `${formatTime(selectedTimeSlot.startTime)} - ${formatTime(selectedTimeSlot.endTime)}`}.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleBookingSubmit} className="space-y-4 pt-4">
            <div className="grid gap-2">
              <label htmlFor="name" className="text-sm font-medium">Your Name</label>
              <Input
                id="name"
                value={bookingForm.studentName}
                onChange={(e) => setBookingForm({...bookingForm, studentName: e.target.value})}
                required
              />
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="email" className="text-sm font-medium">Your Email</label>
              <Input
                id="email"
                type="email"
                value={bookingForm.studentEmail}
                onChange={(e) => setBookingForm({...bookingForm, studentEmail: e.target.value})}
                required
              />
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="topic" className="text-sm font-medium">Topic for Discussion</label>
              <Textarea
                id="topic"
                value={bookingForm.topic}
                onChange={(e) => setBookingForm({...bookingForm, topic: e.target.value})}
                required
              />
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setBookingDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">Book Appointment</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Confirmation Dialog */}
      <Dialog open={confirmationDialogOpen} onOpenChange={setConfirmationDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Appointment Confirmed</DialogTitle>
          </DialogHeader>
          
          <div className="py-6">
            <div className="flex items-center justify-center mb-6">
              <div className="h-12 w-12 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <Check className="h-6 w-6 text-green-600 dark:text-green-300" />
              </div>
            </div>
            
            <div className="text-center space-y-3">
              <p>Your appointment with {facultyName} has been scheduled.</p>
              
              <div className="bg-muted p-3 rounded-md text-sm">
                <p className="font-medium">Appointment Details:</p>
                <p>{selectedDate && format(selectedDate, 'EEEE, MMMM d, yyyy')}</p>
                <p>{selectedTimeSlot && `${formatTime(selectedTimeSlot.startTime)} - ${formatTime(selectedTimeSlot.endTime)}`}</p>
                {selectedDayOfficeHours && <p>Location: {selectedDayOfficeHours.location}</p>}
              </div>
              
              <p className="text-sm text-muted-foreground">
                A confirmation email has been sent to {bookingForm.studentEmail}.
              </p>
            </div>
          </div>
          
          <Button onClick={() => setConfirmationDialogOpen(false)} className="w-full">
            Close
          </Button>
        </DialogContent>
      </Dialog>
    </div>
  )
} 