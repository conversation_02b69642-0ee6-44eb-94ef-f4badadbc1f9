'use client'

import React, { useState, useEffect } from 'react'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { 
  Ta<PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>ist, 
  Ta<PERSON>Trigger 
} from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { 
  FileText, 
  Link as LinkIcon, 
  ExternalLink, 
  Calendar,
  Users,
  BarChart,
  Search,
  ArrowUpRight,
  Bookmark,
  Hash,
  Filter
} from "lucide-react"

// In a real app, these would come from a Google Scholar API
export interface Publication {
  id: string
  title: string
  authors: string[]
  journal: string
  year: number
  citationCount: number
  link: string
  abstract?: string
  tags?: string[]
}

interface ScholarlyPublicationsProps {
  initialPublications: Publication[]
  scholarId?: string // Google Scholar ID
  isLoading?: boolean
}

export function ScholarlyPublications({ 
  initialPublications, 
  scholarId,
  isLoading = false
}: ScholarlyPublicationsProps) {
  const [publications, setPublications] = useState<Publication[]>(initialPublications)
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState<'all' | 'journal' | 'conference' | 'recent'>('all')
  const [loading, setLoading] = useState(isLoading)
  const [selectedYear, setSelectedYear] = useState<number | null>(null)
  
  // For a real implementation, this would fetch from Google Scholar API
  useEffect(() => {
    if (scholarId) {
      // Simulate API loading
      setLoading(true)
      
      // Mock API call with timeout
      const timer = setTimeout(() => {
        // In a real app, fetch data from API here
        // For now, just use initial publications
        setPublications(initialPublications)
        setLoading(false)
      }, 1500)
      
      return () => clearTimeout(timer)
    }
  }, [scholarId, initialPublications])
  
  // Get unique years for filtering
  const years = Array.from(new Set(publications.map(pub => pub.year))).sort((a, b) => b - a)
  
  // Filter publications based on search, active tab, and selected year
  const filteredPublications = publications.filter(pub => {
    const matchesSearch = searchTerm === '' || 
      pub.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pub.authors.some(author => author.toLowerCase().includes(searchTerm.toLowerCase())) ||
      pub.journal.toLowerCase().includes(searchTerm.toLowerCase())
    
    let matchesTab = true
    if (activeTab === 'recent') {
      matchesTab = pub.year >= new Date().getFullYear() - 3 // Last 3 years
    } else if (activeTab === 'journal') {
      matchesTab = !pub.journal.toLowerCase().includes('conference') &&
                   !pub.journal.toLowerCase().includes('proc.')
    } else if (activeTab === 'conference') {
      matchesTab = pub.journal.toLowerCase().includes('conference') ||
                   pub.journal.toLowerCase().includes('proc.')
    }
    
    const matchesYear = selectedYear === null || pub.year === selectedYear
    
    return matchesSearch && matchesTab && matchesYear
  })
  
  // Sort by citation count (descending)
  const sortedPublications = [...filteredPublications].sort((a, b) => 
    b.citationCount - a.citationCount
  )

  // Extract all unique tags
  const allTags = Array.from(new Set(
    publications
      .filter(pub => pub.tags)
      .flatMap(pub => pub.tags || [])
  )).slice(0, 15) // Limit to 15 tags for UI
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <h2 className="text-2xl font-semibold flex items-center">
          <Bookmark className="h-5 w-5 mr-2 text-crimson" />
          Publications
          <Badge variant="outline" className="ml-3 bg-crimson/5 text-crimson border-crimson/10">
            {publications.length}
          </Badge>
        </h2>
        
        {scholarId && (
          <a 
            href={`https://scholar.google.com/citations?user=${scholarId}`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1 text-sm text-primary hover:text-primary/80 transition-colors group"
          >
            <span>View on Google Scholar</span>
            <ArrowUpRight className="h-3.5 w-3.5 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
          </a>
        )}
      </div>
      
      <div className="flex flex-col gap-4">
        {/* Search and filters */}
        <div className="flex flex-col md:flex-row gap-4 md:items-center bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="relative md:w-80">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search publications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 border-gray-200 focus:border-crimson/30 focus:ring-crimson/20"
            />
          </div>
          
          <div className="flex-1 flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-500 whitespace-nowrap">Filter by year:</span>
            <div className="flex flex-wrap gap-1 max-w-md overflow-x-auto pb-1">
              <Badge 
                variant="outline" 
                className={`cursor-pointer hover:bg-gray-100 ${selectedYear === null ? 'bg-crimson/10 text-crimson border-crimson/20' : 'bg-transparent'}`}
                onClick={() => setSelectedYear(null)}
              >
                All
              </Badge>
              {years.map(year => (
                <Badge 
                  key={year} 
                  variant="outline"
                  className={`cursor-pointer hover:bg-gray-100 ${selectedYear === year ? 'bg-crimson/10 text-crimson border-crimson/20' : 'bg-transparent'}`}
                  onClick={() => setSelectedYear(year === selectedYear ? null : year)}
                >
                  {year}
                </Badge>
              ))}
            </div>
          </div>
        </div>
        
        <Tabs value={activeTab} onValueChange={value => setActiveTab(value as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-white border border-gray-100 p-1 rounded-xl">
            <TabsTrigger value="all" className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson rounded-lg">All</TabsTrigger>
            <TabsTrigger value="journal" className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson rounded-lg">Journal</TabsTrigger>
            <TabsTrigger value="conference" className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson rounded-lg">Conference</TabsTrigger>
            <TabsTrigger value="recent" className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson rounded-lg">Recent</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Tags cloud */}
      {allTags.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          <div className="flex items-center gap-1 mr-1">
            <Hash className="h-3.5 w-3.5 text-gray-400" />
            <span className="text-sm text-gray-500">Topics:</span>
          </div>
          {allTags.map(tag => (
            <Badge 
              key={tag} 
              variant="outline" 
              className="bg-gray-50 hover:bg-gray-100 cursor-pointer transition-colors"
              onClick={() => setSearchTerm(tag)}
            >
              {tag}
            </Badge>
          ))}
        </div>
      )}
      
      {loading ? (
        <div className="space-y-6">
          {[1, 2, 3].map(i => (
            <Card key={i} className="overflow-hidden border-none shadow-md bg-white">
              <div className="h-1 w-full bg-gradient-to-r from-gray-200 to-gray-300"></div>
              <CardContent className="p-6">
                <div className="space-y-3">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <div className="flex gap-2 pt-2">
                    <Skeleton className="h-9 w-20" />
                    <Skeleton className="h-9 w-20" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <>
          {sortedPublications.length > 0 ? (
            <div className="space-y-6">
              {sortedPublications.map((pub) => (
                <Card key={pub.id} className="group overflow-hidden hover:shadow-lg transition-shadow duration-300 border-none shadow-md bg-white">
                  <div className="h-1 w-full bg-gradient-to-r from-crimson/70 to-gold/70 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <h3 className="font-medium text-lg mb-2 group-hover:text-crimson transition-colors">{pub.title}</h3>
                      <Badge variant="outline" className="bg-crimson/5 border-crimson/10 text-crimson shrink-0 ml-4">
                        {pub.year}
                      </Badge>
                    </div>
                    
                    <div className="flex flex-wrap gap-x-4 gap-y-2 mb-3 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Users className="h-3.5 w-3.5 text-gray-400" />
                        {pub.authors.join(', ')}
                      </span>
                      <span className="flex items-center gap-1">
                        <FileText className="h-3.5 w-3.5 text-gray-400" />
                        {pub.journal}
                      </span>
                      <span className="flex items-center gap-1">
                        <BarChart className="h-3.5 w-3.5 text-gray-400" />
                        {pub.citationCount} citations
                      </span>
                    </div>
                    
                    {pub.abstract && (
                      <div className="relative mt-3 mb-4">
                        <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-crimson/30 to-gold/30 rounded-full"></div>
                        <p className="text-sm text-muted-foreground pl-4 line-clamp-2">
                          {pub.abstract}
                        </p>
                      </div>
                    )}
                    
                    {pub.tags && pub.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-4 mb-2">
                        {pub.tags.map((tag, idx) => (
                          <Badge 
                            key={idx} 
                            variant="outline" 
                            className="bg-gray-50 text-xs font-normal"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                    
                    <div className="flex flex-wrap gap-2 mt-4">
                      <a href={pub.link} target="_blank" rel="noopener noreferrer">
                        <Button variant="outline" size="sm" className="group/btn bg-white hover:bg-crimson/5 hover:text-crimson hover:border-crimson/20">
                          <ExternalLink className="mr-1 h-3.5 w-3.5 group-hover/btn:animate-pulse" />
                          View Publication
                          <ArrowUpRight className="ml-1 h-3 w-3 opacity-0 group-hover/btn:opacity-100 transition-opacity" />
                        </Button>
                      </a>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 border rounded-lg bg-white">
              <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto flex items-center justify-center mb-4">
                <FileText className="h-8 w-8 text-gray-400" />
              </div>
              <p className="text-muted-foreground mb-2">No publications found.</p>
              {searchTerm && (
                <p className="text-sm text-muted-foreground">
                  Try adjusting your search or filter criteria.
                </p>
              )}
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-4"
                onClick={() => {
                  setSearchTerm('');
                  setSelectedYear(null);
                  setActiveTab('all');
                }}
              >
                Reset Filters
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
} 