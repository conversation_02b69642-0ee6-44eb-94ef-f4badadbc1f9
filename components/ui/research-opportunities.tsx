'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, Calendar, Users, Sparkles, Clock, ArrowRight } from "lucide-react"

export interface ResearchProject {
  id: string
  title: string
  description: string
  requirements?: string[]
  timeline: string
  positions: number
  commitment: string
  isPaid: boolean
  isCredited: boolean
  tags: string[]
  status: 'recruiting' | 'ongoing' | 'completed'
  imageUrl?: string
}

interface ResearchOpportunitiesProps {
  projects: ResearchProject[]
  facultyName: string
  facultyEmail: string
}

export function ResearchOpportunities({ 
  projects,
  facultyName,
  facultyEmail 
}: ResearchOpportunitiesProps) {
  const [activeTab, setActiveTab] = useState<'recruiting' | 'ongoing' | 'all'>('recruiting')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProject, setSelectedProject] = useState<ResearchProject | null>(null)
  const [applicationDialogOpen, setApplicationDialogOpen] = useState(false)
  
  // Filter projects based on active tab and search term
  const filteredProjects = projects.filter(project => {
    const matchesTab = activeTab === 'all' || project.status === activeTab
    const matchesSearch = searchTerm === '' || 
      project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    return matchesTab && matchesSearch
  })
  
  const recruitingCount = projects.filter(p => p.status === 'recruiting').length
  const ongoingCount = projects.filter(p => p.status === 'ongoing').length
  
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold">Research Opportunities</h2>
      
      <div className="flex flex-col md:flex-row gap-4 md:items-center justify-between">
        <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)} className="w-full md:w-auto">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="recruiting">
              Recruiting ({recruitingCount})
            </TabsTrigger>
            <TabsTrigger value="ongoing">
              Ongoing ({ongoingCount})
            </TabsTrigger>
            <TabsTrigger value="all">
              All ({projects.length})
            </TabsTrigger>
          </TabsList>
        </Tabs>
        
        <div className="relative w-full md:w-80">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>
      
      {filteredProjects.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredProjects.map((project) => (
            <Card key={project.id} className="overflow-hidden hover:shadow-md transition-shadow">
              {project.imageUrl && (
                <div className="aspect-video w-full overflow-hidden">
                  <img
                    src={project.imageUrl}
                    alt={project.title}
                    className="h-full w-full object-cover"
                  />
                </div>
              )}
              <CardHeader className="p-5 pb-0">
                <div className="flex flex-wrap gap-2 mb-2">
                  <Badge variant={project.status === 'recruiting' ? 'default' : 'secondary'}>
                    {project.status === 'recruiting' ? 'Recruiting' : 'Ongoing'}
                  </Badge>
                  {project.isPaid && <Badge variant="outline">Paid</Badge>}
                  {project.isCredited && <Badge variant="outline">Credit</Badge>}
                </div>
                <CardTitle className="text-lg">{project.title}</CardTitle>
              </CardHeader>
              <CardContent className="p-5 pt-3">
                <div className="text-sm text-muted-foreground mb-4 line-clamp-3">
                  {project.description}
                </div>
                
                <div className="grid grid-cols-2 gap-2 text-sm mb-3">
                  <div className="flex items-center gap-1">
                    <Users className="h-3.5 w-3.5 text-muted-foreground" />
                    <span>{project.positions} position{project.positions !== 1 ? 's' : ''}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3.5 w-3.5 text-muted-foreground" />
                    <span>{project.commitment}</span>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-1 mb-4">
                  {project.tags.slice(0, 3).map((tag, idx) => (
                    <span
                      key={idx}
                      className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"
                    >
                      {tag}
                    </span>
                  ))}
                  {project.tags.length > 3 && (
                    <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs text-muted-foreground">
                      +{project.tags.length - 3} more
                    </span>
                  )}
                </div>
              </CardContent>
              <CardFooter className="p-5 pt-0">
                <Button
                  className="w-full"
                  variant={project.status === 'recruiting' ? 'default' : 'secondary'}
                  onClick={() => {
                    setSelectedProject(project);
                    if (project.status === 'recruiting') {
                      setApplicationDialogOpen(true);
                    }
                  }}
                >
                  {project.status === 'recruiting' ? 'Apply Now' : 'View Details'}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border rounded-lg bg-muted/20">
          <p className="text-muted-foreground">No research projects found.</p>
          {searchTerm && (
            <p className="text-sm text-muted-foreground mt-2">
              Try adjusting your search or filter criteria.
            </p>
          )}
        </div>
      )}
      
      {/* Project Details Dialog */}
      <Dialog open={selectedProject !== null && !applicationDialogOpen} onOpenChange={(open) => !open && setSelectedProject(null)}>
        <DialogContent className="sm:max-w-[650px]">
          {selectedProject && (
            <>
              <DialogHeader>
                <div className="flex flex-wrap gap-2 mb-2">
                  <Badge variant={selectedProject.status === 'recruiting' ? 'default' : 'secondary'}>
                    {selectedProject.status === 'recruiting' ? 'Recruiting' : 'Ongoing'}
                  </Badge>
                  {selectedProject.isPaid && <Badge variant="outline">Paid</Badge>}
                  {selectedProject.isCredited && <Badge variant="outline">Credit</Badge>}
                </div>
                <DialogTitle>{selectedProject.title}</DialogTitle>
                <DialogDescription>
                  Research project supervised by {facultyName}
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Description</h4>
                  <p className="text-sm">{selectedProject.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Timeline</h4>
                    <div className="flex items-center text-sm">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      {selectedProject.timeline}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Commitment</h4>
                    <div className="flex items-center text-sm">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      {selectedProject.commitment}
                    </div>
                  </div>
                </div>
                
                {selectedProject.requirements && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Requirements</h4>
                    <ul className="list-disc pl-5 text-sm space-y-1">
                      {selectedProject.requirements.map((req, idx) => (
                        <li key={idx}>{req}</li>
                      ))}
                    </ul>
                  </div>
                )}
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Research Areas</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedProject.tags.map((tag, idx) => (
                      <Badge key={idx} variant="secondary">{tag}</Badge>
                    ))}
                  </div>
                </div>
              </div>
              
              <DialogFooter>
                {selectedProject.status === 'recruiting' ? (
                  <Button onClick={() => setApplicationDialogOpen(true)}>
                    Apply for this Project
                  </Button>
                ) : (
                  <Button variant="outline" onClick={() => setSelectedProject(null)}>
                    Close
                  </Button>
                )}
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Application Dialog */}
      <Dialog open={applicationDialogOpen} onOpenChange={setApplicationDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Apply for Research Position</DialogTitle>
            <DialogDescription>
              {selectedProject?.title}
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-6 text-center">
            <div className="rounded-full mx-auto bg-primary/10 p-3 w-12 h-12 flex items-center justify-center mb-4">
              <Sparkles className="h-6 w-6 text-primary" />
            </div>
            
            <h3 className="text-lg font-medium mb-2">Ready to apply?</h3>
            <p className="text-sm text-muted-foreground mb-6">
              Please send your resume and a brief statement of interest to:
            </p>
            
            <div className="text-center p-3 bg-muted rounded-md mb-6">
              <p className="font-medium">{facultyEmail}</p>
              <p className="text-sm text-muted-foreground mt-1">
                Include "{selectedProject?.title} - Application" in the subject line.
              </p>
            </div>
            
            <p className="text-sm text-muted-foreground">
              Applications are reviewed on a rolling basis. 
              You will receive a response within 1-2 weeks.
            </p>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setApplicationDialogOpen(false);
              setSelectedProject(null);
            }}>
              Close
            </Button>
            <Button asChild>
              <a href={`mailto:${facultyEmail}?subject=${encodeURIComponent(`${selectedProject?.title} - Application`)}`}>
                Open Email Client
              </a>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 