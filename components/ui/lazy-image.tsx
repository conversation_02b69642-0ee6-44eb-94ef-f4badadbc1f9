'use client'

import React, { useState, useEffect, useRef } from 'react'
import { ImageSkeleton } from './loading-spinner'

interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string
  alt: string
  aspectRatio?: string
  className?: string
  loadingColor?: string
  fallbackSrc?: string
}

export function LazyImage({
  src,
  alt,
  aspectRatio = 'aspect-video',
  className = '',
  loadingColor = 'bg-muted/50',
  fallbackSrc = '/images/faculty/default-avatar.svg',
  ...props
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInView, setIsInView] = useState(false)
  const [imageSrc, setImageSrc] = useState(src)
  const imgRef = useRef<HTMLImageElement>(null)

  // Reset state when src prop changes
  useEffect(() => {
    setImageSrc(src)
    setIsLoaded(false)
  }, [src])

  // Handle image load and error
  const handleLoad = () => {
    setIsLoaded(true)
  }

  const handleError = () => {
    console.error(`Failed to load image: ${imageSrc}`)
    if (imageSrc !== fallbackSrc && fallbackSrc) {
      setImageSrc(fallbackSrc)
    } else {
      setIsLoaded(true) // Mark as loaded even if fallback fails
    }
  }

  useEffect(() => {
    // Set up intersection observer to detect when image is in viewport
    if (!imgRef.current) return

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        rootMargin: '200px', // Start loading when image is 200px from viewport
        threshold: 0.01,
      }
    )

    observer.observe(imgRef.current)

    return () => {
      observer.disconnect()
    }
  }, [])

  return (
    <div className={`relative overflow-hidden ${aspectRatio} ${className}`}>
      {!isLoaded && <ImageSkeleton aspectRatio="" className="absolute inset-0" />}

      <img
        ref={imgRef}
        src={isInView ? imageSrc : undefined}
        alt={alt}
        onLoad={handleLoad}
        onError={handleError}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        loading="lazy"
        {...props}
      />
    </div>
  )
}
