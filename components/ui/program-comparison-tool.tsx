'use client'

import React, { useState, useEffect } from 'react'
import { Program, programsData } from '@/lib/program-data'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  CheckCircle, X, ArrowRight, CalendarDays, BookOpen, Briefcase, 
  ListFilter, BarChart, Clock, Award, GraduationCap, Filter, School, 
  BadgeDollarSign, RefreshCw, Trash2, Sparkles
} from 'lucide-react'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

// Add custom animation class for pulse-slow
// In a production app, you'd typically add this to your tailwind.config.js
const animatePulseSlow = {
  'animation': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
};

export function ProgramComparisonTool() {
  const [selectedPrograms, setSelectedPrograms] = useState<Program[]>([])
  const [programOptions, setProgramOptions] = useState<Program[]>(programsData)
  const [filterDepartment, setFilterDepartment] = useState<string>('all')
  const [comparisonView, setComparisonView] = useState<'cards' | 'table'>('cards')
  const [highlightDifferences, setHighlightDifferences] = useState(false)

  // Get unique departments for the filter
  const departments = Array.from(
    new Set(programsData.map(program => program.departmentName))
  ).sort();

  // Handle program selection
  const handleAddProgram = (programId: string) => {
    const program = programsData.find(p => p.id === programId)
    if (program && !selectedPrograms.some(p => p.id === programId)) {
      setSelectedPrograms([...selectedPrograms, program])
    }
  }

  // Remove a program from comparison
  const handleRemoveProgram = (programId: string) => {
    setSelectedPrograms(selectedPrograms.filter(p => p.id !== programId))
  }

  // Clear all selected programs
  const clearAllPrograms = () => {
    setSelectedPrograms([])
  }

  // Filter programs by department
  const handleFilterChange = (department: string) => {
    setFilterDepartment(department)
  }

  // Update available options when selected programs change or filter changes
  useEffect(() => {
    const selectedIds = selectedPrograms.map(p => p.id)
    let filteredPrograms = programsData.filter(p => !selectedIds.includes(p.id))
    
    if (filterDepartment !== 'all') {
      filteredPrograms = filteredPrograms.filter(p => p.departmentName === filterDepartment)
    }
    
    setProgramOptions(filteredPrograms)
  }, [selectedPrograms, filterDepartment])

  // Helper function to find differences between programs
  const isDifferent = (property: string, index: number) => {
    if (!highlightDifferences || selectedPrograms.length <= 1) return false;
    
    const values = selectedPrograms.map(program => {
      if (property === 'durationYears') return program.durationYears;
      if (property === 'admissionRequirements') return program.admissionRequirements.length;
      if (property === 'careerProspects') return program.careerProspects.length;
      return null;
    });
    
    return values.some(val => val !== values[0]);
  };

  return (
    <div className="w-full animate-fade-in">
      {/* Filter & Control Bar */}
      <div className="mb-8 bg-background/80 backdrop-blur-sm rounded-lg border shadow-sm p-6 animate-fade-in">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-end justify-between">
          <div className="w-full md:w-auto flex flex-col md:flex-row gap-4">
            {/* Department Filter */}
            <div className="w-full md:w-64">
              <div className="flex items-center mb-2 gap-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <h3 className="text-sm font-medium">Filter by Department</h3>
              </div>
              <Select 
                value={filterDepartment} 
                onValueChange={handleFilterChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Departments" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {departments.map(department => (
                    <SelectItem key={department} value={department}>
                      {department}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Program Selector */}
            <div className="w-full md:w-72">
              <div className="flex items-center mb-2 gap-2">
                <School className="h-4 w-4 text-muted-foreground" />
                <h3 className="text-sm font-medium">Add Program to Compare</h3>
              </div>
              <Select 
                onValueChange={handleAddProgram}
                disabled={selectedPrograms.length >= 3 || programOptions.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a program..." />
                </SelectTrigger>
                <SelectContent>
                  {programOptions.map(program => (
                    <SelectItem key={program.id} value={program.id}>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="font-normal">
                          {program.degree}
                        </Badge>
                        {program.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2 items-center">
            {/* View Toggle */}
            <div className="flex items-center rounded-md border shadow-sm">
              <Button 
                variant={comparisonView === 'cards' ? 'secondary' : 'ghost'}
                size="sm"
                className="rounded-r-none border-r"
                onClick={() => setComparisonView('cards')}
              >
                <ListFilter className="h-4 w-4 mr-2" />
                Cards
              </Button>
              <Button 
                variant={comparisonView === 'table' ? 'secondary' : 'ghost'}
                size="sm"
                className="rounded-l-none"
                onClick={() => setComparisonView('table')}
              >
                <BarChart className="h-4 w-4 mr-2" />
                Table
              </Button>
            </div>

            {selectedPrograms.length > 0 && (
              <>
                {/* Highlight Differences Toggle */}
                <Button 
                  variant={highlightDifferences ? "secondary" : "outline"}
                  size="sm"
                  className="h-9"
                  onClick={() => setHighlightDifferences(!highlightDifferences)}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Highlight Differences
                </Button>
                
                {/* Reset Button */}
                <Button 
                  variant="outline"
                  size="sm"
                  className="h-9 border-destructive/30 hover:bg-destructive/10 hover:text-destructive"
                  onClick={clearAllPrograms}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All
                </Button>
              </>
            )}
          </div>
        </div>
        
        <div className="mt-4 pt-3 border-t">
          <div className="text-sm text-muted-foreground">
            {selectedPrograms.length === 0 ? (
              <p>Select up to 3 programs to compare their features side by side</p>
            ) : selectedPrograms.length >= 3 ? (
              <div className="flex items-center">
                <Badge variant="secondary" className="mr-2">Maximum</Badge>
                <p>3 programs selected</p>
              </div>
            ) : (
              <div className="flex items-center">
                <Badge variant="secondary" className="mr-2">Selected</Badge>
                <p>{selectedPrograms.length} of 3 programs</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {selectedPrograms.length > 0 ? (
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="mb-8 bg-background/90 border shadow-sm p-1 rounded-lg">
            <TabsTrigger value="overview" className="flex items-center gap-1.5 py-3 data-[state=active]:shadow-md">
              <GraduationCap className="h-4 w-4" />
              <span className="hidden sm:inline">Program</span> Overview
            </TabsTrigger>
            <TabsTrigger value="curriculum" className="flex items-center gap-1.5 py-3 data-[state=active]:shadow-md">
              <BookOpen className="h-4 w-4" />
              Curriculum
            </TabsTrigger>
            <TabsTrigger value="admission" className="flex items-center gap-1.5 py-3 data-[state=active]:shadow-md">
              <Award className="h-4 w-4" />
              Admission
            </TabsTrigger>
            <TabsTrigger value="careers" className="flex items-center gap-1.5 py-3 data-[state=active]:shadow-md">
              <Briefcase className="h-4 w-4" />
              Career Paths
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="mt-0">
            {comparisonView === 'cards' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {selectedPrograms.map((program, index) => {
                  // Dynamic styles based on department
                  const deptColor = program.departmentId === 'computer-science' ? 'blue' : 
                                 program.departmentId === 'business' ? 'emerald' : 
                                 program.departmentId === 'agriculture' ? 'green' :
                                 'indigo';
                  
                  const gradientStyle = `bg-gradient-to-r from-${deptColor}-500 to-${deptColor}-400`;
                  const lightBgStyle = `bg-${deptColor}-50`;
                  const borderStyle = `border-${deptColor}-200`;
                  const textStyle = `text-${deptColor}-700`;
                  
                  return (
                    <Card 
                      key={program.id} 
                      className="relative overflow-hidden transition-all duration-300 hover:shadow-xl group animate-fade-in border-0" 
                      style={{ 
                        animationDelay: `${index * 150}ms`,
                        background: `linear-gradient(to bottom right, rgb(255, 255, 255), ${program.departmentId === 'computer-science' ? 'rgba(239, 246, 255, 0.7)' : 
                                                    program.departmentId === 'business' ? 'rgba(236, 253, 245, 0.7)' : 
                                                    'rgba(240, 253, 244, 0.7)'})`
                      }}
                    >
                      {/* Gradient Header Bar */}
                      <div className={`absolute top-0 left-0 w-full h-2 ${
                        program.departmentId === 'computer-science' ? 'bg-gradient-to-r from-blue-500 to-blue-400' : 
                        program.departmentId === 'business' ? 'bg-gradient-to-r from-emerald-500 to-emerald-400' : 
                        program.departmentId === 'agriculture' ? 'bg-gradient-to-r from-green-500 to-green-400' : 
                        'bg-gradient-to-r from-indigo-500 to-indigo-400'
                      }`} />
                      
                      {/* Decorative elements */}
                      <div className={`absolute -right-12 -top-12 h-24 w-24 rounded-full ${
                        program.departmentId === 'computer-science' ? 'bg-blue-100' : 
                        program.departmentId === 'business' ? 'bg-emerald-100' : 
                        program.departmentId === 'agriculture' ? 'bg-green-100' : 
                        'bg-indigo-100'
                      } opacity-50 group-hover:opacity-80 transition-opacity duration-500 blur-xl`} />
                      
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="absolute top-3 right-3 h-8 w-8 rounded-full opacity-80 hover:opacity-100 hover:bg-white/80 z-20" 
                        onClick={() => handleRemoveProgram(program.id)}
                      >
                        <X className={`h-4 w-4 ${
                          program.departmentId === 'computer-science' ? 'text-blue-500' : 
                          program.departmentId === 'business' ? 'text-emerald-500' : 
                          program.departmentId === 'agriculture' ? 'text-green-500' : 
                          'text-indigo-500'
                        }`} />
                      </Button>
                      
                      <CardHeader className="pb-2 relative">
                        <div className="flex items-center gap-2 mb-2">
                          <div className={`h-11 w-11 rounded-lg flex items-center justify-center ${
                            program.departmentId === 'computer-science' ? 'bg-blue-100' : 
                            program.departmentId === 'business' ? 'bg-emerald-100' : 
                            program.departmentId === 'agriculture' ? 'bg-green-100' : 
                            'bg-indigo-100'
                          } group-hover:scale-110 transition-transform duration-300`}>
                            {program.icon && React.isValidElement(program.icon) &&
                              React.cloneElement(program.icon as React.ReactElement, { 
                                className: `h-6 w-6 ${
                                  program.departmentId === 'computer-science' ? 'text-blue-600' : 
                                  program.departmentId === 'business' ? 'text-emerald-600' : 
                                  program.departmentId === 'agriculture' ? 'text-green-600' : 
                                  'text-indigo-600'
                                }`
                              } as any)
                            }
                          </div>
                          <div className="flex flex-col">
                            <div className="flex gap-2 flex-wrap">
                              <Badge variant="outline" className={`font-medium ${
                                program.departmentId === 'computer-science' ? 'border-blue-200 text-blue-700 bg-blue-50' : 
                                program.departmentId === 'business' ? 'border-emerald-200 text-emerald-700 bg-emerald-50' : 
                                program.departmentId === 'agriculture' ? 'border-green-200 text-green-700 bg-green-50' : 
                                'border-indigo-200 text-indigo-700 bg-indigo-50'
                              }`}>
                                {program.degree}
                              </Badge>
                              <Badge variant="secondary" className="font-normal text-xs">
                                {program.departmentName.split(' ').pop()}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        
                        <CardTitle className={`text-xl ${
                          program.departmentId === 'computer-science' ? 'text-blue-950' : 
                          program.departmentId === 'business' ? 'text-emerald-950' : 
                          program.departmentId === 'agriculture' ? 'text-green-950' : 
                          'text-indigo-950'
                        }`}>{program.name}</CardTitle>
                        <CardDescription className="text-base mt-1">{program.tagline}</CardDescription>
                      </CardHeader>
                      
                      <CardContent className="pb-4">
                        <div className="flex flex-wrap gap-3 mb-4">
                          <div className={cn(
                            "flex items-center p-2 rounded-md text-sm",
                            isDifferent('durationYears', index) 
                              ? "bg-amber-50 border-l-4 border-amber-400" 
                              : program.departmentId === 'computer-science' ? 'bg-blue-50/70 border border-blue-100' : 
                                program.departmentId === 'business' ? 'bg-emerald-50/70 border border-emerald-100' : 
                                program.departmentId === 'agriculture' ? 'bg-green-50/70 border border-green-100' : 
                                'bg-indigo-50/70 border border-indigo-100'
                          )}>
                            <Clock className={`h-4 w-4 mr-2 ${
                              program.departmentId === 'computer-science' ? 'text-blue-500' : 
                              program.departmentId === 'business' ? 'text-emerald-500' : 
                              program.departmentId === 'agriculture' ? 'text-green-500' : 
                              'text-indigo-500'
                            }`} />
                            <span className="font-medium">{program.durationYears} Years</span>
                          </div>
                          
                          <div className={`flex items-center p-2 rounded-md text-sm ${
                            program.departmentId === 'computer-science' ? 'bg-blue-50/70 border border-blue-100' : 
                            program.departmentId === 'business' ? 'bg-emerald-50/70 border border-emerald-100' : 
                            program.departmentId === 'agriculture' ? 'bg-green-50/70 border border-green-100' : 
                            'bg-indigo-50/70 border border-indigo-100'
                          }`}>
                            <BadgeDollarSign className={`h-4 w-4 mr-2 ${
                              program.departmentId === 'computer-science' ? 'text-blue-500' : 
                              program.departmentId === 'business' ? 'text-emerald-500' : 
                              program.departmentId === 'agriculture' ? 'text-green-500' : 
                              'text-indigo-500'
                            }`} />
                            <span className="font-medium">Tuition: Contact Us</span>
                          </div>
                        </div>
                        
                        <p className="text-sm line-clamp-3 mb-5 leading-relaxed">{program.description}</p>
                        
                        <div className="flex items-center gap-3 mb-3">
                          <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                            program.departmentId === 'computer-science' ? 'bg-blue-100' : 
                            program.departmentId === 'business' ? 'bg-emerald-100' : 
                            program.departmentId === 'agriculture' ? 'bg-green-100' : 
                            'bg-indigo-100'
                          }`}>
                            <BookOpen className={`h-4 w-4 ${
                              program.departmentId === 'computer-science' ? 'text-blue-600' : 
                              program.departmentId === 'business' ? 'text-emerald-600' : 
                              program.departmentId === 'agriculture' ? 'text-green-600' : 
                              'text-indigo-600'
                            }`} />
                          </div>
                          <div className="flex flex-col">
                            <Badge variant="outline" className={`font-normal ${
                              program.departmentId === 'computer-science' ? 'border-blue-200 text-blue-700 bg-blue-50' : 
                              program.departmentId === 'business' ? 'border-emerald-200 text-emerald-700 bg-emerald-50' : 
                              program.departmentId === 'agriculture' ? 'border-green-200 text-green-700 bg-green-50' : 
                              'border-indigo-200 text-indigo-700 bg-indigo-50'
                            }`}>
                              {program.curriculumHighlights[0].courses.length}+ Courses
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                      
                      <CardFooter className="flex justify-end pt-0 border-t border-gray-100">
                        <Link href={`/programs/${program.slug}`} className="w-full">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className={`w-full group-hover:bg-gradient-to-r group-hover:text-white transition-all ${
                              program.departmentId === 'computer-science' ? 'group-hover:from-blue-600 group-hover:to-blue-500 group-hover:border-blue-600' : 
                              program.departmentId === 'business' ? 'group-hover:from-emerald-600 group-hover:to-emerald-500 group-hover:border-emerald-600' : 
                              program.departmentId === 'agriculture' ? 'group-hover:from-green-600 group-hover:to-green-500 group-hover:border-green-600' : 
                              'group-hover:from-indigo-600 group-hover:to-indigo-500 group-hover:border-indigo-600'
                            }`}
                          >
                            View Program Details <ArrowRight className="ml-1 h-3.5 w-3.5 group-hover:translate-x-1 transition-transform" />
                          </Button>
                        </Link>
                      </CardFooter>
                    </Card>
                  );
                })}
              </div>
            ) : (
              <div className="overflow-x-auto rounded-lg border shadow-sm animate-fade-in">
                <table className="w-full min-w-[800px] text-sm">
                  <thead>
                    <tr className="bg-muted/50">
                      <th className="text-left p-3 font-medium">Program</th>
                      <th className="text-left p-3 font-medium">Department</th>
                      <th className="text-left p-3 font-medium">Degree</th>
                      <th className="text-left p-3 font-medium">Duration</th>
                      <th className="text-left p-3 font-medium">Description</th>
                      <th className="text-left p-3 font-medium">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedPrograms.map((program, index) => (
                      <tr key={program.id} className="border-t hover:bg-muted/20">
                        <td className="p-3">
                          <div className="flex items-center gap-2">
                            {program.icon && React.isValidElement(program.icon) &&
                              React.cloneElement(program.icon as React.ReactElement, { className: "h-5 w-5 text-primary" } as any)
                            }
                            <div>
                              <div className="font-medium">{program.name}</div>
                              <div className="text-xs text-muted-foreground">{program.tagline}</div>
                            </div>
                          </div>
                        </td>
                        <td className="p-3">{program.departmentName}</td>
                        <td className="p-3">
                          <Badge variant="outline">{program.degree}</Badge>
                        </td>
                        <td className={cn(
                          "p-3",
                          isDifferent('durationYears', index) && "bg-amber-50"
                        )}>
                          {program.durationYears} Years
                        </td>
                        <td className="p-3 max-w-xs">
                          <p className="line-clamp-2">{program.description}</p>
                        </td>
                        <td className="p-3">
                          <div className="flex gap-2">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleRemoveProgram(program.id)}
                              className="h-8 w-8 p-0"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                            <Link href={`/programs/${program.slug}`}>
                              <Button variant="outline" size="sm">View</Button>
                            </Link>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </TabsContent>

          {/* Curriculum Tab */}
          <TabsContent value="curriculum" className="mt-0">
            {comparisonView === 'cards' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {selectedPrograms.map((program, index) => (
                  <Card key={program.id} className="relative transition-all hover:shadow-lg animate-fade-in" style={{ animationDelay: `${index * 150}ms` }}>
                    <div className={`absolute top-0 left-0 w-full h-1 ${
                      program.departmentId === 'computer-science' ? 'bg-blue-500' : 
                      program.departmentId === 'business' ? 'bg-emerald-500' : 
                      program.departmentId === 'agriculture' ? 'bg-green-500' : 
                      'bg-primary'
                    }`} />
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="absolute top-2 right-2 h-8 w-8 rounded-full" 
                      onClick={() => handleRemoveProgram(program.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2 mb-1">
                        <BookOpen className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg">{program.name}</CardTitle>
                      </div>
                      <CardDescription>Program Curriculum</CardDescription>
                    </CardHeader>
                    <CardContent className="pb-4">
                      <ScrollArea className="h-80 pr-4">
                        {program.curriculumHighlights.map((highlight, idx) => (
                          <div key={idx} className="mb-6">
                            <div className="flex items-center gap-2 mb-3">
                              <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                              <h4 className="text-sm font-semibold">{highlight.title}</h4>
                            </div>
                            <ul className="space-y-2 pl-4">
                              {highlight.courses.map((course, courseIdx) => (
                                <li key={courseIdx} className="flex text-sm">
                                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                                  <span>{course}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        ))}
                      </ScrollArea>
                    </CardContent>
                    <CardFooter className="pt-0 justify-end border-t mt-2">
                      <Link href={`/programs/${program.slug}`}>
                        <Button variant="outline" size="sm">
                          Full Curriculum <ArrowRight className="ml-1 h-3.5 w-3.5" />
                        </Button>
                      </Link>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto rounded-lg border shadow-sm animate-fade-in">
                <table className="w-full min-w-[800px] text-sm">
                  <thead>
                    <tr className="bg-muted/50">
                      <th className="text-left p-3 font-medium">Program</th>
                      {selectedPrograms.map(program => (
                        <th key={program.id} className="text-left p-3 font-medium">
                          <div className="flex items-center justify-between">
                            <span>{program.shortName || program.name}</span>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleRemoveProgram(program.id)}
                              className="h-7 w-7 p-0 rounded-full"
                            >
                              <X className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t hover:bg-muted/20">
                      <td className="p-3 font-medium">Key Focus Areas</td>
                      {selectedPrograms.map(program => (
                        <td key={program.id} className="p-3">
                          {program.curriculumHighlights.map(highlight => highlight.title).join(', ')}
                        </td>
                      ))}
                    </tr>
                    <tr className="border-t hover:bg-muted/20">
                      <td className="p-3 font-medium">Course Count</td>
                      {selectedPrograms.map(program => (
                        <td key={program.id} className="p-3">
                          {program.curriculumHighlights.reduce(
                            (total, highlight) => total + highlight.courses.length, 0
                          )}+ courses
                        </td>
                      ))}
                    </tr>
                    <tr className="border-t hover:bg-muted/20">
                      <td className="p-3 font-medium">Sample Courses</td>
                      {selectedPrograms.map(program => (
                        <td key={program.id} className="p-3">
                          <ul className="list-disc pl-4 space-y-1">
                            {program.curriculumHighlights[0].courses.slice(0, 3).map((course, idx) => (
                              <li key={idx}>{course}</li>
                            ))}
                            {program.curriculumHighlights[0].courses.length > 3 && (
                              <li className="text-muted-foreground">And more...</li>
                            )}
                          </ul>
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            )}
          </TabsContent>
          
          {/* Admission Tab */}
          <TabsContent value="admission" className="mt-0">
            {comparisonView === 'cards' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {selectedPrograms.map((program, index) => (
                  <Card key={program.id} className="relative transition-all hover:shadow-lg animate-fade-in" style={{ animationDelay: `${index * 150}ms` }}>
                    <div className={`absolute top-0 left-0 w-full h-1 ${
                      program.departmentId === 'computer-science' ? 'bg-blue-500' : 
                      program.departmentId === 'business' ? 'bg-emerald-500' : 
                      program.departmentId === 'agriculture' ? 'bg-green-500' : 
                      'bg-primary'
                    }`} />
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="absolute top-2 right-2 h-8 w-8 rounded-full" 
                      onClick={() => handleRemoveProgram(program.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">{program.name}</CardTitle>
                      <CardDescription>Admission Requirements</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className={cn(
                        "mb-4 p-2 rounded-md bg-muted/50 text-sm",
                        isDifferent('admissionRequirements', index) && "bg-amber-50 border-l-2 border-amber-400"
                      )}>
                        <div className="flex items-center gap-2">
                          <Badge>{program.admissionRequirements.length} requirements</Badge>
                        </div>
                      </div>
                      <ul className="space-y-3">
                        {program.admissionRequirements.map((req, idx) => (
                          <li key={idx} className="flex text-sm">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span>{req}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                    <CardFooter className="pt-0 justify-end border-t mt-4">
                      <Button variant="secondary" size="sm">
                        Apply Now <ArrowRight className="ml-1 h-3.5 w-3.5" />
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto rounded-lg border shadow-sm animate-fade-in">
                <table className="w-full min-w-[800px] text-sm">
                  <thead>
                    <tr className="bg-muted/50">
                      <th className="text-left p-3 font-medium">Requirement</th>
                      {selectedPrograms.map(program => (
                        <th key={program.id} className="text-left p-3 font-medium">
                          <div className="flex items-center justify-between">
                            <span>{program.shortName || program.name}</span>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleRemoveProgram(program.id)}
                              className="h-7 w-7 p-0 rounded-full"
                            >
                              <X className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {/* Create rows for each possible requirement, merging similar ones */}
                    {(() => {
                      // Get all unique requirements across programs
                      const allRequirements = new Set<string>();
                      selectedPrograms.forEach(program => {
                        program.admissionRequirements.forEach(req => {
                          allRequirements.add(req);
                        });
                      });
                      
                      return Array.from(allRequirements).map((req, idx) => (
                        <tr key={idx} className="border-t hover:bg-muted/20">
                          <td className="p-3 font-medium">{req}</td>
                          {selectedPrograms.map(program => (
                            <td key={program.id} className="p-3">
                              {program.admissionRequirements.includes(req) ? 
                                <CheckCircle className="h-5 w-5 text-green-500" /> : 
                                <span className="text-muted-foreground">Not required</span>
                              }
                            </td>
                          ))}
                        </tr>
                      ));
                    })()}
                  </tbody>
                </table>
              </div>
            )}
          </TabsContent>

          {/* Careers Tab */}
          <TabsContent value="careers" className="mt-0">
            {comparisonView === 'cards' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {selectedPrograms.map((program, index) => (
                  <Card key={program.id} className="relative transition-all hover:shadow-lg animate-fade-in" style={{ animationDelay: `${index * 150}ms` }}>
                    <div className={`absolute top-0 left-0 w-full h-1 ${
                      program.departmentId === 'computer-science' ? 'bg-blue-500' : 
                      program.departmentId === 'business' ? 'bg-emerald-500' : 
                      program.departmentId === 'agriculture' ? 'bg-green-500' : 
                      'bg-primary'
                    }`} />
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="absolute top-2 right-2 h-8 w-8 rounded-full" 
                      onClick={() => handleRemoveProgram(program.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Briefcase className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg">{program.name}</CardTitle>
                      </div>
                      <CardDescription>Career Prospects</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className={cn(
                        "mb-4 p-2 rounded-md bg-muted/50 text-sm",
                        isDifferent('careerProspects', index) && "bg-amber-50 border-l-2 border-amber-400"
                      )}>
                        <div className="flex items-center gap-2">
                          <Badge>{program.careerProspects.length} career paths</Badge>
                        </div>
                      </div>
                      <ul className="space-y-2">
                        {program.careerProspects.map((career, idx) => (
                          <li key={idx} className="flex text-sm">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span>{career}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto rounded-lg border shadow-sm animate-fade-in">
                <table className="w-full min-w-[800px] text-sm">
                  <thead>
                    <tr className="bg-muted/50">
                      <th className="text-left p-3 font-medium">Program</th>
                      <th className="text-left p-3 font-medium">Career Paths</th>
                      <th className="text-left p-3 font-medium">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedPrograms.map(program => (
                      <tr key={program.id} className="border-t hover:bg-muted/20">
                        <td className="p-3 font-medium">
                          <div className="flex items-center gap-2">
                            <Briefcase className="h-4 w-4 text-muted-foreground" />
                            {program.name}
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex flex-wrap gap-2">
                            {program.careerProspects.map((career, idx) => (
                              <Badge key={idx} variant="outline" className="font-normal">
                                {career}
                              </Badge>
                            ))}
                          </div>
                        </td>
                        <td className="p-3">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleRemoveProgram(program.id)}
                            className="h-8 w-8 p-0"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </TabsContent>
        </Tabs>
      ) : (
        <Card className="w-full bg-background animate-fade-in overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-background to-purple-50 z-0"></div>
          <div className="absolute inset-0 overflow-hidden z-0">
            <div className="absolute right-1/4 top-1/4 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl" />
            <div className="absolute left-1/3 bottom-1/4 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl" />
          </div>
          
          <CardContent className="flex flex-col items-center justify-center py-32 relative z-10">
            <div className="text-center max-w-xl mx-auto">
              <div className="relative mb-8">
                <div className="absolute -inset-1.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full blur-md opacity-75 animate-pulse-slow"></div>
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-5 rounded-full relative">
                  <School className="h-12 w-12 text-white" />
                </div>
              </div>
              
              <h3 className="text-3xl font-bold mb-4 bg-gradient-to-r from-blue-700 to-purple-700 bg-clip-text text-transparent">No Programs Selected</h3>
              <p className="text-lg text-muted-foreground mb-10 max-w-md">
                Select programs above to start comparing their features, curriculum, 
                admission requirements, and career opportunities side by side.
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
                {['computer-science', 'business', 'agriculture'].map((dept, index) => {
                  const firstProgram = programsData.find(p => p.departmentId === dept);
                  if (!firstProgram) return null;
                  
                  const bgColor = dept === 'computer-science' ? 'bg-blue-500' : 
                                  dept === 'business' ? 'bg-emerald-500' : 'bg-green-500';
                  
                  return (
                    <div 
                      key={dept}
                      className="group relative transition-all duration-300 animate-fade-in"
                      style={{ animationDelay: `${index * 150}ms` }}
                    >
                      <div className={`absolute inset-0 ${bgColor} opacity-5 rounded-lg group-hover:opacity-10 transition-opacity`}></div>
                      <Button 
                        variant="outline" 
                        onClick={() => handleAddProgram(firstProgram.id)}
                        className="relative w-full h-full py-6 shadow-sm group-hover:shadow-md transition-all border-2 rounded-lg flex flex-col items-center justify-center gap-3"
                      >
                        <div className={`h-12 w-12 rounded-full ${bgColor} bg-opacity-10 flex items-center justify-center group-hover:scale-110 transition-transform`}>
                          {firstProgram.icon && React.cloneElement(firstProgram.icon as React.ReactElement, { 
                            className: `h-6 w-6 ${
                              dept === 'computer-science' ? 'text-blue-500' : 
                              dept === 'business' ? 'text-emerald-500' : 'text-green-500'
                            }` 
                          } as any)}
                        </div>
                        <span className="font-medium">{firstProgram.departmentName.split(' ').pop()} Programs</span>
                      </Button>
                    </div>
                  );
                })}
              </div>
              
              <Button
                variant="default"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-md hover:shadow-lg transition-all"
                onClick={() => {
                  const randomProgram = programsData[Math.floor(Math.random() * programsData.length)];
                  handleAddProgram(randomProgram.id);
                }}
              >
                <Sparkles className="mr-2 h-4 w-4" /> Suggest a Random Program
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 