'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { Di<PERSON>, DialogContent } from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { X, ChevronLeft, ChevronRight, ZoomIn } from "lucide-react"
import { motion, AnimatePresence } from 'framer-motion'

interface Photo {
  url: string
  alt: string
  caption?: string
  type: 'headshot' | 'teaching' | 'research' | 'events'
}

interface FacultyPhotoGalleryProps {
  photos: Photo[]
  facultyName: string
}

export function FacultyPhotoGallery({ photos, facultyName }: FacultyPhotoGalleryProps) {
  const [activeTab, setActiveTab] = useState<string>('all')
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null)
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState<number>(0)
  
  // Filter photos based on active tab
  const filteredPhotos = activeTab === 'all' 
    ? photos 
    : photos.filter(photo => photo.type === activeTab)
  
  // Open lightbox with selected photo
  const openLightbox = (photo: Photo) => {
    setSelectedPhoto(photo)
    setCurrentPhotoIndex(filteredPhotos.indexOf(photo))
  }
  
  // Navigate to previous photo in lightbox
  const prevPhoto = () => {
    if (currentPhotoIndex > 0) {
      setCurrentPhotoIndex(currentPhotoIndex - 1)
      setSelectedPhoto(filteredPhotos[currentPhotoIndex - 1])
    }
  }
  
  // Navigate to next photo in lightbox
  const nextPhoto = () => {
    if (currentPhotoIndex < filteredPhotos.length - 1) {
      setCurrentPhotoIndex(currentPhotoIndex + 1)
      setSelectedPhoto(filteredPhotos[currentPhotoIndex + 1])
    }
  }
  
  // Count photos by type for tabs
  const photoCounts = {
    headshot: photos.filter(p => p.type === 'headshot').length,
    teaching: photos.filter(p => p.type === 'teaching').length,
    research: photos.filter(p => p.type === 'research').length,
    events: photos.filter(p => p.type === 'events').length,
  }
  
  // Get photos by type
  const getTypeLabel = (type: string): string => {
    switch (type) {
      case 'headshot': return 'Headshots'
      case 'teaching': return 'Teaching'
      case 'research': return 'Research'
      case 'events': return 'Events'
      default: return 'All Photos'
    }
  }
  
  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold">Photo Gallery</h3>
          <TabsList>
            <TabsTrigger value="all">
              All ({photos.length})
            </TabsTrigger>
            {Object.entries(photoCounts).map(([type, count]) => 
              count > 0 && (
                <TabsTrigger key={type} value={type}>
                  {getTypeLabel(type)} ({count})
                </TabsTrigger>
              )
            )}
          </TabsList>
        </div>
        
        <TabsContent value={activeTab} className="mt-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {filteredPhotos.map((photo, index) => (
              <motion.div
                key={`${photo.url}-${index}`}
                className="relative cursor-pointer group overflow-hidden rounded-lg border bg-card text-card-foreground shadow"
                whileHover={{ y: -5 }}
                onClick={() => openLightbox(photo)}
              >
                <div className="aspect-square relative overflow-hidden">
                  <img 
                    src={photo.url} 
                    alt={photo.alt} 
                    className="object-cover w-full h-full transition-transform group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <ZoomIn className="h-8 w-8 text-white" />
                  </div>
                </div>
                {photo.caption && (
                  <div className="p-2">
                    <p className="text-xs text-muted-foreground">{photo.caption}</p>
                  </div>
                )}
                <div className="absolute top-2 right-2">
                  <div className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                    {getTypeLabel(photo.type)}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
          
          {filteredPhotos.length === 0 && (
            <div className="text-center py-12 border rounded-lg bg-muted/20">
              <p className="text-muted-foreground">No photos available in this category</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
      
      {/* Lightbox dialog */}
      <Dialog open={!!selectedPhoto} onOpenChange={(open) => !open && setSelectedPhoto(null)}>
        <DialogContent className="max-w-4xl p-0 bg-background/95 backdrop-blur-sm">
          <div className="relative">
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 z-10 rounded-full bg-background/80 hover:bg-background"
              onClick={() => setSelectedPhoto(null)}
            >
              <X className="h-4 w-4" />
            </Button>
            
            {selectedPhoto && (
              <div className="flex flex-col">
                <div className="relative aspect-video md:aspect-[16/9] overflow-hidden">
                  <img
                    src={selectedPhoto.url}
                    alt={selectedPhoto.alt}
                    className="w-full h-full object-contain"
                  />
                  
                  {/* Navigation buttons */}
                  <div className="absolute inset-0 flex items-center justify-between p-4">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-full bg-background/60 hover:bg-background/80"
                      onClick={(e) => {
                        e.stopPropagation()
                        prevPhoto()
                      }}
                      disabled={currentPhotoIndex === 0}
                    >
                      <ChevronLeft className="h-6 w-6" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-full bg-background/60 hover:bg-background/80"
                      onClick={(e) => {
                        e.stopPropagation()
                        nextPhoto()
                      }}
                      disabled={currentPhotoIndex === filteredPhotos.length - 1}
                    >
                      <ChevronRight className="h-6 w-6" />
                    </Button>
                  </div>
                </div>
                
                {/* Caption and metadata */}
                <div className="p-4">
                  <h3 className="font-medium">
                    {facultyName} - {getTypeLabel(selectedPhoto.type)}
                  </h3>
                  {selectedPhoto.caption && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {selectedPhoto.caption}
                    </p>
                  )}
                  <div className="text-xs text-muted-foreground mt-3">
                    Photo {currentPhotoIndex + 1} of {filteredPhotos.length}
                  </div>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
} 