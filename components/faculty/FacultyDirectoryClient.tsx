"use client"

import { useState, useCallback, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { FacultyFilters } from "@/components/ui/faculty-filters"
import { LazyImage } from "@/components/ui/lazy-image"
import { RecentlyViewedFaculty } from "@/components/ui/recently-viewed-faculty"

import { Breadcrumb } from "@/components/ui/breadcrumb"
import { cleanupLocalStorageData } from "@/lib/faculty-utils"

interface FacultyMember {
  id: string
  name: string
  title: string
  department: string
  departmentSlug: string
  email: string
  phone: string
  officeLocation: string
  websiteUrl: string
  bio: string
  imageUrl: string
  altText: string
  researchAreas: string[]
  publications: string[]
  courses: string[]
  officeHours: string
  scholarId: string
}

interface Department {
  id: string
  name: string
  slug: string
}

interface FacultyDirectoryClientProps {
  facultyData: FacultyMember[]
  departments: Department[]
  researchAreas: string[]
}

export function FacultyDirectoryClient({
  facultyData,
  departments,
  researchAreas
}: FacultyDirectoryClientProps) {
  // State for filters and view mode
  const [viewMode] = useState<'grid'>('grid')
  const [filters, setFilters] = useState({
    search: '',
    department: '',
    researchArea: '',
  })
  const [showRecent, setShowRecent] = useState(true)

  // Clean up localStorage data on component mount
  useEffect(() => {
    cleanupLocalStorageData()
  }, [])

  // Helper functions for filtering
  const getUniqueDepartments = () => departments.map(d => d.name)
  const getUniqueResearchAreas = () => researchAreas

  const searchFaculty = (faculty: FacultyMember[], searchTerm: string) => {
    if (!searchTerm) return faculty
    const term = searchTerm.toLowerCase()
    return faculty.filter(f =>
      f.name.toLowerCase().includes(term) ||
      f.title.toLowerCase().includes(term) ||
      f.department.toLowerCase().includes(term) ||
      f.researchAreas.some((area: string) => area.toLowerCase().includes(term))
    )
  }

  const filterFacultyByDepartment = (faculty: FacultyMember[], department: string) => {
    if (!department) return faculty
    return faculty.filter(f => f.department === department)
  }

  const filterFacultyByResearchArea = (faculty: FacultyMember[], researchArea: string) => {
    if (!researchArea) return faculty
    return faculty.filter(f => f.researchAreas.includes(researchArea))
  }

  // Derived state
  const uniqueDepartments = getUniqueDepartments()
  const uniqueResearchAreas = getUniqueResearchAreas()

  // Apply filters
  let filteredFaculty = facultyData
  filteredFaculty = searchFaculty(filteredFaculty, filters.search)
  filteredFaculty = filterFacultyByDepartment(filteredFaculty, filters.department)
  filteredFaculty = filterFacultyByResearchArea(filteredFaculty, filters.researchArea)

  // Search suggestions
  const searchSuggestions = [
    ...uniqueDepartments.map(dept => ({ type: 'department', value: dept })),
    ...uniqueResearchAreas.map(area => ({ type: 'research', value: area })),
    ...facultyData.slice(0, 5).map(faculty => ({ type: 'faculty', value: faculty.name }))
  ]

  // Event handlers
  const handleFilterChange = useCallback((newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }, [])

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 py-4">
        <Breadcrumb
          items={[
            { label: 'Home', href: '/' },
            { label: 'Faculty', href: '/faculty' }
          ]}
        />
      </div>

      {/* Hero Section */}
      <section className="py-12 bg-gradient-to-r from-primary/10 to-secondary/10">
        <div className="container mx-auto px-4 text-center max-w-4xl">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Our Faculty
          </h1>
          <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
            Meet our distinguished faculty members who are leaders in their fields,
            dedicated to excellence in teaching, research, and service.
          </p>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{facultyData.length}</div>
              <div className="text-sm text-muted-foreground">Faculty Members</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{uniqueDepartments.length}</div>
              <div className="text-sm text-muted-foreground">Departments</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{uniqueResearchAreas.length}</div>
              <div className="text-sm text-muted-foreground">Research Areas</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {facultyData.reduce((sum, f) => sum + f.publications.length, 0)}
              </div>
              <div className="text-sm text-muted-foreground">Publications</div>
            </div>
          </div>
        </div>
      </section>

      {/* Faculty Directory with filters */}
      <section className="py-8 bg-gradient-to-b from-background to-light/50">
        <div className="container mx-auto px-4 max-w-6xl">
          {/* Filters - Streamlined */}
          <div className="mb-8">
            <FacultyFilters
              departments={uniqueDepartments}
              researchAreas={uniqueResearchAreas}
              onFilterChange={handleFilterChange}
              activeViewMode={viewMode}
              searchSuggestions={searchSuggestions}
            />
          </div>

          {/* Recently Viewed - Compact and subtle */}
          {showRecent && (
            <div className="mb-8">
              <RecentlyViewedFaculty />
            </div>
          )}

          {/* View Content Based on Selected Mode */}
          <div>
            {/* Grid View - Clean and minimalist cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-5">
              {filteredFaculty.map((faculty) => (
                <Link key={faculty.id} href={`/faculty/${faculty.id}`}>
                  <Card className="h-full border hover:shadow-sm transition-shadow overflow-hidden group">
                    <div className="aspect-[3/2] w-full overflow-hidden bg-gray-100">
                      <LazyImage
                        src={faculty.imageUrl}
                        alt={faculty.altText}
                        aspectRatio="aspect-[3/2]"
                        className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <CardContent className="p-4">
                      <div className="text-xs uppercase tracking-wide text-gray-500 mb-1">
                        {faculty.department}
                      </div>
                      <h3 className="font-semibold text-gray-900 mb-1 line-clamp-1">
                        {faculty.name}
                      </h3>
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {faculty.title}
                      </p>

                      {/* Research Areas */}
                      {faculty.researchAreas.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {faculty.researchAreas.slice(0, 2).map((area, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {area}
                            </Badge>
                          ))}
                          {faculty.researchAreas.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{faculty.researchAreas.length - 2}
                            </Badge>
                          )}
                        </div>
                      )}

                      {/* Contact Info */}
                      <div className="text-xs text-gray-500 space-y-1">
                        {faculty.officeLocation && (
                          <div>📍 {faculty.officeLocation}</div>
                        )}
                        <div>✉️ {faculty.email}</div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>

            {/* No Results */}
            {filteredFaculty.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No faculty found</h3>
                <p className="text-gray-500 mb-4">Try adjusting your search criteria or filters.</p>
                <Button
                  variant="outline"
                  onClick={() => setFilters({ search: '', department: '', researchArea: '' })}
                >
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  )
}
