"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Plus, Trash2, Check } from "lucide-react"
import { Separator } from "@/components/ui/separator"

interface IndustryExperience {
  id?: string
  company: string
  position: string
  startDate: string
  endDate?: string | null
  description?: string | null
}

interface IndustryExperienceFormProps {
  facultyId: string
  initialExperiences?: IndustryExperience[]
}

export function IndustryExperienceForm({ facultyId, initialExperiences = [] }: IndustryExperienceFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [experiences, setExperiences] = useState<IndustryExperience[]>(initialExperiences)
  const [isLoading, setIsLoading] = useState(false)

  const addNewExperience = () => {
    setExperiences([
      ...experiences, 
      { company: '', position: '', startDate: '', endDate: '', description: '' }
    ])
  }

  const removeExperience = (index: number) => {
    setExperiences(experiences.filter((_, i) => i !== index))
  }

  const updateExperience = (index: number, field: keyof IndustryExperience, value: string) => {
    const updatedExperiences = [...experiences]
    updatedExperiences[index] = {
      ...updatedExperiences[index],
      [field]: value
    }
    setExperiences(updatedExperiences)
  }

  const handleSubmit = async () => {
    try {
      setIsLoading(true)
      
      // Filter out empty experiences
      const validExperiences = experiences.filter(exp => 
        exp.company.trim() !== '' && 
        exp.position.trim() !== '' && 
        exp.startDate.trim() !== ''
      )
      
      const response = await fetch('/api/faculty/industry-experience', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          facultyId,
          experiences: validExperiences
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to save industry experience')
      }

      toast({
        title: "Success",
        description: "Industry experience updated successfully",
      })

      router.refresh()
    } catch (error) {
      console.error('Error saving industry experience:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save industry experience",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Industry Experience</CardTitle>
        <CardDescription>
          Add your industry work experience to showcase your practical expertise
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {experiences.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-gray-500 mb-4">No industry experience added yet</p>
            <Button onClick={addNewExperience} variant="outline">
              <Plus className="mr-2 h-4 w-4" />
              Add Experience
            </Button>
          </div>
        ) : (
          <>
            {experiences.map((experience, index) => (
              <div key={index} className="space-y-4">
                {index > 0 && <Separator className="my-4" />}
                
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Experience {index + 1}</h3>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => removeExperience(index)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`company-${index}`}>Company</Label>
                    <Input
                      id={`company-${index}`}
                      value={experience.company}
                      onChange={(e) => updateExperience(index, 'company', e.target.value)}
                      placeholder="e.g., Microsoft"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`position-${index}`}>Position</Label>
                    <Input
                      id={`position-${index}`}
                      value={experience.position}
                      onChange={(e) => updateExperience(index, 'position', e.target.value)}
                      placeholder="e.g., Software Engineer"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`start-date-${index}`}>Start Date</Label>
                    <Input
                      id={`start-date-${index}`}
                      value={experience.startDate}
                      onChange={(e) => updateExperience(index, 'startDate', e.target.value)}
                      placeholder="e.g., Jan 2020"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`end-date-${index}`}>End Date</Label>
                    <Input
                      id={`end-date-${index}`}
                      value={experience.endDate || ''}
                      onChange={(e) => updateExperience(index, 'endDate', e.target.value)}
                      placeholder="e.g., Dec 2022 (or 'Present')"
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor={`description-${index}`}>Description</Label>
                  <Textarea
                    id={`description-${index}`}
                    value={experience.description || ''}
                    onChange={(e) => updateExperience(index, 'description', e.target.value)}
                    rows={3}
                    placeholder="Describe your responsibilities and achievements..."
                  />
                </div>
              </div>
            ))}
            
            <Button 
              onClick={addNewExperience} 
              variant="outline" 
              className="mt-4"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Another Experience
            </Button>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-end space-x-2 border-t pt-4">
        <Button 
          onClick={handleSubmit} 
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Check className="mr-2 h-4 w-4" />
              Save Changes
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
} 