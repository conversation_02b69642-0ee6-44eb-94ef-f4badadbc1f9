"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { GraduationCap, Plus, Edit, Trash2, Award, Loader2 } from "lucide-react"

interface Education {
  id: string
  degree: string
  institution: string
  field?: string
  year?: number
  createdAt: string
}

interface EducationManagerProps {
  initialEducation: Education[]
}

export function EducationManager({ initialEducation }: EducationManagerProps) {
  const [education, setEducation] = useState<Education[]>(initialEducation)
  const [isLoading, setIsLoading] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    degree: "",
    institution: "",
    field: "",
    year: ""
  })

  const { toast } = useToast()
  const router = useRouter()

  const resetForm = () => {
    setFormData({
      degree: "",
      institution: "",
      field: "",
      year: ""
    })
    setEditingId(null)
    setShowAddForm(false)
  }

  const handleEdit = (edu: Education) => {
    setFormData({
      degree: edu.degree,
      institution: edu.institution,
      field: edu.field || "",
      year: edu.year ? edu.year.toString() : ""
    })
    setEditingId(edu.id)
    setShowAddForm(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const submitData = {
        degree: formData.degree,
        institution: formData.institution,
        field: formData.field || undefined,
        year: formData.year ? parseInt(formData.year) : undefined
      }

      const url = editingId 
        ? `/api/faculty/education/${editingId}`
        : '/api/faculty/education'
      
      const method = editingId ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save education record')
      }

      const result = await response.json()

      toast({
        title: editingId ? "Education Updated" : "Education Added",
        description: `Your education record has been ${editingId ? 'updated' : 'added'} successfully.`,
      })

      // Refresh the page to show updated data
      router.refresh()
      resetForm()
    } catch (error) {
      console.error('Error saving education:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save education record. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this education record?')) {
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch(`/api/faculty/education/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete education record')
      }

      toast({
        title: "Education Deleted",
        description: "Your education record has been deleted successfully.",
      })

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error deleting education:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete education record. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Education</h1>
          <p className="text-gray-600">Manage your educational background and academic degrees</p>
        </div>
        <Button onClick={() => setShowAddForm(true)} disabled={isLoading}>
          <Plus className="w-4 h-4 mr-2" />
          Add Degree
        </Button>
      </div>

      {/* Education Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Educational Background</CardTitle>
          <CardDescription>
            Your academic degrees and educational achievements
          </CardDescription>
        </CardHeader>
        <CardContent>
          {education.length > 0 ? (
            <div className="space-y-4">
              {education.map((edu) => (
                <div key={edu.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <GraduationCap className="w-5 h-5 text-blue-600" />
                        <h3 className="font-semibold text-lg">{edu.degree}</h3>
                        {edu.year && (
                          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            {edu.year}
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 font-medium mb-1">{edu.institution}</p>
                      {edu.field && (
                        <p className="text-sm text-gray-500">
                          <strong>Field of Study:</strong> {edu.field}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleEdit(edu)}
                        disabled={isLoading}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleDelete(edu.id)}
                        disabled={isLoading}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <GraduationCap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No education records yet</h3>
              <p className="text-gray-600 mb-4">Add your academic degrees and educational background.</p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Degree
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Education Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingId ? 'Edit Degree' : 'Add New Degree'}</CardTitle>
            <CardDescription>
              {editingId ? 'Update your education details' : 'Add a new academic degree or educational achievement'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="degree">Degree *</Label>
                <Input 
                  id="degree" 
                  value={formData.degree}
                  onChange={(e) => setFormData(prev => ({ ...prev, degree: e.target.value }))}
                  placeholder="e.g., Ph.D. in Computer Science, M.S. in Engineering" 
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Include the full degree title and field of study
                </p>
              </div>

              <div>
                <Label htmlFor="institution">Institution *</Label>
                <Input 
                  id="institution" 
                  value={formData.institution}
                  onChange={(e) => setFormData(prev => ({ ...prev, institution: e.target.value }))}
                  placeholder="e.g., Stanford University, MIT, University of California" 
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="field">Field of Study</Label>
                  <Input 
                    id="field" 
                    value={formData.field}
                    onChange={(e) => setFormData(prev => ({ ...prev, field: e.target.value }))}
                    placeholder="e.g., Computer Science, Electrical Engineering" 
                  />
                </div>
                <div>
                  <Label htmlFor="year">Year Completed</Label>
                  <Input 
                    id="year" 
                    type="number" 
                    value={formData.year}
                    onChange={(e) => setFormData(prev => ({ ...prev, year: e.target.value }))}
                    placeholder="2020" 
                    min="1950" 
                    max="2030" 
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={resetForm} disabled={isLoading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                  {editingId ? 'Update Degree' : 'Add Degree'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
