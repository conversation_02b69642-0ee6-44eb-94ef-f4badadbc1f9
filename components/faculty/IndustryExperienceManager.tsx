"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { Briefcase, Plus, Edit, Trash2, Calendar, Building, Loader2 } from "lucide-react"

interface IndustryExperience {
  id: string
  company: string
  position: string
  startDate: string
  endDate?: string | null
  isCurrent: boolean
  description?: string | null
  location?: string | null
  createdAt: string
}

interface IndustryExperienceManagerProps {
  initialExperience: IndustryExperience[]
}

export function IndustryExperienceManager({ initialExperience }: IndustryExperienceManagerProps) {
  const [experience, setExperience] = useState<IndustryExperience[]>(initialExperience)
  const [isLoading, setIsLoading] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    company: "",
    position: "",
    startDate: "",
    endDate: "",
    isCurrent: false,
    description: "",
    location: ""
  })

  const { toast } = useToast()
  const router = useRouter()

  const resetForm = () => {
    setFormData({
      company: "",
      position: "",
      startDate: "",
      endDate: "",
      isCurrent: false,
      description: "",
      location: ""
    })
    setEditingId(null)
    setShowAddForm(false)
  }

  const handleEdit = (exp: IndustryExperience) => {
    setFormData({
      company: exp.company,
      position: exp.position,
      startDate: exp.startDate,
      endDate: exp.endDate || "",
      isCurrent: exp.isCurrent,
      description: exp.description || "",
      location: exp.location || ""
    })
    setEditingId(exp.id)
    setShowAddForm(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const submitData = {
        company: formData.company,
        position: formData.position,
        startDate: formData.startDate,
        endDate: formData.isCurrent ? null : formData.endDate || null,
        isCurrent: formData.isCurrent,
        description: formData.description || null,
        location: formData.location || null
      }

      const url = editingId 
        ? `/api/faculty/industry-experience/${editingId}`
        : '/api/faculty/industry-experience'
      
      const method = editingId ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save experience')
      }

      const result = await response.json()

      toast({
        title: editingId ? "Experience Updated" : "Experience Added",
        description: `Your industry experience has been ${editingId ? 'updated' : 'added'} successfully.`,
      })

      // Refresh the page to show updated data
      router.refresh()
      resetForm()
    } catch (error) {
      console.error('Error saving experience:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save experience. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this experience?')) {
      return
    }

    try {
      const response = await fetch(`/api/faculty/industry-experience/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete experience')
      }

      toast({
        title: "Experience Deleted",
        description: "Your industry experience has been deleted successfully.",
      })

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error deleting experience:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete experience. Please try again.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    })
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Industry Experience</h1>
          <p className="text-gray-600">Manage your professional work experience and industry background</p>
        </div>
        <Button onClick={() => setShowAddForm(true)} disabled={isLoading}>
          <Plus className="w-4 h-4 mr-2" />
          Add Experience
        </Button>
      </div>

      {/* Experience Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Professional Experience</CardTitle>
          <CardDescription>
            Your industry work experience and professional background
          </CardDescription>
        </CardHeader>
        <CardContent>
          {experience.length > 0 ? (
            <div className="space-y-4">
              {experience.map((exp) => (
                <div key={exp.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <Briefcase className="w-5 h-5 text-blue-600" />
                        <h3 className="font-semibold text-lg">{exp.position}</h3>
                        {exp.isCurrent && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            Current
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                        <div className="flex items-center">
                          <Building className="w-4 h-4 mr-1" />
                          {exp.company}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {formatDate(exp.startDate)} - {exp.isCurrent ? 'Present' : (exp.endDate ? formatDate(exp.endDate) : 'Present')}
                        </div>
                        {exp.location && (
                          <div>
                            📍 {exp.location}
                          </div>
                        )}
                      </div>
                      {exp.description && (
                        <p className="text-sm text-gray-700 mt-2">{exp.description}</p>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(exp)}
                        disabled={isLoading}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(exp.id)}
                        disabled={isLoading}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Briefcase className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No industry experience added</h3>
              <p className="text-gray-500 mb-4">Add your professional work experience to showcase your industry background</p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Experience
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Experience Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingId ? 'Edit Experience' : 'Add New Experience'}</CardTitle>
            <CardDescription>
              {editingId ? 'Update your industry experience details' : 'Add a new professional work experience'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="position">Position *</Label>
                  <Input
                    id="position"
                    value={formData.position}
                    onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value }))}
                    placeholder="e.g., Senior Software Engineer, Data Scientist"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="company">Company *</Label>
                  <Input
                    id="company"
                    value={formData.company}
                    onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                    placeholder="e.g., Google, Microsoft, Apple"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate">Start Date *</Label>
                  <Input
                    id="startDate"
                    type="month"
                    value={formData.startDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="month"
                    value={formData.endDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                    disabled={formData.isCurrent}
                    placeholder={formData.isCurrent ? "Current position" : ""}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isCurrent"
                  checked={formData.isCurrent}
                  onCheckedChange={(checked) => {
                    setFormData(prev => ({
                      ...prev,
                      isCurrent: checked as boolean,
                      endDate: checked ? "" : prev.endDate
                    }))
                  }}
                />
                <Label htmlFor="isCurrent">This is my current position</Label>
              </div>

              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="e.g., San Francisco, CA or Remote"
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  rows={4}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your role, responsibilities, and key achievements..."
                />
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={resetForm} disabled={isLoading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                  {editingId ? 'Update Experience' : 'Add Experience'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
