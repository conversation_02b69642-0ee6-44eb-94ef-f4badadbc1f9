"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { Calendar, Plus, Edit, Trash2, Award, Briefcase, GraduationCap, FileText, Loader2 } from "lucide-react"

interface TimelineEvent {
  id: string
  year: string
  title: string
  description: string
  type: 'EDUCATION' | 'POSITION' | 'AWARD' | 'PUBLICATION'
  createdAt: string
}

interface TimelineManagerProps {
  initialTimeline: TimelineEvent[]
}

const eventTypeIcons = {
  EDUCATION: GraduationCap,
  POSITION: Briefcase,
  AWARD: Award,
  PUBLICATION: FileText
}

const eventTypeColors = {
  EDUCATION: 'bg-blue-100 text-blue-800',
  POSITION: 'bg-green-100 text-green-800',
  AWARD: 'bg-yellow-100 text-yellow-800',
  PUBLICATION: 'bg-purple-100 text-purple-800'
}

export function TimelineManager({ initialTimeline }: TimelineManagerProps) {
  const [timeline, setTimeline] = useState<TimelineEvent[]>(initialTimeline)
  const [isLoading, setIsLoading] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    year: "",
    title: "",
    description: "",
    type: "" as TimelineEvent['type'] | ""
  })

  const { toast } = useToast()
  const router = useRouter()

  const resetForm = () => {
    setFormData({
      year: "",
      title: "",
      description: "",
      type: ""
    })
    setEditingId(null)
    setShowAddForm(false)
  }

  const handleEdit = (event: TimelineEvent) => {
    setFormData({
      year: event.year,
      title: event.title,
      description: event.description,
      type: event.type
    })
    setEditingId(event.id)
    setShowAddForm(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const url = editingId 
        ? `/api/faculty/timeline/${editingId}`
        : '/api/faculty/timeline'
      
      const method = editingId ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save timeline event')
      }

      const result = await response.json()

      toast({
        title: editingId ? "Timeline Event Updated" : "Timeline Event Added",
        description: `Your timeline event has been ${editingId ? 'updated' : 'added'} successfully.`,
      })

      // Refresh the page to show updated data
      router.refresh()
      resetForm()
    } catch (error) {
      console.error('Error saving timeline event:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save timeline event. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this timeline event?')) {
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch(`/api/faculty/timeline/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete timeline event')
      }

      toast({
        title: "Timeline Event Deleted",
        description: "Your timeline event has been deleted successfully.",
      })

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error deleting timeline event:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete timeline event. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Career Timeline</h1>
          <p className="text-gray-600">Manage your career milestones and achievements</p>
        </div>
        <Button onClick={() => setShowAddForm(true)} disabled={isLoading}>
          <Plus className="w-4 h-4 mr-2" />
          Add Event
        </Button>
      </div>

      {/* Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Your Career Timeline</CardTitle>
          <CardDescription>
            Key milestones, achievements, and events in your academic and professional career
          </CardDescription>
        </CardHeader>
        <CardContent>
          {timeline.length > 0 ? (
            <div className="space-y-6">
              {timeline.map((event, index) => {
                const IconComponent = eventTypeIcons[event.type]
                const colorClass = eventTypeColors[event.type]
                
                return (
                  <div key={event.id} className="relative">
                    {/* Timeline line */}
                    {index < timeline.length - 1 && (
                      <div className="absolute left-6 top-12 w-0.5 h-16 bg-gray-200"></div>
                    )}
                    
                    <div className="flex items-start space-x-4">
                      {/* Timeline icon */}
                      <div className={`flex-shrink-0 w-12 h-12 rounded-full ${colorClass} flex items-center justify-center`}>
                        <IconComponent className="w-6 h-6" />
                      </div>
                      
                      {/* Event content */}
                      <div className="flex-1 border rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-semibold text-lg">{event.title}</h3>
                              <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                                {event.year}
                              </span>
                              <span className={`text-xs px-2 py-1 rounded-full ${colorClass}`}>
                                {event.type.toLowerCase().replace('_', ' ')}
                              </span>
                            </div>
                            <p className="text-gray-600 text-sm leading-relaxed">
                              {event.description}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => handleEdit(event)}
                              disabled={isLoading}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => handleDelete(event.id)}
                              disabled={isLoading}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No timeline events yet</h3>
              <p className="text-gray-600 mb-4">Start building your career timeline by adding key milestones and achievements.</p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Event
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Timeline Event Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingId ? 'Edit Timeline Event' : 'Add New Timeline Event'}</CardTitle>
            <CardDescription>
              {editingId ? 'Update your timeline event details' : 'Add a new milestone or achievement to your career timeline'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="title">Event Title *</Label>
                <Input 
                  id="title" 
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="e.g., Promoted to Associate Professor, Received Best Paper Award" 
                  required
                />
              </div>

              <div>
                <Label htmlFor="year">Year *</Label>
                <Input 
                  id="year" 
                  value={formData.year}
                  onChange={(e) => setFormData(prev => ({ ...prev, year: e.target.value }))}
                  placeholder="e.g., 2023, 2020-2022" 
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Can be a single year or a range (e.g., "2020-2022")
                </p>
              </div>

              <div>
                <Label htmlFor="type">Event Type *</Label>
                <select 
                  id="type" 
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as TimelineEvent['type'] }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Select event type</option>
                  <option value="EDUCATION">Education</option>
                  <option value="POSITION">Position/Job</option>
                  <option value="AWARD">Award/Recognition</option>
                  <option value="PUBLICATION">Publication/Research</option>
                </select>
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea 
                  id="description" 
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the event, achievement, or milestone in detail..."
                  required
                />
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={resetForm} disabled={isLoading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                  {editingId ? 'Update Event' : 'Add Event'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
