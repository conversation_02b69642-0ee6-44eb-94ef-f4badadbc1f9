"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Plus, Trash2, Check, ChevronDown } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface Skill {
  id?: string
  skillName: string
  proficiency?: string | null
  category?: string | null
}

interface SkillsFormProps {
  facultyId: string
  initialSkills?: Skill[]
}

const proficiencyLevels = ["Beginner", "Intermediate", "Advanced", "Expert"]
const skillCategories = ["Technical", "Programming Languages", "Software", "Hardware", "Research", "Teaching", "Languages", "Soft Skills"]

export function SkillsForm({ facultyId, initialSkills = [] }: SkillsFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [skills, setSkills] = useState<Skill[]>(initialSkills)
  const [isLoading, setIsLoading] = useState(false)
  const [newSkill, setNewSkill] = useState<Skill>({ skillName: '', proficiency: null, category: null })

  const addSkill = () => {
    if (newSkill.skillName.trim() === '') return
    
    setSkills([...skills, { ...newSkill }])
    setNewSkill({ skillName: '', proficiency: null, category: null })
  }

  const removeSkill = (index: number) => {
    setSkills(skills.filter((_, i) => i !== index))
  }

  const handleSubmit = async () => {
    try {
      setIsLoading(true)
      
      // Filter out empty skills
      const validSkills = skills.filter(skill => skill.skillName.trim() !== '')
      
      const response = await fetch('/api/faculty/skills', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          facultyId,
          skills: validSkills
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to save skills')
      }

      toast({
        title: "Success",
        description: "Skills updated successfully",
      })

      router.refresh()
    } catch (error) {
      console.error('Error saving skills:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save skills",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Skills</CardTitle>
        <CardDescription>
          Add your technical and professional skills to highlight your expertise
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add new skill form */}
        <div className="space-y-4">
          <div className="grid grid-cols-12 gap-4">
            <div className="col-span-5">
              <Label htmlFor="skill-name">Skill Name</Label>
              <Input
                id="skill-name"
                value={newSkill.skillName}
                onChange={(e) => setNewSkill({ ...newSkill, skillName: e.target.value })}
                placeholder="e.g., Python, Leadership, Project Management"
              />
            </div>
            <div className="col-span-3">
              <Label htmlFor="proficiency">Proficiency</Label>
              <Select
                onValueChange={(value) => setNewSkill({ ...newSkill, proficiency: value })}
                value={newSkill.proficiency || ''}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  {proficiencyLevels.map((level) => (
                    <SelectItem key={level} value={level}>
                      {level}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="col-span-3">
              <Label htmlFor="category">Category</Label>
              <Select
                onValueChange={(value) => setNewSkill({ ...newSkill, category: value })}
                value={newSkill.category || ''}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  {skillCategories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="col-span-1 flex items-end">
              <Button
                onClick={addSkill}
                disabled={!newSkill.skillName.trim()}
                type="button"
                className="w-full"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Current skills list */}
        <div className="border rounded-md p-4">
          <h3 className="text-sm font-medium mb-3">Current Skills</h3>
          
          {skills.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-gray-500">No skills added yet</p>
            </div>
          ) : (
            <div className="space-y-2">
              {skills.map((skill, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{skill.skillName}</span>
                    {skill.proficiency && (
                      <Badge variant="outline">{skill.proficiency}</Badge>
                    )}
                    {skill.category && (
                      <Badge variant="secondary">{skill.category}</Badge>
                    )}
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => removeSkill(index)}
                    className="h-7 w-7 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2 border-t pt-4">
        <Button 
          onClick={handleSubmit} 
          disabled={isLoading || skills.length === 0}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Check className="mr-2 h-4 w-4" />
              Save Changes
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
} 