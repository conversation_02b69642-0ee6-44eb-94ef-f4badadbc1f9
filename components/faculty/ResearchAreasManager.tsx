"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { Beaker, Plus, Edit, Trash2, Loader2 } from "lucide-react"

interface ResearchArea {
  id: string
  areaName: string
  description?: string
  createdAt: string
}

interface ResearchAreasManagerProps {
  initialResearchAreas: ResearchArea[]
}

export function ResearchAreasManager({ initialResearchAreas }: ResearchAreasManagerProps) {
  const [researchAreas, setResearchAreas] = useState<ResearchArea[]>(initialResearchAreas)
  const [isLoading, setIsLoading] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    areaName: "",
    description: ""
  })

  const { toast } = useToast()
  const router = useRouter()

  const resetForm = () => {
    setFormData({
      areaName: "",
      description: ""
    })
    setEditingId(null)
    setShowAddForm(false)
  }

  const handleEdit = (researchArea: ResearchArea) => {
    setFormData({
      areaName: researchArea.areaName,
      description: researchArea.description || ""
    })
    setEditingId(researchArea.id)
    setShowAddForm(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const url = editingId 
        ? `/api/faculty/research-areas/${editingId}`
        : '/api/faculty/research-areas'
      
      const method = editingId ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save research area')
      }

      const result = await response.json()

      toast({
        title: editingId ? "Research Area Updated" : "Research Area Added",
        description: `Your research area has been ${editingId ? 'updated' : 'added'} successfully.`,
      })

      // Refresh the page to show updated data
      router.refresh()
      resetForm()
    } catch (error) {
      console.error('Error saving research area:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save research area. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this research area?')) {
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch(`/api/faculty/research-areas/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete research area')
      }

      toast({
        title: "Research Area Deleted",
        description: "Your research area has been deleted successfully.",
      })

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error deleting research area:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete research area. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Research Areas</h1>
          <p className="text-gray-600">Manage your research interests and areas of expertise</p>
        </div>
        <Button onClick={() => setShowAddForm(true)} disabled={isLoading}>
          <Plus className="w-4 h-4 mr-2" />
          Add Research Area
        </Button>
      </div>

      {/* Research Areas List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Research Areas</CardTitle>
          <CardDescription>
            Define your areas of research interest and expertise
          </CardDescription>
        </CardHeader>
        <CardContent>
          {researchAreas.length > 0 ? (
            <div className="space-y-4">
              {researchAreas.map((area) => (
                <div key={area.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <Beaker className="w-5 h-5 text-blue-600" />
                        <h3 className="font-semibold text-lg">{area.areaName}</h3>
                      </div>
                      {area.description && (
                        <p className="text-gray-600 text-sm leading-relaxed">
                          {area.description}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleEdit(area)}
                        disabled={isLoading}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleDelete(area.id)}
                        disabled={isLoading}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Beaker className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No research areas yet</h3>
              <p className="text-gray-600 mb-4">Define your research interests and areas of expertise.</p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Research Area
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Research Area Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingId ? 'Edit Research Area' : 'Add New Research Area'}</CardTitle>
            <CardDescription>
              {editingId ? 'Update your research area details' : 'Define a new area of research interest or expertise'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="areaName">Research Area Name *</Label>
                <Input 
                  id="areaName" 
                  value={formData.areaName}
                  onChange={(e) => setFormData(prev => ({ ...prev, areaName: e.target.value }))}
                  placeholder="e.g., Machine Learning, Artificial Intelligence, Data Science" 
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Enter a specific research area or field of study
                </p>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea 
                  id="description" 
                  rows={4}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your research interests, methodologies, and focus areas within this field..."
                />
                <p className="text-xs text-gray-500 mt-1">
                  Provide details about your specific interests and expertise in this area
                </p>
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={resetForm} disabled={isLoading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                  {editingId ? 'Update Research Area' : 'Add Research Area'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
