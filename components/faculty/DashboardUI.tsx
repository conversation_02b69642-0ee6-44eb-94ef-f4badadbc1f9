"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  User, 
  FileText, 
  BookOpen, 
  Calendar,
  Clock,
  Beaker,
  Plus,
  Bell
} from "lucide-react"
import Link from "next/link"

// Define types for the component props
type FacultyProfileType = any // Use a more specific type in production
type OfficeHourBookingType = any // Use a more specific type in production

interface DashboardUIProps {
  facultyProfile: FacultyProfileType
  upcomingBookings: OfficeHourBookingType[]
}

export function DashboardUI({ facultyProfile, upcomingBookings }: DashboardUIProps) {
  const stats = [
    {
      title: "Publications",
      value: facultyProfile.publications.length,
      description: "Research publications",
      icon: FileText,
      color: "text-blue-600"
    },
    {
      title: "Blog Posts",
      value: facultyProfile.posts.length,
      description: "Published articles",
      icon: FileText,
      color: "text-indigo-600"
    },
    {
      title: "Research Projects",
      value: facultyProfile.researchProjects.length,
      description: "Active projects",
      icon: Beaker,
      color: "text-green-600"
    },
    {
      title: "Courses",
      value: facultyProfile.classes.length,
      description: "Teaching assignments",
      icon: BookOpen,
      color: "text-purple-600"
    },
    {
      title: "Office Hours",
      value: facultyProfile.officeHours.length,
      description: "Weekly slots",
      icon: Clock,
      color: "text-orange-600"
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {facultyProfile.user.name}
        </h1>
        <p className="text-gray-600">
          {facultyProfile.title} • {facultyProfile.department.name}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Professional Profile */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Professional Profile</CardTitle>
              <CardDescription>
                Your industry experience and skills
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button asChild variant="outline" size="sm">
                <Link href="/faculty-portal/profile">
                  <User className="w-4 h-4 mr-2" />
                  Edit Profile
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Industry Experience */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium">Industry Experience</h4>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-7 text-xs"
                  asChild
                >
                  <Link href="/faculty-portal/industry-experience">
                    <Plus className="h-3 w-3 mr-1" />
                    Add Experience
                  </Link>
                </Button>
              </div>
              <div className="space-y-3">
                {facultyProfile.industryExperience.length > 0 ? (
                  facultyProfile.industryExperience.map((exp: any) => (
                    <div key={exp.id} className="border-l-2 border-amber-200 pl-4">
                      <h5 className="text-sm font-medium">{exp.position}</h5>
                      <p className="text-xs text-gray-500">
                        {exp.company} • {exp.startDate} - {exp.endDate || 'Present'}
                      </p>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No industry experience added yet</p>
                )}
              </div>
            </div>
            
            {/* Skills */}
            <div className="mt-4">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium">Skills & Expertise</h4>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-7 text-xs"
                  asChild
                >
                  <Link href="/faculty-portal/skills">
                    <Plus className="h-3 w-3 mr-1" />
                    Add Skills
                  </Link>
                </Button>
              </div>
              {facultyProfile.skills.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {facultyProfile.skills.map((skill: any) => (
                    <span 
                      key={skill.id}
                      className="bg-gray-100 px-2 py-0.5 rounded-full text-xs text-gray-700"
                    >
                      {skill.skillName}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No skills added yet</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recent Publications */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Publications</CardTitle>
            <CardDescription>
              Your latest research publications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {facultyProfile.publications.length > 0 ? (
                facultyProfile.publications.map((pub: any) => (
                  <div key={pub.id} className="border-l-2 border-blue-200 pl-4">
                    <h4 className="font-medium text-sm">{pub.title}</h4>
                    <p className="text-xs text-gray-500">
                      {pub.journal} • {pub.year}
                    </p>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No publications yet</p>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/publications">
                  <FileText className="w-4 h-4 mr-2" />
                  Manage Publications
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Office Hour Bookings */}
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Appointments</CardTitle>
            <CardDescription>
              Student office hour bookings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingBookings.length > 0 ? (
                upcomingBookings.map((booking: any) => (
                  <div key={booking.id} className="flex items-center space-x-4">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {booking.student.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(booking.date).toLocaleDateString()} • {booking.startTime}
                      </p>
                    </div>
                    <Bell className="w-4 h-4 text-gray-400" />
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No upcoming appointments</p>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/office-hours">
                  <Clock className="w-4 h-4 mr-2" />
                  Manage Office Hours
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Research Projects */}
        <Card>
          <CardHeader>
            <CardTitle>Research Projects</CardTitle>
            <CardDescription>
              Your active research initiatives
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {facultyProfile.researchProjects.length > 0 ? (
                facultyProfile.researchProjects.map((project: any) => (
                  <div key={project.id} className="border-l-2 border-green-200 pl-4">
                    <h4 className="font-medium text-sm">{project.title}</h4>
                    <p className="text-xs text-gray-500 capitalize">
                      {project.status.toLowerCase()} • {project.positions} positions
                    </p>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No research projects yet</p>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/research">
                  <Beaker className="w-4 h-4 mr-2" />
                  Manage Research
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Posts */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Posts</CardTitle>
            <CardDescription>
              Your latest blog posts and articles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {facultyProfile.posts.length > 0 ? (
                facultyProfile.posts.map((post: any) => (
                  <div key={post.id} className="border-l-2 border-indigo-200 pl-4">
                    <h4 className="font-medium text-sm">{post.title}</h4>
                    <div className="flex items-center gap-2 mt-1">
                      <span
                        className="inline-block w-2 h-2 rounded-full"
                        style={{ backgroundColor: post.category.color || '#e5e7eb' }}
                      />
                      <span className="text-xs text-gray-500">
                        {post.category.name} • {post.status.toLowerCase()}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No posts yet</p>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/posts">
                  <FileText className="w-4 h-4 mr-2" />
                  Manage Posts
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-3">
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/profile">
                  <User className="w-4 h-4 mr-2" />
                  Update Profile
                </Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/courses">
                  <BookOpen className="w-4 h-4 mr-2" />
                  Manage Courses
                </Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/office-hours">
                  <Clock className="w-4 h-4 mr-2" />
                  Manage Office Hours
                </Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/posts">
                  <FileText className="w-4 h-4 mr-2" />
                  Manage Posts
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 