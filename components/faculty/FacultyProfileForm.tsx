"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { Save, Loader2, Upload, User, Check, AlertCircle, RefreshCw } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"

interface FacultyProfile {
  id: string
  bio?: string | null
  title?: string | null
  office?: string | null
  officeHours?: string | null
  website?: string | null
  phone?: string | null
}

interface FacultyProfileFormProps {
  profile: FacultyProfile
  userEmail: string
  userName: string
  currentAvatar?: string
}

export function FacultyProfileForm({ profile, userEmail, userName, currentAvatar }: FacultyProfileFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isUploadingPhoto, setIsUploadingPhoto] = useState(false)
  const [avatarUrl, setAvatarUrl] = useState(currentAvatar || "/images/faculty/default-avatar.svg")
  const [formData, setFormData] = useState({
    bio: profile.bio || "",
    title: profile.title || "",
    office: profile.office || "",
    officeHours: (typeof profile.officeHours === 'string' ? profile.officeHours : "") || "",
    website: profile.website || "",
    phone: profile.phone || ""
  })

  // Add states for form feedback
  const [formSuccess, setFormSuccess] = useState(false)
  const [formError, setFormError] = useState<string | null>(null)
  const [isDirty, setIsDirty] = useState(false)

  const { toast } = useToast()
  const router = useRouter()

  // Reset success/error messages when form changes
  useEffect(() => {
    if (isDirty) {
      setFormSuccess(false)
      setFormError(null)
    }
  }, [formData, isDirty])

  const handleInputChange = (field: string, value: string) => {
    setIsDirty(true)
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handlePhotoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setIsUploadingPhoto(true)
    setFormError(null)

    try {
      // Validate file size (5MB max)
      const MAX_SIZE = 5 * 1024 * 1024 // 5MB
      if (file.size > MAX_SIZE) {
        throw new Error('Photo exceeds maximum size of 5MB')
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
      if (!allowedTypes.includes(file.type)) {
        throw new Error('Only JPG, PNG, or WebP images are allowed')
      }

      const formData = new FormData()
      formData.append('photo', file)

      const response = await fetch('/api/faculty/upload-photo', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      })

      console.log('Upload response status:', response.status)

      // Try to parse the response text
      const responseText = await response.text()
      console.log('Raw upload response:', responseText)

      let result
      try {
        result = JSON.parse(responseText)
      } catch (e) {
        console.error('Error parsing upload response:', e)
        throw new Error('Server returned an invalid response')
      }

      if (!response.ok) {
        throw new Error(result.error || 'Failed to upload photo')
      }

      setAvatarUrl(result.avatarUrl)
      setFormSuccess(true)
      setIsDirty(true) // Mark form as dirty to enable save button

      toast({
        title: "Photo Uploaded",
        description: "Your profile photo has been updated successfully.",
      })

      // Wait a moment before refreshing to show the success state
      setTimeout(() => {
        router.refresh()
      }, 1000)
    } catch (error) {
      console.error('Error uploading photo:', error)
      setFormError(error instanceof Error ? error.message : "Failed to upload photo")
      toast({
        title: "Upload Error",
        description: error instanceof Error ? error.message : "Failed to upload photo. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUploadingPhoto(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setFormError(null)
    setFormSuccess(false)

    try {
      // Add the profile ID to ensure the correct profile is updated
      const dataToSubmit = {
        ...formData,
        facultyId: profile.id,
      }
      
      console.log('Submitting profile update:', dataToSubmit)

      const response = await fetch('/api/faculty/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSubmit),
        // Add credentials to ensure cookies are sent
        credentials: 'include'
      })

      // Log the response status to help with debugging
      console.log('Update response status:', response.status)

      // Try to parse the response text regardless of status
      const responseText = await response.text()
      console.log('Raw response:', responseText)

      // Now try to parse as JSON if possible
      let result
      try {
        result = JSON.parse(responseText)
      } catch (e) {
        console.error('Error parsing response as JSON:', e)
        throw new Error('Server returned an invalid response')
      }

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update profile')
      }

      // Show success message
      setFormSuccess(true)
      setIsDirty(false)
      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
      })

      // Wait a moment before refreshing to show the success state
      setTimeout(() => {
        router.refresh()
      }, 1000)
    } catch (error) {
      console.error('Error updating profile:', error)
      setFormError(error instanceof Error ? error.message : "Failed to update profile")
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Failed to update profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Success and Error Alerts */}
      {formSuccess && (
        <Alert className="bg-green-50 border-green-500">
          <Check className="h-4 w-4 text-green-500" />
          <AlertTitle className="text-green-800">Profile Updated</AlertTitle>
          <AlertDescription className="text-green-700">
            Your faculty profile has been successfully updated.
          </AlertDescription>
        </Alert>
      )}
      
      {formError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Update Failed</AlertTitle>
          <AlertDescription>
            {formError}
          </AlertDescription>
        </Alert>
      )}

      {/* Profile Photo */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Photo</CardTitle>
          <CardDescription>
            Upload a professional photo for your faculty profile
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-6">
            <div className="relative">
              <div className="w-24 h-24 rounded-full overflow-hidden bg-gray-100 border-2 border-gray-200">
                {avatarUrl && avatarUrl !== "/images/faculty/default-avatar.svg" ? (
                  <img
                    src={avatarUrl}
                    alt="Profile photo"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-100">
                    <User className="w-8 h-8 text-gray-400" />
                  </div>
                )}
              </div>
            </div>
            <div className="flex-1">
              <Label htmlFor="photo-upload" className="block text-sm font-medium mb-2">
                Choose Photo
              </Label>
              <div className="flex items-center space-x-3">
                <Input
                  id="photo-upload"
                  type="file"
                  accept="image/*"
                  onChange={handlePhotoUpload}
                  disabled={isUploadingPhoto}
                  className="hidden"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById('photo-upload')?.click()}
                  disabled={isUploadingPhoto}
                >
                  {isUploadingPhoto ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      Upload Photo
                    </>
                  )}
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                JPG, PNG, or WebP. Max file size 5MB.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Update your basic profile information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={userName}
                disabled
                className="bg-gray-50"
              />
              <p className="text-xs text-gray-500 mt-1">Contact admin to change name</p>
            </div>
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                value={userEmail}
                disabled
                className="bg-gray-50"
              />
              <p className="text-xs text-gray-500 mt-1">Contact admin to change email</p>
            </div>
          </div>

          <div>
            <Label htmlFor="title">Title/Position</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="e.g., Associate Professor, Department Chair"
            />
          </div>

          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              rows={4}
              placeholder="Write a brief bio about yourself, your background, and interests..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
          <CardDescription>
            Update your contact details and office information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="(*************"
              />
            </div>
            <div>
              <Label htmlFor="office">Office Location</Label>
              <Input
                id="office"
                value={formData.office}
                onChange={(e) => handleInputChange('office', e.target.value)}
                placeholder="e.g., Science Building, Room 305"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="website">Website URL</Label>
            <Input
              id="website"
              type="url"
              value={formData.website}
              onChange={(e) => handleInputChange('website', e.target.value)}
              placeholder="https://your-website.com"
            />
          </div>

          <div>
            <Label htmlFor="officeHours">Office Hours</Label>
            <Textarea
              id="officeHours"
              value={formData.officeHours}
              onChange={(e) => handleInputChange('officeHours', e.target.value)}
              rows={3}
              placeholder="e.g., Mondays and Wednesdays: 2:00 PM - 4:00 PM, or by appointment"
            />
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button 
          type="submit" 
          disabled={isLoading || (!isDirty && !formSuccess)}
          className={formSuccess ? "bg-green-600 hover:bg-green-700" : ""}
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : formSuccess ? (
            <>
              <Check className="w-4 h-4 mr-2" />
              Saved
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
