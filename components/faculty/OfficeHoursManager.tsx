"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Clock, Plus, Edit, Trash2, Calendar, MapPin, User, Loader2 } from "lucide-react"

interface OfficeHour {
  id: string
  dayOfWeek: number
  startTime: string
  endTime: string
  location: string
  notes?: string | null
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface Booking {
  id: string
  date: string
  startTime: string
  endTime: string
  topic?: string | null
  status: string
  student: {
    name: string | null
    email: string
  }
  officeHour: {
    dayOfWeek: number
    startTime: string
    endTime: string
    location: string
  }
}

interface OfficeHoursManagerProps {
  initialOfficeHours: OfficeHour[]
  upcomingBookings: Booking[]
}

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' }
]

export function OfficeHoursManager({ initialOfficeHours, upcomingBookings }: OfficeHoursManagerProps) {
  const [officeHours, setOfficeHours] = useState<OfficeHour[]>(initialOfficeHours)
  const [isLoading, setIsLoading] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    dayOfWeek: "",
    startTime: "",
    endTime: "",
    location: "",
    notes: ""
  })

  const { toast } = useToast()
  const router = useRouter()

  const resetForm = () => {
    setFormData({
      dayOfWeek: "",
      startTime: "",
      endTime: "",
      location: "",
      notes: ""
    })
    setEditingId(null)
    setShowAddForm(false)
  }

  const handleEdit = (officeHour: OfficeHour) => {
    setFormData({
      dayOfWeek: officeHour.dayOfWeek.toString(),
      startTime: officeHour.startTime,
      endTime: officeHour.endTime,
      location: officeHour.location,
      notes: officeHour.notes || ""
    })
    setEditingId(officeHour.id)
    setShowAddForm(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const submitData = {
        dayOfWeek: parseInt(formData.dayOfWeek),
        startTime: formData.startTime,
        endTime: formData.endTime,
        location: formData.location,
        notes: formData.notes || undefined
      }

      const url = editingId 
        ? `/api/faculty/office-hours/${editingId}`
        : '/api/faculty/office-hours'
      
      const method = editingId ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save office hour')
      }

      const result = await response.json()

      // Update local state
      if (editingId) {
        setOfficeHours(prev => prev.map(hour =>
          hour.id === editingId ? result : hour
        ))
      } else {
        setOfficeHours(prev => [...prev, result])
      }

      toast({
        title: editingId ? "Office Hour Updated" : "Office Hour Added",
        description: `Your office hour has been ${editingId ? 'updated' : 'added'} successfully.`,
      })

      // Refresh the page to show updated data
      router.refresh()
      resetForm()
    } catch (error) {
      console.error('Error saving office hour:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save office hour. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this office hour?')) {
      return
    }

    try {
      const response = await fetch(`/api/faculty/office-hours/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete office hour')
      }

      // Update local state
      setOfficeHours(prev => prev.filter(hour => hour.id !== id))

      toast({
        title: "Office Hour Deleted",
        description: "Your office hour has been deleted successfully.",
      })

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error deleting office hour:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete office hour. Please try again.",
        variant: "destructive",
      })
    }
  }

  const formatTime = (time: string) => {
    const [hour, minute] = time.split(':')
    const hourNum = parseInt(hour)
    const ampm = hourNum >= 12 ? 'PM' : 'AM'
    const hour12 = hourNum % 12 || 12
    return `${hour12}:${minute} ${ampm}`
  }

  const getDayName = (dayOfWeek: number) => {
    return DAYS_OF_WEEK.find(day => day.value === dayOfWeek)?.label || 'Unknown'
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Office Hours</h1>
          <p className="text-gray-600">Manage your weekly office hours schedule</p>
        </div>
        <Button onClick={() => setShowAddForm(true)} disabled={isLoading}>
          <Plus className="w-4 h-4 mr-2" />
          Add Office Hours
        </Button>
      </div>

      {/* Current Office Hours */}
      <Card>
        <CardHeader>
          <CardTitle>Weekly Schedule</CardTitle>
          <CardDescription>
            Your current office hours schedule
          </CardDescription>
        </CardHeader>
        <CardContent>
          {officeHours.length > 0 ? (
            <div className="space-y-4">
              {officeHours.map((hour) => (
                <div key={hour.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <Calendar className="w-5 h-5 text-blue-600" />
                        <h3 className="font-semibold text-lg">{getDayName(hour.dayOfWeek)}</h3>
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                          {formatTime(hour.startTime)} - {formatTime(hour.endTime)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          {hour.location}
                        </div>
                      </div>
                      {hour.notes && (
                        <p className="text-sm text-gray-600 mt-2">{hour.notes}</p>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(hour)}
                        disabled={isLoading}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(hour.id)}
                        disabled={isLoading}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No office hours scheduled</h3>
              <p className="text-gray-500 mb-4">Add your first office hours to get started</p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Office Hours
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upcoming Bookings */}
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Appointments</CardTitle>
          <CardDescription>
            Student bookings for your office hours
          </CardDescription>
        </CardHeader>
        <CardContent>
          {upcomingBookings.length > 0 ? (
            <div className="space-y-4">
              {upcomingBookings.map((booking) => (
                <div key={booking.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <User className="w-5 h-5 text-green-600" />
                        <h3 className="font-semibold">{booking.student.name || booking.student.email}</h3>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          booking.status === 'CONFIRMED'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {booking.status}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(booking.date).toLocaleDateString()}
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
                        </div>
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          {booking.officeHour.location}
                        </div>
                      </div>
                      {booking.topic && (
                        <p className="text-sm text-gray-600 mt-2">
                          <strong>Topic:</strong> {booking.topic}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No upcoming appointments</h3>
              <p className="text-gray-500">Students will be able to book your office hours once you set them up</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Office Hours Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingId ? 'Edit Office Hours' : 'Add New Office Hours'}</CardTitle>
            <CardDescription>
              {editingId ? 'Update your office hours details' : 'Set up a new weekly office hours slot'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="dayOfWeek">Day of Week *</Label>
                  <Select
                    value={formData.dayOfWeek}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, dayOfWeek: value }))}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select day" />
                    </SelectTrigger>
                    <SelectContent>
                      {DAYS_OF_WEEK.map((day) => (
                        <SelectItem key={day.value} value={day.value.toString()}>
                          {day.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="location">Location *</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    placeholder="e.g., Office 123, Building A"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startTime">Start Time *</Label>
                  <Input
                    id="startTime"
                    type="time"
                    value={formData.startTime}
                    onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="endTime">End Time *</Label>
                  <Input
                    id="endTime"
                    type="time"
                    value={formData.endTime}
                    onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Any additional information for students..."
                />
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={resetForm} disabled={isLoading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                  {editingId ? 'Update Office Hours' : 'Add Office Hours'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
