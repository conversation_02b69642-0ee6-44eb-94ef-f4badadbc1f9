"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  User,
  FileText,
  BookOpen,
  Calendar,
  Clock,
  Beaker,
  GraduationCap,
  Home,
  Settings,
  Briefcase,
  BarChart,
  PenTool
} from "lucide-react"
import { UserRole } from "@prisma/client"

interface FacultySidebarProps {
  user: {
    id: string
    email: string
    name?: string | null
    role: UserRole
  }
}

const navigation = [
  {
    name: "Dashboard",
    href: "/faculty-portal",
    icon: Home
  },
  {
    name: "My Profile",
    href: "/faculty-portal/profile",
    icon: User
  },
  {
    name: "My Posts",
    href: "/faculty-portal/posts",
    icon: PenTool
  },
  {
    name: "Publications",
    href: "/faculty-portal/publications",
    icon: FileText
  },
  {
    name: "Research Areas",
    href: "/faculty-portal/research",
    icon: Beaker
  },
  {
    name: "Education",
    href: "/faculty-portal/education",
    icon: GraduationCap
  },
  {
    name: "Timeline",
    href: "/faculty-portal/timeline",
    icon: Calendar
  },
  {
    name: "Office Hours",
    href: "/faculty-portal/office-hours",
    icon: Clock
  },
  {
    name: "My Courses",
    href: "/faculty-portal/courses",
    icon: BookOpen
  },
  {
    name: "Industry Experience",
    href: "/faculty-portal/industry-experience",
    icon: Briefcase
  },
  {
    name: "Skills",
    href: "/faculty-portal/skills",
    icon: BarChart
  },
  {
    name: "Settings",
    href: "/faculty-portal/settings",
    icon: Settings
  }
]

export function FacultySidebar({ user }: FacultySidebarProps) {
  const pathname = usePathname()

  return (
    <div className="flex flex-col w-64 bg-white shadow-lg">
      {/* Logo/Brand */}
      <div className="flex items-center justify-center h-16 px-4 bg-green-600 text-white">
        <GraduationCap className="w-6 h-6 mr-2" />
        <h1 className="text-xl font-bold">Faculty Portal</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href ||
            (item.href !== "/faculty-portal" && pathname.startsWith(item.href))

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors",
                isActive
                  ? "bg-green-100 text-green-700"
                  : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
              )}
            >
              <item.icon className="w-5 h-5 mr-3" />
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* User info */}
      <div className="px-4 py-4 border-t border-gray-200">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-700">
              {user.name || user.email}
            </p>
            <p className="text-xs text-gray-500">Faculty Member</p>
          </div>
        </div>
      </div>
    </div>
  )
}
