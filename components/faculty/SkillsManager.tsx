"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Bar<PERSON>hart, Plus, Edit, Trash2, Star, Loader2 } from "lucide-react"

interface Skill {
  id: string
  skillName: string
  proficiency?: string | null
  category?: string | null
  createdAt: string
}

interface SkillsManagerProps {
  initialSkills: Skill[]
}

const PROFICIENCY_LEVELS = [
  { value: 'Beginner', label: 'Beginner' },
  { value: 'Intermediate', label: 'Intermediate' },
  { value: 'Advanced', label: 'Advanced' },
  { value: 'Expert', label: 'Expert' }
]

const SKILL_CATEGORIES = [
  { value: 'Technical', label: 'Technical' },
  { value: 'Programming', label: 'Programming' },
  { value: 'Research', label: 'Research' },
  { value: 'Teaching', label: 'Teaching' },
  { value: 'Languages', label: 'Languages' },
  { value: 'Soft Skills', label: 'Soft Skills' },
  { value: 'Tools', label: 'Tools' },
  { value: 'Other', label: 'Other' }
]

export function SkillsManager({ initialSkills }: SkillsManagerProps) {
  const [skills, setSkills] = useState<Skill[]>(initialSkills)
  const [isLoading, setIsLoading] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    skillName: "",
    proficiency: "",
    category: ""
  })

  const { toast } = useToast()
  const router = useRouter()

  const resetForm = () => {
    setFormData({
      skillName: "",
      proficiency: "",
      category: ""
    })
    setEditingId(null)
    setShowAddForm(false)
  }

  const handleEdit = (skill: Skill) => {
    setFormData({
      skillName: skill.skillName,
      proficiency: skill.proficiency || "",
      category: skill.category || ""
    })
    setEditingId(skill.id)
    setShowAddForm(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const submitData = {
        skillName: formData.skillName,
        proficiency: formData.proficiency || null,
        category: formData.category || null
      }

      const url = editingId 
        ? `/api/faculty/skills/${editingId}`
        : '/api/faculty/skills'
      
      const method = editingId ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save skill')
      }

      const result = await response.json()

      toast({
        title: editingId ? "Skill Updated" : "Skill Added",
        description: `Your skill has been ${editingId ? 'updated' : 'added'} successfully.`,
      })

      // Refresh the page to show updated data
      router.refresh()
      resetForm()
    } catch (error) {
      console.error('Error saving skill:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save skill. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this skill?')) {
      return
    }

    try {
      const response = await fetch(`/api/faculty/skills/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete skill')
      }

      toast({
        title: "Skill Deleted",
        description: "Your skill has been deleted successfully.",
      })

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error deleting skill:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete skill. Please try again.",
        variant: "destructive",
      })
    }
  }

  const getProficiencyColor = (proficiency?: string | null) => {
    switch (proficiency) {
      case 'Expert': return 'bg-green-100 text-green-800'
      case 'Advanced': return 'bg-blue-100 text-blue-800'
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'Beginner': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getCategoryColor = (category?: string | null) => {
    switch (category) {
      case 'Technical': return 'bg-purple-100 text-purple-800'
      case 'Programming': return 'bg-indigo-100 text-indigo-800'
      case 'Research': return 'bg-green-100 text-green-800'
      case 'Teaching': return 'bg-orange-100 text-orange-800'
      case 'Languages': return 'bg-pink-100 text-pink-800'
      case 'Soft Skills': return 'bg-cyan-100 text-cyan-800'
      case 'Tools': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Group skills by category
  const groupedSkills = skills.reduce((acc, skill) => {
    const category = skill.category || 'Other'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(skill)
    return acc
  }, {} as Record<string, Skill[]>)

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Skills & Expertise</h1>
          <p className="text-gray-600">Manage your technical skills, expertise, and proficiency levels</p>
        </div>
        <Button onClick={() => setShowAddForm(true)} disabled={isLoading}>
          <Plus className="w-4 h-4 mr-2" />
          Add Skill
        </Button>
      </div>

      {/* Skills by Category */}
      <div className="space-y-6">
        {Object.keys(groupedSkills).length > 0 ? (
          Object.entries(groupedSkills).map(([category, categorySkills]) => (
            <Card key={category}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart className="w-5 h-5 mr-2" />
                  {category}
                  <Badge variant="secondary" className="ml-2">
                    {categorySkills.length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {categorySkills.map((skill) => (
                    <div key={skill.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold">{skill.skillName}</h3>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(skill)}
                            disabled={isLoading}
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(skill.id)}
                            disabled={isLoading}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                      {skill.proficiency && (
                        <Badge className={getProficiencyColor(skill.proficiency)}>
                          {skill.proficiency}
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-8">
              <BarChart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No skills added</h3>
              <p className="text-gray-500 mb-4">Add your skills and expertise to showcase your capabilities</p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Skill
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Add/Edit Skill Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingId ? 'Edit Skill' : 'Add New Skill'}</CardTitle>
            <CardDescription>
              {editingId ? 'Update your skill details' : 'Add a new skill to your profile'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="skillName">Skill Name *</Label>
                <Input
                  id="skillName"
                  value={formData.skillName}
                  onChange={(e) => setFormData(prev => ({ ...prev, skillName: e.target.value }))}
                  placeholder="e.g., Python, Machine Learning, Public Speaking"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {SKILL_CATEGORIES.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="proficiency">Proficiency Level</Label>
                  <Select
                    value={formData.proficiency}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, proficiency: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select proficiency" />
                    </SelectTrigger>
                    <SelectContent>
                      {PROFICIENCY_LEVELS.map((level) => (
                        <SelectItem key={level.value} value={level.value}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={resetForm} disabled={isLoading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                  {editingId ? 'Update Skill' : 'Add Skill'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
