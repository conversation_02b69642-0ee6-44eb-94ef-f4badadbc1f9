"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { FileText, Plus, Edit, Trash2, ExternalLink, Loader2 } from "lucide-react"

interface Publication {
  id: string
  title: string
  authors: string[]
  journal: string
  year: number
  citationCount: number
  link?: string
  abstract?: string
  tags: string[]
  createdAt: string
  updatedAt: string
}

interface PublicationsManagerProps {
  initialPublications: Publication[]
}

export function PublicationsManager({ initialPublications }: PublicationsManagerProps) {
  const [publications, setPublications] = useState<Publication[]>(initialPublications)
  const [isLoading, setIsLoading] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    title: "",
    authors: "",
    journal: "",
    year: new Date().getFullYear(),
    citationCount: 0,
    link: "",
    abstract: "",
    tags: ""
  })

  const { toast } = useToast()
  const router = useRouter()

  const resetForm = () => {
    setFormData({
      title: "",
      authors: "",
      journal: "",
      year: new Date().getFullYear(),
      citationCount: 0,
      link: "",
      abstract: "",
      tags: ""
    })
    setEditingId(null)
    setShowAddForm(false)
  }

  const handleEdit = (publication: Publication) => {
    setFormData({
      title: publication.title,
      authors: publication.authors.join(", "),
      journal: publication.journal,
      year: publication.year,
      citationCount: publication.citationCount,
      link: publication.link || "",
      abstract: publication.abstract || "",
      tags: publication.tags.join(", ")
    })
    setEditingId(publication.id)
    setShowAddForm(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const submitData = {
        title: formData.title,
        authors: formData.authors.split(",").map(a => a.trim()).filter(a => a),
        journal: formData.journal,
        year: formData.year,
        citationCount: formData.citationCount,
        link: formData.link || undefined,
        abstract: formData.abstract || undefined,
        tags: formData.tags.split(",").map(t => t.trim()).filter(t => t)
      }

      const url = editingId 
        ? `/api/faculty/publications/${editingId}`
        : '/api/faculty/publications'
      
      const method = editingId ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save publication')
      }

      const result = await response.json()

      toast({
        title: editingId ? "Publication Updated" : "Publication Added",
        description: `Your publication has been ${editingId ? 'updated' : 'added'} successfully.`,
      })

      // Refresh the page to show updated data
      router.refresh()
      resetForm()
    } catch (error) {
      console.error('Error saving publication:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save publication. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this publication?')) {
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch(`/api/faculty/publications/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete publication')
      }

      toast({
        title: "Publication Deleted",
        description: "Your publication has been deleted successfully.",
      })

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error deleting publication:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete publication. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Publications</h1>
          <p className="text-gray-600">Manage your academic publications and research papers</p>
        </div>
        <Button onClick={() => setShowAddForm(true)} disabled={isLoading}>
          <Plus className="w-4 h-4 mr-2" />
          Add Publication
        </Button>
      </div>

      {/* Publications List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Publications</CardTitle>
          <CardDescription>
            Manage your academic publications, research papers, and scholarly works
          </CardDescription>
        </CardHeader>
        <CardContent>
          {publications.length > 0 ? (
            <div className="space-y-4">
              {publications.map((publication) => (
                <div key={publication.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">{publication.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">
                        <strong>Authors: <AUTHORS>
                      </p>
                      <p className="text-sm text-gray-600 mb-2">
                        <strong>Journal/Conference:</strong> {publication.journal}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                        <span><strong>Year:</strong> {publication.year}</span>
                        <span><strong>Citations:</strong> {publication.citationCount}</span>
                      </div>
                      {publication.abstract && (
                        <p className="text-sm text-gray-600 mb-2">
                          <strong>Abstract:</strong> {publication.abstract.substring(0, 200)}
                          {publication.abstract.length > 200 && '...'}
                        </p>
                      )}
                      {publication.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {publication.tags.map((tag, index) => (
                            <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      {publication.link && (
                        <Button variant="outline" size="sm" asChild>
                          <a href={publication.link} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="w-4 h-4" />
                          </a>
                        </Button>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleEdit(publication)}
                        disabled={isLoading}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleDelete(publication.id)}
                        disabled={isLoading}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No publications yet</h3>
              <p className="text-gray-600 mb-4">Start building your academic profile by adding your first publication.</p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Publication
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Publication Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingId ? 'Edit Publication' : 'Add New Publication'}</CardTitle>
            <CardDescription>
              {editingId ? 'Update your publication details' : 'Add a new academic publication to your profile'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="title">Title *</Label>
                <Input 
                  id="title" 
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter publication title" 
                  required
                />
              </div>

              <div>
                <Label htmlFor="authors">Authors *</Label>
                <Input 
                  id="authors" 
                  value={formData.authors}
                  onChange={(e) => setFormData(prev => ({ ...prev, authors: e.target.value }))}
                  placeholder="Enter authors (comma-separated)" 
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  List all authors separated by commas
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="journal">Journal/Conference *</Label>
                  <Input 
                    id="journal" 
                    value={formData.journal}
                    onChange={(e) => setFormData(prev => ({ ...prev, journal: e.target.value }))}
                    placeholder="Journal or conference name" 
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="year">Year *</Label>
                  <Input 
                    id="year" 
                    type="number" 
                    value={formData.year}
                    onChange={(e) => setFormData(prev => ({ ...prev, year: parseInt(e.target.value) }))}
                    placeholder="2024" 
                    min="1900" 
                    max="2030" 
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="citations">Citation Count</Label>
                  <Input 
                    id="citations" 
                    type="number" 
                    value={formData.citationCount}
                    onChange={(e) => setFormData(prev => ({ ...prev, citationCount: parseInt(e.target.value) || 0 }))}
                    placeholder="0" 
                    min="0" 
                  />
                </div>
                <div>
                  <Label htmlFor="link">Publication Link</Label>
                  <Input 
                    id="link" 
                    type="url" 
                    value={formData.link}
                    onChange={(e) => setFormData(prev => ({ ...prev, link: e.target.value }))}
                    placeholder="https://..." 
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="abstract">Abstract</Label>
                <Textarea 
                  id="abstract" 
                  rows={4}
                  value={formData.abstract}
                  onChange={(e) => setFormData(prev => ({ ...prev, abstract: e.target.value }))}
                  placeholder="Enter the abstract or summary of your publication..."
                />
              </div>

              <div>
                <Label htmlFor="tags">Tags</Label>
                <Input 
                  id="tags" 
                  value={formData.tags}
                  onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                  placeholder="machine learning, AI, computer vision" 
                />
                <p className="text-xs text-gray-500 mt-1">
                  Add relevant tags separated by commas
                </p>
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={resetForm} disabled={isLoading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                  {editingId ? 'Update Publication' : 'Add Publication'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
