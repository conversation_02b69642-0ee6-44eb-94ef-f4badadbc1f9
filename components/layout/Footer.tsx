import Link from "next/link"
import { Mail, Phone, MapPin, ArrowRight, ExternalLink, GraduationCap, BookOpen, Calculator, Beaker } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export default function Footer() {
  return (
    <footer className="w-full bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 border-t border-border/20" role="contentinfo" aria-label="Site footer">
      {/* Easter egg hex: 4d6164652062792053686973686972207769746820e29da4efb88f20666f72205545463a20596f752066756e64206974212041626f757420686572652c2049276d206a757374206120646576656c6f706572207768696c65206465766c6f70696e6720506172616c6c656c732d756e69766572736520666f7220556c6c656e7320456475636174696f6e616c20466f756e646174696f6e206163726f737320746865206d756c746976657273652e */}
      <div className="px-4 md:px-6 py-16 md:py-20" data-secret="TWFkZSBieSBTaGlzaGlyIHdpdGg=">
        {/* Main Footer Content */}
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 mb-12">
            
            {/* Brand Section */}
            <div className="lg:col-span-5 space-y-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-white rounded-xl shadow-sm border border-blue-100">
                  <img src="/UC_logo.svg" alt="Ullens College Logo" className="h-10 w-10" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">Ullens College</h3>
                  <p className="text-sm text-blue-600 font-medium">Excellence in Education</p>
                </div>
              </div>
              
              {/* Quick Contact */}
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-900 text-sm mb-3">Contact Information</h4>
                <div className="space-y-2">
                  <a 
                    href="mailto:<EMAIL>" 
                    className="flex items-center gap-2 text-sm text-gray-600 hover:text-blue-600 transition-colors"
                  >
                    <Mail className="h-4 w-4 text-blue-500" />
                    <span><EMAIL></span>
                  </a>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="flex items-center gap-2 text-sm text-gray-600 hover:text-blue-600 transition-colors"
                  >
                    <GraduationCap className="h-4 w-4 text-emerald-500" />
                    <span><EMAIL></span>
                  </a>
                </div>
              </div>
            </div>

            {/* Academic Programs */}
            <div className="lg:col-span-4 space-y-4">
              <h4 className="font-semibold text-gray-900 text-sm mb-3">Academic Programs</h4>
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Link 
                    href="/schools/computer-science" 
                    className="group flex items-center gap-2 text-sm text-gray-600 hover:text-blue-600 transition-colors"
                  >
                    <div className="w-5 h-5 bg-blue-100 rounded flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                      <Calculator className="h-3 w-3 text-blue-600" />
                    </div>
                    <span>Computer Science</span>
                  </Link>
                  <Link 
                    href="/schools/business" 
                    className="group flex items-center gap-2 text-sm text-gray-600 hover:text-purple-600 transition-colors"
                  >
                    <div className="w-5 h-5 bg-purple-100 rounded flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                      <BookOpen className="h-3 w-3 text-purple-600" />
                    </div>
                    <span>Business</span>
                  </Link>
                </div>
                <div className="space-y-2">
                  <Link 
                    href="/schools/agriculture-climate" 
                    className="group flex items-center gap-2 text-sm text-gray-600 hover:text-emerald-600 transition-colors"
                  >
                    <div className="w-5 h-5 bg-emerald-100 rounded flex items-center justify-center group-hover:bg-emerald-200 transition-colors">
                      <Beaker className="h-3 w-3 text-emerald-600" />
                    </div>
                    <span>Agriculture</span>
                  </Link>
                  <Link 
                    href="/schools/education" 
                    className="group flex items-center gap-2 text-sm text-gray-600 hover:text-indigo-600 transition-colors"
                  >
                    <div className="w-5 h-5 bg-indigo-100 rounded flex items-center justify-center group-hover:bg-indigo-200 transition-colors">
                      <GraduationCap className="h-3 w-3 text-indigo-600" />
                    </div>
                    <span>Education</span>
                  </Link>
                </div>
              </div>
            </div>

            {/* Resources & Tools */}
            <div className="lg:col-span-3 space-y-4">
              <h4 className="font-semibold text-gray-900 text-sm mb-3">Resources & Tools</h4>
              <div className="space-y-2">
                <Link 
                  href="/programs/compare" 
                  className="group flex items-center gap-2 text-sm text-gray-600 hover:text-blue-600 transition-colors"
                >
                  <div className="w-5 h-5 bg-blue-100 rounded flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                    <Calculator className="h-3 w-3 text-blue-600" />
                  </div>
                  <span>Program Comparison</span>
                </Link>

                <Link 
                  href="/student-journey" 
                  className="group flex items-center gap-2 text-sm text-gray-600 hover:text-emerald-600 transition-colors"
                >
                  <div className="w-5 h-5 bg-emerald-100 rounded flex items-center justify-center group-hover:bg-emerald-200 transition-colors">
                    <GraduationCap className="h-3 w-3 text-emerald-600" />
                  </div>
                  <span>Student Journey</span>
                </Link>

                <div className="pt-2 border-t border-gray-100">
                  <Link href="/schools/education" className="flex items-center gap-2 text-sm text-gray-600 hover:text-purple-600 transition-colors mb-1">
                    <Badge className="bg-purple-100 text-purple-700 text-xs px-2 py-0.5">UCED</Badge>
                    <span>Educator Development</span>
                  </Link>
                  <Link href="/business-tools" className="flex items-center gap-2 text-sm text-gray-600 hover:text-blue-600 transition-colors">
                    <Badge className="bg-blue-100 text-blue-700 text-xs px-2 py-0.5">Tools</Badge>
                    <span>Business Tools</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-gray-200 pt-8">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">
              <div className="flex flex-col md:flex-row items-center gap-6">
                <p className="text-sm text-gray-500">© 2025 Ullens College. All rights reserved.</p>
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <Link href="/privacy" className="hover:text-blue-600 transition-colors">Privacy Policy</Link>
                  <span>•</span>
                  <Link href="/terms" className="hover:text-blue-600 transition-colors">Terms of Service</Link>
                </div>
              </div>
              
              {/* Easter Egg */}
              <div 
                className="relative cursor-default group"
                title="🥚 Look closer..."
              >
                <Badge variant="outline" className="text-xs text-gray-400 border-gray-200 hover:border-crimson/30 hover:text-crimson/60 transition-colors">
                  Made with ❤️ for UEF
                </Badge>
                <div className="absolute -top-8 right-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs text-crimson/60 font-mono tracking-wide select-none pointer-events-none bg-white px-2 py-1 rounded shadow-lg">
                  By Shishir
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Detective work: The truth lies in the shadows between pixels and possibilities */}
    </footer>
  )
} 