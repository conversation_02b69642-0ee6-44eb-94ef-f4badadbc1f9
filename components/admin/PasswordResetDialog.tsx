"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Co<PERSON>, Refresh<PERSON>w, Eye, EyeOff, Key } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Switch } from "@/components/ui/switch"

interface PasswordResetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  userId: string
  userName: string
  onSuccess?: () => void
}

export function PasswordResetDialog({
  open,
  onOpenChange,
  userId,
  userName,
  onSuccess
}: PasswordResetDialogProps) {
  const [resetMethod, setResetMethod] = useState<'manual' | 'auto'>('auto')
  const [manualPassword, setManualPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [generatedPassword, setGeneratedPassword] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [sendEmail, setSendEmail] = useState(true)
  const { toast } = useToast()

  // Reset the form when dialog opens/closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setManualPassword('')
      setConfirmPassword('')
      setGeneratedPassword(null)
      setIsLoading(false)
      setShowPassword(false)
    }
    onOpenChange(open)
  }

  // Copy password to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "Password has been copied to clipboard",
    })
  }

  // Handle form submission
  const handleSubmit = async () => {
    // Validate manual password
    if (resetMethod === 'manual') {
      if (!manualPassword) {
        toast({
          title: "Error",
          description: "Please enter a password",
          variant: "destructive",
        })
        return
      }
      if (manualPassword !== confirmPassword) {
        toast({
          title: "Error",
          description: "Passwords do not match",
          variant: "destructive",
        })
        return
      }
      if (manualPassword.length < 6) {
        toast({
          title: "Error",
          description: "Password must be at least 6 characters",
          variant: "destructive",
        })
        return
      }
    }

    setIsLoading(true)

    try {
      const response = await fetch(`/api/admin/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(
          resetMethod === 'manual'
            ? { newPassword: manualPassword, sendEmail }
            : { generateRandom: true, sendEmail }
        ),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to reset password')
      }

      const data = await response.json()

      // Save generated password to state if auto-generated
      if (resetMethod === 'auto' && data.tempPassword) {
        setGeneratedPassword(data.tempPassword)
      } else {
        // If manually set, show success and close
        toast({
          title: "Success",
          description: `Password has been reset for ${userName}`,
        })

        if (onSuccess) {
          onSuccess()
        }

        // Close dialog if manually set
        if (resetMethod === 'manual') {
          handleOpenChange(false)
        }
      }
    } catch (error) {
      console.error('Error resetting password:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to reset password. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Reset Password</DialogTitle>
          <DialogDescription>
            Reset password for user: <span className="font-medium">{userName}</span>
          </DialogDescription>
        </DialogHeader>

        {!generatedPassword ? (
          <Tabs
            defaultValue="auto"
            value={resetMethod}
            onValueChange={(v) => setResetMethod(v as 'manual' | 'auto')}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="auto">Auto-Generate</TabsTrigger>
              <TabsTrigger value="manual">Manual Entry</TabsTrigger>
            </TabsList>

            <TabsContent value="auto" className="space-y-4 mt-4">
              <div className="text-center py-6 px-4 border rounded-md">
                <Key className="h-12 w-12 mx-auto text-primary mb-2" />
                <p className="text-sm text-muted-foreground mb-2">
                  A strong random password will be generated for this user.
                </p>
                <p className="text-xs text-muted-foreground">
                  You'll be able to copy and share it after generation.
                </p>
              </div>
            </TabsContent>

            <TabsContent value="manual" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="password">New Password</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      type="button"
                      className="h-8 px-2"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={manualPassword}
                    onChange={(e) => setManualPassword(e.target.value)}
                    placeholder="Enter new password"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirm-password">Confirm Password</Label>
                  <Input
                    id="confirm-password"
                    type={showPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm new password"
                  />
                  {manualPassword && confirmPassword && manualPassword !== confirmPassword && (
                    <p className="text-sm text-red-500">Passwords do not match</p>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          <div className="space-y-4">
            <div className="p-4 border rounded-md bg-green-50">
              <h4 className="font-medium text-green-800 mb-2">Password Generated Successfully</h4>
              <p className="text-sm text-green-700 mb-4">
                Make sure to copy this password and share it securely with the user.
              </p>

              <div className="relative">
                <Input
                  value={generatedPassword}
                  readOnly
                  className="pr-10 font-mono text-base"
                  type={showPassword ? "text" : "password"}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>

              <div className="flex justify-center mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(generatedPassword)}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy to Clipboard
                </Button>
              </div>
            </div>
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Switch
            id="send-email"
            checked={sendEmail}
            onCheckedChange={setSendEmail}
          />
          <Label htmlFor="send-email">Send password reset email to user</Label>
        </div>

        <DialogFooter>
          {!generatedPassword ? (
            <>
              <Button
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Resetting...
                  </>
                ) : (
                  'Reset Password'
                )}
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                onClick={() => {
                  setGeneratedPassword(null)
                  setResetMethod('auto')
                }}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Generate Another
              </Button>
              <Button onClick={() => handleOpenChange(false)}>Done</Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 