"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ConfirmDialog } from "@/components/admin/ConfirmDialog"
import { useToast } from "@/hooks/use-toast"
import { Trash2, Loader2 } from "lucide-react"

interface ProgramActionsProps {
  programId: string
  programName: string
}

export function ProgramActions({ programId, programName }: ProgramActionsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const handleDelete = async () => {
    setIsDeleting(true)
    
    try {
      const response = await fetch(`/api/admin/programs/${programId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete program')
      }

      toast({
        title: "Success",
        description: `Program "${programName}" has been deleted.`,
      })

      router.refresh()
    } catch (error) {
      console.error('Error deleting program:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete program. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
      setIsDeleteDialogOpen(false)
    }
  }

  return (
    <>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => setIsDeleteDialogOpen(true)}
        disabled={isDeleting}
      >
        {isDeleting ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Trash2 className="w-4 h-4 text-red-600" />
        )}
      </Button>

      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Delete Program"
        description={`Are you sure you want to delete "${programName}"? This action cannot be undone. Consider deactivating the program instead if there are enrolled students or associated data.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        onConfirm={handleDelete}
      />
    </>
  )
}
