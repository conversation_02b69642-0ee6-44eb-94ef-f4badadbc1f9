"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ConfirmDialog } from "@/components/admin/ConfirmDialog"
import { useToast } from "@/hooks/use-toast"
import { Trash2, Loader2 } from "lucide-react"

interface CourseActionsProps {
  courseId: string
  courseName: string
  courseCode: string
  classCount?: number
}

export function CourseActions({ courseId, courseName, courseCode, classCount = 0 }: CourseActionsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const handleDelete = async () => {
    setIsDeleting(true)
    
    try {
      const response = await fetch(`/api/admin/courses/${courseId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete course')
      }

      toast({
        title: "Success",
        description: `Course "${courseCode} - ${courseName}" has been deleted.`,
      })

      router.refresh()
    } catch (error) {
      console.error('Error deleting course:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete course. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
      setIsDeleteDialogOpen(false)
    }
  }

  const getDeleteMessage = () => {
    if (classCount > 0) {
      return `Are you sure you want to delete "${courseCode} - ${courseName}"? This course has ${classCount} associated class(es). You must remove or reassign these classes before deleting the course.`
    }
    return `Are you sure you want to delete "${courseCode} - ${courseName}"? This action cannot be undone. Consider deactivating the course instead if it has historical data.`
  }

  return (
    <>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => setIsDeleteDialogOpen(true)}
        disabled={isDeleting}
      >
        {isDeleting ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Trash2 className="w-4 h-4 text-red-600" />
        )}
      </Button>

      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Delete Course"
        description={getDeleteMessage()}
        confirmText={classCount > 0 ? "Cannot Delete" : "Delete"}
        cancelText="Cancel"
        variant="destructive"
        onConfirm={classCount > 0 ? undefined : handleDelete}
        disabled={classCount > 0}
      />
    </>
  )
}
