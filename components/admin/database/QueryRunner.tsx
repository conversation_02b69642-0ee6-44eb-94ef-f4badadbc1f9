"use client"

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Play, 
  Save, 
  History, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  FileText
} from 'lucide-react'

interface QueryResult {
  success: boolean
  data?: any[]
  error?: string
  executionTime?: number
  rowsAffected?: number
}

export function QueryRunner() {
  const [query, setQuery] = useState('')
  const [result, setResult] = useState<QueryResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [queryHistory, setQueryHistory] = useState<string[]>([])

  const commonQueries = [
    {
      name: 'Show all tables',
      query: `SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;`
    },
    {
      name: 'User count by role',
      query: `SELECT role, COUNT(*) as count 
FROM users 
GROUP BY role 
ORDER BY count DESC;`
    },
    {
      name: 'Recent faculty profiles',
      query: `SELECT u.name, u.email, fp.title, d.name as department
FROM users u
JOIN faculty_profiles fp ON u.id = fp.user_id
JOIN departments d ON fp.department_id = d.id
WHERE u.status = 'ACTIVE'
ORDER BY u.created_at DESC
LIMIT 10;`
    },
    {
      name: 'Database size info',
      query: `SELECT 
  schemaname,
  tablename,
  attname,
  n_distinct,
  correlation
FROM pg_stats
WHERE schemaname = 'public'
ORDER BY tablename, attname;`
    }
  ]

  const executeQuery = async () => {
    if (!query.trim()) return

    try {
      setLoading(true)
      setResult(null)

      const response = await fetch('/api/admin/database/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: query.trim() }),
      })

      const data = await response.json()
      setResult(data)

      // Add to history if successful
      if (data.success && !queryHistory.includes(query.trim())) {
        setQueryHistory(prev => [query.trim(), ...prev.slice(0, 9)]) // Keep last 10 queries
      }
    } catch (error) {
      setResult({
        success: false,
        error: 'Failed to execute query'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadQuery = (queryText: string) => {
    setQuery(queryText)
  }

  const formatQueryResult = (data: any[]) => {
    if (!data || data.length === 0) return null

    const columns = Object.keys(data[0])
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Badge variant="outline">
            {data.length} row{data.length !== 1 ? 's' : ''} returned
          </Badge>
        </div>
        
        <ScrollArea className="h-[400px]">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b">
                {columns.map((column) => (
                  <th key={column} className="text-left p-2 font-medium text-sm bg-muted/50">
                    {column}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {data.map((row, index) => (
                <tr key={index} className="border-b hover:bg-muted/50">
                  {columns.map((column) => (
                    <td key={column} className="p-2 text-sm">
                      {row[column] === null ? (
                        <span className="text-gray-400 italic">null</span>
                      ) : (
                        String(row[column])
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </ScrollArea>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Query Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="h-5 w-5 mr-2" />
            SQL Query Runner
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">SQL Query</label>
            <Textarea
              placeholder="Enter your SQL query here..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="min-h-[120px] font-mono text-sm"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button 
                onClick={executeQuery} 
                disabled={loading || !query.trim()}
                className="flex items-center"
              >
                <Play className="h-4 w-4 mr-2" />
                {loading ? 'Executing...' : 'Execute Query'}
              </Button>
              <Button variant="outline" size="sm">
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>
            
            <div className="text-xs text-muted-foreground">
              ⚠️ Be careful with UPDATE, DELETE, and DROP statements
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Common Queries */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Common Queries
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {commonQueries.map((item, index) => (
              <div
                key={index}
                className="p-3 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                onClick={() => loadQuery(item.query)}
              >
                <h4 className="font-medium text-sm mb-2">{item.name}</h4>
                <pre className="text-xs text-muted-foreground bg-muted/30 p-2 rounded overflow-x-auto">
                  {item.query.split('\n')[0]}...
                </pre>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Query History */}
      {queryHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <History className="h-5 w-5 mr-2" />
              Query History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {queryHistory.map((historyQuery, index) => (
                <div
                  key={index}
                  className="p-2 border rounded cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => loadQuery(historyQuery)}
                >
                  <pre className="text-xs text-muted-foreground truncate">
                    {historyQuery}
                  </pre>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Query Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              {result.success ? (
                <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
              ) : (
                <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
              )}
              Query Results
            </CardTitle>
            {result.success && result.executionTime && (
              <div className="flex items-center text-sm text-muted-foreground">
                <Clock className="h-4 w-4 mr-1" />
                Executed in {result.executionTime}ms
              </div>
            )}
          </CardHeader>
          <CardContent>
            {result.success ? (
              result.data && result.data.length > 0 ? (
                formatQueryResult(result.data)
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    Query executed successfully
                    {result.rowsAffected !== undefined && (
                      <span className="block mt-1">
                        {result.rowsAffected} row(s) affected
                      </span>
                    )}
                  </p>
                </div>
              )
            ) : (
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
                <p className="text-red-600 font-medium mb-2">Query Error</p>
                <pre className="text-sm text-red-600 bg-red-50 p-4 rounded border text-left">
                  {result.error}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
} 