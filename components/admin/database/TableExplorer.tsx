"use client"

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Table, 
  Search, 
  RefreshCw, 
  ChevronLeft, 
  ChevronRight,
  Eye,
  Edit,
  Trash2,
  Plus,
  Filter,
  Download,
  AlertTriangle,
  Maximize2,
  Minimize2,
  MoveHorizontal,
  ChevronsRight
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface DatabaseTable {
  name: string
  count: number
  columns: Array<{
    name: string
    type: string
    nullable: boolean
    default: string | null
  }>
}

interface TableExplorerProps {
  tableName: string
  tableInfo?: DatabaseTable
}

interface TableData {
  rows: Record<string, any>[]
  totalCount: number
  columns: Array<{
    name: string
    type: string
    nullable: boolean
    default: string | null
  }>
}

export function TableExplorer({ tableName, tableInfo }: TableExplorerProps) {
  const [data, setData] = useState<TableData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [pageSize] = useState(20)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortColumn, setSortColumn] = useState<string>('')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({})
  const [expandedCells, setExpandedCells] = useState<Record<string, boolean>>({})
  const [isFullscreen, setIsFullscreen] = useState(false)
  const tableContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (tableName) {
      fetchTableData()
    }
  }, [tableName, page, searchTerm, sortColumn, sortDirection])

  const fetchTableData = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        table: tableName,
        page: page.toString(),
        pageSize: pageSize.toString(),
        search: searchTerm,
        sortColumn,
        sortDirection
      })

      const response = await fetch(`/api/admin/database/table-data?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch table data')
      }

      const result = await response.json()
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(column)
      setSortDirection('asc')
    }
  }

  const toggleCellExpand = (rowIndex: number, columnName: string) => {
    const cellId = `${rowIndex}-${columnName}`
    setExpandedCells(prev => ({
      ...prev,
      [cellId]: !prev[cellId]
    }))
  }

  const startColumnResize = (columnName: string, initialWidth: number, e: React.MouseEvent) => {
    e.preventDefault()
    
    const startX = e.clientX
    const currentWidth = columnWidths[columnName] || initialWidth

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - startX
      const newWidth = Math.max(150, currentWidth + deltaX)
      setColumnWidths(prev => ({
        ...prev,
        [columnName]: newWidth
      }))
    }

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const formatValue = (value: any, type: string, rowIndex: number, columnName: string) => {
    if (value === null) return <span className="text-gray-400 italic">null</span>
    if (value === undefined) return <span className="text-gray-400 italic">undefined</span>
    
    if (type.includes('timestamp') || type.includes('date')) {
      return new Date(value).toLocaleString()
    }
    
    if (type === 'boolean') {
      return (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'true' : 'false'}
        </Badge>
      )
    }
    
    const cellId = `${rowIndex}-${columnName}`
    const isExpanded = expandedCells[cellId]
    
    if (typeof value === 'string' && value.length > 100) {
      return (
        <div className="relative">
          {isExpanded ? (
            <div className="whitespace-pre-wrap break-words">{value}</div>
          ) : (
            <div className="truncate">{value.substring(0, 100)}...</div>
          )}
          <Button 
            variant="ghost" 
            size="sm" 
            className="absolute top-0 right-0 h-6 w-6 p-0 rounded-full hover:bg-blue-100"
            onClick={(e) => {
              e.stopPropagation();
              toggleCellExpand(rowIndex, columnName);
            }}
          >
            {isExpanded ? 
              <Minimize2 className="h-3 w-3 text-blue-600" /> : 
              <ChevronsRight className="h-3 w-3 text-blue-600" />
            }
          </Button>
        </div>
      )
    }
    
    return String(value)
  }

  const totalPages = data ? Math.ceil(data.totalCount / pageSize) : 0

  return (
    <div className={`space-y-6 ${isFullscreen ? 'fixed inset-0 bg-white z-50 p-6 overflow-auto' : ''}`}>
      {isFullscreen && (
        <div className="fixed top-4 right-4 z-50">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={toggleFullscreen}
            className="bg-white border-red-200 hover:bg-red-50 text-red-700"
          >
            <Minimize2 className="h-4 w-4 mr-2" />
            Exit Fullscreen
          </Button>
        </div>
      )}
      
      {/* Enhanced Table Header */}
      <Card className="border-0 bg-gradient-to-r from-indigo-50 to-purple-50 shadow-xl">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <CardTitle className="flex items-center text-2xl text-gray-800">
                <div className="rounded-xl bg-indigo-100 p-3 mr-4">
                  <Table className="h-6 w-6 text-indigo-600" />
                </div>
                <div>
                  <span className="gradient-text">{tableName}</span>
                  <div className="text-sm font-normal text-gray-600 mt-1">
                    {tableInfo ? (
                      <div className="flex items-center space-x-4">
                        <span className="flex items-center">
                          <div className="h-2 w-2 bg-green-500 rounded-full mr-2"></div>
                          {tableInfo.count.toLocaleString()} records
                        </span>
                        <span className="flex items-center">
                          <div className="h-2 w-2 bg-blue-500 rounded-full mr-2"></div>
                          {tableInfo.columns.length} columns
                        </span>
                      </div>
                    ) : (
                      'Loading table information...'
                    )}
                  </div>
                </div>
              </CardTitle>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={fetchTableData}
                disabled={loading}
                className="border-indigo-200 hover:bg-indigo-50 hover:border-indigo-300"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleFullscreen}
                className="border-purple-200 hover:bg-purple-50 hover:border-purple-300"
              >
                {isFullscreen ? 
                  <Minimize2 className="h-4 w-4 mr-2" /> : 
                  <Maximize2 className="h-4 w-4 mr-2" />
                }
                {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
              </Button>
              <Button variant="outline" size="sm" className="border-purple-200 hover:bg-purple-50 hover:border-purple-300">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Enhanced Search and Filters */}
      <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-center space-x-6">
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                placeholder="Search across all columns..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 pr-4 py-3 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 rounded-xl text-sm"
              />
              {searchTerm && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Badge variant="secondary" className="text-xs">
                    Searching...
                  </Badge>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm" className="border-gray-200 hover:bg-gray-50 rounded-lg">
                <Filter className="h-4 w-4 mr-2" />
                Advanced Filters
              </Button>
              <div className="text-sm text-gray-500">
                {data ? `${data.totalCount.toLocaleString()} total` : ''}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Table Data */}
      <Card className={`border-0 bg-white/90 backdrop-blur-sm shadow-xl ${isFullscreen ? 'flex-1 max-h-[calc(100vh-250px)]' : ''}`}>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-center space-y-4">
                <div className="relative">
                  <RefreshCw className="h-8 w-8 text-blue-500 animate-spin mx-auto" />
                  <div className="absolute inset-0 rounded-full border-4 border-blue-200 border-t-blue-500 animate-spin"></div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-gray-800">Loading Data</h3>
                  <p className="text-sm text-gray-600">Fetching records from {tableName}...</p>
                </div>
                <div className="flex justify-center space-x-1">
                  <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce"></div>
                  <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-center space-y-4">
                <div className="rounded-full bg-red-100 p-4 mx-auto w-fit">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-red-800">Error Loading Data</h3>
                  <p className="text-red-600 max-w-md">{error}</p>
                </div>
                <Button onClick={fetchTableData} variant="outline" className="border-red-200 hover:bg-red-50">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </div>
          ) : data && data.rows.length > 0 ? (
            <div className="border border-gray-200 rounded-lg overflow-hidden h-full">
              {/* Table container with both horizontal and vertical scrolling */}
              <div 
                className="overflow-auto" 
                style={{ 
                  maxHeight: isFullscreen ? "calc(100vh - 300px)" : "600px",
                  minHeight: "400px"
                }} 
                ref={tableContainerRef}
              >
                <table 
                  className="w-full table-auto" 
                  style={{ 
                    minWidth: Math.max(800, data.columns.length * 180 + 120) + "px"
                  }}
                >
                  <thead className="bg-gradient-to-r from-gray-50 to-slate-50 sticky top-0 z-20">
                    <tr>
                      {data.columns.map((column, colIndex) => (
                        <th
                          key={column.name}
                          className={`px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors border-b border-gray-200 whitespace-nowrap min-w-[180px] relative ${colIndex === 0 ? 'sticky left-0 z-20 bg-gray-50' : ''}`}
                          onClick={() => handleSort(column.name)}
                          style={{ 
                            width: columnWidths[column.name] ? `${columnWidths[column.name]}px` : undefined,
                            boxShadow: colIndex === 0 ? '2px 0 5px rgba(0,0,0,0.05)' : 'none'
                          }}
                        >
                          <div className="flex items-center space-x-2">
                            <span className="truncate max-w-[100px]" title={column.name}>
                              {column.name}
                            </span>
                            <Badge 
                              variant="outline" 
                              className="text-xs bg-white border-gray-300 text-gray-600 shrink-0"
                            >
                              {column.type}
                            </Badge>
                            {sortColumn === column.name && (
                              <span className="text-blue-600 font-bold shrink-0">
                                {sortDirection === 'asc' ? '↑' : '↓'}
                              </span>
                            )}
                          </div>
                          <div 
                            className="absolute right-0 top-0 h-full w-2 cursor-col-resize hover:bg-blue-400"
                            onMouseDown={(e) => startColumnResize(column.name, 180, e)}
                          >
                            <div className="h-full w-[3px] bg-gray-200 opacity-0 hover:opacity-100"></div>
                          </div>
                        </th>
                      ))}
                      <th className="px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider border-b border-gray-200 sticky right-0 bg-gray-50 min-w-[120px] shadow-lg z-20">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-100">
                    {data.rows.map((row, rowIndex) => {
                      const firstColumnName = data.columns[0]?.name;
                      
                      return (
                        <tr 
                          key={rowIndex} 
                          className="hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-200"
                        >
                          {data.columns.map((column, colIndex) => {
                            const cellId = `${rowIndex}-${column.name}`;
                            const isExpanded = expandedCells[cellId];
                            const cellValue = row[column.name];
                            const hasLongText = typeof cellValue === 'string' && cellValue.length > 100;
                            
                            return (
                              <td 
                                key={column.name} 
                                className={`px-6 py-4 text-sm text-gray-900 ${isExpanded ? '' : 'max-h-[100px]'} overflow-hidden relative ${colIndex === 0 ? 'sticky left-0 z-10 bg-white' : ''}`}
                                style={{ 
                                  width: columnWidths[column.name] ? `${columnWidths[column.name]}px` : undefined,
                                  minWidth: "180px",
                                  boxShadow: colIndex === 0 ? '2px 0 5px rgba(0,0,0,0.05)' : 'none'
                                }}
                              >
                                {hasLongText ? (
                                  <>
                                    <div className={isExpanded ? "whitespace-pre-wrap break-words" : "truncate"}>
                                      {isExpanded ? cellValue : cellValue.substring(0, 100) + '...'}
                                    </div>
                                    <TooltipProvider>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Button 
                                            variant="ghost" 
                                            size="sm" 
                                            className="absolute top-2 right-2 h-6 w-6 p-0 rounded-full hover:bg-blue-100"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              toggleCellExpand(rowIndex, column.name);
                                            }}
                                          >
                                            {isExpanded ? 
                                              <Minimize2 className="h-3 w-3 text-blue-600" /> : 
                                              <ChevronsRight className="h-3 w-3 text-blue-600" />
                                            }
                                          </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          {isExpanded ? 'Collapse' : 'Expand'}
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  </>
                                ) : (
                                  <div className="truncate">
                                    {formatValue(cellValue, column.type, rowIndex, column.name)}
                                  </div>
                                )}
                                <Dialog>
                                  <DialogTrigger asChild>
                                    <Button 
                                      variant="ghost" 
                                      size="sm" 
                                      className="absolute top-2 right-8 h-6 w-6 p-0 rounded-full hover:bg-green-100 opacity-0 group-hover:opacity-100"
                                    >
                                      <Maximize2 className="h-3 w-3 text-green-600" />
                                    </Button>
                                  </DialogTrigger>
                                  <DialogContent className="max-w-3xl">
                                    <DialogHeader>
                                      <DialogTitle>Cell Content: {column.name}</DialogTitle>
                                      <DialogDescription>
                                        Row {rowIndex + 1}, Column: {column.name} ({column.type})
                                      </DialogDescription>
                                    </DialogHeader>
                                    <div className="max-h-[70vh] overflow-auto p-4 bg-gray-50 rounded border">
                                      <pre className="whitespace-pre-wrap break-words">{cellValue === null ? 'null' : String(cellValue)}</pre>
                                    </div>
                                  </DialogContent>
                                </Dialog>
                              </td>
                            );
                          })}
                          <td className="px-6 py-4 text-right text-sm sticky right-0 bg-white min-w-[120px] shadow-lg z-20">
                            <div className="flex items-center justify-end space-x-1">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button 
                                      variant="ghost" 
                                      size="sm" 
                                      className="hover:bg-blue-100 hover:text-blue-700 rounded-lg"
                                    >
                                      <Eye className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>View details</TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button 
                                      variant="ghost" 
                                      size="sm" 
                                      className="hover:bg-green-100 hover:text-green-700 rounded-lg"
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>Edit record</TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button 
                                      variant="ghost" 
                                      size="sm" 
                                      className="hover:bg-red-100 hover:text-red-700 rounded-lg"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>Delete record</TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
              
              {/* Improved horizontal scroll indicator */}
              <div className="bg-blue-50 px-4 py-3 border-t border-blue-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <div className="animate-pulse mr-2">
                        <MoveHorizontal className="h-4 w-4 text-blue-600" />
                      </div>
                      <span className="text-sm text-blue-700 font-medium">Scroll horizontally to see all {data.columns.length} columns</span>
                    </div>
                    
                    <div className="h-4 w-px bg-blue-200 mx-1"></div>
                    
                    <div className="flex items-center text-sm text-blue-600">
                      <span className="inline-flex items-center">
                        <div className="h-2 w-2 bg-blue-500 rounded-full mr-1"></div>
                        Drag column edges to resize
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={toggleFullscreen}
                      className="border-blue-200 bg-white hover:bg-blue-50 text-blue-700"
                    >
                      {isFullscreen ? <Minimize2 className="h-4 w-4 mr-1" /> : <Maximize2 className="h-4 w-4 mr-1" />}
                      {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Table className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No data found in this table</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Pagination */}
      {data && data.totalCount > pageSize && (
        <Card className="border-0 bg-gradient-to-r from-slate-50 to-gray-50 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-600">
                  Showing <span className="font-semibold text-gray-900">{(page - 1) * pageSize + 1}</span> to{' '}
                  <span className="font-semibold text-gray-900">{Math.min(page * pageSize, data.totalCount)}</span> of{' '}
                  <span className="font-semibold text-gray-900">{data.totalCount.toLocaleString()}</span> records
                </div>
                <Badge variant="outline" className="bg-white border-gray-300">
                  {pageSize} per page
                </Badge>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={page === 1}
                  className="border-gray-300 hover:bg-white hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                
                <div className="flex items-center space-x-1">
                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, page - 2)) + i
                    if (pageNum > totalPages) return null
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setPage(pageNum)}
                        className={`w-10 h-10 ${
                          pageNum === page 
                            ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </Button>
                    )
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page === totalPages}
                  className="border-gray-300 hover:bg-white hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 