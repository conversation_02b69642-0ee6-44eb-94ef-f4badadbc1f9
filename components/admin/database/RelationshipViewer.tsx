import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { GitBranch, ArrowRight, Database, Key } from 'lucide-react'

interface DatabaseTable {
  name: string
  count: number
  columns: Array<{
    name: string
    type: string
    nullable: boolean
    default: string | null
  }>
}

interface RelationshipViewerProps {
  tables: DatabaseTable[]
}

export function RelationshipViewer({ tables }: RelationshipViewerProps) {
  // Common relationship patterns based on naming conventions
  const detectRelationships = () => {
    const relationships: Array<{
      from: string
      to: string
      type: 'one-to-many' | 'many-to-many' | 'one-to-one'
      foreignKey: string
    }> = []

    tables.forEach(table => {
      table.columns.forEach(column => {
        // Detect foreign key patterns
        if (column.name.endsWith('_id') || column.name.endsWith('Id')) {
          const referencedTable = column.name.replace(/_id$|Id$/, '')
          const pluralTable = referencedTable + 's'
          
          // Check if referenced table exists
          const targetTable = tables.find(t => 
            t.name === referencedTable || 
            t.name === pluralTable ||
            t.name === referencedTable.toLowerCase() ||
            t.name === pluralTable.toLowerCase()
          )
          
          if (targetTable) {
            relationships.push({
              from: table.name,
              to: targetTable.name,
              type: 'one-to-many',
              foreignKey: column.name
            })
          }
        }
      })
    })

    // Detect many-to-many relationships (junction tables)
    tables.forEach(table => {
      const foreignKeys = table.columns.filter(col => 
        col.name.endsWith('_id') || col.name.endsWith('Id')
      )
      
      if (foreignKeys.length === 2 && table.columns.length <= 4) {
        // Likely a junction table
        const table1 = foreignKeys[0].name.replace(/_id$|Id$/, '')
        const table2 = foreignKeys[1].name.replace(/_id$|Id$/, '')
        
        relationships.push({
          from: table1,
          to: table2,
          type: 'many-to-many',
          foreignKey: table.name
        })
      }
    })

    return relationships
  }

  const relationships = detectRelationships()

  // Group tables by category
  const categorizeTable = (tableName: string) => {
    if (tableName.includes('user') || tableName.includes('auth') || tableName.includes('profile')) {
      return 'User & Auth'
    }
    if (tableName.includes('faculty') || tableName.includes('course') || tableName.includes('program') || tableName.includes('department')) {
      return 'Academic'
    }
    if (tableName.includes('post') || tableName.includes('page') || tableName.includes('content')) {
      return 'Content'
    }
    return 'Other'
  }

  const categories = ['User & Auth', 'Academic', 'Content', 'Other']
  const categorizedTables = categories.reduce((acc, category) => {
    acc[category] = tables.filter(table => categorizeTable(table.name) === category)
    return acc
  }, {} as Record<string, DatabaseTable[]>)

  return (
    <div className="space-y-6">
      {/* Relationships Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <GitBranch className="h-5 w-5 mr-2" />
            Database Relationships ({relationships.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {relationships.length > 0 ? (
            <ScrollArea className="h-[300px]">
              <div className="space-y-3">
                {relationships.map((rel, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline" className="font-mono text-xs">
                        {rel.from}
                      </Badge>
                      <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      <Badge variant="outline" className="font-mono text-xs">
                        {rel.to}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className="text-xs">
                        {rel.type}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        <Key className="h-3 w-3 mr-1" />
                        {rel.foreignKey}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="text-center py-8">
              <GitBranch className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No relationships detected</p>
              <p className="text-sm text-muted-foreground mt-2">
                Relationships are detected based on naming conventions (columns ending with _id)
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Table Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {categories.map(category => (
          categorizedTables[category].length > 0 && (
            <Card key={category}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="h-5 w-5 mr-2" />
                  {category} Tables ({categorizedTables[category].length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {categorizedTables[category].map(table => (
                    <div key={table.name} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm">{table.name}</h4>
                        <Badge variant="secondary" className="text-xs">
                          {table.count} records
                        </Badge>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="text-xs text-muted-foreground">
                          Columns ({table.columns.length}):
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {table.columns.slice(0, 6).map(column => (
                            <Badge 
                              key={column.name} 
                              variant="outline" 
                              className="text-xs"
                            >
                              {column.name}
                              {(column.name.endsWith('_id') || column.name.endsWith('Id')) && (
                                <Key className="h-3 w-3 ml-1" />
                              )}
                            </Badge>
                          ))}
                          {table.columns.length > 6 && (
                            <Badge variant="outline" className="text-xs">
                              +{table.columns.length - 6} more
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Show relationships for this table */}
                      {relationships.filter(rel => rel.from === table.name || rel.to === table.name).length > 0 && (
                        <div className="mt-3 pt-3 border-t">
                          <div className="text-xs text-muted-foreground mb-2">Relationships:</div>
                          <div className="space-y-1">
                            {relationships
                              .filter(rel => rel.from === table.name || rel.to === table.name)
                              .map((rel, index) => (
                                <div key={index} className="flex items-center text-xs">
                                  {rel.from === table.name ? (
                                    <>
                                      <span className="text-muted-foreground">→ {rel.to}</span>
                                      <Badge variant="outline" className="ml-2 text-xs">
                                        {rel.type}
                                      </Badge>
                                    </>
                                  ) : (
                                    <>
                                      <span className="text-muted-foreground">← {rel.from}</span>
                                      <Badge variant="outline" className="ml-2 text-xs">
                                        {rel.type}
                                      </Badge>
                                    </>
                                  )}
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )
        ))}
      </div>

      {/* Schema Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Schema Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{tables.length}</div>
              <div className="text-sm text-muted-foreground">Total Tables</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{relationships.length}</div>
              <div className="text-sm text-muted-foreground">Relationships</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {tables.reduce((sum, table) => sum + table.columns.length, 0)}
              </div>
              <div className="text-sm text-muted-foreground">Total Columns</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {tables.reduce((sum, table) => 
                  sum + table.columns.filter(col => 
                    col.name.endsWith('_id') || col.name.endsWith('Id')
                  ).length, 0
                )}
              </div>
              <div className="text-sm text-muted-foreground">Foreign Keys</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 