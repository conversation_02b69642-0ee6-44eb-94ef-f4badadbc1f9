"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Database,
  Table,
  Search,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Plus,
  Download,
  Upload,
  AlertCircle,
  CheckCircle,
  Info,
  BarChart3,
  Users,
  FileText,
  Building2,
  Calendar,
  Tag,
  User,
  Key,
  Clock,
  GraduationCap
} from 'lucide-react'
import { TableExplorer } from './database/TableExplorer'
import { QueryRunner } from './database/QueryRunner'
import { DatabaseStats } from './database/DatabaseStats'
import { RelationshipViewer } from './database/RelationshipViewer'

interface DatabaseTable {
  name: string
  displayName: string
  description: string
  category: string
  icon: string
  count: number
  size: number
  columns: Array<{
    name: string
    type: string
    nullable: boolean
    default: string | null
    isPrimary?: boolean
    isForeignKey?: boolean
    foreignKey?: { table: string; column: string } | null
  }>
  primaryKeys: string[]
  foreignKeys: string[]
  lastModified: string
}

export function DatabaseDebugClient() {
  const [tables, setTables] = useState<DatabaseTable[]>([])
  const [selectedTable, setSelectedTable] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch database schema and table information
  useEffect(() => {
    fetchDatabaseInfo()
  }, [])

  const fetchDatabaseInfo = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/admin/database/schema')
      if (!response.ok) {
        throw new Error('Failed to fetch database information')
      }
      
      const data = await response.json()
      setTables(data.tables || [])
      
      if (data.tables && data.tables.length > 0 && !selectedTable) {
        setSelectedTable(data.tables[0].name)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const filteredTables = tables.filter(table =>
    table.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    table.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    table.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    table.category.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Group tables by category for better organization
  const tablesByCategory = filteredTables.reduce((acc, table) => {
    if (!acc[table.category]) {
      acc[table.category] = []
    }
    acc[table.category].push(table)
    return acc
  }, {} as Record<string, DatabaseTable[]>)

  // Icon mapping for table categories
  const iconMap: Record<string, React.ElementType> = {
    Users,
    FileText,
    GraduationCap,
    Building2,
    BookOpen: Calendar,
    Calendar,
    Tag,
    User,
    Key,
    Clock,
    Database
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <Database className="h-6 w-6 animate-spin" />
            <span>Loading database information...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-700 mb-2">Database Connection Error</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchDatabaseInfo} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Connection
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Database Overview */}
      <div className="animate-fade-in">
        <DatabaseStats
          tables={tables}
          onRefresh={fetchDatabaseInfo}
          isRefreshing={loading}
        />
      </div>

      {/* Enhanced Main Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Enhanced Table List Sidebar */}
        <Card className="lg:col-span-1 border-0 bg-white/80 backdrop-blur-sm shadow-xl">
          <CardHeader className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-t-lg">
            <CardTitle className="flex items-center text-gray-800">
              <div className="rounded-lg bg-blue-100 p-2 mr-3">
                <Table className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <span className="text-lg">Database Tables</span>
                <div className="text-sm font-normal text-gray-600">
                  {tables.length} tables found
                </div>
              </div>
            </CardTitle>
            <div className="relative mt-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search tables..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
              />
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-[600px]">
              <div className="p-4 space-y-6">
                {Object.entries(tablesByCategory).map(([category, categoryTables]) => (
                  <div key={category} className="space-y-3">
                    {/* Category Header */}
                    <div className="flex items-center space-x-2 px-2">
                      <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"></div>
                      <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wider">
                        {category}
                      </h3>
                      <div className="flex-1 h-px bg-gray-200"></div>
                      <Badge variant="outline" className="text-xs">
                        {categoryTables.length}
                      </Badge>
                    </div>

                    {/* Tables in Category */}
                    {categoryTables.map((table, index) => {
                      const IconComponent = iconMap[table.icon] || Database

                      return (
                        <div
                          key={table.name}
                          className={`group relative p-4 rounded-xl cursor-pointer transition-all duration-200 transform hover:scale-[1.02] ${
                            selectedTable === table.name
                              ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                              : 'bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setSelectedTable(table.name)}
                          style={{ animationDelay: `${index * 50}ms` }}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3 flex-1 min-w-0">
                              <div className={`p-2 rounded-lg ${
                                selectedTable === table.name
                                  ? 'bg-white/20'
                                  : 'bg-blue-100'
                              }`}>
                                <IconComponent className={`h-4 w-4 ${
                                  selectedTable === table.name
                                    ? 'text-white'
                                    : 'text-blue-600'
                                }`} />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2">
                                  <span className="font-semibold text-sm truncate">{table.displayName}</span>
                                  {table.primaryKeys.length > 0 && (
                                    <Badge
                                      variant="outline"
                                      className={`text-xs ${
                                        selectedTable === table.name
                                          ? 'bg-white/20 text-white border-white/30'
                                          : 'bg-yellow-50 text-yellow-700 border-yellow-200'
                                      }`}
                                    >
                                      PK
                                    </Badge>
                                  )}
                                  {table.foreignKeys.length > 0 && (
                                    <Badge
                                      variant="outline"
                                      className={`text-xs ${
                                        selectedTable === table.name
                                          ? 'bg-white/20 text-white border-white/30'
                                          : 'bg-green-50 text-green-700 border-green-200'
                                      }`}
                                    >
                                      FK
                                    </Badge>
                                  )}
                                </div>
                                <p className={`text-xs mt-1 truncate ${
                                  selectedTable === table.name
                                    ? 'text-blue-100'
                                    : 'text-gray-500'
                                }`}>
                                  {table.description}
                                </p>
                                <div className={`text-xs mt-2 flex items-center space-x-3 ${
                                  selectedTable === table.name
                                    ? 'text-blue-100'
                                    : 'text-gray-500'
                                }`}>
                                  <span>{table.columns.length} columns</span>
                                  <span>•</span>
                                  <span>{(table.size / 1024).toFixed(1)} KB</span>
                                </div>
                              </div>
                            </div>
                            <div className="flex flex-col items-end space-y-1">
                              <Badge
                                variant={selectedTable === table.name ? "secondary" : "outline"}
                                className={`text-xs font-medium ${
                                  selectedTable === table.name
                                    ? 'bg-white/20 text-white border-white/30'
                                    : 'bg-blue-50 text-blue-700 border-blue-200'
                                }`}
                              >
                                {table.count.toLocaleString()}
                              </Badge>
                              <div className={`text-xs ${
                                selectedTable === table.name
                                  ? 'text-blue-100'
                                  : 'text-gray-400'
                              }`}>
                                records
                              </div>
                            </div>
                          </div>

                          {/* Hover effect indicator */}
                          <div className={`absolute inset-0 rounded-xl border-2 transition-opacity ${
                            selectedTable === table.name
                              ? 'border-white/30 opacity-100'
                              : 'border-blue-300 opacity-0 group-hover:opacity-100'
                          }`}></div>
                        </div>
                      )
                    })}
                  </div>
                ))}

                {filteredTables.length === 0 && (
                  <div className="text-center py-8">
                    <Database className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500 text-sm">No tables found</p>
                    <p className="text-gray-400 text-xs">Try adjusting your search</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Enhanced Main Content Area */}
        <div className="lg:col-span-3">
          <Tabs defaultValue="explorer" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-xl p-2">
              <TabsTrigger 
                value="explorer"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg rounded-lg transition-all duration-200"
              >
                <Eye className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Data Explorer</span>
                <span className="sm:hidden">Explorer</span>
              </TabsTrigger>
              <TabsTrigger 
                value="query"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-lg rounded-lg transition-all duration-200"
              >
                <Database className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">SQL Runner</span>
                <span className="sm:hidden">Query</span>
              </TabsTrigger>
              <TabsTrigger 
                value="relationships"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-600 data-[state=active]:text-white data-[state=active]:shadow-lg rounded-lg transition-all duration-200"
              >
                <Info className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Schema</span>
                <span className="sm:hidden">Relations</span>
              </TabsTrigger>
              <TabsTrigger 
                value="tools"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-600 data-[state=active]:text-white data-[state=active]:shadow-lg rounded-lg transition-all duration-200"
              >
                <Plus className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Tools</span>
                <span className="sm:hidden">Tools</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="explorer" className="space-y-6 mt-6">
              {selectedTable ? (
                <div className="animate-fade-in">
                  <TableExplorer 
                    tableName={selectedTable} 
                    tableInfo={tables.find(t => t.name === selectedTable)}
                  />
                </div>
              ) : (
                <Card className="border-0 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-xl">
                  <CardContent className="flex items-center justify-center py-16">
                    <div className="text-center space-y-4">
                      <div className="relative">
                        <Table className="h-16 w-16 text-blue-300 mx-auto" />
                        <div className="absolute inset-0 rounded-full border-4 border-blue-200 border-t-blue-500 animate-spin"></div>
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-xl font-semibold text-gray-800">Ready to Explore</h3>
                        <p className="text-gray-600">Select a table from the sidebar to view its data and structure</p>
                      </div>
                      <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                        <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <span>Choose from {tables.length} available tables</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="query" className="space-y-6 mt-6">
              <div className="animate-fade-in">
                <QueryRunner />
              </div>
            </TabsContent>

            <TabsContent value="relationships" className="space-y-6 mt-6">
              <div className="animate-fade-in">
                <RelationshipViewer tables={tables} />
              </div>
            </TabsContent>

            <TabsContent value="tools" className="space-y-6 mt-6">
              <div className="animate-fade-in">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Card className="border-0 bg-gradient-to-br from-green-50 to-emerald-50 shadow-xl hover:shadow-2xl transition-all duration-300">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center text-green-800">
                        <div className="rounded-lg bg-green-100 p-2 mr-3">
                          <Download className="h-5 w-5 text-green-600" />
                        </div>
                        Export Tools
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-green-700 mb-4">
                        Export database content in various formats
                      </p>
                      <div className="space-y-3">
                        <Button variant="outline" className="w-full border-green-200 hover:bg-green-100 hover:border-green-300">
                          <Download className="h-4 w-4 mr-2" />
                          Export as CSV
                        </Button>
                        <Button variant="outline" className="w-full border-green-200 hover:bg-green-100 hover:border-green-300">
                          <Download className="h-4 w-4 mr-2" />
                          Export as JSON
                        </Button>
                        <Button variant="outline" className="w-full border-green-200 hover:bg-green-100 hover:border-green-300">
                          <Download className="h-4 w-4 mr-2" />
                          Export Schema
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 bg-gradient-to-br from-blue-50 to-cyan-50 shadow-xl hover:shadow-2xl transition-all duration-300">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center text-blue-800">
                        <div className="rounded-lg bg-blue-100 p-2 mr-3">
                          <RefreshCw className="h-5 w-5 text-blue-600" />
                        </div>
                        Maintenance
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-blue-700 mb-4">
                        Database maintenance and optimization
                      </p>
                      <div className="space-y-3">
                        <Button 
                          variant="outline" 
                          className="w-full border-blue-200 hover:bg-blue-100 hover:border-blue-300"
                          onClick={fetchDatabaseInfo}
                        >
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Refresh Schema
                        </Button>
                        <Button variant="outline" className="w-full border-blue-200 hover:bg-blue-100 hover:border-blue-300">
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Analyze Tables
                        </Button>
                        <Button variant="outline" className="w-full border-blue-200 hover:bg-blue-100 hover:border-blue-300">
                          <AlertCircle className="h-4 w-4 mr-2" />
                          Check Integrity
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 bg-gradient-to-br from-purple-50 to-pink-50 shadow-xl hover:shadow-2xl transition-all duration-300">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center text-purple-800">
                        <div className="rounded-lg bg-purple-100 p-2 mr-3">
                          <Upload className="h-5 w-5 text-purple-600" />
                        </div>
                        Import Tools
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-purple-700 mb-4">
                        Import data and manage backups
                      </p>
                      <div className="space-y-3">
                        <Button variant="outline" className="w-full border-purple-200 hover:bg-purple-100 hover:border-purple-300">
                          <Upload className="h-4 w-4 mr-2" />
                          Import CSV
                        </Button>
                        <Button variant="outline" className="w-full border-purple-200 hover:bg-purple-100 hover:border-purple-300">
                          <Upload className="h-4 w-4 mr-2" />
                          Import JSON
                        </Button>
                        <Button variant="outline" className="w-full border-purple-200 hover:bg-purple-100 hover:border-purple-300">
                          <Database className="h-4 w-4 mr-2" />
                          Restore Backup
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Quick Stats */}
                <Card className="border-0 bg-gradient-to-r from-gray-50 to-slate-50 shadow-xl">
                  <CardHeader>
                    <CardTitle className="flex items-center text-gray-800">
                      <div className="rounded-lg bg-gray-100 p-2 mr-3">
                        <BarChart3 className="h-5 w-5 text-gray-600" />
                      </div>
                      Quick Statistics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                        <div className="text-2xl font-bold text-blue-600">{tables.length}</div>
                        <div className="text-sm text-gray-600">Tables</div>
                      </div>
                      <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                        <div className="text-2xl font-bold text-green-600">
                          {tables.reduce((sum, table) => sum + table.count, 0).toLocaleString()}
                        </div>
                        <div className="text-sm text-gray-600">Records</div>
                      </div>
                      <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                        <div className="text-2xl font-bold text-purple-600">
                          {tables.reduce((sum, table) => sum + table.columns.length, 0)}
                        </div>
                        <div className="text-sm text-gray-600">Columns</div>
                      </div>
                      <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                        <div className="text-2xl font-bold text-orange-600">
                          {Math.round((tables.reduce((sum, table) => sum + table.count, 0) / tables.length) || 0)}
                        </div>
                        <div className="text-sm text-gray-600">Avg/Table</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
} 