"use client"

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { Search, X, Users, GraduationCap, FileText, Building2, BookOpen, Calendar, Loader2 } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { cn } from '@/lib/utils'

interface SearchResult {
  id: string
  type: 'user' | 'faculty' | 'content' | 'department' | 'program' | 'course'
  title: string
  subtitle: string
  description: string
  url: string
  avatar?: string
  metadata?: any
}

interface SearchResults {
  users: SearchResult[]
  faculty: SearchResult[]
  content: SearchResult[]
  departments: SearchResult[]
  programs: SearchResult[]
  courses: SearchResult[]
  total: number
}

const typeIcons = {
  user: Users,
  faculty: GraduationCap,
  content: FileText,
  department: Building2,
  program: Book<PERSON><PERSON>,
  course: Calendar
}

const typeColors = {
  user: 'text-blue-600',
  faculty: 'text-green-600',
  content: 'text-purple-600',
  department: 'text-orange-600',
  program: 'text-indigo-600',
  course: 'text-pink-600'
}

export function GlobalSearch() {
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResults | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  
  const router = useRouter()
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Debounced search
  useEffect(() => {
    if (!query || query.length < 2) {
      setResults(null)
      return
    }

    const timeoutId = setTimeout(async () => {
      setIsLoading(true)
      try {
        const response = await fetch(`/api/admin/search?q=${encodeURIComponent(query)}&limit=5`)
        const data = await response.json()
        
        if (data.success) {
          setResults(data.results)
        }
      } catch (error) {
        console.error('Search error:', error)
      } finally {
        setIsLoading(false)
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [query])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setIsOpen(true)
        setTimeout(() => inputRef.current?.focus(), 100)
      }

      if (!isOpen) return

      if (e.key === 'Escape') {
        setIsOpen(false)
        setQuery('')
        setResults(null)
        setSelectedIndex(-1)
      }

      if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
        e.preventDefault()
        const allResults = getAllResults()
        if (allResults.length === 0) return

        if (e.key === 'ArrowDown') {
          setSelectedIndex(prev => (prev + 1) % allResults.length)
        } else {
          setSelectedIndex(prev => prev <= 0 ? allResults.length - 1 : prev - 1)
        }
      }

      if (e.key === 'Enter' && selectedIndex >= 0) {
        e.preventDefault()
        const allResults = getAllResults()
        const selectedResult = allResults[selectedIndex]
        if (selectedResult) {
          handleResultClick(selectedResult)
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, selectedIndex, results])

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const getAllResults = (): SearchResult[] => {
    if (!results) return []
    return [
      ...results.users,
      ...results.faculty,
      ...results.content,
      ...results.departments,
      ...results.programs,
      ...results.courses
    ]
  }

  const handleResultClick = (result: SearchResult) => {
    router.push(result.url)
    setIsOpen(false)
    setQuery('')
    setResults(null)
    setSelectedIndex(-1)
  }

  const renderResultGroup = (title: string, items: SearchResult[], icon: React.ElementType) => {
    if (items.length === 0) return null

    const Icon = icon

    return (
      <div className="mb-4">
        <div className="flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider">
          <Icon className="w-3 h-3 mr-2" />
          {title} ({items.length})
        </div>
        {items.map((item, index) => {
          const globalIndex = getAllResults().indexOf(item)
          const ItemIcon = typeIcons[item.type]
          
          return (
            <button
              key={item.id}
              onClick={() => handleResultClick(item)}
              className={cn(
                "w-full flex items-center px-3 py-2 text-left hover:bg-gray-50 transition-colors",
                selectedIndex === globalIndex && "bg-blue-50 border-r-2 border-blue-500"
              )}
            >
              <div className="flex items-center flex-1 min-w-0">
                {item.avatar ? (
                  <Avatar className="w-8 h-8 mr-3">
                    <AvatarImage src={item.avatar} alt={item.title} />
                    <AvatarFallback>
                      <ItemIcon className={cn("w-4 h-4", typeColors[item.type])} />
                    </AvatarFallback>
                  </Avatar>
                ) : (
                  <div className="w-8 h-8 mr-3 rounded-full bg-gray-100 flex items-center justify-center">
                    <ItemIcon className={cn("w-4 h-4", typeColors[item.type])} />
                  </div>
                )}
                
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm text-gray-900 truncate">
                    {item.title}
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    {item.subtitle}
                  </div>
                  {item.description && (
                    <div className="text-xs text-gray-400 truncate mt-1">
                      {item.description}
                    </div>
                  )}
                </div>
                
                <Badge variant="secondary" className="ml-2 text-xs">
                  {item.type}
                </Badge>
              </div>
            </button>
          )
        })}
      </div>
    )
  }

  return (
    <>
      {/* Search Trigger Button */}
      <Button
        variant="outline"
        onClick={() => setIsOpen(true)}
        className="relative w-64 justify-start text-sm text-muted-foreground"
      >
        <Search className="w-4 h-4 mr-2" />
        Search everything...
        <kbd className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      {/* Search Modal */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-start justify-center pt-[10vh]">
          <div
            ref={searchRef}
            className="w-full max-w-2xl mx-4 bg-white rounded-lg shadow-2xl max-h-[70vh] flex flex-col"
          >
            {/* Search Input */}
            <div className="flex items-center border-b px-4 py-3">
              <Search className="w-5 h-5 text-gray-400 mr-3" />
              <Input
                ref={inputRef}
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search users, faculty, content, departments..."
                className="border-0 focus:ring-0 text-base"
                autoFocus
              />
              {isLoading && (
                <Loader2 className="w-4 h-4 text-gray-400 animate-spin mr-2" />
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="ml-2"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Search Results */}
            <div className="flex-1 overflow-y-auto">
              {!query || query.length < 2 ? (
                <div className="p-8 text-center text-gray-500">
                  <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium mb-2">Search everything</p>
                  <p className="text-sm">
                    Find users, faculty, content, departments, programs, and courses
                  </p>
                  <div className="mt-4 text-xs text-gray-400">
                    Use <kbd className="px-1 py-0.5 bg-gray-100 rounded">⌘K</kbd> to open search
                  </div>
                </div>
              ) : isLoading ? (
                <div className="p-8 text-center">
                  <Loader2 className="w-8 h-8 mx-auto animate-spin text-blue-500 mb-4" />
                  <p className="text-gray-500">Searching...</p>
                </div>
              ) : results && results.total > 0 ? (
                <div className="py-2">
                  {renderResultGroup('Users', results.users, Users)}
                  {renderResultGroup('Faculty', results.faculty, GraduationCap)}
                  {renderResultGroup('Content', results.content, FileText)}
                  {renderResultGroup('Departments', results.departments, Building2)}
                  {renderResultGroup('Programs', results.programs, BookOpen)}
                  {renderResultGroup('Courses', results.courses, Calendar)}
                </div>
              ) : (
                <div className="p-8 text-center text-gray-500">
                  <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium mb-2">No results found</p>
                  <p className="text-sm">
                    Try adjusting your search terms or check for typos
                  </p>
                </div>
              )}
            </div>

            {/* Footer */}
            {results && results.total > 0 && (
              <div className="border-t px-4 py-2 text-xs text-gray-500 flex items-center justify-between">
                <span>Found {results.total} results</span>
                <div className="flex items-center space-x-4">
                  <span>↑↓ Navigate</span>
                  <span>↵ Select</span>
                  <span>ESC Close</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  )
}
