"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Save, ArrowLeft } from "lucide-react"
import Link from "next/link"

const programSchema = z.object({
  name: z.string().min(1, 'Program name is required'),
  slug: z.string().min(1, 'Slug is required'),
  departmentId: z.string().min(1, 'Department is required'),
  degreeType: z.string().min(1, 'Degree type is required'),
  duration: z.number().min(1, 'Duration must be at least 1 year').max(10, 'Duration cannot exceed 10 years'),
  description: z.string().optional(),
  imageUrl: z.string().url().optional().or(z.literal('')),
  isActive: z.boolean(),
})

type ProgramFormData = z.infer<typeof programSchema>

interface Department {
  id: string
  name: string
  slug: string
}

interface ProgramFormProps {
  programId?: string
  initialData?: Partial<ProgramFormData>
  departments: Department[]
}

const degreeTypes = [
  'Bachelor',
  'Master',
  'PhD',
  'Diploma',
  'Certificate'
]

export function ProgramForm({ programId, initialData, departments }: ProgramFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const router = useRouter()
  const isEditing = !!programId

  const form = useForm<ProgramFormData>({
    resolver: zodResolver(programSchema),
    defaultValues: {
      name: initialData?.name || '',
      slug: initialData?.slug || '',
      departmentId: initialData?.departmentId || '',
      degreeType: initialData?.degreeType || '',
      duration: initialData?.duration || 4,
      description: initialData?.description || '',
      imageUrl: initialData?.imageUrl || '',
      isActive: initialData?.isActive ?? true,
    }
  })

  // Auto-generate slug from name
  const handleNameChange = (name: string) => {
    form.setValue('name', name)
    if (!isEditing || !initialData?.slug) {
      const slug = name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      form.setValue('slug', slug)
    }
  }

  const onSubmit = async (data: ProgramFormData) => {
    setIsLoading(true)
    
    try {
      const url = isEditing ? `/api/admin/programs/${programId}` : '/api/admin/programs'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || `Failed to ${isEditing ? 'update' : 'create'} program`)
      }

      toast({
        title: "Success",
        description: `Program ${isEditing ? 'updated' : 'created'} successfully.`,
      })

      router.push('/admin/programs')
      router.refresh()
    } catch (error) {
      console.error('Error saving program:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEditing ? 'Edit Program' : 'Add Program'}
          </h1>
          <p className="text-gray-600">
            {isEditing ? 'Update program information and settings' : 'Create a new academic program'}
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/admin/programs">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Programs
          </Link>
        </Button>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>Program details and identification</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Program Name *</Label>
                <Input
                  id="name"
                  {...form.register('name')}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="Bachelor of Technology in Computer Science"
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug *</Label>
                <Input
                  id="slug"
                  {...form.register('slug')}
                  placeholder="btech-computer-science"
                />
                {form.formState.errors.slug && (
                  <p className="text-sm text-red-600">{form.formState.errors.slug.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="departmentId">Department *</Label>
                <Select
                  value={form.watch('departmentId')}
                  onValueChange={(value) => form.setValue('departmentId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((department) => (
                      <SelectItem key={department.id} value={department.id}>
                        {department.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.departmentId && (
                  <p className="text-sm text-red-600">{form.formState.errors.departmentId.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="degreeType">Degree Type *</Label>
                <Select
                  value={form.watch('degreeType')}
                  onValueChange={(value) => form.setValue('degreeType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select degree type" />
                  </SelectTrigger>
                  <SelectContent>
                    {degreeTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.degreeType && (
                  <p className="text-sm text-red-600">{form.formState.errors.degreeType.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="duration">Duration (Years) *</Label>
                <Input
                  id="duration"
                  type="number"
                  min="1"
                  max="10"
                  {...form.register('duration', { valueAsNumber: true })}
                  placeholder="4"
                />
                {form.formState.errors.duration && (
                  <p className="text-sm text-red-600">{form.formState.errors.duration.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...form.register('description')}
                placeholder="Detailed description of the program..."
                rows={4}
              />
              {form.formState.errors.description && (
                <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="imageUrl">Image URL</Label>
              <Input
                id="imageUrl"
                type="url"
                {...form.register('imageUrl')}
                placeholder="https://example.com/program-image.jpg"
              />
              {form.formState.errors.imageUrl && (
                <p className="text-sm text-red-600">{form.formState.errors.imageUrl.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Program Settings</CardTitle>
            <CardDescription>Visibility and status configuration</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={form.watch('isActive')}
                onCheckedChange={(checked) => form.setValue('isActive', checked)}
              />
              <Label htmlFor="isActive">Active Program</Label>
            </div>
            <p className="text-sm text-gray-500">
              Inactive programs will not be visible to students and will not accept new applications.
            </p>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            <Save className="w-4 h-4 mr-2" />
            {isEditing ? 'Update Program' : 'Create Program'}
          </Button>
        </div>
      </form>
    </div>
  )
}
