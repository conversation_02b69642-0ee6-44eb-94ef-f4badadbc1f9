"use client"

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
  isActive?: boolean
}

// Define route mappings for better breadcrumb labels
const routeLabels: Record<string, string> = {
  'admin': 'Dashboard',
  'faculty': 'Faculty Management',
  'users': 'User Management',
  'posts': 'Content Management',
  'departments': 'Departments',
  'programs': 'Programs',
  'courses': 'Courses',
  'analytics': 'Analytics',
  'database': 'Database Debug',
  'settings': 'Settings',
  'new': 'Create New',
  'edit': 'Edit',
  'profile': 'Profile'
}

// Special route patterns that need custom handling
const specialRoutes: Record<string, (segments: string[]) => BreadcrumbItem[]> = {
  '/admin/faculty/[id]/edit': (segments) => [
    { label: 'Dashboard', href: '/admin' },
    { label: 'Faculty Management', href: '/admin/faculty' },
    { label: 'Edit Faculty', isActive: true }
  ],
  '/admin/users/[id]/edit': (segments) => [
    { label: 'Dashboard', href: '/admin' },
    { label: 'User Management', href: '/admin/users' },
    { label: 'Edit User', isActive: true }
  ],
  '/admin/posts/[id]/edit': (segments) => [
    { label: 'Dashboard', href: '/admin' },
    { label: 'Content Management', href: '/admin/posts' },
    { label: 'Edit Post', isActive: true }
  ],
  '/admin/departments/[id]/edit': (segments) => [
    { label: 'Dashboard', href: '/admin' },
    { label: 'Departments', href: '/admin/departments' },
    { label: 'Edit Department', isActive: true }
  ],
  '/admin/programs/[id]/edit': (segments) => [
    { label: 'Dashboard', href: '/admin' },
    { label: 'Programs', href: '/admin/programs' },
    { label: 'Edit Program', isActive: true }
  ],
  '/admin/courses/[id]/edit': (segments) => [
    { label: 'Dashboard', href: '/admin' },
    { label: 'Courses', href: '/admin/courses' },
    { label: 'Edit Course', isActive: true }
  ]
}

function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean)
  
  // Handle root admin path
  if (pathname === '/admin') {
    return [{ label: 'Dashboard', isActive: true }]
  }

  // Check for special route patterns
  for (const [pattern, handler] of Object.entries(specialRoutes)) {
    const patternSegments = pattern.split('/').filter(Boolean)
    
    if (segments.length === patternSegments.length) {
      let matches = true
      for (let i = 0; i < patternSegments.length; i++) {
        if (patternSegments[i].startsWith('[') && patternSegments[i].endsWith(']')) {
          // This is a dynamic segment, skip validation
          continue
        }
        if (patternSegments[i] !== segments[i]) {
          matches = false
          break
        }
      }
      
      if (matches) {
        return handler(segments)
      }
    }
  }

  // Generate breadcrumbs for standard routes
  const breadcrumbs: BreadcrumbItem[] = []
  let currentPath = ''

  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const isLast = index === segments.length - 1
    
    // Skip dynamic IDs in breadcrumbs (they're usually UUIDs)
    if (segment.length > 20 && segment.includes('-')) {
      return
    }

    const label = routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)
    
    breadcrumbs.push({
      label,
      href: isLast ? undefined : currentPath,
      isActive: isLast
    })
  })

  return breadcrumbs
}

export function Breadcrumbs() {
  const pathname = usePathname()
  
  // Only show breadcrumbs in admin routes
  if (!pathname.startsWith('/admin')) {
    return null
  }

  const breadcrumbs = generateBreadcrumbs(pathname)

  // Don't show breadcrumbs if there's only one item (the current page)
  if (breadcrumbs.length <= 1) {
    return null
  }

  return (
    <nav aria-label="Breadcrumb" className="flex items-center space-x-1 text-sm text-gray-500 mb-4">
      <Link
        href="/admin"
        className="flex items-center hover:text-gray-700 transition-colors"
      >
        <Home className="w-4 h-4" />
        <span className="sr-only">Dashboard</span>
      </Link>
      
      {breadcrumbs.map((item, index) => (
        <div key={index} className="flex items-center">
          <ChevronRight className="w-4 h-4 mx-1 text-gray-400" />
          
          {item.href && !item.isActive ? (
            <Link
              href={item.href}
              className="hover:text-gray-700 transition-colors"
            >
              {item.label}
            </Link>
          ) : (
            <span
              className={cn(
                item.isActive ? 'text-gray-900 font-medium' : 'text-gray-500'
              )}
            >
              {item.label}
            </span>
          )}
        </div>
      ))}
    </nav>
  )
}

// Hook for getting current page title from breadcrumbs
export function usePageTitle(): string {
  const pathname = usePathname()
  
  if (!pathname.startsWith('/admin')) {
    return 'Admin'
  }

  const breadcrumbs = generateBreadcrumbs(pathname)
  const currentPage = breadcrumbs[breadcrumbs.length - 1]
  
  return currentPage?.label || 'Admin'
}

// Component for displaying just the current page title
export function PageTitle({ className }: { className?: string }) {
  const title = usePageTitle()
  
  return (
    <h1 className={cn("text-2xl font-bold text-gray-900", className)}>
      {title}
    </h1>
  )
}
