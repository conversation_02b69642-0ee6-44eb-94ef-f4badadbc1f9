"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Building2, Plus, Search, Filter, MoreHorizontal, Users, BookOpen, Calendar, Edit, Trash2, X } from "lucide-react"
import Link from "next/link"
import { DepartmentActions } from "@/components/admin/DepartmentActions"

interface Department {
  id: string
  name: string
  slug: string
  description: string | null
  headFacultyId: string | null
  createdAt: Date
  updatedAt: Date
  _count: {
    faculty: number
    programs: number
    courses: number
  }
  faculty: Array<{
    id: string
    user: {
      name: string | null
      email: string
    }
  }>
  programs: Array<{
    name: string
    degreeType: string
  }>
}

interface DepartmentsClientProps {
  departments: Department[]
  totalDepartments: number
  totalFaculty: number
  totalPrograms: number
  totalCourses: number
}

export function DepartmentsClient({
  departments,
  totalDepartments,
  totalFaculty,
  totalPrograms,
  totalCourses
}: DepartmentsClientProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [sortBy, setSortBy] = useState<"name" | "faculty" | "programs" | "courses">("name")
  const [showFilters, setShowFilters] = useState(false)

  // Filter departments based on search term
  const filteredDepartments = departments.filter(dept =>
    dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (dept.description && dept.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  // Sort departments
  const sortedDepartments = [...filteredDepartments].sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.name.localeCompare(b.name)
      case "faculty":
        return b._count.faculty - a._count.faculty
      case "programs":
        return b._count.programs - a._count.programs
      case "courses":
        return b._count.courses - a._count.courses
      default:
        return 0
    }
  })

  const clearFilters = () => {
    setSearchTerm("")
    setSortBy("name")
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Departments</h1>
          <p className="text-gray-600">Manage academic departments and their structure</p>
        </div>
        <Button asChild>
          <Link href="/admin/departments/new">
            <Plus className="w-4 h-4 mr-2" />
            Add Department
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Departments</CardTitle>
            <Building2 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDepartments}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Faculty</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalFaculty}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Programs</CardTitle>
            <BookOpen className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPrograms}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
            <Calendar className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCourses}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Department Directory</CardTitle>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="w-4 h-4 mr-2" />
                {showFilters ? 'Hide Filters' : 'Show Filters'}
              </Button>
            </div>
          </div>
          
          {/* Search and Filter Controls */}
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search departments by name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchTerm("")}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="flex gap-4 items-center p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Sort by:</label>
                  <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="faculty">Faculty Count</SelectItem>
                      <SelectItem value="programs">Programs Count</SelectItem>
                      <SelectItem value="courses">Courses Count</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <Button variant="outline" size="sm" onClick={clearFilters}>
                  Clear Filters
                </Button>
              </div>
            )}

            {/* Results Summary */}
            <div className="text-sm text-gray-600">
              Showing {sortedDepartments.length} of {departments.length} departments
              {searchTerm && (
                <span> matching "{searchTerm}"</span>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {sortedDepartments.map((department) => (
              <Card key={department.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{department.name}</CardTitle>
                      <CardDescription className="mt-1">
                        {department.description || 'No description available'}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{department._count.faculty}</div>
                      <div className="text-xs text-gray-500">Faculty</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{department._count.programs}</div>
                      <div className="text-xs text-gray-500">Programs</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">{department._count.courses}</div>
                      <div className="text-xs text-gray-500">Courses</div>
                    </div>
                  </div>

                  {/* Faculty Preview */}
                  {department.faculty.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium mb-2">Faculty</h4>
                      <div className="space-y-1">
                        {department.faculty.map((faculty) => (
                          <div key={faculty.id} className="text-sm text-gray-600">
                            {faculty.user.name || faculty.user.email}
                          </div>
                        ))}
                        {department._count.faculty > 3 && (
                          <div className="text-sm text-gray-500">
                            +{department._count.faculty - 3} more
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Programs Preview */}
                  {department.programs.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium mb-2">Programs</h4>
                      <div className="flex flex-wrap gap-1">
                        {department.programs.map((program, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {program.degreeType}
                          </Badge>
                        ))}
                        {department._count.programs > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{department._count.programs - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-2 border-t">
                    <Button asChild variant="outline" size="sm" className="flex-1">
                      <Link href={`/admin/departments/${department.id}/edit`}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </Link>
                    </Button>
                    <DepartmentActions 
                      departmentId={department.id} 
                      departmentName={department.name}
                      dependencies={{
                        faculty: department._count.faculty,
                        programs: department._count.programs,
                        courses: department._count.courses
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {sortedDepartments.length === 0 && (
              <div className="col-span-full text-center py-8 text-gray-500">
                <Building2 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                {searchTerm ? (
                  <>
                    <p>No departments found matching "{searchTerm}"</p>
                    <Button variant="outline" onClick={clearFilters} className="mt-4">
                      Clear Search
                    </Button>
                  </>
                ) : (
                  <>
                    <p>No departments found</p>
                    <Button asChild className="mt-4">
                      <Link href="/admin/departments/new">Create First Department</Link>
                    </Button>
                  </>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common department management tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/departments/new" className="flex flex-col items-center space-y-2">
                <Building2 className="w-8 h-8 text-blue-600" />
                <div className="text-center">
                  <h3 className="font-medium">New Department</h3>
                  <p className="text-sm text-gray-500">Create academic department</p>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/programs" className="flex flex-col items-center space-y-2">
                <BookOpen className="w-8 h-8 text-green-600" />
                <div className="text-center">
                  <h3 className="font-medium">Manage Programs</h3>
                  <p className="text-sm text-gray-500">View degree programs</p>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/courses" className="flex flex-col items-center space-y-2">
                <Calendar className="w-8 h-8 text-purple-600" />
                <div className="text-center">
                  <h3 className="font-medium">Manage Courses</h3>
                  <p className="text-sm text-gray-500">View course catalog</p>
                </div>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
