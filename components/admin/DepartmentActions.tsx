"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ConfirmDialog } from "@/components/admin/ConfirmDialog"
import { useToast } from "@/hooks/use-toast"
import { Trash2, Loader2 } from "lucide-react"

interface DepartmentActionsProps {
  departmentId: string
  departmentName: string
  dependencies: {
    faculty: number
    programs: number
    courses: number
  }
}

export function DepartmentActions({ departmentId, departmentName, dependencies }: DepartmentActionsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const hasDependencies = dependencies.faculty > 0 || dependencies.programs > 0 || dependencies.courses > 0

  const handleDelete = async () => {
    setIsDeleting(true)
    
    try {
      const response = await fetch(`/api/admin/departments/${departmentId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete department')
      }

      toast({
        title: "Success",
        description: `Department ${departmentName} has been deleted.`,
      })

      router.refresh()
    } catch (error) {
      console.error('Error deleting department:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete department. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
      setIsDeleteDialogOpen(false)
    }
  }

  const getDeleteDescription = () => {
    if (hasDependencies) {
      const deps = []
      if (dependencies.faculty > 0) deps.push(`${dependencies.faculty} faculty member(s)`)
      if (dependencies.programs > 0) deps.push(`${dependencies.programs} program(s)`)
      if (dependencies.courses > 0) deps.push(`${dependencies.courses} course(s)`)
      
      return `Cannot delete ${departmentName} because it has ${deps.join(', ')}. Please reassign or remove these items first.`
    }
    
    return `Are you sure you want to delete ${departmentName}? This action cannot be undone.`
  }

  return (
    <>
      <Button 
        variant="outline" 
        size="sm" 
        onClick={() => setIsDeleteDialogOpen(true)}
        disabled={isDeleting}
        className="flex-1"
      >
        {isDeleting ? (
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
        ) : (
          <Trash2 className="w-4 h-4 mr-2 text-red-600" />
        )}
        Delete
      </Button>

      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title={hasDependencies ? "Cannot Delete Department" : "Delete Department"}
        description={getDeleteDescription()}
        confirmText={hasDependencies ? "OK" : "Delete"}
        cancelText={hasDependencies ? undefined : "Cancel"}
        variant={hasDependencies ? "default" : "destructive"}
        onConfirm={hasDependencies ? () => setIsDeleteDialogOpen(false) : handleDelete}
      />
    </>
  )
}
