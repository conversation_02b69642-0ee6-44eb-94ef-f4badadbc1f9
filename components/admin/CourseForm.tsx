"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Save, ArrowLeft } from "lucide-react"
import Link from "next/link"

const courseSchema = z.object({
  code: z.string().min(1, 'Course code is required'),
  name: z.string().min(1, 'Course name is required'),
  description: z.string().optional(),
  credits: z.number().min(1, 'Credits must be at least 1').max(10, 'Credits cannot exceed 10'),
  departmentId: z.string().min(1, 'Department is required'),
  isActive: z.boolean(),
})

type CourseFormData = z.infer<typeof courseSchema>

interface Department {
  id: string
  name: string
  slug: string
}

interface CourseFormProps {
  courseId?: string
  initialData?: Partial<CourseFormData>
  departments: Department[]
}

const creditOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

export function CourseForm({ courseId, initialData, departments }: CourseFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const router = useRouter()
  const isEditing = !!courseId

  const form = useForm<CourseFormData>({
    resolver: zodResolver(courseSchema),
    defaultValues: {
      code: initialData?.code || '',
      name: initialData?.name || '',
      description: initialData?.description || '',
      credits: initialData?.credits || 3,
      departmentId: initialData?.departmentId || '',
      isActive: initialData?.isActive ?? true,
    }
  })

  const onSubmit = async (data: CourseFormData) => {
    setIsLoading(true)
    
    try {
      const url = isEditing ? `/api/admin/courses/${courseId}` : '/api/admin/courses'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || `Failed to ${isEditing ? 'update' : 'create'} course`)
      }

      toast({
        title: "Success",
        description: `Course ${isEditing ? 'updated' : 'created'} successfully.`,
      })

      router.push('/admin/courses')
      router.refresh()
    } catch (error) {
      console.error('Error saving course:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEditing ? 'Edit Course' : 'Add Course'}
          </h1>
          <p className="text-gray-600">
            {isEditing ? 'Update course information and settings' : 'Create a new course in the catalog'}
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/admin/courses">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Courses
          </Link>
        </Button>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Course Information</CardTitle>
            <CardDescription>Basic course details and identification</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="code">Course Code *</Label>
                <Input
                  id="code"
                  {...form.register('code')}
                  placeholder="CS101"
                  className="uppercase"
                  onChange={(e) => {
                    e.target.value = e.target.value.toUpperCase()
                    form.setValue('code', e.target.value)
                  }}
                />
                {form.formState.errors.code && (
                  <p className="text-sm text-red-600">{form.formState.errors.code.message}</p>
                )}
                <p className="text-sm text-gray-500">
                  Unique identifier for the course (e.g., CS101, MATH201)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Course Name *</Label>
                <Input
                  id="name"
                  {...form.register('name')}
                  placeholder="Introduction to Computer Science"
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="departmentId">Department *</Label>
                <Select
                  value={form.watch('departmentId')}
                  onValueChange={(value) => form.setValue('departmentId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((department) => (
                      <SelectItem key={department.id} value={department.id}>
                        {department.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.departmentId && (
                  <p className="text-sm text-red-600">{form.formState.errors.departmentId.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="credits">Credits *</Label>
                <Select
                  value={form.watch('credits')?.toString()}
                  onValueChange={(value) => form.setValue('credits', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select credits" />
                  </SelectTrigger>
                  <SelectContent>
                    {creditOptions.map((credit) => (
                      <SelectItem key={credit} value={credit.toString()}>
                        {credit} credit{credit !== 1 ? 's' : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.credits && (
                  <p className="text-sm text-red-600">{form.formState.errors.credits.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Course Description</Label>
              <Textarea
                id="description"
                {...form.register('description')}
                placeholder="Detailed description of the course content, objectives, and learning outcomes..."
                rows={4}
              />
              {form.formState.errors.description && (
                <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
              )}
              <p className="text-sm text-gray-500">
                Provide a comprehensive description that will help students understand what they'll learn
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Course Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Course Settings</CardTitle>
            <CardDescription>Availability and status configuration</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={form.watch('isActive')}
                onCheckedChange={(checked) => form.setValue('isActive', checked)}
              />
              <Label htmlFor="isActive">Active Course</Label>
            </div>
            <p className="text-sm text-gray-500">
              Inactive courses will not be available for scheduling and will not appear in the course catalog.
            </p>

            {/* Course Guidelines */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Course Code Guidelines:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Use department prefix followed by course number (e.g., CS101, MATH201)</li>
                <li>• Lower numbers (100-199) for introductory courses</li>
                <li>• Higher numbers (300-499) for advanced undergraduate courses</li>
                <li>• Graduate courses typically use 500+ numbers</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            <Save className="w-4 h-4 mr-2" />
            {isEditing ? 'Update Course' : 'Create Course'}
          </Button>
        </div>
      </form>
    </div>
  )
}
