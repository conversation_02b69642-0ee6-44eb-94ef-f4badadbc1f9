"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Globe, MapPin, Users, Loader2 } from 'lucide-react'

interface GeographicData {
  countries: Array<{
    country: string
    users: number
  }>
  cities: Array<{
    city: string
    country: string
    users: number
  }>
}

export function GeographicAnalytics() {
  const [data, setData] = useState<GeographicData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchGeographicData()
  }, [])

  const fetchGeographicData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/admin/analytics/geographic')
      const result = await response.json()

      if (result.success) {
        setData(result.data)
      } else {
        setError(result.error || 'Failed to fetch geographic data')
      }
    } catch (err) {
      setError('Failed to fetch geographic data')
      console.error('Geographic analytics error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Globe className="w-5 h-5 mr-2" />
            Geographic Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Globe className="w-5 h-5 mr-2" />
            Geographic Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-500">
            <p>Error: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const totalUsers = data?.countries.reduce((sum, country) => sum + country.users, 0) || 0

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Globe className="w-5 h-5 mr-2" />
          Geographic Analytics
        </CardTitle>
        <CardDescription>
          User distribution by location
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="countries" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="countries">Countries</TabsTrigger>
            <TabsTrigger value="cities">Cities</TabsTrigger>
          </TabsList>
          
          <TabsContent value="countries" className="space-y-4">
            <div className="space-y-3">
              {data?.countries.slice(0, 10).map((country, index) => {
                const percentage = totalUsers > 0 ? (country.users / totalUsers) * 100 : 0
                
                return (
                  <div key={country.country} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">{country.country || 'Unknown'}</p>
                        <p className="text-sm text-gray-500">
                          {percentage.toFixed(1)}% of total users
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">
                        <Users className="w-3 h-3 mr-1" />
                        {country.users}
                      </Badge>
                    </div>
                  </div>
                )
              })}
              
              {(!data?.countries || data.countries.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  <Globe className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>No geographic data available</p>
                  <p className="text-sm">User location tracking may not be enabled</p>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="cities" className="space-y-4">
            <div className="space-y-3">
              {data?.cities.slice(0, 15).map((city, index) => {
                const percentage = totalUsers > 0 ? (city.users / totalUsers) * 100 : 0
                
                return (
                  <div key={`${city.city}-${city.country}`} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                        <MapPin className="w-4 h-4 text-green-600" />
                      </div>
                      <div>
                        <p className="font-medium">{city.city || 'Unknown'}</p>
                        <p className="text-sm text-gray-500">
                          {city.country} • {percentage.toFixed(1)}% of total
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">
                        <Users className="w-3 h-3 mr-1" />
                        {city.users}
                      </Badge>
                    </div>
                  </div>
                )
              })}
              
              {(!data?.cities || data.cities.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  <MapPin className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>No city data available</p>
                  <p className="text-sm">User location tracking may not be enabled</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
        
        {totalUsers > 0 && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Total Users with Location Data:</span>
              <span className="font-medium">{totalUsers}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
