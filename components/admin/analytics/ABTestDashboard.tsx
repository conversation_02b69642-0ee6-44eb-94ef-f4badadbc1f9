"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { FlaskConical, TrendingUp, Users, Target, Loader2, RefreshCw, Plus } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ABTestSummary {
  id: string
  name: string
  status: string
  participants: number
  startDate: string | null
  endDate: string | null
}

interface ABTestResult {
  variant: string
  participants: number
  conversions: number
  conversionRate: number
}

interface ABTestDetails {
  test: {
    id: string
    name: string
    description: string
    status: string
    startDate: string | null
    endDate: string | null
    variants: any
    metrics: any
  }
  results: ABTestResult[]
}

export function ABTestDashboard() {
  const [tests, setTests] = useState<ABTestSummary[]>([])
  const [selectedTest, setSelectedTest] = useState<ABTestDetails | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingDetails, setIsLoadingDetails] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchTests()
  }, [])

  const fetchTests = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/admin/analytics/ab-tests')
      const result = await response.json()

      if (result.success) {
        setTests(result.data)
      } else {
        setError(result.error || 'Failed to fetch A/B tests')
      }
    } catch (err) {
      setError('Failed to fetch A/B tests')
      console.error('A/B test analytics error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchTestDetails = async (testId: string) => {
    try {
      setIsLoadingDetails(true)
      const response = await fetch(`/api/admin/analytics/ab-tests?testId=${testId}`)
      const result = await response.json()

      if (result.success) {
        setSelectedTest(result.data)
      } else {
        setError(result.error || 'Failed to fetch test details')
      }
    } catch (err) {
      setError('Failed to fetch test details')
      console.error('A/B test details error:', err)
    } finally {
      setIsLoadingDetails(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500'
      case 'completed': return 'bg-blue-500'
      case 'paused': return 'bg-yellow-500'
      case 'draft': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'active': return 'default'
      case 'completed': return 'secondary'
      case 'paused': return 'outline'
      case 'draft': return 'outline'
      default: return 'secondary'
    }
  }

  const getWinningVariant = (results: ABTestResult[]) => {
    return results.reduce((winner, current) => 
      current.conversionRate > winner.conversionRate ? current : winner
    )
  }

  const getStatisticalSignificance = (variantA: ABTestResult, variantB: ABTestResult) => {
    // Simplified statistical significance calculation
    // In a real implementation, you'd use proper statistical tests
    const diff = Math.abs(variantA.conversionRate - variantB.conversionRate)
    const minParticipants = Math.min(variantA.participants, variantB.participants)
    
    if (minParticipants < 100) return 'insufficient-data'
    if (diff > 5 && minParticipants > 500) return 'significant'
    if (diff > 2 && minParticipants > 1000) return 'significant'
    return 'not-significant'
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FlaskConical className="w-5 h-5 mr-2" />
            A/B Testing Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FlaskConical className="w-5 h-5 mr-2" />
            A/B Testing Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-500">
            <p>Error: {error}</p>
            <Button onClick={fetchTests} className="mt-4">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <FlaskConical className="w-5 h-5 mr-2" />
          A/B Testing Dashboard
        </CardTitle>
        <CardDescription>
          Manage and analyze A/B test experiments
        </CardDescription>
        <div className="flex justify-end space-x-2">
          <Button onClick={fetchTests} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button size="sm">
            <Plus className="w-4 h-4 mr-2" />
            New Test
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Test Overview</TabsTrigger>
            <TabsTrigger value="details">Test Details</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            {tests.length > 0 ? (
              <div className="space-y-4">
                {tests.map((test) => (
                  <div
                    key={test.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                    onClick={() => fetchTestDetails(test.id)}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
                        <FlaskConical className="w-5 h-5 text-blue-600" />
                      </div>
                      
                      <div>
                        <h4 className="font-medium">{test.name}</h4>
                        <p className="text-sm text-gray-500">
                          {test.participants} participants
                          {test.startDate && (
                            <> • Started {new Date(test.startDate).toLocaleDateString()}</>
                          )}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Badge variant={getStatusVariant(test.status)}>
                        {test.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <FlaskConical className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No A/B tests found</p>
                <p className="text-sm">Create your first experiment to get started</p>
                <Button className="mt-4">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Test
                </Button>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="details" className="space-y-4">
            {isLoadingDetails ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
              </div>
            ) : selectedTest ? (
              <div className="space-y-6">
                {/* Test Info */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2">{selectedTest.test.name}</h3>
                  <p className="text-sm text-gray-600 mb-3">{selectedTest.test.description}</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <Badge variant={getStatusVariant(selectedTest.test.status)}>
                      {selectedTest.test.status}
                    </Badge>
                    {selectedTest.test.startDate && (
                      <span>Started: {new Date(selectedTest.test.startDate).toLocaleDateString()}</span>
                    )}
                    {selectedTest.test.endDate && (
                      <span>Ended: {new Date(selectedTest.test.endDate).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>

                {/* Results */}
                <div className="space-y-4">
                  <h4 className="font-medium">Test Results</h4>
                  
                  {selectedTest.results.map((result, index) => {
                    const isWinner = selectedTest.results.length > 1 && 
                      result === getWinningVariant(selectedTest.results)
                    
                    return (
                      <div
                        key={result.variant}
                        className={cn(
                          "p-4 border rounded-lg",
                          isWinner && "border-green-500 bg-green-50"
                        )}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <h5 className="font-medium">Variant {result.variant}</h5>
                            {isWinner && (
                              <Badge variant="default" className="bg-green-500">
                                <Target className="w-3 h-3 mr-1" />
                                Winner
                              </Badge>
                            )}
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold">{result.conversionRate.toFixed(2)}%</div>
                            <div className="text-sm text-gray-500">conversion rate</div>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Participants:</span>
                            <span className="ml-2 font-medium">{result.participants}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Conversions:</span>
                            <span className="ml-2 font-medium">{result.conversions}</span>
                          </div>
                        </div>
                        
                        {/* Conversion Rate Bar */}
                        <div className="mt-3">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={cn(
                                "h-2 rounded-full transition-all",
                                isWinner ? "bg-green-500" : "bg-blue-500"
                              )}
                              style={{ width: `${Math.min(result.conversionRate, 100)}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    )
                  })}
                  
                  {/* Statistical Significance */}
                  {selectedTest.results.length === 2 && (
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h5 className="font-medium mb-2">Statistical Analysis</h5>
                      {(() => {
                        const significance = getStatisticalSignificance(
                          selectedTest.results[0],
                          selectedTest.results[1]
                        )
                        
                        return (
                          <div className="text-sm">
                            {significance === 'significant' && (
                              <p className="text-green-700">
                                ✅ Results are statistically significant
                              </p>
                            )}
                            {significance === 'not-significant' && (
                              <p className="text-yellow-700">
                                ⚠️ Results are not yet statistically significant
                              </p>
                            )}
                            {significance === 'insufficient-data' && (
                              <p className="text-red-700">
                                ❌ Insufficient data for statistical significance
                              </p>
                            )}
                          </div>
                        )
                      })()}
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Target className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Select a test from the overview to view details</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
