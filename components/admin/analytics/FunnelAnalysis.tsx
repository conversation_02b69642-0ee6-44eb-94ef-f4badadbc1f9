"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { TrendingDown, Users, UserCheck, Activity, RotateCcw, Loader2, RefreshCw } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FunnelStage {
  name: string
  count: number
  percentage: number
}

interface FunnelData {
  stages: FunnelStage[]
}

const stageIcons = {
  'Total Users': Users,
  'Registered': UserCheck,
  'Activated': Activity,
  'Engaged': TrendingDown,
  'Retained (7d)': RotateCcw
}

const stageColors = {
  'Total Users': 'bg-blue-500',
  'Registered': 'bg-green-500',
  'Activated': 'bg-purple-500',
  'Engaged': 'bg-orange-500',
  'Retained (7d)': 'bg-red-500'
}

export function FunnelAnalysis() {
  const [data, setData] = useState<FunnelData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchFunnelData()
  }, [])

  const fetchFunnelData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/admin/analytics/funnel')
      const result = await response.json()

      if (result.success) {
        setData(result.data)
      } else {
        setError(result.error || 'Failed to fetch funnel data')
      }
    } catch (err) {
      setError('Failed to fetch funnel data')
      console.error('Funnel analytics error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const getDropoffRate = (currentStage: FunnelStage, previousStage?: FunnelStage) => {
    if (!previousStage) return 0
    const dropoff = previousStage.count - currentStage.count
    return previousStage.count > 0 ? (dropoff / previousStage.count) * 100 : 0
  }

  const getConversionRate = (currentStage: FunnelStage, firstStage?: FunnelStage) => {
    if (!firstStage) return 100
    return firstStage.count > 0 ? (currentStage.count / firstStage.count) * 100 : 0
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingDown className="w-5 h-5 mr-2" />
            User Journey Funnel
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingDown className="w-5 h-5 mr-2" />
            User Journey Funnel
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-500">
            <p>Error: {error}</p>
            <Button onClick={fetchFunnelData} className="mt-4">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const stages = data?.stages || []
  const firstStage = stages[0]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <TrendingDown className="w-5 h-5 mr-2" />
          User Journey Funnel
        </CardTitle>
        <CardDescription>
          Track user progression through key engagement stages
        </CardDescription>
        <div className="flex justify-end">
          <Button onClick={fetchFunnelData} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {stages.length > 0 ? (
          <div className="space-y-4">
            {stages.map((stage, index) => {
              const previousStage = index > 0 ? stages[index - 1] : undefined
              const dropoffRate = getDropoffRate(stage, previousStage)
              const conversionRate = getConversionRate(stage, firstStage)
              const IconComponent = stageIcons[stage.name as keyof typeof stageIcons] || Users
              const colorClass = stageColors[stage.name as keyof typeof stageColors] || 'bg-gray-500'

              // Calculate funnel width based on percentage
              const funnelWidth = Math.max(conversionRate, 10) // Minimum 10% width for visibility

              return (
                <div key={stage.name} className="relative">
                  {/* Funnel Visual */}
                  <div className="flex items-center space-x-4">
                    <div className="relative flex-1">
                      {/* Funnel Bar */}
                      <div className="h-16 bg-gray-100 rounded-lg overflow-hidden relative">
                        <div
                          className={cn("h-full transition-all duration-500 flex items-center justify-center", colorClass)}
                          style={{ width: `${funnelWidth}%` }}
                        >
                          <div className="flex items-center space-x-2 text-white">
                            <IconComponent className="w-5 h-5" />
                            <span className="font-medium">{stage.count.toLocaleString()}</span>
                          </div>
                        </div>
                        
                        {/* Stage Label */}
                        <div className="absolute inset-0 flex items-center justify-between px-4">
                          <span className="text-sm font-medium text-gray-700">
                            {stage.name}
                          </span>
                          <span className="text-sm text-gray-600">
                            {conversionRate.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                      
                      {/* Dropoff Indicator */}
                      {index > 0 && dropoffRate > 0 && (
                        <div className="absolute -top-2 right-4 bg-red-100 text-red-700 text-xs px-2 py-1 rounded-full">
                          -{dropoffRate.toFixed(1)}% dropoff
                        </div>
                      )}
                    </div>
                    
                    {/* Stats */}
                    <div className="w-32 text-right">
                      <div className="text-lg font-bold">{stage.count.toLocaleString()}</div>
                      <div className="text-sm text-gray-500">users</div>
                      {index > 0 && (
                        <div className="text-xs text-gray-400">
                          {conversionRate.toFixed(1)}% of total
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Connection Line */}
                  {index < stages.length - 1 && (
                    <div className="flex justify-center my-2">
                      <div className="w-px h-4 bg-gray-300"></div>
                    </div>
                  )}
                </div>
              )
            })}
            
            {/* Summary Stats */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {firstStage ? firstStage.count.toLocaleString() : '0'}
                </div>
                <div className="text-sm text-blue-700">Total Users</div>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600">
                  {stages.length > 1 ? getConversionRate(stages[stages.length - 1], firstStage).toFixed(1) : '0'}%
                </div>
                <div className="text-sm text-green-700">Overall Conversion</div>
              </div>
              
              <div className="bg-orange-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {stages.length > 1 ? 
                    stages.slice(1).reduce((sum, stage, index) => 
                      sum + getDropoffRate(stage, stages[index]), 0
                    ).toFixed(1) : '0'
                  }%
                </div>
                <div className="text-sm text-orange-700">Avg Dropoff Rate</div>
              </div>
            </div>
            
            {/* Insights */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Key Insights</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                {stages.length > 1 && getDropoffRate(stages[1], stages[0]) > 50 && (
                  <li>• High dropoff after registration - consider improving onboarding</li>
                )}
                {stages.length > 2 && getDropoffRate(stages[2], stages[1]) > 30 && (
                  <li>• Users struggle with activation - simplify initial setup</li>
                )}
                {stages.length > 3 && getConversionRate(stages[3], firstStage) < 20 && (
                  <li>• Low engagement rate - improve content relevance</li>
                )}
                {stages.length > 4 && getConversionRate(stages[4], firstStage) < 10 && (
                  <li>• Poor retention - focus on user value and experience</li>
                )}
              </ul>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <TrendingDown className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No funnel data available</p>
            <p className="text-sm">User event tracking may not be enabled</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
