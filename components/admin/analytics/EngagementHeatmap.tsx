"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Activity, Eye, Clock, MousePointer, Loader2, RefreshCw } from 'lucide-react'
import { cn } from '@/lib/utils'

interface EngagementData {
  contentId: string
  contentType: string
  views: number
  totalDuration: number
  engagements: Record<string, number>
}

const contentTypeIcons = {
  post: Eye,
  faculty_profile: MousePointer,
  course: Clock,
  department: Activity
}

const contentTypeColors = {
  post: 'bg-blue-500',
  faculty_profile: 'bg-green-500',
  course: 'bg-purple-500',
  department: 'bg-orange-500'
}

export function EngagementHeatmap() {
  const [data, setData] = useState<EngagementData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [days, setDays] = useState('30')
  const [sortBy, setSortBy] = useState<'views' | 'duration' | 'engagements'>('views')

  useEffect(() => {
    fetchEngagementData()
  }, [days])

  const fetchEngagementData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/admin/analytics/engagement?days=${days}`)
      const result = await response.json()

      if (result.success) {
        setData(result.data)
      } else {
        setError(result.error || 'Failed to fetch engagement data')
      }
    } catch (err) {
      setError('Failed to fetch engagement data')
      console.error('Engagement analytics error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const getSortedData = () => {
    return [...data].sort((a, b) => {
      switch (sortBy) {
        case 'views':
          return b.views - a.views
        case 'duration':
          return b.totalDuration - a.totalDuration
        case 'engagements':
          return Object.values(b.engagements).reduce((sum, val) => sum + val, 0) - 
                 Object.values(a.engagements).reduce((sum, val) => sum + val, 0)
        default:
          return 0
      }
    })
  }

  const getEngagementIntensity = (item: EngagementData) => {
    const maxViews = Math.max(...data.map(d => d.views))
    const intensity = maxViews > 0 ? (item.views / maxViews) * 100 : 0
    
    if (intensity >= 80) return 'high'
    if (intensity >= 40) return 'medium'
    return 'low'
  }

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`
    return `${Math.round(seconds / 3600)}h`
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            Content Engagement Heatmap
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            Content Engagement Heatmap
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-500">
            <p>Error: {error}</p>
            <Button onClick={fetchEngagementData} className="mt-4">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const sortedData = getSortedData()

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Activity className="w-5 h-5 mr-2" />
          Content Engagement Heatmap
        </CardTitle>
        <CardDescription>
          Content performance and user engagement patterns
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium">Time Period:</label>
            <Select value={days} onValueChange={setDays}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">7 days</SelectItem>
                <SelectItem value="30">30 days</SelectItem>
                <SelectItem value="90">90 days</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium">Sort by:</label>
            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-36">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="views">Views</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
                <SelectItem value="engagements">Engagements</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Button onClick={fetchEngagementData} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Heatmap */}
        <div className="space-y-3">
          {sortedData.slice(0, 20).map((item, index) => {
            const intensity = getEngagementIntensity(item)
            const totalEngagements = Object.values(item.engagements).reduce((sum, val) => sum + val, 0)
            const avgDuration = item.views > 0 ? item.totalDuration / item.views : 0
            const IconComponent = contentTypeIcons[item.contentType as keyof typeof contentTypeIcons] || Activity

            return (
              <div
                key={`${item.contentType}-${item.contentId}`}
                className={cn(
                  "flex items-center justify-between p-4 rounded-lg border transition-all",
                  intensity === 'high' && "bg-red-50 border-red-200",
                  intensity === 'medium' && "bg-yellow-50 border-yellow-200",
                  intensity === 'low' && "bg-green-50 border-green-200"
                )}
              >
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <div className={cn(
                    "w-10 h-10 rounded-lg flex items-center justify-center text-white",
                    contentTypeColors[item.contentType as keyof typeof contentTypeColors] || 'bg-gray-500'
                  )}>
                    <IconComponent className="w-5 h-5" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {item.contentType.replace('_', ' ')}
                      </Badge>
                      <Badge 
                        variant={intensity === 'high' ? 'destructive' : intensity === 'medium' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {intensity} engagement
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 truncate mt-1">
                      Content ID: {item.contentId}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-center">
                    <div className="font-medium">{item.views}</div>
                    <div className="text-gray-500">Views</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="font-medium">{formatDuration(avgDuration)}</div>
                    <div className="text-gray-500">Avg Time</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="font-medium">{totalEngagements}</div>
                    <div className="text-gray-500">Actions</div>
                  </div>
                </div>
              </div>
            )
          })}
          
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Activity className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No engagement data available</p>
              <p className="text-sm">Content engagement tracking may not be enabled</p>
            </div>
          )}
        </div>

        {/* Legend */}
        {sortedData.length > 0 && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium mb-3">Engagement Intensity</h4>
            <div className="flex items-center space-x-6 text-xs">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-200 rounded"></div>
                <span>High (80%+)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-200 rounded"></div>
                <span>Medium (40-80%)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-200 rounded"></div>
                <span>Low (&lt;40%)</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
