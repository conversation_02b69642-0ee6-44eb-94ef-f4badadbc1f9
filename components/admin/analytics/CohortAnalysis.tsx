"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Calendar, Users, TrendingUp, Loader2, RefreshCw } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CohortData {
  cohort: string
  totalUsers: number
  retention: {
    week1: number
    month1: number
    month3: number
  }
}

export function CohortAnalysis() {
  const [data, setData] = useState<CohortData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchCohortData()
  }, [])

  const fetchCohortData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/admin/analytics/cohort')
      const result = await response.json()

      if (result.success) {
        setData(result.data)
      } else {
        setError(result.error || 'Failed to fetch cohort data')
      }
    } catch (err) {
      setError('Failed to fetch cohort data')
      console.error('Cohort analytics error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const getRetentionRate = (retained: number, total: number) => {
    return total > 0 ? (retained / total) * 100 : 0
  }

  const getRetentionColor = (rate: number) => {
    if (rate >= 70) return 'bg-green-500'
    if (rate >= 50) return 'bg-yellow-500'
    if (rate >= 30) return 'bg-orange-500'
    return 'bg-red-500'
  }

  const getRetentionIntensity = (rate: number) => {
    if (rate >= 70) return 'opacity-100'
    if (rate >= 50) return 'opacity-80'
    if (rate >= 30) return 'opacity-60'
    if (rate >= 10) return 'opacity-40'
    return 'opacity-20'
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Cohort Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Cohort Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-500">
            <p>Error: {error}</p>
            <Button onClick={fetchCohortData} className="mt-4">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const totalUsers = data.reduce((sum, cohort) => sum + cohort.totalUsers, 0)
  const avgRetentionWeek1 = data.length > 0 ? 
    data.reduce((sum, cohort) => sum + getRetentionRate(cohort.retention.week1, cohort.totalUsers), 0) / data.length : 0
  const avgRetentionMonth1 = data.length > 0 ? 
    data.reduce((sum, cohort) => sum + getRetentionRate(cohort.retention.month1, cohort.totalUsers), 0) / data.length : 0
  const avgRetentionMonth3 = data.length > 0 ? 
    data.reduce((sum, cohort) => sum + getRetentionRate(cohort.retention.month3, cohort.totalUsers), 0) / data.length : 0

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calendar className="w-5 h-5 mr-2" />
          Cohort Analysis
        </CardTitle>
        <CardDescription>
          User retention by registration month
        </CardDescription>
        <div className="flex justify-end">
          <Button onClick={fetchCohortData} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {data.length > 0 ? (
          <div className="space-y-6">
            {/* Cohort Table */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3 font-medium">Cohort</th>
                    <th className="text-center p-3 font-medium">Users</th>
                    <th className="text-center p-3 font-medium">Week 1</th>
                    <th className="text-center p-3 font-medium">Month 1</th>
                    <th className="text-center p-3 font-medium">Month 3</th>
                  </tr>
                </thead>
                <tbody>
                  {data.map((cohort) => {
                    const week1Rate = getRetentionRate(cohort.retention.week1, cohort.totalUsers)
                    const month1Rate = getRetentionRate(cohort.retention.month1, cohort.totalUsers)
                    const month3Rate = getRetentionRate(cohort.retention.month3, cohort.totalUsers)

                    return (
                      <tr key={cohort.cohort} className="border-b hover:bg-gray-50">
                        <td className="p-3">
                          <div className="font-medium">{cohort.cohort}</div>
                        </td>
                        <td className="p-3 text-center">
                          <div className="font-medium">{cohort.totalUsers}</div>
                        </td>
                        <td className="p-3 text-center">
                          <div className="flex items-center justify-center space-x-2">
                            <div
                              className={cn(
                                "w-12 h-6 rounded flex items-center justify-center text-white text-xs font-medium",
                                getRetentionColor(week1Rate),
                                getRetentionIntensity(week1Rate)
                              )}
                            >
                              {week1Rate.toFixed(0)}%
                            </div>
                            <span className="text-xs text-gray-500">
                              ({cohort.retention.week1})
                            </span>
                          </div>
                        </td>
                        <td className="p-3 text-center">
                          <div className="flex items-center justify-center space-x-2">
                            <div
                              className={cn(
                                "w-12 h-6 rounded flex items-center justify-center text-white text-xs font-medium",
                                getRetentionColor(month1Rate),
                                getRetentionIntensity(month1Rate)
                              )}
                            >
                              {month1Rate.toFixed(0)}%
                            </div>
                            <span className="text-xs text-gray-500">
                              ({cohort.retention.month1})
                            </span>
                          </div>
                        </td>
                        <td className="p-3 text-center">
                          <div className="flex items-center justify-center space-x-2">
                            <div
                              className={cn(
                                "w-12 h-6 rounded flex items-center justify-center text-white text-xs font-medium",
                                getRetentionColor(month3Rate),
                                getRetentionIntensity(month3Rate)
                              )}
                            >
                              {month3Rate.toFixed(0)}%
                            </div>
                            <span className="text-xs text-gray-500">
                              ({cohort.retention.month3})
                            </span>
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>

            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {totalUsers.toLocaleString()}
                </div>
                <div className="text-sm text-blue-700">Total Users</div>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600">
                  {avgRetentionWeek1.toFixed(1)}%
                </div>
                <div className="text-sm text-green-700">Avg Week 1 Retention</div>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {avgRetentionMonth1.toFixed(1)}%
                </div>
                <div className="text-sm text-purple-700">Avg Month 1 Retention</div>
              </div>
              
              <div className="bg-orange-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {avgRetentionMonth3.toFixed(1)}%
                </div>
                <div className="text-sm text-orange-700">Avg Month 3 Retention</div>
              </div>
            </div>

            {/* Legend */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-3">Retention Rate Legend</h4>
              <div className="flex items-center space-x-6 text-xs">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span>Excellent (70%+)</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                  <span>Good (50-70%)</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-orange-500 rounded"></div>
                  <span>Fair (30-50%)</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-red-500 rounded"></div>
                  <span>Poor (&lt;30%)</span>
                </div>
              </div>
              <p className="text-xs text-gray-600 mt-2">
                Numbers in parentheses show absolute user count
              </p>
            </div>

            {/* Insights */}
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center">
                <TrendingUp className="w-4 h-4 mr-2" />
                Key Insights
              </h4>
              <ul className="text-sm text-gray-700 space-y-1">
                {avgRetentionWeek1 < 30 && (
                  <li>• Week 1 retention is low - improve onboarding experience</li>
                )}
                {avgRetentionMonth1 < 20 && (
                  <li>• Month 1 retention needs attention - enhance user engagement</li>
                )}
                {avgRetentionMonth3 < 15 && (
                  <li>• Long-term retention is poor - focus on user value proposition</li>
                )}
                {data.length > 1 && (
                  <li>• {data[data.length - 1].cohort} is your most recent cohort with {data[data.length - 1].totalUsers} users</li>
                )}
              </ul>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No cohort data available</p>
            <p className="text-sm">User session tracking may not be enabled</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
