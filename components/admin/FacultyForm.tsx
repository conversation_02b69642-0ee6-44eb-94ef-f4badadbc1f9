"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Save, ArrowLeft, Check, AlertCircle, RefreshCw, UserPlus, BookOpen, Building, Briefcase, Phone, Mail, Lock, Eye, EyeOff, ChevronRight, ChevronLeft } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/alert"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"

// Create two completely separate schemas for create and update
// Create schema requires password
const createFacultySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Valid email is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  title: z.string().min(1, 'Title is required'),
  departmentId: z.string().min(1, 'Department is required'),
  officeLocation: z.string().optional(),
  websiteUrl: z.string().url().optional().or(z.literal('')),
  scholarId: z.string().optional(),
  bio: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED']).optional(),
});

// Update schema doesn't have password at all
const updateFacultySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Valid email is required'),
  title: z.string().min(1, 'Title is required'),
  departmentId: z.string().min(1, 'Department is required'),
  officeLocation: z.string().optional(),
  websiteUrl: z.string().url().optional().or(z.literal('')),
  scholarId: z.string().optional(),
  bio: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED']).optional(),
});

// Use a type union for the form data
type CreateFacultyFormData = z.infer<typeof createFacultySchema>;
type UpdateFacultyFormData = z.infer<typeof updateFacultySchema>;
type FacultyFormData = CreateFacultyFormData | UpdateFacultyFormData;

interface Department {
  id: string
  name: string
}

interface FacultyFormProps {
  facultyId?: string
  initialData?: Partial<FacultyFormData>
  departments: Department[]
}

export function FacultyForm({ facultyId, initialData, departments }: FacultyFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [formSuccess, setFormSuccess] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const isEditing = !!facultyId;

  // Safely stringify objects with circular references
  const safeStringify = (obj: any, indent = 2) => {
    const cache = new Set();
    return JSON.stringify(
      obj,
      (key, value) => {
        if (typeof value === 'object' && value !== null) {
          if (cache.has(value)) {
            return '[Circular Reference]';
          }
          cache.add(value);
        }
        return value;
      },
      indent
    );
  };

  // Define form steps
  const steps = [
    { title: "Account", description: "Basic account information", icon: <UserPlus className="h-5 w-5" /> },
    { title: "Personal", description: "Personal details", icon: <Phone className="h-5 w-5" /> },
    { title: "Academic", description: "Academic information", icon: <BookOpen className="h-5 w-5" /> },
  ];

  // Use the appropriate schema based on whether we're editing or creating
  const schema = isEditing ? updateFacultySchema : createFacultySchema;
  
  // Create default values without password for editing
  const defaultValues = isEditing
    ? {
        name: initialData?.name || '',
        email: initialData?.email || '',
        title: initialData?.title || '',
        departmentId: initialData?.departmentId || '',
        officeLocation: initialData?.officeLocation || '',
        websiteUrl: initialData?.websiteUrl || '',
        scholarId: initialData?.scholarId || '',
        bio: initialData?.bio || '',
        firstName: initialData?.firstName || '',
        lastName: initialData?.lastName || '',
        phone: initialData?.phone || '',
        status: initialData?.status || 'ACTIVE',
      }
    : {
        name: initialData?.name || '',
        email: initialData?.email || '',
        password: '',
        title: initialData?.title || '',
        departmentId: initialData?.departmentId || '',
        officeLocation: initialData?.officeLocation || '',
        websiteUrl: initialData?.websiteUrl || '',
        scholarId: initialData?.scholarId || '',
        bio: initialData?.bio || '',
        firstName: initialData?.firstName || '',
        lastName: initialData?.lastName || '',
        phone: initialData?.phone || '',
        status: initialData?.status || 'ACTIVE',
      };

  const form = useForm({
    // Use type assertion to handle the two schema types
    resolver: zodResolver(schema) as any,
    defaultValues: defaultValues as any,
    mode: "onChange" // Enable validation as user types
  });

  // Function to handle step navigation
  const goToNextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Calculate form completion percentage
  const calculateCompletion = () => {
    const fields = form.getValues();
    const requiredFields = ['name', 'email', 'title', 'departmentId'];
    if (!isEditing) requiredFields.push('password');
    
    const filledRequired = requiredFields.filter(field => !!fields[field as keyof typeof fields]).length;
    const percentRequired = (filledRequired / requiredFields.length) * 70;
    
    const optionalFields = ['firstName', 'lastName', 'officeLocation', 'websiteUrl', 'scholarId', 'bio', 'phone'];
    const filledOptional = optionalFields.filter(field => !!fields[field as keyof typeof fields]).length;
    const percentOptional = (filledOptional / optionalFields.length) * 30;
    
    return Math.round(percentRequired + percentOptional);
  };

  // Reset form error when values change
  useEffect(() => {
    const subscription = form.watch(() => {
      if (formError) setFormError(null);
      if (apiError) setApiError(null);
    });
    return () => subscription.unsubscribe();
  }, [form, formError, apiError]);

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    setFormSuccess(false);
    setFormError(null);
    setApiError(null);
    
    try {
      const url = isEditing ? `/api/admin/faculty/${facultyId}` : '/api/admin/faculty';
      const method = isEditing ? 'PUT' : 'POST';
      
      // Prepare submission data based on mode
      const submissionData = { ...data };
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
        credentials: 'include'
      });
      
      const responseText = await response.text();
      
      if (!response.ok) {
        let errorMessage = `Failed to ${isEditing ? 'update' : 'create'} faculty`;
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          // If parsing fails, use the raw text
          errorMessage = responseText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      // Parse the response
      let result;
      try {
        result = responseText ? JSON.parse(responseText) : {};
      } catch (e) {
        throw new Error('Server returned invalid JSON');
      }

      // Show success message
      setFormSuccess(true);
      toast({
        title: "Success",
        description: `Faculty ${isEditing ? 'updated' : 'created'} successfully.`,
      });

      // Navigate after a short delay to show success state
      setTimeout(() => {
        router.push('/admin/faculty');
        router.refresh();
      }, 1000);
      
    } catch (error) {
      setApiError(error instanceof Error ? error.message : "An error occurred. Please try again.");
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Check if current step is valid before allowing to proceed
  const isStepValid = () => {
    const fields = form.getValues();
    
    if (currentStep === 0) { // Account step
      const accountFields = ['name', 'email'];
      if (!isEditing) accountFields.push('password');
      
      return accountFields.every(field => {
        const value = fields[field as keyof typeof fields];
        const fieldName = field as string;
        return !!value && !form.formState.errors[fieldName];
      });
    }
    
    if (currentStep === 1) { // Personal step
      // Personal step doesn't have required fields
      return true;
    }
    
    if (currentStep === 2) { // Academic step
      return !!fields.title && 
        !!fields.departmentId && 
        !form.formState.errors.title && 
        !form.formState.errors.departmentId;
    }
    
    return true;
  };

  const formCompletion = calculateCompletion();

  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEditing ? 'Edit Faculty' : 'Add Faculty'}
          </h1>
          <p className="text-gray-600">
            {isEditing ? 'Update faculty information' : 'Create a new faculty member'}
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/admin/faculty">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Faculty
          </Link>
        </Button>
      </div>

      {/* Form Success/Error Messages */}
      {formSuccess && (
        <Alert className="bg-green-50 border-green-500">
          <Check className="h-4 w-4 text-green-500" />
          <AlertTitle className="text-green-800">Success</AlertTitle>
          <AlertDescription className="text-green-700">
            Faculty {isEditing ? 'updated' : 'created'} successfully. Redirecting...
          </AlertDescription>
        </Alert>
      )}
      
      {(formError || apiError) && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {formError || apiError || 'An error occurred. Please try again.'}
          </AlertDescription>
        </Alert>
      )}

      {/* Progress */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Form completion</span>
          <span className="text-sm font-medium">{formCompletion}%</span>
        </div>
        <Progress value={formCompletion} className="h-2" />
      </div>

      {/* Wizard Steps */}
      <div className="flex justify-between border-b pb-4">
        {steps.map((step, index) => (
          <div 
            key={index} 
            className={`flex flex-col items-center space-y-2 cursor-pointer transition-all w-1/3
              ${currentStep === index ? 'opacity-100 scale-105' : 'opacity-70 scale-95'}`}
            onClick={() => setCurrentStep(index)}
          >
            <div className={`flex items-center justify-center w-12 h-12 rounded-full 
              ${currentStep === index 
                ? 'bg-primary text-white' 
                : index < currentStep 
                  ? 'bg-green-100 text-green-600 border border-green-500' 
                  : 'bg-gray-100 text-gray-500 border border-gray-300'}`}>
              {index < currentStep ? <Check className="h-6 w-6" /> : step.icon}
            </div>
            <div className="text-center">
              <p className="font-medium">{step.title}</p>
              <p className="text-xs text-gray-500">{step.description}</p>
            </div>
          </div>
        ))}
      </div>

      <form 
        onSubmit={(e) => {
          e.preventDefault();
          if (currentStep === steps.length - 1) {
            form.handleSubmit(onSubmit)(e);
          } else {
            goToNextStep();
          }
        }} 
        className="space-y-6"
      >
        {/* Step 1: Account Information */}
        {currentStep === 0 && (
          <Card>
            <CardHeader className="bg-slate-50 border-b">
              <CardTitle className="flex items-center">
                <UserPlus className="h-5 w-5 mr-2 text-primary" />
                Account Information
              </CardTitle>
              <CardDescription>Set up the faculty member's account credentials</CardDescription>
            </CardHeader>
            <CardContent className="pt-6 space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-base">Full Name <span className="text-red-500">*</span></Label>
                <Input
                  id="name"
                  {...form.register('name')}
                  placeholder="Dr. John Smith"
                  className={`text-base h-11 ${form.formState.errors.name ? "border-red-500" : ""}`}
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-600">{form.formState.errors.name.message as string}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-base">Email <span className="text-red-500">*</span></Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    {...form.register('email')}
                    placeholder="<EMAIL>"
                    className={`pl-10 text-base h-11 ${form.formState.errors.email ? "border-red-500" : ""}`}
                  />
                </div>
                {form.formState.errors.email && (
                  <p className="text-sm text-red-600">{form.formState.errors.email.message as string}</p>
                )}
              </div>

              {!isEditing && (
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-base">Password <span className="text-red-500">*</span></Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      {...form.register('password')}
                      placeholder="Minimum 6 characters"
                      className={`pl-10 pr-10 text-base h-11 ${form.formState.errors.password ? "border-red-500" : ""}`}
                    />
                    <button 
                      type="button"
                      className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                  {form.formState.errors.password && (
                    <p className="text-sm text-red-600">{form.formState.errors.password?.message as string}</p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    Password must be at least 6 characters long.
                  </p>
                </div>
              )}

              {isEditing && (
                <div className="space-y-2">
                  <Label htmlFor="status" className="text-base">Account Status</Label>
                  <Select
                    value={form.watch('status')}
                    onValueChange={(value) => form.setValue('status', value as any)}
                  >
                    <SelectTrigger className="text-base h-11">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="INACTIVE">Inactive</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                      <SelectItem value="SUSPENDED">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Step 2: Personal Information */}
        {currentStep === 1 && (
          <Card>
            <CardHeader className="bg-slate-50 border-b">
              <CardTitle className="flex items-center">
                <Phone className="h-5 w-5 mr-2 text-primary" />
                Personal Information
              </CardTitle>
              <CardDescription>Add personal details for the faculty member</CardDescription>
            </CardHeader>
            <CardContent className="pt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-base">First Name</Label>
                  <Input
                    id="firstName"
                    {...form.register('firstName')}
                    placeholder="John"
                    className="text-base h-11"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-base">Last Name</Label>
                  <Input
                    id="lastName"
                    {...form.register('lastName')}
                    placeholder="Smith"
                    className="text-base h-11"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="text-base">Phone Number</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <Input
                    id="phone"
                    {...form.register('phone')}
                    placeholder="+****************"
                    className="pl-10 text-base h-11"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Academic Information */}
        {currentStep === 2 && (
          <Card>
            <CardHeader className="bg-slate-50 border-b">
              <CardTitle className="flex items-center">
                <BookOpen className="h-5 w-5 mr-2 text-primary" />
                Academic Information
              </CardTitle>
              <CardDescription>Set professional and academic details</CardDescription>
            </CardHeader>
            <CardContent className="pt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title" className="text-base">Title/Position <span className="text-red-500">*</span></Label>
                  <div className="relative">
                    <Briefcase className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <Input
                      id="title"
                      {...form.register('title')}
                      placeholder="Associate Professor"
                      className={`pl-10 text-base h-11 ${form.formState.errors.title ? "border-red-500" : ""}`}
                    />
                  </div>
                  {form.formState.errors.title && (
                    <p className="text-sm text-red-600">{form.formState.errors.title.message as string}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="departmentId" className="text-base">Department <span className="text-red-500">*</span></Label>
                  <div className="relative">
                    <Building className="absolute left-3 top-3 h-5 w-5 text-gray-400 pointer-events-none" />
                    <Select
                      value={form.watch('departmentId')}
                      onValueChange={(value) => form.setValue('departmentId', value)}
                    >
                      <SelectTrigger 
                        className={`pl-10 text-base h-11 ${form.formState.errors.departmentId ? "border-red-500" : ""}`}
                      >
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map((dept) => (
                          <SelectItem key={dept.id} value={dept.id}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {form.formState.errors.departmentId && (
                    <p className="text-sm text-red-600">{form.formState.errors.departmentId.message as string}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="officeLocation" className="text-base">Office Location</Label>
                  <div className="relative">
                    <Building className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <Input
                      id="officeLocation"
                      {...form.register('officeLocation')}
                      placeholder="Building A, Room 101"
                      className="pl-10 text-base h-11"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="scholarId" className="text-base">Google Scholar ID</Label>
                  <Input
                    id="scholarId"
                    {...form.register('scholarId')}
                    placeholder="Google Scholar ID"
                    className="text-base h-11"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="websiteUrl" className="text-base">Website URL</Label>
                <Input
                  id="websiteUrl"
                  {...form.register('websiteUrl')}
                  placeholder="https://example.com"
                  className={`text-base h-11 ${form.formState.errors.websiteUrl ? "border-red-500" : ""}`}
                />
                {form.formState.errors.websiteUrl && (
                  <p className="text-sm text-red-600">{form.formState.errors.websiteUrl.message as string}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="bio" className="text-base">Bio</Label>
                <Textarea
                  id="bio"
                  {...form.register('bio')}
                  placeholder="Brief biography and research interests..."
                  rows={4}
                  className="text-base resize-y"
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-2">
          <Button 
            type="button" 
            variant="outline" 
            onClick={goToPreviousStep}
            disabled={currentStep === 0 || isLoading || formSuccess}
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Previous Step
          </Button>
          
          {currentStep < steps.length - 1 ? (
            <Button 
              type="button" 
              onClick={goToNextStep}
              disabled={!isStepValid() || isLoading || formSuccess}
            >
              Next Step
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button 
              type="submit" 
              disabled={!isStepValid() || isLoading || formSuccess}
              className={formSuccess ? "bg-green-600 hover:bg-green-700" : ""}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {isEditing ? 'Updating...' : 'Creating...'}
                </>
              ) : formSuccess ? (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  {isEditing ? 'Updated' : 'Created'}
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {isEditing ? 'Update Faculty' : 'Create Faculty'}
                </>
              )}
            </Button>
          )}
        </div>
      </form>

      {/* Debug Information - Only visible in development */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="mt-8 border-dashed border-orange-300">
          <CardHeader>
            <CardTitle className="text-orange-600">Debug Information</CardTitle>
            <CardDescription>This section is only visible in development mode</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><strong>Mode:</strong> {isEditing ? 'Editing' : 'Creating'}</div>
            <div><strong>Current Step:</strong> {currentStep + 1} of {steps.length}</div>
            <div><strong>Faculty ID:</strong> {facultyId || 'N/A'}</div>
            <div><strong>Schema:</strong> {isEditing ? 'updateFacultySchema' : 'createFacultySchema'}</div>
            <div><strong>Form State:</strong> {Object.keys(form.formState.errors).length > 0 ? 'Has Errors' : 'Valid'}</div>
            <div>
              <strong>Form Errors:</strong>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-24">
                {safeStringify(form.formState.errors) || 'None'}
              </pre>
            </div>
            <div>
              <strong>Current Values:</strong>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-24">
                {safeStringify(form.getValues())}
              </pre>
            </div>
            <div className="pt-2 flex space-x-2">
              <Button 
                type="button" 
                variant="outline" 
                size="sm"
                onClick={() => {
                  // Reset the form
                  form.reset();
                  setFormError(null);
                  setApiError(null);
                  setFormSuccess(false);
                  setCurrentStep(0);
                  toast({
                    title: "Form Reset",
                    description: "Form values have been reset to initial values",
                  });
                }}
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                Reset Form
              </Button>
              
              <Button 
                type="button" 
                variant="outline" 
                size="sm"
                onClick={() => {
                  // Manual test submission
                  const testData = form.getValues();
                  
                  fetch(isEditing ? `/api/admin/faculty/${facultyId}` : '/api/admin/faculty', {
                    method: isEditing ? 'PUT' : 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData),
                    credentials: 'include'
                  })
                  .then(response => {
                    const isSuccess = response.ok;
                    return response.text().then(text => ({ text, isSuccess, status: response.status }));
                  })
                  .then(({ text, isSuccess, status }) => {
                    try {
                      const json = JSON.parse(text);
                      
                      toast({
                        title: isSuccess ? "Test Success" : "Test Failed",
                        description: isSuccess 
                          ? "Manual test request succeeded" 
                          : `Error: ${json.error || 'Unknown error'}`,
                        variant: isSuccess ? "default" : "destructive",
                      });
                    } catch (e) {
                      toast({
                        title: "Parse Error",
                        description: "Failed to parse server response",
                        variant: "destructive",
                      });
                    }
                  })
                  .catch(error => {
                    toast({
                      title: "Network Error",
                      description: error.message,
                      variant: "destructive",
                    });
                  });
                }}
              >
                Test API Call
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
