"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { BookOpen, User, Calendar, MapPin, Clock, Users, Loader2 } from "lucide-react"

const classAssignmentSchema = z.object({
  courseId: z.string().min(1, 'Course is required'),
  facultyId: z.string().min(1, 'Faculty is required'),
  semester: z.enum(['Spring', 'Summer', 'Fall'], {
    errorMap: () => ({ message: 'Semester must be Spring, Summer, or Fall' })
  }),
  year: z.number().min(2020, 'Year must be 2020 or later').max(2030, 'Year cannot exceed 2030'),
  section: z.string().optional(),
  maxEnrollment: z.number().min(1, 'Max enrollment must be at least 1').max(500, 'Max enrollment cannot exceed 500'),
  schedule: z.object({
    days: z.array(z.string()).min(1, 'At least one day must be selected'),
    time: z.string().min(1, 'Time is required'),
    location: z.string().min(1, 'Location is required')
  }),
  syllabusUrl: z.string().url().optional().or(z.literal('')),
  description: z.string().optional(),
  status: z.enum(['upcoming', 'current', 'past']).default('upcoming')
})

type ClassAssignmentFormData = z.infer<typeof classAssignmentSchema>

interface Course {
  id: string
  code: string
  name: string
  credits: number
  department: {
    name: string
    slug: string
  }
}

interface Faculty {
  id: string
  user: {
    name: string | null
    email: string
  }
  department: {
    name: string
    slug: string
  }
}

interface ClassAssignmentFormProps {
  courses: Course[]
  faculty: Faculty[]
  classId?: string
  initialData?: Partial<ClassAssignmentFormData>
}

const daysOfWeek = [
  { id: 'monday', label: 'Monday' },
  { id: 'tuesday', label: 'Tuesday' },
  { id: 'wednesday', label: 'Wednesday' },
  { id: 'thursday', label: 'Thursday' },
  { id: 'friday', label: 'Friday' },
  { id: 'saturday', label: 'Saturday' },
  { id: 'sunday', label: 'Sunday' }
]

const currentYear = new Date().getFullYear()
const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i)

export function ClassAssignmentForm({ courses, faculty, classId, initialData }: ClassAssignmentFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [selectedDays, setSelectedDays] = useState<string[]>(initialData?.schedule?.days || [])
  const { toast } = useToast()
  const router = useRouter()
  const isEditing = !!classId

  const form = useForm<ClassAssignmentFormData>({
    resolver: zodResolver(classAssignmentSchema),
    defaultValues: {
      courseId: initialData?.courseId || '',
      facultyId: initialData?.facultyId || '',
      semester: initialData?.semester || 'Fall',
      year: initialData?.year || currentYear,
      section: initialData?.section || '',
      maxEnrollment: initialData?.maxEnrollment || 30,
      schedule: {
        days: initialData?.schedule?.days || [],
        time: initialData?.schedule?.time || '',
        location: initialData?.schedule?.location || ''
      },
      syllabusUrl: initialData?.syllabusUrl || '',
      description: initialData?.description || '',
      status: initialData?.status || 'upcoming'
    }
  })

  const handleDayChange = (dayId: string, checked: boolean) => {
    const newDays = checked 
      ? [...selectedDays, dayId]
      : selectedDays.filter(d => d !== dayId)
    
    setSelectedDays(newDays)
    form.setValue('schedule.days', newDays)
  }

  const onSubmit = async (data: ClassAssignmentFormData) => {
    setIsLoading(true)

    try {
      const url = isEditing ? `/api/admin/classes/${classId}` : '/api/admin/classes'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || `Failed to ${isEditing ? 'update' : 'create'} course assignment`)
      }

      toast({
        title: "Success",
        description: `Course assignment ${isEditing ? 'updated' : 'created'} successfully.`,
      })

      router.push('/admin/classes')
      router.refresh()
    } catch (error) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} assignment:`, error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      {/* Course and Faculty Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BookOpen className="w-5 h-5 mr-2" />
            Course & Faculty Assignment
          </CardTitle>
          <CardDescription>Select the course and faculty member for this assignment</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="courseId">Course *</Label>
              <Select
                value={form.watch('courseId')}
                onValueChange={(value) => form.setValue('courseId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select course" />
                </SelectTrigger>
                <SelectContent>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.code}: {course.name} ({course.credits} credits)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.courseId && (
                <p className="text-sm text-red-600">{form.formState.errors.courseId.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="facultyId">Faculty Member *</Label>
              <Select
                value={form.watch('facultyId')}
                onValueChange={(value) => form.setValue('facultyId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select faculty" />
                </SelectTrigger>
                <SelectContent>
                  {faculty.map((member) => (
                    <SelectItem key={member.id} value={member.id}>
                      {member.user.name} - {member.department.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.facultyId && (
                <p className="text-sm text-red-600">{form.formState.errors.facultyId.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Semester and Year */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Semester Information
          </CardTitle>
          <CardDescription>When will this course be offered?</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="semester">Semester *</Label>
              <Select
                value={form.watch('semester')}
                onValueChange={(value: 'Spring' | 'Summer' | 'Fall') => form.setValue('semester', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select semester" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Spring">Spring</SelectItem>
                  <SelectItem value="Summer">Summer</SelectItem>
                  <SelectItem value="Fall">Fall</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.semester && (
                <p className="text-sm text-red-600">{form.formState.errors.semester.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="year">Year *</Label>
              <Select
                value={form.watch('year').toString()}
                onValueChange={(value) => form.setValue('year', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select year" />
                </SelectTrigger>
                <SelectContent>
                  {yearOptions.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.year && (
                <p className="text-sm text-red-600">{form.formState.errors.year.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="section">Section</Label>
              <Input
                id="section"
                {...form.register('section')}
                placeholder="A, B, 01, etc."
              />
              {form.formState.errors.section && (
                <p className="text-sm text-red-600">{form.formState.errors.section.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Schedule */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Schedule
          </CardTitle>
          <CardDescription>Set the class schedule and location</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Days of Week *</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {daysOfWeek.map((day) => (
                <div key={day.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={day.id}
                    checked={selectedDays.includes(day.id)}
                    onCheckedChange={(checked) => handleDayChange(day.id, checked as boolean)}
                  />
                  <Label htmlFor={day.id} className="text-sm font-normal">
                    {day.label}
                  </Label>
                </div>
              ))}
            </div>
            {form.formState.errors.schedule?.days && (
              <p className="text-sm text-red-600">{form.formState.errors.schedule.days.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="time">Time *</Label>
              <Input
                id="time"
                {...form.register('schedule.time')}
                placeholder="9:00 AM - 10:30 AM"
              />
              {form.formState.errors.schedule?.time && (
                <p className="text-sm text-red-600">{form.formState.errors.schedule.time.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location *</Label>
              <Input
                id="location"
                {...form.register('schedule.location')}
                placeholder="Room 101, Building A"
              />
              {form.formState.errors.schedule?.location && (
                <p className="text-sm text-red-600">{form.formState.errors.schedule.location.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="w-5 h-5 mr-2" />
            Additional Information
          </CardTitle>
          <CardDescription>Enrollment limits and course details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="maxEnrollment">Maximum Enrollment *</Label>
            <Input
              id="maxEnrollment"
              type="number"
              {...form.register('maxEnrollment', { valueAsNumber: true })}
              min="1"
              max="500"
            />
            {form.formState.errors.maxEnrollment && (
              <p className="text-sm text-red-600">{form.formState.errors.maxEnrollment.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="syllabusUrl">Syllabus URL</Label>
            <Input
              id="syllabusUrl"
              type="url"
              {...form.register('syllabusUrl')}
              placeholder="https://example.com/syllabus.pdf"
            />
            {form.formState.errors.syllabusUrl && (
              <p className="text-sm text-red-600">{form.formState.errors.syllabusUrl.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Course Description</Label>
            <Textarea
              id="description"
              {...form.register('description')}
              placeholder="Additional course information, requirements, etc."
              rows={3}
            />
            {form.formState.errors.description && (
              <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
          {isEditing ? 'Update Assignment' : 'Create Assignment'}
        </Button>
      </div>
    </form>
  )
}
