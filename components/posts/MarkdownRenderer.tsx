'use client'

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import rehypeHighlight from 'rehype-highlight'
import rehypeRaw from 'rehype-raw'
import { Components } from 'react-markdown'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import 'katex/dist/katex.min.css'
import 'highlight.js/styles/github-dark.css'

interface MarkdownRendererProps {
  content: string
  className?: string
}

export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  const components: Components = {
    // Custom heading components with better styling
    h1: ({ children, ...props }) => (
      <h1 
        className="text-4xl font-bold mb-6 mt-8 text-primary border-b-2 border-primary/20 pb-3" 
        {...props}
      >
        {children}
      </h1>
    ),
    h2: ({ children, ...props }) => (
      <h2 
        className="text-3xl font-semibold mb-4 mt-8 text-primary border-b border-border pb-2" 
        {...props}
      >
        {children}
      </h2>
    ),
    h3: ({ children, ...props }) => (
      <h3 
        className="text-2xl font-semibold mb-3 mt-6 text-primary" 
        {...props}
      >
        {children}
      </h3>
    ),
    h4: ({ children, ...props }) => (
      <h4 
        className="text-xl font-semibold mb-2 mt-4 text-primary" 
        {...props}
      >
        {children}
      </h4>
    ),
    h5: ({ children, ...props }) => (
      <h5 
        className="text-lg font-semibold mb-2 mt-4 text-primary" 
        {...props}
      >
        {children}
      </h5>
    ),
    h6: ({ children, ...props }) => (
      <h6 
        className="text-base font-semibold mb-2 mt-4 text-primary" 
        {...props}
      >
        {children}
      </h6>
    ),

    // Enhanced paragraph styling
    p: ({ children, ...props }) => (
      <p 
        className="mb-4 text-lg leading-relaxed text-foreground" 
        {...props}
      >
        {children}
      </p>
    ),

    // Beautiful blockquotes
    blockquote: ({ children, ...props }) => (
      <blockquote 
        className="border-l-4 border-primary/50 pl-6 py-4 my-6 bg-muted/30 rounded-r-lg italic text-muted-foreground" 
        {...props}
      >
        {children}
      </blockquote>
    ),

    // Enhanced lists
    ul: ({ children, ...props }) => (
      <ul 
        className="mb-4 pl-6 space-y-2 list-disc marker:text-primary" 
        {...props}
      >
        {children}
      </ul>
    ),
    ol: ({ children, ...props }) => (
      <ol 
        className="mb-4 pl-6 space-y-2 list-decimal marker:text-primary" 
        {...props}
      >
        {children}
      </ol>
    ),
    li: ({ children, ...props }) => (
      <li 
        className="text-lg leading-relaxed" 
        {...props}
      >
        {children}
      </li>
    ),

    // Code blocks with syntax highlighting
    code: ({ node, inline, className, children, ...props }) => {
      const match = /language-(\w+)/.exec(className || '')
      const language = match ? match[1] : ''

      if (!inline && language) {
        return (
          <div className="my-6 rounded-lg overflow-hidden border border-border">
            <div className="bg-muted px-4 py-2 text-sm font-medium text-muted-foreground border-b border-border">
              {language}
            </div>
            <SyntaxHighlighter
              style={oneDark}
              language={language}
              PreTag="div"
              className="!m-0 !bg-background"
              customStyle={{
                margin: 0,
                padding: '1rem',
                background: 'hsl(var(--background))',
                fontSize: '0.875rem',
              }}
              {...props}
            >
              {String(children).replace(/\n$/, '')}
            </SyntaxHighlighter>
          </div>
        )
      }

      return (
        <code 
          className="bg-muted px-2 py-1 rounded text-sm font-mono text-primary" 
          {...props}
        >
          {children}
        </code>
      )
    },

    // Enhanced links
    a: ({ children, href, ...props }) => (
      <a 
        href={href}
        className="text-primary underline underline-offset-4 decoration-2 hover:decoration-primary/60 transition-colors font-medium"
        target={href?.startsWith('http') ? '_blank' : undefined}
        rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}
        {...props}
      >
        {children}
      </a>
    ),

    // Beautiful images
    img: ({ src, alt, ...props }) => (
      <div className="my-8">
        <img 
          src={src}
          alt={alt}
          className="w-full rounded-lg shadow-lg border border-border"
          {...props}
        />
        {alt && (
          <p className="text-center text-sm text-muted-foreground mt-2 italic">
            {alt}
          </p>
        )}
      </div>
    ),

    // Enhanced tables
    table: ({ children, ...props }) => (
      <div className="my-6 overflow-x-auto">
        <table 
          className="w-full border-collapse border border-border rounded-lg overflow-hidden" 
          {...props}
        >
          {children}
        </table>
      </div>
    ),
    thead: ({ children, ...props }) => (
      <thead 
        className="bg-muted" 
        {...props}
      >
        {children}
      </thead>
    ),
    th: ({ children, ...props }) => (
      <th 
        className="border border-border px-4 py-3 text-left font-semibold text-foreground" 
        {...props}
      >
        {children}
      </th>
    ),
    td: ({ children, ...props }) => (
      <td 
        className="border border-border px-4 py-3 text-foreground" 
        {...props}
      >
        {children}
      </td>
    ),

    // Horizontal rules
    hr: ({ ...props }) => (
      <hr 
        className="my-8 border-0 border-t-2 border-border" 
        {...props}
      />
    ),

    // Task lists (GitHub-style)
    input: ({ type, checked, ...props }) => {
      if (type === 'checkbox') {
        return (
          <input
            type="checkbox"
            checked={checked}
            readOnly
            className="mr-2 accent-primary"
            {...props}
          />
        )
      }
      return <input type={type} {...props} />
    },
  }

  return (
    <div className={`prose prose-lg dark:prose-invert max-w-none ${className}`}>
      <ReactMarkdown
        components={components}
        remarkPlugins={[remarkGfm, remarkMath]}
        rehypePlugins={[rehypeKatex, rehypeHighlight, rehypeRaw]}
        className="markdown-content"
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}
