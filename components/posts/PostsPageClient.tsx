"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowRight, Search, Calendar, FileText } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"

interface Post {
  id: string
  title: string
  slug: string
  excerpt?: string | null
  featured: boolean
  imageUrl?: string | null
  imageAlt?: string | null
  viewCount: number
  readingTime?: string | null
  publishedAt?: Date | null
  author: {
    name: string | null
    email: string
  }
  category: {
    name: string
    slug: string
    color?: string | null
  }
  tags: Array<{
    tag: {
      name: string
      slug: string
    }
  }>
}

interface Category {
  id: string
  name: string
  slug: string
  color?: string | null
  _count: {
    posts: number
  }
}

interface PostsPageClientProps {
  posts: Post[]
  categories: Category[]
}

function formatDate(date: Date | null) {
  if (!date) return 'Not published'
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date))
}

export function PostsPageClient({ posts, categories }: PostsPageClientProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [activeCategory, setActiveCategory] = useState("all")

  // Filter posts based on search term and active category
  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (post.excerpt && post.excerpt.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         post.tags.some(tag => tag.tag.name.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = activeCategory === "all" || post.category.slug === activeCategory

    return matchesSearch && matchesCategory
  })

  // Get featured post if any
  const featuredPost = posts.find(post => post.featured)

  return (
    <div className="container mx-auto px-4 md:px-6">
      <div className="mb-12 text-center">
        <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl text-primary">
          News, Events & Insights
        </h1>
        <p className="mt-4 text-xl text-muted-foreground max-w-2xl mx-auto">
          Explore the latest updates, stories, and announcements from our college community.
        </p>
      </div>

      {/* Search and filter */}
      <div className="mb-8 space-y-4">
        <div className="relative max-w-lg mx-auto">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search posts..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <Tabs defaultValue="all" className="w-full" onValueChange={setActiveCategory}>
          <TabsList className="w-full max-w-3xl mx-auto justify-start overflow-x-auto py-2">
            <TabsTrigger value="all" className="rounded-full">All</TabsTrigger>
            {categories.map(category => (
              <TabsTrigger
                key={category.slug}
                value={category.slug}
                className="rounded-full"
              >
                {category.name} ({category._count.posts})
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* Featured post */}
      {featuredPost && activeCategory === "all" && searchTerm === "" && (
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Featured Post
          </h2>
          <div className="bg-card rounded-xl overflow-hidden shadow-lg">
            <div className="md:flex">
              <div className="md:w-1/2">
                <img
                  src={featuredPost.imageUrl || "/images/campus/main-building.jpg"}
                  alt={featuredPost.imageAlt || featuredPost.title}
                  className="h-64 md:h-full w-full object-cover"
                />
              </div>
              <div className="p-6 md:w-1/2 flex flex-col justify-between">
                <div>
                  <Badge 
                    className="mb-2"
                    style={{ 
                      backgroundColor: featuredPost.category.color || '#e5e7eb',
                      color: '#374151'
                    }}
                  >
                    {featuredPost.category.name}
                  </Badge>
                  <h3 className="text-2xl font-bold mb-3">
                    <Link href={`/posts/${featuredPost.slug}`} className="hover:text-primary transition-colors">
                      {featuredPost.title}
                    </Link>
                  </h3>
                  <p className="text-muted-foreground mb-4">{featuredPost.excerpt}</p>
                </div>
                <div className="mt-4">
                  <div className="flex items-center text-sm text-muted-foreground mb-4">
                    <Calendar className="mr-2 h-4 w-4" />
                    <span>{formatDate(featuredPost.publishedAt)}</span>
                    {featuredPost.readingTime && (
                      <>
                        <span className="mx-2">•</span>
                        <span>{featuredPost.readingTime}</span>
                      </>
                    )}
                  </div>
                  <Button asChild>
                    <Link href={`/posts/${featuredPost.slug}`}>
                      Read Full Article <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* All posts */}
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {filteredPosts.map((post) => (
          <Card key={post.id} className="flex flex-col overflow-hidden rounded-lg shadow-lg border-0 transition-all duration-300 hover:shadow-xl">
            <div className="h-48 overflow-hidden">
              <img
                src={post.imageUrl || "/placeholder.svg?height=400&width=600"}
                alt={post.imageAlt || post.title}
                className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
            <CardHeader>
              <div className="flex items-center justify-between mb-2">
                <Badge 
                  variant="outline" 
                  className="text-xs font-medium"
                  style={{ 
                    backgroundColor: post.category.color || '#e5e7eb',
                    color: '#374151'
                  }}
                >
                  {post.category.name}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {formatDate(post.publishedAt)}
                </span>
              </div>
              <CardTitle className="line-clamp-2">
                <Link href={`/posts/${post.slug}`} className="hover:text-primary transition-colors">
                  {post.title}
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-sm text-muted-foreground line-clamp-3">{post.excerpt}</p>
              {post.readingTime && (
                <p className="text-xs text-muted-foreground mt-2">{post.readingTime}</p>
              )}
            </CardContent>
            <CardFooter className="flex flex-col items-start gap-4 pt-4 border-t">
              <div className="flex flex-wrap gap-2">
                {post.tags.slice(0, 3).map(({ tag }) => (
                  <Badge key={tag.slug} variant="secondary" className="text-xs">
                    {tag.name}
                  </Badge>
                ))}
                {post.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs">+{post.tags.length - 3}</Badge>
                )}
              </div>
              <div className="flex items-center justify-between w-full">
                <Link
                  href={`/posts/${post.slug}`}
                  className="text-sm font-medium text-primary hover:underline flex items-center"
                >
                  Read More <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
                <span className="text-xs text-muted-foreground">
                  {post.viewCount} views
                </span>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Empty state */}
      {filteredPosts.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-xl font-medium mb-2">No posts found</h3>
          <p className="text-muted-foreground mb-6">Try adjusting your search or filter criteria</p>
          <Button onClick={() => {setSearchTerm(""); setActiveCategory("all");}}>
            Reset Filters
          </Button>
        </div>
      )}

      {/* Newsletter subscription */}
      <div className="mt-16 bg-muted rounded-xl p-8 text-center">
        <h2 className="text-2xl font-bold mb-2">Stay Updated</h2>
        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
          Subscribe to our newsletter to receive the latest posts and updates directly in your inbox.
        </p>
        <form className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
          <Input
            type="email"
            placeholder="Enter your email"
            className="flex-grow"
            required
          />
          <Button type="submit">Subscribe</Button>
        </form>
      </div>
    </div>
  )
}
