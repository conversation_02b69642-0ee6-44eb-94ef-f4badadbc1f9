'use client'

import { useEffect } from 'react'

export function useParallaxEffect() {
  useEffect(() => {
    const handleParallax = () => {
      const parallaxElements = document.querySelectorAll('.motion-safe\\:parallax')
      
      parallaxElements.forEach((element) => {
        const elementTop = element.getBoundingClientRect().top
        const elementHeight = element.clientHeight
        const windowHeight = window.innerHeight
        
        // Calculate how far the element is from the viewport center
        const distanceFromCenter = elementTop - windowHeight / 2 + elementHeight / 2
        
        // Calculate the parallax effect (move slower than scroll)
        const parallaxOffset = distanceFromCenter * 0.15
        
        // Apply the transform
        if (element instanceof HTMLElement) {
          element.style.transform = `translateY(${-5 + parallaxOffset * 0.1}%)`
        }
      })
    }
    
    // Add scroll event listener
    window.addEventListener('scroll', handleParallax, { passive: true })
    
    // Initial call to position elements
    handleParallax()
    
    // Clean up
    return () => {
      window.removeEventListener('scroll', handleParallax)
    }
  }, [])
}
