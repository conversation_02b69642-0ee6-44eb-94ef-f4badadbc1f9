import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import { ChevronDownIcon, AcademicCapIcon, BookOpenIcon, ClockIcon } from '@heroicons/react/24/outline';

interface Course {
  name: string;
  credits: number;
  type: 'core' | 'elective' | 'project' | 'lab';
  description?: string;
  prerequisites?: string[];
}

interface Semester {
  name: string;
  courses: Course[];
}

interface Year {
  name: string;
  description: string;
  semesters: Semester[];
  color: string;
  icon: string;
}

const curriculum: Year[] = [
  {
    name: "Year 1: Foundation Year",
    description: "Building the fundamental knowledge base for cybersecurity",
    color: "from-teal-600 to-emerald-600",
    icon: "🎯",
    semesters: [
      {
        name: "Semester 1",
        courses: [
          { name: "Introduction to Cybersecurity", credits: 3, type: 'core', description: "Fundamental concepts of information security" },
          { name: "Introduction to Programming", credits: 3, type: 'core' },
          { name: "Mathematics I", credits: 3, type: 'core' },
          { name: "Introduction to Networks", credits: 3, type: 'core' },
          { name: "Communication Skills", credits: 3, type: 'core' },
          { name: "Cybersecurity Lab I", credits: 2, type: 'lab' },
        ],
      },
      {
        name: "Semester 2",
        courses: [
          { name: "Data Structures & Algorithms", credits: 3, type: 'core' },
          { name: "Discrete Mathematics/Structure", credits: 3, type: 'core' },
          { name: "Database Systems", credits: 3, type: 'core' },
          { name: "Ethics and Professional Conducts", credits: 3, type: 'core' },
          { name: "Introduction of Object-Oriented Programming", credits: 3, type: 'core' },
          { name: "Project-I", credits: 2, type: 'project' },
        ],
      },
    ],
  },
  {
    name: "Year 2: Core Cyber Security Knowledge",
    description: "Diving deep into cybersecurity fundamentals and practices",
    color: "from-sky-600 to-stone-600",
    icon: "🛡️",
    semesters: [
      {
        name: "Semester 1",
        courses: [
          { name: "Operating Systems and Security", credits: 3, type: 'core' },
          { name: "Mathematics II", credits: 3, type: 'core' },
          { name: "Ethical Hacking and Penetration Testing", credits: 3, type: 'core' },
          { name: "Cryptography", credits: 3, type: 'core' },
          { name: "Exploring Leadership: Global Lessons, Local Context", credits: 3, type: 'core' },
          { name: "Project-II", credits: 2, type: 'project' },
        ],
      },
      {
        name: "Semester 2",
        courses: [
          { name: "Secure Software Development", credits: 3, type: 'core' },
          { name: "Distributed System Application and Security", credits: 3, type: 'core' },
          { name: "Risk Management, Governance and Cyber Law", credits: 3, type: 'core' },
          { name: "Probability and Statistics", credits: 3, type: 'core' },
          { name: "Computer Architecture", credits: 3, type: 'core' },
          { name: "Project-III", credits: 2, type: 'project' },
        ],
      },
    ],
  },
  {
    name: "Year 3: Advanced Topics and Specialization",
    description: "Exploring advanced cybersecurity concepts and specialized areas",
    color: "from-violet-600 to-stone-600",
    icon: "🔬",
    semesters: [
      {
        name: "Semester 1",
        courses: [
          { name: "Introduction to Systems Programming", credits: 3, type: 'core' },
          { name: "IoT and Embedded Systems Security", credits: 3, type: 'core' },
          { name: "Web Application Security", credits: 3, type: 'core' },
          { name: "Foundation of AI", credits: 3, type: 'core' },
          { name: "Engineering for Global Good", credits: 3, type: 'core' },
          { name: "Project-IV", credits: 2, type: 'project' },
        ],
      },
      {
        name: "Semester 2",
        courses: [
          { name: "System Analysis and Audit", credits: 3, type: 'core' },
          { name: "Malware Analysis and Reverse Engineering", credits: 3, type: 'core' },
          { name: "Advanced Network Security", credits: 3, type: 'core' },
          { name: "Advanced AI", credits: 3, type: 'core' },
          { name: "Information Security", credits: 3, type: 'core' },
          { name: "Project-V", credits: 2, type: 'project' },
        ],
      },
    ],
  },
  {
    name: "Year 4: Specialization and Capstone",
    description: "Final year focusing on specialization and practical application",
    color: "from-red-600 to-stone-600",
    icon: "🎓",
    semesters: [
      {
        name: "Semester 1",
        courses: [
          { name: "Digital Forensics", credits: 3, type: 'core' },
          { name: "Introduction to Entrepreneurship", credits: 3, type: 'core' },
          { 
            name: "Elective I", 
            credits: 3, 
            type: 'elective',
            description: "Choose from: Application of AI in Cybersecurity, Research in Blockchain, Social Engineering & Psychology, Quantum Algorithms and Cryptography"
          },
          { name: "Introduction to Blockchain", credits: 3, type: 'core' },
        ],
      },
      {
        name: "Semester 2",
        courses: [
          { 
            name: "Elective II", 
            credits: 3, 
            type: 'elective',
            description: "Choose from: Introduction to Finance, Introduction to Economics"
          },
          { name: "Internship/Capstone", credits: 6, type: 'project' },
        ],
      },
    ],
  },
];



const TimelineView = ({ 
  selectedCourse, 
  setSelectedCourse, 
  currentYear, 
  setCurrentYear 
}: {
  selectedCourse: Course | null;
  setSelectedCourse: (course: Course | null) => void;
  currentYear: number;
  setCurrentYear: (year: number) => void;
}) => {
  const [expandedYears, setExpandedYears] = useState<Set<number>>(new Set([0])); // Start with first year expanded

  const toggleYear = (yearIndex: number) => {
    setExpandedYears(prev => {
      const newSet = new Set(prev);
      if (newSet.has(yearIndex)) {
        newSet.delete(yearIndex);
      } else {
        newSet.add(yearIndex);
      }
      return newSet;
    });
  };

  const toggleAllYears = () => {
    if (expandedYears.size === curriculum.length) {
      setExpandedYears(new Set()); // Collapse all
    } else {
      setExpandedYears(new Set(curriculum.map((_, index) => index))); // Expand all
    }
  };
  return (
    <div className="relative">
      {/* Expand/Collapse All Button */}
      <div className="mb-8 flex justify-center">
        <motion.button
          onClick={toggleAllYears}
          className="bg-stone-800 hover:bg-stone-700 text-teal-200 px-6 py-2 rounded-lg border border-stone-600 font-mono text-sm transition-all duration-200 flex items-center gap-2"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span className="text-stone-400">$</span>
          {expandedYears.size === curriculum.length ? 'collapse --all' : 'expand --all'}
          <motion.div
            animate={{ rotate: expandedYears.size === curriculum.length ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <ChevronDownIcon className="h-4 w-4" />
          </motion.div>
        </motion.button>
      </div>

      {/* Timeline Line - Network Cable Style */}
      <div className="absolute left-8 top-16 bottom-0 w-2 bg-gradient-to-b from-teal-500 via-sky-500 via-violet-500 to-red-500 rounded-full shadow-lg"></div>
      {/* Data Flow Animation */}
      <div className="absolute left-8 top-16 bottom-0 w-2 rounded-full overflow-hidden">
        <div className="w-full h-4 bg-white/30 animate-pulse" style={{
          background: 'linear-gradient(to bottom, transparent 0%, rgba(255,255,255,0.5) 50%, transparent 100%)',
          animation: 'flow 3s linear infinite'
        }}></div>
      </div>
      
      {curriculum.map((year, yearIndex) => (
        <motion.div
          key={yearIndex}
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: yearIndex * 0.3 }}
          className="relative mb-16"
        >
          {/* Timeline Node - Network Router Style */}
          <div className="absolute left-5 w-7 h-7 bg-slate-900 border-2 border-current rounded-lg z-10 flex items-center justify-center shadow-lg"
               style={{ color: yearIndex <= currentYear ? '#10b981' : '#6b7280' }}>
            <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>
          </div>
          
          {/* Year Card - System Module Style */}
          <div className="ml-20">
            <motion.div
              className={`bg-gradient-to-r ${year.color} p-6 rounded-lg text-white cursor-pointer shadow-lg border-l-4 border-white/30 relative overflow-hidden`}
              whileHover={{ scale: 1.02, y: -5 }}
              onClick={() => {
                setCurrentYear(yearIndex);
                toggleYear(yearIndex);
              }}
            >
              {/* Circuit Pattern Background */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-2 left-2 w-16 h-16 border border-white/30 rounded"></div>
                <div className="absolute top-4 right-4 w-8 h-8 border border-white/30 rounded-full"></div>
                <div className="absolute bottom-4 left-8 w-12 h-12 border border-white/30 rounded"></div>
              </div>
              
              <div className="relative z-10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                      <span className="text-xl">{year.icon}</span>
                    </div>
                    <div>
                      <div className="font-mono text-xs text-white/70 mb-1">module_{yearIndex + 1}.initialize()</div>
                      <h3 className="text-lg font-bold">{year.name}</h3>
                    </div>
                  </div>
                  {/* Collapse/Expand Indicator */}
                  <motion.div
                    animate={{ rotate: expandedYears.has(yearIndex) ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="text-white/70"
                  >
                    <ChevronDownIcon className="h-5 w-5" />
                  </motion.div>
                </div>
                <div className="mt-2">
                  <p className="text-white/90 text-sm">{year.description}</p>
                  
                  {/* Status Indicator */}
                  <div className="flex items-center gap-2 mt-3">
                    <div className={`w-2 h-2 rounded-full ${yearIndex <= currentYear ? 'bg-green-400' : 'bg-gray-400'} animate-pulse`}></div>
                    <span className="font-mono text-xs text-white/70">
                      {yearIndex <= currentYear ? 'ACTIVE' : 'PENDING'}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>
            
            {/* Collapsible Semesters - Database Table Style */}
            <AnimatePresence>
              {expandedYears.has(yearIndex) && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="mt-6 space-y-6 overflow-hidden"
                >
                  {year.semesters.map((semester, semIndex) => (
                <div key={semIndex} className="bg-stone-900 border border-stone-600 rounded-lg shadow-md p-4 font-mono">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-2 h-2 bg-teal-400 rounded-full"></div>
                    <h4 className="font-semibold text-teal-300 text-sm">{semester.name.toUpperCase()}</h4>
                    <div className="flex-1 border-b border-stone-600"></div>
                    <span className="text-stone-400 text-xs">{semester.courses.length} modules</span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {semester.courses.map((course, courseIndex) => (
                      <motion.div
                        key={courseIndex}
                        className={`p-3 rounded border cursor-pointer transition-all duration-200 relative ${
                          course.type === 'core' ? 'bg-sky-800/20 border-sky-500/30 hover:border-sky-400/50' :
                          course.type === 'elective' ? 'bg-emerald-800/20 border-emerald-500/30 hover:border-emerald-400/50' :
                          course.type === 'project' ? 'bg-violet-800/20 border-violet-500/30 hover:border-violet-400/50' :
                          'bg-red-800/20 border-red-500/30 hover:border-red-400/50'
                        }`}
                        whileHover={{ scale: 1.02, y: -2 }}
                        onClick={() => setSelectedCourse(course)}
                      >
                        {/* Type indicator */}
                        <div className={`absolute top-1 right-1 w-2 h-2 rounded-full ${
                          course.type === 'core' ? 'bg-sky-400' :
                          course.type === 'elective' ? 'bg-emerald-400' :
                          course.type === 'project' ? 'bg-violet-400' :
                          'bg-red-400'
                        }`}></div>
                        
                        <div className="font-mono text-xs text-stone-400 mb-1">
                          {course.type}.load("{course.name.toLowerCase().replace(/\s+/g, '_')}")
                        </div>
                        <h5 className="font-medium text-white text-sm leading-tight">{course.name}</h5>
                        <div className="flex justify-between items-center mt-2">
                          <span className="text-xs text-stone-400">{course.credits} CR</span>
                          <span className={`text-xs px-2 py-1 rounded-full font-mono ${
                            course.type === 'core' ? 'bg-sky-500/20 text-sky-200' :
                            course.type === 'elective' ? 'bg-emerald-500/20 text-emerald-200' :
                            course.type === 'project' ? 'bg-violet-500/20 text-violet-200' :
                            'bg-red-500/20 text-red-200'
                          }`}>
                            {course.type.toUpperCase()}
                          </span>
                        </div>
                      </motion.div>
                    ))}
                    </div>
                  </div>
                ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

const CybersecurityTimeline: React.FC = () => {
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [currentYear, setCurrentYear] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({ target: containerRef });
  const y = useTransform(scrollYProgress, [0, 1], [0, -50]);

  useEffect(() => {
    // Auto-advance current year for demo (optional)
    const interval = setInterval(() => {
      setCurrentYear(prev => (prev + 1) % curriculum.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div ref={containerRef} className="relative overflow-hidden">
      <motion.div style={{ y }} className="relative z-10">


        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 pb-16">
          <TimelineView 
            selectedCourse={selectedCourse}
            setSelectedCourse={setSelectedCourse}
            currentYear={currentYear}
            setCurrentYear={setCurrentYear}
          />
        </div>
      </motion.div>

      {/* Enhanced Course Modal */}
      <AnimatePresence>
        {selectedCourse && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedCourse(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0, rotateY: 90 }}
              animate={{ scale: 1, opacity: 1, rotateY: 0 }}
              exit={{ scale: 0.8, opacity: 0, rotateY: -90 }}
              className="bg-stone-900 border border-stone-600 rounded-lg p-6 max-w-lg w-full shadow-2xl font-mono"
              onClick={e => e.stopPropagation()}
            >
              {/* Terminal Header */}
              <div className="flex items-center gap-2 mb-4 pb-3 border-b border-stone-600">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-stone-400 text-xs ml-4">course-details@system:~$</span>
              </div>

              <div className="space-y-4">
                {/* Course Header */}
                <div className="bg-stone-800 border border-stone-600 rounded p-3">
                  <div className="text-teal-300 text-xs mb-2">
                    <span className="text-stone-400">$</span> cat course.json
                  </div>
                  <div className="text-violet-300 text-sm">
                    {'{'}<br />
                    <span className="ml-4 text-amber-300">"name"</span>: <span className="text-emerald-300">"{selectedCourse.name}"</span>,<br />
                    <span className="ml-4 text-amber-300">"credits"</span>: <span className="text-sky-300">{selectedCourse.credits}</span>,<br />
                    <span className="ml-4 text-amber-300">"type"</span>: <span className="text-emerald-300">"{selectedCourse.type}"</span><br />
                    {'}'}
                  </div>
                </div>
                
                {/* Course Description */}
                {selectedCourse.description && (
                  <div className="bg-stone-800 border border-stone-600 rounded p-3">
                    <div className="text-teal-300 text-xs mb-2">
                      <span className="text-stone-400">$</span> cat description.txt
                    </div>
                    <p className="text-stone-200 text-sm">{selectedCourse.description}</p>
                  </div>
                )}
                
                {/* Prerequisites */}
                {selectedCourse.prerequisites && (
                  <div className="bg-stone-800 border border-stone-600 rounded p-3">
                    <div className="text-teal-300 text-xs mb-2">
                      <span className="text-stone-400">$</span> ls prerequisites/
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {selectedCourse.prerequisites.map((prereq, index) => (
                        <span key={index} className="px-2 py-1 bg-sky-800/30 border border-sky-500/30 text-sky-200 rounded text-xs font-mono">
                          {prereq}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Close Button */}
                <div className="pt-4 border-t border-stone-600">
                  <button
                    onClick={() => setSelectedCourse(null)}
                    className="w-full bg-gradient-to-r from-red-800 to-red-700 hover:from-red-700 hover:to-red-600 text-red-100 px-4 py-2 rounded border border-red-600 font-mono text-sm transition-all duration-200"
                  >
                    <span className="text-stone-300">$</span> exit
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CybersecurityTimeline; 