import ProgramOverviewCard from "./ProgramOverviewCard"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Lock, Leaf, Building, BookOpen, Sparkles, Target } from "lucide-react"
import { Badge } from "@/components/ui/badge"

const programsData = [
  {
    title: "Computer Science",
    description: "Technology & Innovation",
    details: "Our flagship B.Tech in Cybersecurity program is now available, with Computer Engineering and Artificial Intelligence programs launching soon.",
    icon: <Lock className="h-6 w-6" />,
    themeColor: "crimson",
    upcoming: false,
    stats: { programs: "1 Available + 2 Coming Soon" },
    gradient: "from-red-500/20 to-pink-500/20",
    accentColor: "border-red-500/30",
    highlights: ["B.Tech Cybersecurity", "Computer Engineering (Soon)", "Artificial Intelligence (Soon)"]
  },
  {
    title: "Agriculture and Climate Science",
    description: "Sustainable Practices",
    details: "Pioneer sustainable agriculture and climate solutions to address global food security and environmental challenges.",
    icon: <Leaf className="h-6 w-6" />,
    themeColor: "green-600",
    upcoming: true,
    stats: { programs: "Coming Soon" },
    gradient: "from-green-500/20 to-emerald-500/20",
    accentColor: "border-green-500/30",
    highlights: ["Sustainable Farming", "Climate Science", "Food Security"]
  },
  {
    title: "Business",
    description: "Leadership & Management",
    details: "Develop strategic thinking, leadership skills, and business acumen to drive innovation in the global marketplace.",
    icon: <Building className="h-6 w-6" />,
    themeColor: "gold",
    upcoming: true,
    stats: { programs: "Coming Soon" },
    gradient: "from-yellow-500/20 to-orange-500/20",
    accentColor: "border-yellow-500/30",
    highlights: ["Strategic Management", "Entrepreneurship", "Global Business"]
  },
  {
    title: "Education",
    description: "Teaching & Learning",
    details: "Our comprehensive Ullens Center for Educator Development prepares passionate educators through innovative teaching methods and educational technology.",
    icon: <BookOpen className="h-6 w-6" />,
    themeColor: "blue-500",
    upcoming: false,
    stats: { programs: "1 Available" },
    gradient: "from-blue-500/20 to-indigo-500/20",
    accentColor: "border-blue-500/30",
    highlights: ["Ullens Center for Educator Development", "Innovative Pedagogy", "Educational Technology"]
  },
];

export default function ProgramsOverviewSection() {
  return (
    <section id="programs" className="w-full py-24 md:py-32 lg:py-40 relative overflow-hidden bg-gradient-to-b from-background via-muted/10 to-background">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Dynamic gradient backgrounds */}
        <div className="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-br from-[#f89c0e]/10 to-[#e6890c]/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 left-1/4 w-80 h-80 bg-gradient-to-br from-[#c82f48]/10 to-[#b91c1c]/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-72 h-72 bg-gradient-to-br from-[#40c0ce]/10 to-[#22d3ee]/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        
        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `
            radial-gradient(circle at 25% 25%, #c82f48 1px, transparent 1px),
            radial-gradient(circle at 75% 75%, #40c0ce 1px, transparent 1px)
          `,
          backgroundSize: '80px 80px, 120px 120px'
        }}></div>
      </div>

      <div className="px-4 md:px-6 relative z-10">
        {/* Enhanced Header Section */}
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-20">
            {/* Enhanced Badge */}
            <div className="flex items-center gap-4">
              <div className="w-16 h-[1px] bg-gradient-to-r from-transparent via-[#f89c0e] to-transparent"></div>
              <Badge className="px-6 py-3 bg-gradient-to-r from-[#f89c0e]/10 to-[#e6890c]/10 border-[#f89c0e]/30 text-[#f89c0e] hover:from-[#f89c0e]/20 hover:to-[#e6890c]/20 transition-all duration-300 backdrop-blur-sm">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Schools of Excellence
              </Badge>
              <div className="w-16 h-[1px] bg-gradient-to-l from-transparent via-[#f89c0e] to-transparent"></div>
            </div>
            
            {/* Enhanced Typography */}
            <div className="space-y-6">
              <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight">
                Discover Your Path to{" "}
                <span className="bg-gradient-to-r from-[#c82f48] via-[#40c0ce] to-[#f89c0e] bg-clip-text text-transparent">
                  Excellence
                </span>
              </h2>
              <p className="text-xl md:text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto font-light">
                Explore our interdisciplinary schools that foster{" "}
                <span className="font-medium text-[#c82f48]">deep thinking</span>,{" "}
                <span className="font-medium text-[#40c0ce]">creative problem-solving</span>, and{" "}
                <span className="font-medium text-[#f89c0e]">global perspectives</span>{" "}
                across diverse fields of study.
              </p>
            </div>
          </div>

          {/* Enhanced Programs Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6">
            {programsData.map((program, index) => (
              <ProgramOverviewCard
                key={program.title}
                title={program.title}
                description={program.description}
                details={program.details}
                icon={program.icon}
                themeColor={program.themeColor}
                upcoming={program.upcoming}
                stats={program.stats}
                gradient={program.gradient}
                accentColor={program.accentColor}
                highlights={program.highlights}
                index={index}
              />
            ))}
          </div>


        </div>
      </div>

      {/* Enhanced Scroll Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center gap-2 text-muted-foreground/60">
        <div className="text-xs uppercase tracking-wider font-medium">Continue Exploring</div>
        <div className="flex items-center gap-2">
          <div className="w-8 h-[1px] bg-gradient-to-r from-transparent via-muted-foreground/30 to-transparent"></div>
          <div className="w-2 h-2 rounded-full bg-muted-foreground/30 animate-pulse"></div>
          <div className="w-8 h-[1px] bg-gradient-to-r from-transparent via-muted-foreground/30 to-transparent"></div>
        </div>
      </div>
    </section>
  )
} 