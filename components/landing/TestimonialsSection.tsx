'use client'

import { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight, Quote } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { motion, AnimatePresence } from 'framer-motion'

const testimonials = [
  {
    quote: "Studying at Ullens College has been transformative. The faculty are not just teachers but mentors who truly care about our success.",
    author: "<PERSON><PERSON>",
    role: "Computer Science, Class of 2023",
    image: "/placeholder.svg?height=100&width=100"
  },
  {
    quote: "The hands-on learning approach and industry connections at Ullens prepared me for a successful career in sustainable agriculture.",
    author: "<PERSON><PERSON>",
    role: "Agricultural Science, Class of 2022",
    image: "/placeholder.svg?height=100&width=100"
  },
  {
    quote: "The supportive community and state-of-the-art facilities make Ullens stand out from other colleges. I've found lifelong friends and mentors here.",
    author: "<PERSON><PERSON> <PERSON>",
    role: "Business Administration, Class of 2023",
    image: "/placeholder.svg?height=100&width=100"
  },
]

export default function TestimonialsSection() {
  const [current, setCurrent] = useState(0)
  const [autoplay, setAutoplay] = useState(true)

  useEffect(() => {
    let timer: NodeJS.Timeout
    
    if (autoplay) {
      timer = setInterval(() => {
        setCurrent((prev) => (prev + 1) % testimonials.length)
      }, 5000)
    }
    
    return () => clearInterval(timer)
  }, [autoplay])

  const goToPrevious = () => {
    setAutoplay(false)
    setCurrent((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const goToNext = () => {
    setAutoplay(false)
    setCurrent((prev) => (prev + 1) % testimonials.length)
  }

  return (
    <section className="w-full py-16 md:py-24 bg-background relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden opacity-5">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full border-8 border-crimson"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 rounded-full border-8 border-gold"></div>
      </div>

      <div className="px-4 md:px-6 relative">
        <div className="text-center mb-12">
          <h2 className="heading-lg mb-4">Student Voices</h2>
          <p className="text-muted-foreground text-lg">
            Hear from our students about their experiences at Ullens College.
          </p>
        </div>

        <div className="relative">
          {/* Large quote icon */}
          <Quote className="absolute -top-6 -left-6 h-16 w-16 text-crimson/10 rotate-180" strokeWidth={1} />
          
          <div className="relative h-[300px] md:h-[250px] overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={current}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="flex flex-col md:flex-row items-center gap-8 p-6 md:p-10 rounded-2xl bg-light"
              >
                <div className="flex-shrink-0 w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden border-4 border-white shadow-lg">
                  <img 
                    src={testimonials[current].image} 
                    alt={testimonials[current].author}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex flex-col text-center md:text-left">
                  <p className="text-lg md:text-xl italic mb-4">"{testimonials[current].quote}"</p>
                  <h4 className="font-semibold text-lg">{testimonials[current].author}</h4>
                  <p className="text-sm text-muted-foreground">{testimonials[current].role}</p>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          <div className="flex justify-center mt-8 gap-2">
            {testimonials.map((_, index) => (
              <button 
                key={index}
                onClick={() => {
                  setAutoplay(false)
                  setCurrent(index)
                }}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === current ? 'bg-crimson scale-125' : 'bg-muted hover:bg-crimson/50'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              ></button>
            ))}
          </div>
          
          <div className="absolute top-1/2 -translate-y-1/2 left-0 right-0 flex justify-between pointer-events-none">
            <Button
              size="icon"
              variant="outline"
              onClick={goToPrevious}
              className="h-10 w-10 rounded-full border-border/30 bg-background/80 backdrop-blur-sm pointer-events-auto"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button
              size="icon"
              variant="outline"
              onClick={goToNext}
              className="h-10 w-10 rounded-full border-border/30 bg-background/80 backdrop-blur-sm pointer-events-auto"
              aria-label="Next testimonial"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
} 