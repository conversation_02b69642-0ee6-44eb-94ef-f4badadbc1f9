'use client'

export default function HeroSection() {
  return (
    <section className="min-h-screen relative overflow-hidden bg-gradient-to-br from-background via-muted/10 to-background">
      {/* Enhanced Artistic Background Elements */}
      <div className="absolute inset-0">
        {/* Softer, more sophisticated pattern */}
        <div className="absolute inset-0 opacity-[0.015]" style={{
          backgroundImage: `
            radial-gradient(circle at 20% 50%, #dc2626 1px, transparent 1px),
            radial-gradient(circle at 80% 20%, #2563eb 1px, transparent 1px),
            radial-gradient(circle at 40% 80%, #7c3aed 1px, transparent 1px)
          `,
          backgroundSize: '140px 140px, 100px 100px, 120px 120px'
        }}></div>
        
        {/* Gentle floating elements for depth */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-br from-blue-400/8 to-purple-400/8 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/4 w-40 h-40 bg-gradient-to-br from-crimson/6 to-amber-400/6 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-gradient-to-br from-teal-400/8 to-cyan-400/8 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 px-6 md:px-8 lg:px-12 h-full min-h-screen flex items-center">
        <div className="max-w-7xl mx-auto w-full">
          {/* Enhanced Artistic Magazine Layout with Better Spacing */}
          <div className="grid grid-cols-12 gap-8 md:gap-12 lg:gap-16 items-center min-h-[85vh]">
            
            {/* Left Column - Enhanced Editorial Artistry with Better Spacing */}
            <div className="col-span-12 lg:col-span-7 space-y-16 lg:space-y-20">
              
              {/* Enhanced Artistic Masthead with Better Spacing */}
              <div className="flex items-center gap-6 md:gap-8">
                <div className="w-16 md:w-24 h-[1px] bg-gradient-to-r from-[#c82f48]/60 to-transparent"></div>
                <div className="text-xs md:text-sm font-mono tracking-[0.25em] text-muted-foreground uppercase px-4 py-2 rounded-full bg-muted/20 backdrop-blur-sm whitespace-nowrap">
                  Est. 2025 — Pioneering Excellence
                </div>
                <div className="w-16 md:w-24 h-[1px] bg-gradient-to-l from-[#40c0ce]/60 to-transparent"></div>
              </div>

              {/* Enhanced Dramatic Typography with Better Spacing */}
              <div className="space-y-12 md:space-y-16">
                <div className="space-y-6 md:space-y-8">
                  <div className="text-base md:text-lg font-light text-muted-foreground uppercase tracking-[0.35em] opacity-80">
                    Welcome to
                  </div>
                  <h1 className="text-5xl md:text-7xl lg:text-8xl font-black leading-[0.8] tracking-tighter transform transition-all duration-1000">
                    <span className="text-[#231f20] drop-shadow-sm">ULLENS</span>
                  </h1>
                  <div className="text-3xl md:text-5xl lg:text-6xl font-light text-[#c82f48] ml-2 md:ml-4 tracking-wide drop-shadow-sm">
                    College
                  </div>
                </div>
              </div>

              {/* Enhanced Artistic Tagline with Better Visual Elements and Spacing */}
              <div className="relative py-12 md:py-16">
                <div className="absolute -left-6 md:-left-8 top-4 md:top-6 text-6xl md:text-8xl text-[#c82f48]/12 font-serif leading-none">"</div>
                <div className="relative">
                  <blockquote className="text-xl md:text-2xl lg:text-3xl font-light text-muted-foreground leading-relaxed pl-8 md:pl-12">
                    <span className="text-[#231f20] font-medium">Think Deeply.</span>{" "}
                    <span className="text-[#c82f48] font-medium">Create Boldly.</span>{" "}
                    <span className="text-[#40c0ce] font-medium">Lead Globally.</span>
                  </blockquote>
                  <div className="mt-6 md:mt-8 pl-8 md:pl-12 text-sm md:text-base text-muted-foreground/75 italic font-light">
                    Where innovation meets integrity
                  </div>
                </div>
                <div className="absolute -right-6 md:-right-8 bottom-4 md:bottom-6 text-6xl md:text-8xl text-[#40c0ce]/12 font-serif leading-none transform rotate-180">"</div>
              </div>

              {/* Enhanced Stats with Better Visual Flair and Spacing */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-[#c82f48]/4 via-[#40c0ce]/4 to-[#f89c0e]/4 rounded-3xl backdrop-blur-sm"></div>
                <div className="relative grid grid-cols-3 gap-6 md:gap-8 py-12 md:py-16 px-8 md:px-12 border border-border/20 rounded-3xl shadow-lg backdrop-blur-sm">
                  <div className="text-center group cursor-default">
                    <div className="text-3xl md:text-4xl font-black text-[#c82f48] mb-3 md:mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">4</div>
                    <div className="text-2xs md:text-xs text-muted-foreground uppercase tracking-widest font-medium">Schools of Excellence</div>
                  </div>
                  <div className="text-center group cursor-default">
                    <div className="text-3xl md:text-4xl font-black text-[#40c0ce] mb-3 md:mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">∞</div>
                    <div className="text-2xs md:text-xs text-muted-foreground uppercase tracking-widest font-medium">Boundless Futures</div>
                  </div>
                  <div className="text-center group cursor-default">
                    <div className="text-3xl md:text-4xl font-black text-[#f89c0e] mb-3 md:mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">1</div>
                    <div className="text-2xs md:text-xs text-muted-foreground uppercase tracking-widest font-medium">Unified Vision</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Enhanced Artistic Visual with Better Spacing */}
            <div className="col-span-12 lg:col-span-5">
              <div className="relative h-full flex items-center pt-8 lg:pt-0">
                {/* Enhanced Dynamic Campus Showcase */}
                <div className="relative w-full group">
                  {/* Softer Artistic Background Elements */}
                  <div className="absolute -inset-12 md:-inset-16 bg-gradient-to-br from-[#c82f48]/8 via-[#40c0ce]/8 to-[#f89c0e]/8 rounded-[4rem] md:rounded-[5rem] blur-3xl opacity-60 group-hover:opacity-80 transition-all duration-700"></div>
                  
                  {/* Enhanced Main Campus Display */}
                  <div className="relative overflow-hidden rounded-2xl md:rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 border border-white/10 backdrop-blur-sm">
                    {/* Campus Image with Better Positioning */}
                    <div className="relative h-80 md:h-96 lg:h-[28rem] overflow-hidden">
                      <img
                        src="/images/campus/campus-aerial-view.png"
                        alt="Ullens College Campus - Architectural Excellence and Innovation"
                        className="w-full h-full object-cover object-top scale-110 group-hover:scale-105 transition-transform duration-700"
                      />
                      
                      {/* Enhanced Overlay for Depth */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/5 via-transparent to-transparent"></div>
                      
                      {/* Subtle shine effect */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </div>
                  </div>
                  
                  {/* Enhanced Floating Design Elements */}
                  <div className="absolute -top-6 md:-top-8 -right-6 md:-right-8 w-24 md:w-28 h-24 md:h-28 bg-gradient-to-br from-[#f89c0e]/15 to-[#e6890c]/15 rounded-full blur-xl animate-pulse"></div>
                  <div className="absolute -bottom-8 md:-bottom-10 -left-8 md:-left-10 w-32 md:w-36 h-32 md:h-36 bg-gradient-to-br from-[#40c0ce]/12 to-[#369ca8]/12 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}