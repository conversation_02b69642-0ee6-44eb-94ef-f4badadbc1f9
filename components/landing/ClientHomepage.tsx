'use client'

import Link from "next/link"
import { <PERSON><PERSON><PERSON>, BarChart3, <PERSON><PERSON>uationCap, <PERSON><PERSON><PERSON>, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useParallaxEffect } from "@/components/parallax-effect"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"

import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import HeroSection from "@/components/landing/HeroSection"
import VisionSection from "@/components/landing/VisionSection"
import ProgramsOverviewSection from "@/components/landing/ProgramsOverviewSection"
import CallToActionSection from "@/components/landing/CallToActionSection"

import { Badge } from "@/components/ui/badge"

interface ClientHomepageProps {
  children: React.ReactNode // This will be the FacultySection
}

export default function ClientHomepage({ children }: ClientHomepageProps) {
  useParallaxEffect()

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          <HeroSection />
          <VisionSection />
          
          {/* Enhanced Tools Section with Better Spacing */}
          <section className="py-24 md:py-32 lg:py-40 relative overflow-hidden bg-gradient-to-b from-muted/15 to-background/90">
            {/* Enhanced Background Elements */}
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute right-1/4 top-1/3 w-80 h-80 bg-blue-500/6 rounded-full blur-3xl animate-pulse" />
              <div className="absolute left-1/4 top-2/3 w-96 h-96 bg-crimson/4 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
              <div className="absolute right-1/3 bottom-1/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
            </div>
            
            <div className="px-6 md:px-8 lg:px-12 relative z-10">
              <div className="mb-16 md:mb-20 lg:mb-24 text-center">
                <div className="inline-flex items-center gap-4 md:gap-6 mb-8 md:mb-12">
                  <div className="w-12 md:w-16 h-[1px] bg-gradient-to-r from-transparent to-muted-foreground/20"></div>
                  <Badge className="px-6 md:px-8 py-3 md:py-4 bg-gradient-to-r from-blue-50/60 to-purple-50/60 border-blue-200/40 text-blue-700 backdrop-blur-sm" variant="outline">Tools & Resources</Badge>
                  <div className="w-12 md:w-16 h-[1px] bg-gradient-to-l from-transparent to-muted-foreground/20"></div>
                </div>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-light mb-6 md:mb-8 tracking-tight">Explore Your Academic Journey</h2>
                <p className="text-base md:text-lg text-muted-foreground max-w-2xl lg:max-w-3xl mx-auto leading-relaxed">
                  Discover the right program for your goals and visualize your path from application to graduation.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 lg:gap-16 max-w-6xl mx-auto">
                {/* Enhanced Program Comparison Tool Card with Better Spacing */}
                <div className="bg-gradient-to-br from-background/80 to-blue-50/30 rounded-2xl md:rounded-3xl shadow-lg border border-blue-100/40 p-6 md:p-8 lg:p-10 hover:shadow-xl transition-all duration-500 group relative overflow-hidden backdrop-blur-sm">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400/60 to-purple-400/60"></div>
                  <div className="absolute -right-20 -top-20 w-40 h-40 bg-blue-500/8 rounded-full blur-3xl group-hover:bg-blue-500/12 transition-all duration-700"></div>
                  
                  <div className="flex items-center gap-4 md:gap-6 mb-6 md:mb-8">
                    <div className="h-14 md:h-16 w-14 md:w-16 rounded-xl md:rounded-2xl bg-gradient-to-br from-blue-500/10 to-purple-500/10 p-1 group-hover:scale-105 transition-all duration-300 flex items-center justify-center backdrop-blur-sm border border-blue-200/30">
                      <BarChart3 className="h-7 md:h-8 w-7 md:w-8 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg md:text-xl font-semibold group-hover:text-blue-600 transition-colors mb-2">Program Comparison Tool</h3>
                      <Badge variant="outline" className="bg-blue-50/60 text-blue-700 border-blue-200/50 backdrop-blur-sm text-xs md:text-sm">
                        <Sparkles className="h-3 w-3 mr-1" /> Interactive
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="mb-6 md:mb-8 text-sm md:text-base text-muted-foreground leading-relaxed">
                    Compare different programs side-by-side to find the perfect fit for your academic goals and interests.
                  </p>
                  
                  <div className="flex flex-wrap gap-2 md:gap-3 mb-6 md:mb-8">
                    <Badge variant="secondary" className="font-normal text-xs md:text-sm bg-blue-50/60 text-blue-700 border-blue-200/40">Compare Curriculums</Badge>
                    <Badge variant="secondary" className="font-normal text-xs md:text-sm bg-purple-50/60 text-purple-700 border-purple-200/40">Requirements</Badge>
                    <Badge variant="secondary" className="font-normal text-xs md:text-sm bg-indigo-50/60 text-indigo-700 border-indigo-200/40">Career Paths</Badge>
                  </div>
                  
                  <Link href="/programs/compare" className="block">
                    <Button className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-full py-3 md:py-4 text-sm md:text-base">
                      Compare Programs <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>
                
                {/* Enhanced Student Journey Card with Better Spacing */}
                <div className="bg-gradient-to-br from-background/80 to-rose-50/30 rounded-2xl md:rounded-3xl shadow-lg border border-rose-100/40 p-6 md:p-8 lg:p-10 hover:shadow-xl transition-all duration-500 group relative overflow-hidden backdrop-blur-sm">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-crimson/60 to-amber-400/60"></div>
                  <div className="absolute -left-20 -top-20 w-40 h-40 bg-crimson/6 rounded-full blur-3xl group-hover:bg-crimson/10 transition-all duration-700"></div>
                  
                  <div className="flex items-center gap-4 md:gap-6 mb-6 md:mb-8">
                    <div className="h-14 md:h-16 w-14 md:w-16 rounded-xl md:rounded-2xl bg-gradient-to-br from-crimson/10 to-amber-500/10 p-1 group-hover:scale-105 transition-all duration-300 flex items-center justify-center backdrop-blur-sm border border-rose-200/30">
                      <GraduationCap className="h-7 md:h-8 w-7 md:w-8 text-crimson" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg md:text-xl font-semibold group-hover:text-crimson transition-colors mb-2">Student Journey</h3>
                      <Badge variant="outline" className="bg-rose-50/60 text-crimson border-rose-200/50 backdrop-blur-sm text-xs md:text-sm">
                        <Clock className="h-3 w-3 mr-1" /> Timeline
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="mb-6 md:mb-8 text-sm md:text-base text-muted-foreground leading-relaxed">
                    Explore the student experience from application to graduation with our interactive timeline and resources.
                  </p>
                  
                  <div className="flex flex-wrap gap-2 md:gap-3 mb-6 md:mb-8">
                    <Badge variant="secondary" className="font-normal text-xs md:text-sm bg-rose-50/60 text-rose-700 border-rose-200/40">Application</Badge>
                    <Badge variant="secondary" className="font-normal text-xs md:text-sm bg-amber-50/60 text-amber-700 border-amber-200/40">Coursework</Badge>
                    <Badge variant="secondary" className="font-normal text-xs md:text-sm bg-orange-50/60 text-orange-700 border-orange-200/40">Graduation</Badge>
                  </div>
                  
                  <Link href="/student-journey" className="block">
                    <Button className="w-full bg-gradient-to-r from-crimson to-amber-500 hover:from-crimson/90 hover:to-amber-500/90 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-full py-3 md:py-4 text-sm md:text-base">
                      View Student Journey <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </section>
          
          <ProgramsOverviewSection />
          
          {children} {/* This will be the server-side FacultySection and PostsSection */}
          <CallToActionSection id="apply" />
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
}
