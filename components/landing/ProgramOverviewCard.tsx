import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON>R<PERSON>, Clock, Star, TrendingUp } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"

interface ProgramOverviewCardProps {
  title: string;
  description: string;
  details: string;
  icon: React.ReactNode;
  themeColor?: string;
  upcoming?: boolean;
  stats?: { programs: string; careers?: string };
  gradient?: string;
  accentColor?: string;
  highlights?: string[];
  index?: number;
}

type ColorMapping = {
  icon: string;
  bg: string;
  border: string;
  hover: string;
  gradient: string;
  accent: string;
  stats: string;
};

export default function ProgramOverviewCard({ 
  title, 
  description, 
  details, 
  icon, 
  themeColor = 'crimson',
  upcoming = false,
  stats,
  gradient,
  accentColor,
  highlights = [],
  index = 0
}: ProgramOverviewCardProps) {
  
  // Enhanced color mappings
  const colorMappings: Record<string, ColorMapping> = {
    'crimson': {
      icon: 'text-red-600',
      bg: 'bg-red-50/80',
      border: 'border-red-200/50',
      hover: 'hover:border-red-300',
      gradient: 'from-red-500/10 to-pink-500/10',
      accent: 'text-red-600',
      stats: 'bg-red-500/10 text-red-700'
    },
    'green-600': {
      icon: 'text-green-600',
      bg: 'bg-green-50/80',
      border: 'border-green-200/50',
      hover: 'hover:border-green-300',
      gradient: 'from-green-500/10 to-emerald-500/10',
      accent: 'text-green-600',
      stats: 'bg-green-500/10 text-green-700'
    },
    'gold': {
      icon: 'text-yellow-600',
      bg: 'bg-yellow-50/80',
      border: 'border-yellow-200/50',
      hover: 'hover:border-yellow-300',
      gradient: 'from-yellow-500/10 to-orange-500/10',
      accent: 'text-yellow-600',
      stats: 'bg-yellow-500/10 text-yellow-700'
    },
    'blue-500': {
      icon: 'text-blue-600',
      bg: 'bg-blue-50/80',
      border: 'border-blue-200/50',
      hover: 'hover:border-blue-300',
      gradient: 'from-blue-500/10 to-indigo-500/10',
      accent: 'text-blue-600',
      stats: 'bg-blue-500/10 text-blue-700'
    },
  };
  
  const currentTheme = colorMappings[themeColor] || colorMappings['crimson'];

  const getSchoolRoute = (title: string) => {
    const routeMap: { [key: string]: string } = {
      'Computer Science': '/schools/computer-science',
      'Agriculture and Climate Science': '/schools/agriculture-climate',
      'Business': '/schools/business',
      'Education': '/schools/education',
    };
    return routeMap[title] || '#';
  };

  const CardWrapper = ({ children }: { children: React.ReactNode }) => {
    return (
      <Link href={getSchoolRoute(title)} className="block group">
        {children}
      </Link>
    );
  };

  return (
    <div 
      className="h-full"
      style={{ 
        animationDelay: `${index * 150}ms`,
        animation: 'fadeInUp 0.6s ease-out forwards',
        opacity: 0
      }}
    >
      <CardWrapper>
        <Card className={`
          relative h-full overflow-hidden border-0 
          bg-gradient-to-br ${gradient || currentTheme.gradient || 'from-red-500/10 to-pink-500/10'}
          backdrop-blur-sm shadow-lg
          transition-all duration-500 ease-out
          hover:shadow-2xl hover:scale-[1.02] group-hover:-translate-y-2
          ${currentTheme.border}
          ${currentTheme.hover}
        `}>
          {/* Enhanced Background Effects */}
          <div className={`absolute inset-0 bg-gradient-to-br ${gradient || 'from-transparent to-transparent'} opacity-30`}></div>
          
          {/* Upcoming Badge */}
          {upcoming && (
            <div className="absolute top-4 right-4 z-10">
              <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white border-0 shadow-lg">
                <Clock className="h-3 w-3 mr-1" />
                Coming Soon
              </Badge>
            </div>
          )}

          <CardHeader className="relative z-10 pb-4">
            {/* Enhanced Icon Container */}
            <div className={`
              w-16 h-16 rounded-2xl flex items-center justify-center mb-6
              bg-gradient-to-br from-white/80 to-white/40 backdrop-blur-sm
              border border-white/20 shadow-lg
              group-hover:scale-110 group-hover:rotate-3
              transition-all duration-500
            `}>
                           <div className={currentTheme.icon}>
               {icon}
             </div>
            </div>
            
            {/* Title and Description */}
            <div className="space-y-3">
              <CardTitle className="text-2xl font-bold tracking-tight leading-tight">
                {title}
              </CardTitle>
              <CardDescription className="text-base font-medium text-muted-foreground/80">
                {description}
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="relative z-10 space-y-6">
            {/* Enhanced Details */}
            <p className="text-muted-foreground leading-relaxed text-sm">
              {details}
            </p>

            {/* Highlights */}
            {highlights.length > 0 && (
              <div className="space-y-2">
                <div className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Key Areas
                </div>
                <div className="flex flex-wrap gap-1">
                  {highlights.slice(0, 3).map((highlight, idx) => (
                                         <Badge
                       key={idx}
                       variant="outline"
                       className={`text-xs px-2 py-1 ${currentTheme.stats} border-0`}
                     >
                       {highlight}
                     </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Stats Section */}
            {stats && (
              <div className="pt-2">
                <div className={`
                  p-3 rounded-lg backdrop-blur-sm border border-white/20
                  ${currentTheme.bg}
                `}>
                  <div className="text-xs text-muted-foreground font-medium uppercase tracking-wider">
                    Programs
                  </div>
                  <div className={`text-lg font-bold ${currentTheme.accent}`}>
                    {stats.programs}
                  </div>
                </div>
              </div>
            )}

            {/* Action Area */}
            <div className="pt-4 border-t border-white/10">
              <div className={`
                flex items-center text-sm font-semibold transition-all duration-300
                ${currentTheme.accent}
                group-hover:gap-3
              `}>
                <span>Explore School</span>
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
              </div>
            </div>
          </CardContent>

          {/* Subtle hover overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        </Card>
      </CardWrapper>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  )
} 