import Link from 'next/link';

interface FacultyCardProps {
  id: string;
  imageUrl: string;
  altText: string;
  name: string;
  title: string;
  bio: string;
}

export default function FacultyCard({ id, imageUrl, altText, name, title, bio }: FacultyCardProps) {
  return (
    <Link href={`/faculty/${id}`} className="block group">
      <div className="group relative overflow-hidden rounded-xl bg-light shadow-md transition-all duration-300 hover:shadow-xl">
        <div className="aspect-[3/4] overflow-hidden">
          {imageUrl && (
            <img
              src={imageUrl || undefined} // Pass undefined if imageUrl is empty, <PERSON><PERSON> will omit the attribute
              alt={altText}
              className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
            />
          )}
        </div>
        <div className="absolute inset-0 bg-gradient-to-t from-dark/80 via-dark/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
        <div className="absolute bottom-0 left-0 right-0 p-6 translate-y-6 transition-transform duration-300 group-hover:translate-y-0">
          <h3 className="text-xl font-bold text-white">{name}</h3>
          <p className="text-white/90 font-medium">{title}</p>
          <p className="mt-2 text-white/80 text-sm opacity-0 transition-opacity duration-300 group-hover:opacity-100">
            {bio}
          </p>
        </div>
      </div>
    </Link>
  );
} 