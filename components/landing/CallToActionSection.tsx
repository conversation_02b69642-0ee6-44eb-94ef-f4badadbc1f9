import { Button } from "@/components/ui/button";

export default function CallToActionSection({ id }: { id?: string }) {
  return (
    <section id={id} className="w-full py-20 md:py-28 lg:py-32 bg-gradient-to-br from-crimson to-crimson/90 text-white relative overflow-hidden">
      {/* Abstract geometric shapes */}
      <div className="absolute inset-0 overflow-hidden opacity-20">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gold rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-white rounded-full blur-3xl"></div>
      </div>

      <div className="px-4 md:px-6 relative">
        <div className="flex flex-col items-center justify-center space-y-6 text-center">
          <div className="space-y-4">
            <h2 className="heading-lg text-white">Ready to Set New Standards of Excellence?</h2>
            <p className="text-white/90 text-lg leading-relaxed">
              Join a community of innovative thinkers and ethical leaders. Begin your journey toward becoming a technologically advanced graduate with a global mindset.
            </p>
          </div>
          <div className="flex justify-center pt-4">
            <Button
              variant="secondary"
              className="bg-white text-crimson hover:bg-white/90 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 relative overflow-hidden group"
            >
              <span className="relative z-10">Apply Now</span>
              <span className="absolute inset-0 bg-gold/10 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></span>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
} 