import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LazyImage } from "@/components/ui/lazy-image"
import { cn } from "@/lib/utils" // Assuming you have a cn utility for classnames
import { Lock, Building } from "lucide-react"

interface Program {
  name: string;
  slug: string;
  icon?: React.ReactNode; // Icon for the program list
  description?: string; // Optional: for simple list items
  details?: string; // Added optional details for program cards
}

interface FeaturedProgram extends Program {
  title: string; // e.g. "B.Tech in Cybersecurity"
  details: string; // Longer description for featured program
  points: { text: string; icon?: React.ReactNode }[]; // Key points/features of the program
}

interface DepartmentSectionProps {
  id: string;
  departmentName: string;
  departmentBadgeText?: string; // Optional: e.g. "School of Computer Science"
  tagline: string;
  introduction: string;
  imageUrl: string;
  imageAlt: string;
  imagePosition?: "left" | "right";
  programs?: Program[]; // For sections with a simple list of programs
  featuredProgram?: FeaturedProgram; // For sections highlighting one main program
  programCards?: Program[]; // For sections like CS with multiple cards at the bottom
  themeColor?: 'crimson' | 'green-600' | 'gold' | 'blue-500' | 'muted';
  bgColor?: string; // e.g. "bg-light", "bg-background", "bg-muted/50"
}

export default function DepartmentSection({
  id,
  departmentName,
  departmentBadgeText,
  tagline,
  introduction,
  imageUrl,
  imageAlt,
  imagePosition = "right",
  programs,
  featuredProgram,
  programCards,
  themeColor = "crimson",
  bgColor = "bg-background",
}: DepartmentSectionProps) {

  const accentColorClass = {
    'crimson': 'text-crimson',
    'green-600': 'text-green-600',
    'gold': 'text-gold',
    'blue-500': 'text-blue-500',
    'muted': 'text-muted-foreground'
  }[themeColor];

  const accentBgClass = {
    'crimson': 'bg-crimson/10',
    'green-600': 'bg-green-600/10',
    'gold': 'bg-gold/10',
    'blue-500': 'bg-blue-500/10',
    'muted': 'bg-muted'
  }[themeColor];
  
  const borderAccentClass = {
    'crimson': 'border-crimson/20 hover:bg-crimson/5',
    'green-600': 'border-green-600/20 hover:bg-green-600/5',
    'gold': 'border-gold/20 hover:bg-gold/5',
    'blue-500': 'border-blue-500/20 hover:bg-blue-500/5',
    'muted': 'border-muted-foreground/20 hover:bg-muted-foreground/5'
  }[themeColor];

  const featuredProgramAccentBg = themeColor === 'crimson' ? 'bg-crimson text-white' : accentBgClass;

  const imageOrderClass = imagePosition === 'left' ? "lg:order-1" : "lg:order-2";
  const contentOrderClass = imagePosition === 'left' ? "lg:order-2" : "lg:order-1";

  return (
    <section id={id} className={cn("w-full py-20 md:py-28 lg:py-32", bgColor)}>
      <div className="px-4 md:px-6">
        {/* Section Header */}
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className={cn("inline-flex items-center px-3 py-1 rounded-full text-sm font-medium", accentBgClass, accentColorClass)}>
            {departmentBadgeText || departmentName}
          </div>
          <div className="space-y-4">
            <h2 className="heading-lg">{tagline}</h2>
            <p className="text-muted-foreground text-lg leading-relaxed">
              {introduction}
            </p>
          </div>
        </div>

        {/* Main Content Area (Featured Program or Generic Program List) */}
        {(featuredProgram || programs) && (
          <div className="grid items-center gap-10 py-16 lg:grid-cols-2 lg:gap-16">
            {/* Image */}
            <div className={cn("flex items-center justify-center relative", imageOrderClass)}>
              <div className={cn("absolute -z-10 w-[120%] h-[120% rounded-full blur-2xl", 
                themeColor === 'crimson' && "bg-gradient-to-bl from-gold/10 via-crimson/5 to-transparent",
                themeColor === 'green-600' && "bg-gradient-to-tr from-green-600/10 via-gold/10 to-transparent",
                themeColor === 'gold' && "", // Add specific gradient for gold if needed
                themeColor === 'blue-500' && "" // Add specific gradient for blue if needed
              )}></div>
              {imageUrl && imageUrl.trim() !== "" ? (
                <LazyImage
                  src={imageUrl}
                  alt={imageAlt}
                  aspectRatio="aspect-square"
                  className="rounded-2xl shadow-lg"
                  width={imagePosition === 'left' ? 400 : 450}
                  height={imagePosition === 'left' ? 400 : 450}
                />
              ) : null}
            </div>

            {/* Text Content */}
            <div className={cn("flex flex-col justify-center space-y-6", contentOrderClass)}>
              {featuredProgram && (
                <>
                  <div className="space-y-4">
                    <div className={cn("inline-flex items-center rounded-full px-3 py-1 text-sm font-medium", featuredProgramAccentBg, themeColor === 'crimson' ? 'text-white' : accentColorClass)}>
                      Featured Program
                    </div>
                    <h3 className="heading-md">{featuredProgram.title}</h3>
                    <p className="text-muted-foreground text-lg leading-relaxed">
                      {featuredProgram.details}
                    </p>
                  </div>
                  <ul className="grid gap-3 mt-2">
                    {featuredProgram.points.map((point, index) => (
                      <li key={index} className="flex items-center gap-3">
                        <div className={cn("flex items-center justify-center w-8 h-8 rounded-full", accentBgClass)}>
                          {point.icon || <Lock className={cn("h-4 w-4", accentColorClass)} />} 
                        </div>
                        <span className="font-medium">{point.text}</span>
                      </li>
                    ))}
                  </ul>
                  <div className="pt-2">
                    <Button asChild variant="outline" className={borderAccentClass}>
                        <Link href={`/programs/${featuredProgram.slug}`}>Learn More</Link>
                    </Button>
                  </div>
                </>
              )}
              {programs && !featuredProgram && (
                <>
                  <div className="space-y-2">
                    <h3 className="text-2xl font-bold">Programs Offered</h3>
                     {/* This p tag was present in Business and Education sections, adjust if needed */}
                    <p className="text-muted-foreground">
                       Our programs combine theoretical knowledge with practical applications, preparing students for leadership roles.
                    </p>
                  </div>
                  <ul className="grid gap-2">
                    {programs.map((program, index) => (
                      <li key={index}>
                        <Link href={`/programs/${program.slug}`} className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors">
                            {program.icon || <Building className={cn("h-4 w-4", accentColorClass)} />}
                            <span>{program.name}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </>
              )}
            </div>
          </div>
        )}

        {/* Additional Program Cards (e.g., for Computer Science) */}
        {programCards && (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mt-8">
            {programCards.map((card, index) => (
              <Link key={index} href={`/programs/${card.slug}`} className="block card-hover no-underline">
                <Card className="h-full border-0 bg-light shadow-sm transition-all duration-300 hover:shadow-md">
                  <CardHeader>
                    <CardTitle className="text-xl text-foreground">{card.name}</CardTitle>
                    {card.description && <CardDescription className="text-muted-foreground/80">{card.description}</CardDescription>}
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">
                      {card.details || `Explore various aspects of ${card.name}.`}
                    </p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        )}
      </div>
    </section>
  )
} 