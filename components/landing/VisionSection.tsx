'use client'

import { Badge } from "@/components/ui/badge"
import { Globe, Lightbulb, Users, Cpu, Heart, BookOpen, Brain, Palette } from "lucide-react"

const visionElements = [
  {
    icon: <Cpu className="h-12 w-12" />,
    title: "Technologically Advanced",
    subtitle: "Innovation at Core",
    description: "Embracing cutting-edge technology to shape tomorrow's solutions",
    color: "text-blue-600",
    accentColor: "border-l-blue-400",
    bgPattern: "bg-gradient-to-br from-blue-50/60 to-cyan-50/40",
    hoverBg: "hover:from-blue-50/80 hover:to-cyan-50/60",
  },
  {
    icon: <Heart className="h-12 w-12" />,
    title: "Ethically Grounded",
    subtitle: "Moral Foundation",
    description: "Building character alongside competence in every endeavor",
    color: "text-crimson",
    accentColor: "border-l-rose-400",
    bgPattern: "bg-gradient-to-br from-red-50/60 to-pink-50/40",
    hoverBg: "hover:from-red-50/80 hover:to-pink-50/60",
  },
  {
    icon: <BookOpen className="h-12 w-12" />,
    title: "Interdisciplinary Learning",
    subtitle: "Beyond Boundaries",
    description: "Weaving knowledge across fields to create holistic understanding",
    color: "text-purple-600",
    accentColor: "border-l-purple-400",
    bgPattern: "bg-gradient-to-br from-purple-50/60 to-indigo-50/40",
    hoverBg: "hover:from-purple-50/80 hover:to-indigo-50/60",
  },
  {
    icon: <Lightbulb className="h-12 w-12" />,
    title: "Creative Problem-Solving",
    subtitle: "Innovative Thinking",
    description: "Transforming challenges into opportunities through creative insight",
    color: "text-amber-600",
    accentColor: "border-l-amber-400",
    bgPattern: "bg-gradient-to-br from-yellow-50/60 to-orange-50/40",
    hoverBg: "hover:from-yellow-50/80 hover:to-orange-50/60",
  },
  {
    icon: <Globe className="h-12 w-12" />,
    title: "Global Mindset",
    subtitle: "World Perspective",
    description: "Cultivating citizens who think locally and act globally",
    color: "text-green-600",
    accentColor: "border-l-emerald-400",
    bgPattern: "bg-gradient-to-br from-green-50/60 to-emerald-50/40",
    hoverBg: "hover:from-green-50/80 hover:to-emerald-50/60",
  },
  {
    icon: <Brain className="h-12 w-12" />,
    title: "Deep Thinking",
    subtitle: "Intellectual Depth",
    description: "Nurturing contemplative minds that question and understand",
    color: "text-indigo-600",
    accentColor: "border-l-indigo-400",
    bgPattern: "bg-gradient-to-br from-indigo-50/60 to-blue-50/40",
    hoverBg: "hover:from-indigo-50/80 hover:to-blue-50/60",
  },
]

export default function VisionSection() {
  return (
    <section id="vision" className="py-24 md:py-32 lg:py-40 bg-gradient-to-b from-background/80 to-muted/15 relative overflow-hidden scroll-mt-20">
      {/* Enhanced Subtle Background Pattern */}
      <div className="absolute inset-0 opacity-[0.012]">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25px 25px, #dc2626 1px, transparent 0), 
                           radial-gradient(circle at 75px 75px, #2563eb 1px, transparent 0)`,
          backgroundSize: '120px 120px'
        }}></div>
      </div>

      {/* Gentle floating ambient elements */}
      <div className="absolute top-20 left-16 w-40 h-40 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-20 right-16 w-48 h-48 bg-gradient-to-br from-emerald-200/15 to-teal-200/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      <div className="absolute top-1/2 left-1/4 w-32 h-32 bg-gradient-to-br from-rose-200/18 to-pink-200/18 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>

      <div className="px-6 md:px-8 lg:px-12 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Enhanced Artistic Header with Better Spacing */}
          <div className="text-center mb-20 md:mb-28 lg:mb-32">
            <div className="inline-flex items-center gap-4 md:gap-6 mb-8 md:mb-12">
              <div className="w-12 md:w-16 h-[1px] bg-gradient-to-r from-transparent to-muted-foreground/25"></div>
              <Badge variant="outline" className="px-6 md:px-8 py-3 md:py-4 font-medium tracking-wide border-purple-200/50 text-purple-700 bg-purple-50/40 backdrop-blur-sm">
                <Palette className="h-3 w-3 mr-2" />
                Our Vision
              </Badge>
              <div className="w-12 md:w-16 h-[1px] bg-gradient-to-l from-transparent to-muted-foreground/25"></div>
            </div>
            
            <h2 className="text-4xl md:text-6xl lg:text-7xl font-light mb-8 md:mb-12 lg:mb-16 tracking-tight">
              Setting Standards of{" "}
              <span className="font-bold bg-gradient-to-r from-crimson via-blue-600 to-purple-600 bg-clip-text text-transparent drop-shadow-sm">
                Excellence
              </span>
            </h2>
            
            <div className="max-w-4xl lg:max-w-5xl mx-auto space-y-6 md:space-y-8">
              <p className="text-lg md:text-xl lg:text-2xl text-muted-foreground leading-relaxed font-light">
                Ullens College aims to set standards of excellence by nurturing{" "}
                <em className="text-blue-600 not-italic font-medium">technologically advanced</em>{" "}
                and{" "}
                <em className="text-crimson not-italic font-medium">ethically grounded</em>{" "}
                graduates
              </p>
              <p className="text-base md:text-lg text-muted-foreground/85 leading-relaxed">
                who embrace{" "}
                <em className="text-purple-600 not-italic font-medium">interdisciplinary learning</em>
                , think deeply, solve problems creatively, and act with a{" "}
                <em className="text-green-600 not-italic font-medium">global mindset</em>{" "}
                in a rapidly changing world.
              </p>
            </div>
          </div>

          {/* Enhanced Artistic Vision Layout with Better Spacing */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 lg:gap-16 mb-20 md:mb-28 lg:mb-32">
            {/* Left Column - Enhanced Asymmetric Layout with Better Spacing */}
            <div className="space-y-6 md:space-y-8">
              {visionElements.slice(0, 3).map((element, index) => (
                <div
                  key={index}
                  className={`group ${element.bgPattern} ${element.hoverBg} rounded-2xl md:rounded-3xl p-6 md:p-8 lg:p-10 border-l-4 ${element.accentColor} shadow-sm hover:shadow-lg transition-all duration-500 transform hover:-translate-y-1 backdrop-blur-sm border border-white/20`}
                  style={{ 
                    marginLeft: `${index * 1}rem`,
                    animationDelay: `${index * 0.1}s`
                  }}
                >
                  <div className="flex items-start gap-4 md:gap-6">
                    <div className={`${element.color} opacity-80 flex-shrink-0 transform group-hover:scale-110 transition-all duration-300`}>
                      {element.icon}
                    </div>
                    <div className="flex-1">
                      <div className="mb-3 md:mb-4">
                        <h3 className="text-lg md:text-xl font-bold text-foreground leading-tight group-hover:text-opacity-90 transition-colors">
                          {element.title}
                        </h3>
                        <p className={`text-xs md:text-sm ${element.color} font-medium opacity-75 mt-1 md:mt-2`}>
                          {element.subtitle}
                        </p>
                      </div>
                      <p className="text-sm md:text-base text-muted-foreground leading-relaxed">
                        {element.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Right Column - Enhanced Offset Layout with Better Spacing */}
            <div className="space-y-6 md:space-y-8 lg:mt-8">
              {visionElements.slice(3).map((element, index) => (
                <div
                  key={index + 3}
                  className={`group ${element.bgPattern} ${element.hoverBg} rounded-2xl md:rounded-3xl p-6 md:p-8 lg:p-10 border-l-4 ${element.accentColor} shadow-sm hover:shadow-lg transition-all duration-500 transform hover:-translate-y-1 backdrop-blur-sm border border-white/20`}
                  style={{ 
                    marginRight: `${index * 1}rem`,
                    animationDelay: `${(index + 3) * 0.1}s`
                  }}
                >
                  <div className="flex items-start gap-4 md:gap-6">
                    <div className={`${element.color} opacity-80 flex-shrink-0 transform group-hover:scale-110 transition-all duration-300`}>
                      {element.icon}
                    </div>
                    <div className="flex-1">
                      <div className="mb-3 md:mb-4">
                        <h3 className="text-lg md:text-xl font-bold text-foreground leading-tight group-hover:text-opacity-90 transition-colors">
                          {element.title}
                        </h3>
                        <p className={`text-xs md:text-sm ${element.color} font-medium opacity-75 mt-1 md:mt-2`}>
                          {element.subtitle}
                        </p>
                      </div>
                      <p className="text-sm md:text-base text-muted-foreground leading-relaxed">
                        {element.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Artistic Bottom Section with Better Spacing */}
          <div className="text-center">
            <div className="max-w-3xl lg:max-w-4xl mx-auto">
              <div className="relative bg-gradient-to-br from-background/60 to-muted/20 rounded-2xl md:rounded-3xl p-8 md:p-12 lg:p-16 border border-border/20 backdrop-blur-sm shadow-lg">
                {/* Enhanced Decorative Quote Marks */}
                <div className="absolute -top-1 md:-top-2 -left-1 md:-left-2 text-4xl md:text-5xl text-muted-foreground/15 font-serif">"</div>
                <div className="absolute -bottom-4 md:-bottom-6 -right-1 md:-right-2 text-4xl md:text-5xl text-muted-foreground/15 font-serif">"</div>
                
                <blockquote className="text-xl md:text-2xl lg:text-3xl font-light text-muted-foreground italic leading-relaxed px-4 md:px-6">
                  Shaping the leaders of tomorrow through excellence in education, 
                  innovation in thinking, and integrity in action.
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 