'use client'

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { ArrowRight } from "lucide-react"

// Placeholder data - in a real app, this would come from a CMS or database
const recentPosts = [
  {
    id: "1",
    slug: "welcome-to-our-new-blog",
    title: "Welcome to Our New Blog!",
    date: "October 26, 2023",
    excerpt: "We're excited to launch our new blog. Stay tuned for updates, insights, and stories from our vibrant community.",
    category: "College News",
  },
  {
    id: "2",
    slug: "annual-tech-symposium-2023",
    title: "Highlights from the Annual Tech Symposium 2023",
    date: "October 24, 2023",
    excerpt: "Our annual Tech Symposium was a massive success, featuring groundbreaking talks and innovative student projects...",
    category: "Events",
  },
  {
    id: "3",
    slug: "faculty-spotlight-dr-jane-doe",
    title: "Faculty Spotlight: Dr. <PERSON> on AI Ethics",
    date: "October 20, 2023",
    excerpt: "Dr. <PERSON> from our Computer Science department shares her valuable insights on the future of AI ethics.",
    category: "Faculty Insights",
  },
]

export default function PostsSection() {
  return (
    <section id="news-blog" className="py-12 md:py-20 bg-muted/30">
      <div className="px-4 md:px-6">
        <div className="mb-10 text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-primary">
            Latest News & Insights
          </h2>
          <p className="mt-3 text-lg text-muted-foreground">
            Stay updated with the latest happenings, research, and stories from our college.
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {recentPosts.map((post) => (
            <Card key={post.id} className="flex flex-col overflow-hidden rounded-lg shadow-lg transition-transform duration-300 hover:scale-105">
              <CardHeader>
                <span className="text-xs uppercase tracking-wider text-muted-foreground">{post.category}</span>
                <CardTitle className="mt-1">
                  <Link href={`/posts/${post.slug}`} className="hover:text-primary transition-colors">
                    {post.title}
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-grow">
                <p className="text-sm text-muted-foreground">{post.excerpt}</p>
              </CardContent>
              <CardFooter className="flex justify-between items-center">
                <span className="text-xs text-muted-foreground">{post.date}</span>
                <Link href={`/posts/${post.slug}`} className="text-sm font-medium text-primary hover:underline">
                  Read More <ArrowRight className="inline h-4 w-4" />
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
        <div className="mt-12 text-center">
          <Button asChild variant="outline" size="lg">
            <Link href="/posts">
              View All Posts <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
} 