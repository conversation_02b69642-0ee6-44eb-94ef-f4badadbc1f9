import { prisma } from "@/lib/prisma"
import { PostStatus } from "@prisma/client"
import PostsSectionClient from "./PostsSectionClient"

async function getRecentPosts() {
  const posts = await prisma.post.findMany({
    where: { status: PostStatus.PUBLISHED },
    include: {
      author: {
        select: {
          name: true,
          email: true
        }
      },
      category: {
        select: {
          name: true,
          slug: true,
          color: true
        }
      }
    },
    orderBy: {
      publishedAt: 'desc'
    },
    take: 3
  })

  return posts
}

export default async function PostsSectionServer() {
  const recentPosts = await getRecentPosts()

  return <PostsSectionClient posts={recentPosts} />
}
