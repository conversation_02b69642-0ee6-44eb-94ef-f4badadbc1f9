'use client'

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight } from "lucide-react"

interface Post {
  id: string
  title: string
  slug: string
  excerpt?: string | null
  publishedAt?: Date | null
  author: {
    name: string | null
    email: string
  }
  category: {
    name: string
    slug: string
    color?: string | null
  }
}

interface PostsSectionClientProps {
  posts: Post[]
}

function formatDate(date: Date | null) {
  if (!date) return 'Not published'
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date))
}

export default function PostsSectionClient({ posts }: PostsSectionClientProps) {
  return (
    <section id="news-blog" className="py-12 md:py-20 bg-muted/30">
      <div className="px-4 md:px-6">
        <div className="mb-10 text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-primary">
            Latest News & Insights
          </h2>
          <p className="mt-3 text-lg text-muted-foreground">
            Stay updated with the latest happenings, research, and stories from our college.
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {posts.map((post) => (
            <Card key={post.id} className="flex flex-col overflow-hidden rounded-lg shadow-lg transition-transform duration-300 hover:scale-105">
              <CardHeader>
                <Badge 
                  variant="outline" 
                  className="text-xs uppercase tracking-wider w-fit"
                  style={{ 
                    backgroundColor: post.category.color || '#e5e7eb',
                    color: '#374151'
                  }}
                >
                  {post.category.name}
                </Badge>
                <CardTitle className="mt-1">
                  <Link href={`/posts/${post.slug}`} className="hover:text-primary transition-colors">
                    {post.title}
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-grow">
                <p className="text-sm text-muted-foreground">{post.excerpt}</p>
              </CardContent>
              <CardFooter className="flex justify-between items-center">
                <span className="text-xs text-muted-foreground">
                  {formatDate(post.publishedAt)}
                </span>
                <Link href={`/posts/${post.slug}`} className="text-sm font-medium text-primary hover:underline">
                  Read More <ArrowRight className="inline h-4 w-4" />
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        {posts.length === 0 && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No posts available yet.</p>
          </div>
        )}
        
        <div className="mt-12 text-center">
          <Button asChild variant="outline" size="lg">
            <Link href="/posts">
              View All Posts <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
