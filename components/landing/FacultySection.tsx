import FacultyCard from "./FacultyCard";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Users, BookOpen, Award, GraduationCap, Sparkles, Star, ChevronRight } from "lucide-react";
import Link from "next/link";
import { prisma } from "@/lib/prisma";

async function getFeaturedFaculty() {
  try {
    // Use the same approach as the main faculty page - query facultyProfile instead of user
    const facultyProfiles = await prisma.facultyProfile.findMany({
      where: {
        user: {
          status: 'ACTIVE'
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            profile: {
              select: {
                avatarUrl: true
              }
            }
          }
        },
        department: {
          select: {
            name: true,
            slug: true
          }
        },
        researchAreas: {
          select: {
            areaName: true
          }
        },
        publications: {
          select: {
            id: true
          }
        }
      },
      take: 4, // Show only 4 faculty on homepage
      orderBy: {
        user: {
          createdAt: 'asc' // Show the first faculty members added
        }
      }
    });

    return facultyProfiles.map(faculty => ({
      id: faculty.user.id,
      imageUrl: faculty.user.profile?.avatarUrl || faculty.user.image || "/images/faculty/default-avatar.svg",
      altText: faculty.user.name || "Faculty Member",
      name: faculty.user.name || "Faculty Member",
      title: faculty.title || "Faculty Member",
      bio: faculty.bio || "Dedicated educator and researcher.",
      department: faculty.department.name,
      departmentSlug: faculty.department.slug,
      researchAreas: faculty.researchAreas.map(area => area.areaName).slice(0, 3),
      publicationsCount: faculty.publications.length
    }));
  } catch (error) {
    console.error('Error fetching featured faculty:', error);
    // Fallback to empty array if database fails
    return [];
  }
}

export default async function FacultySection() {
  const facultyData = await getFeaturedFaculty();

  // If no faculty data, show a placeholder message
  if (facultyData.length === 0) {
    return (
      <section id="faculty" className="w-full py-24 md:py-32 lg:py-40 relative overflow-hidden bg-gradient-to-b from-background via-muted/5 to-background">
        <div className="px-4 md:px-6 relative">
          <div className="flex flex-col items-center justify-center space-y-6 text-center">
            <div className="flex items-center gap-4">
              <div className="w-16 h-[1px] bg-gradient-to-r from-transparent via-[#c82f48] to-transparent"></div>
              <div className="px-6 py-3 bg-gradient-to-r from-[#c82f48]/20 to-pink-500/20 border border-[#c82f48]/50 rounded-full backdrop-blur-sm font-semibold text-[#c82f48] flex items-center hover:from-[#c82f48]/30 hover:to-pink-500/30 transition-all duration-300">
                <Users className="h-4 w-4 mr-2 text-[#c82f48]" />
                <span className="text-[#c82f48] font-semibold">Our Distinguished Faculty</span>
              </div>
              <div className="w-16 h-[1px] bg-gradient-to-l from-transparent via-[#c82f48] to-transparent"></div>
            </div>
            <div className="space-y-6">
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight">
                Meet Our <span className="bg-gradient-to-r from-crimson via-pink-500 to-purple-500 bg-clip-text text-transparent">Faculty</span>
              </h2>
              <p className="text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto">
                Our faculty profiles are being updated. Please check back soon to meet our distinguished educators.
              </p>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section id="faculty" className="w-full py-24 md:py-32 lg:py-40 relative overflow-hidden bg-gradient-to-b from-background via-muted/5 to-background">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Dynamic gradient backgrounds */}
        <div className="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-br from-crimson/10 to-pink-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 left-1/4 w-80 h-80 bg-gradient-to-br from-purple-500/10 to-indigo-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-72 h-72 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        
        {/* Floating academic elements */}
        <div className="absolute top-16 left-16 text-crimson/10 animate-float">
          <GraduationCap className="h-8 w-8" />
        </div>
        <div className="absolute top-32 right-20 text-blue-500/10 animate-float" style={{ animationDelay: '1s' }}>
          <BookOpen className="h-6 w-6" />
        </div>
        <div className="absolute bottom-20 right-16 text-purple-500/10 animate-float" style={{ animationDelay: '2s' }}>
          <Award className="h-7 w-7" />
        </div>
        
        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `
            radial-gradient(circle at 25% 25%, #c82f48 1px, transparent 1px),
            radial-gradient(circle at 75% 75%, #7c3aed 1px, transparent 1px)
          `,
          backgroundSize: '60px 60px, 80px 80px'
        }}></div>
      </div>

      <div className="px-4 md:px-6 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Enhanced Header Section */}
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-20">
            <div className="flex items-center gap-4">
              <div className="w-16 h-[1px] bg-gradient-to-r from-transparent via-[#c82f48] to-transparent"></div>
              <div className="px-6 py-3 bg-gradient-to-r from-[#c82f48]/20 to-pink-500/20 border border-[#c82f48]/50 rounded-full backdrop-blur-sm font-semibold text-[#c82f48] flex items-center hover:from-[#c82f48]/30 hover:to-pink-500/30 transition-all duration-300">
                <Users className="h-4 w-4 mr-2 text-[#c82f48]" />
                <span className="text-[#c82f48] font-semibold">Our Distinguished Faculty</span>
              </div>
              <div className="w-16 h-[1px] bg-gradient-to-l from-transparent via-[#c82f48] to-transparent"></div>
            </div>
            
            <div className="space-y-6">
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight">
                Meet Our <span className="bg-gradient-to-r from-crimson via-pink-500 to-purple-500 bg-clip-text text-transparent">Faculty</span>
              </h2>
              <p className="text-xl md:text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto font-light">
                Our distinguished faculty members are{" "}
                <span className="font-medium text-crimson">leaders in their fields</span>,{" "}
                dedicated to{" "}
                <span className="font-medium text-purple-600">excellence in teaching</span>{" "}
                and{" "}
                <span className="font-medium text-blue-600">groundbreaking research</span>.
              </p>
            </div>
          </div>

          {/* Enhanced Faculty Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6 mb-16">
            {facultyData.map((faculty, index) => (
              <div key={faculty.id} className="group" style={{ animationDelay: `${index * 100}ms` }}>
                <Link href={`/faculty/${faculty.id}`} className="block">
                  <div className="relative overflow-hidden rounded-2xl bg-background/50 backdrop-blur-sm border border-border/50 shadow-lg transition-all duration-500 hover:shadow-2xl hover:shadow-crimson/10 hover:border-crimson/30 hover:-translate-y-2">
                    {/* Faculty Image */}
                    <div className="aspect-[3/4] overflow-hidden relative">
                      <img
                        src={faculty.imageUrl}
                        alt={faculty.altText}
                        className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-110"
                      />
                      {/* Overlay Gradient */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      
                      {/* Floating Badge */}
                      <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                        <Badge className="bg-crimson/90 text-white border-0 backdrop-blur-sm">
                          <Star className="h-3 w-3 mr-1" />
                          Featured
                        </Badge>
                      </div>
                    </div>

                    {/* Faculty Info */}
                    <div className="p-6 space-y-4">
                      <div>
                        <h3 className="text-xl font-bold text-foreground group-hover:text-crimson transition-colors duration-300 leading-tight">
                          {faculty.name}
                        </h3>
                        <p className="text-muted-foreground font-medium mt-1">{faculty.title}</p>
                        <p className="text-sm text-muted-foreground/80 mt-1">{faculty.department}</p>
                      </div>

                      {/* Research Areas */}
                      {faculty.researchAreas && faculty.researchAreas.length > 0 && (
                        <div className="space-y-2">
                          <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Research Areas</div>
                          <div className="flex flex-wrap gap-1">
                            {faculty.researchAreas.map((area, idx) => (
                              <Badge 
                                key={idx} 
                                variant="outline" 
                                className="text-xs px-2 py-1 border-border/50 text-muted-foreground hover:border-crimson/50 hover:text-crimson transition-colors"
                              >
                                {area}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Publications Count */}
                      {faculty.publicationsCount > 0 && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <BookOpen className="h-4 w-4" />
                          <span>{faculty.publicationsCount} Publications</span>
                        </div>
                      )}

                      {/* Bio Preview */}
                      <p className="text-sm text-muted-foreground leading-relaxed line-clamp-3">
                        {faculty.bio}
                      </p>

                      {/* Learn More Link */}
                      <div className="flex items-center gap-2 text-crimson font-medium text-sm group-hover:gap-3 transition-all duration-300">
                        <span>Learn More</span>
                        <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>

          {/* Enhanced CTA Section */}
          <div className="text-center">
            <div className="inline-flex flex-col items-center gap-6 p-8 bg-gradient-to-br from-background/80 via-muted/20 to-background/80 backdrop-blur-sm rounded-3xl border border-border/30 shadow-xl">
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-lg font-semibold text-foreground">
                  <Sparkles className="h-5 w-5 text-crimson" />
                  <span>Discover Our Complete Faculty</span>
                  <Sparkles className="h-5 w-5 text-crimson" />
                </div>
                <p className="text-muted-foreground max-w-md">
                  Explore detailed profiles, research interests, and academic achievements of all our distinguished faculty members.
                </p>
              </div>
              <Link href="/faculty">
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-crimson to-pink-500 hover:from-crimson/90 hover:to-pink-500/90 shadow-lg hover:shadow-xl hover:shadow-crimson/25 transition-all duration-300 hover:scale-105 border-0"
                >
                  <Users className="mr-2 h-5 w-5" />
                  Meet All Faculty
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Scroll Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center gap-2 text-muted-foreground/60">
        <div className="text-xs uppercase tracking-wider font-medium">Explore More</div>
        <div className="flex items-center gap-2">
          <div className="w-8 h-[1px] bg-gradient-to-r from-transparent via-muted-foreground/30 to-transparent"></div>
          <div className="w-2 h-2 rounded-full bg-muted-foreground/30 animate-pulse"></div>
          <div className="w-8 h-[1px] bg-gradient-to-r from-transparent via-muted-foreground/30 to-transparent"></div>
        </div>
      </div>
    </section>
  );
}