'use client'

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { SessionProvider } from './SessionProvider'
import { ErrorHandlerWrapper } from './ErrorHandlerWrapper'

interface ConditionalProvidersProps {
  children: React.ReactNode
}

// Routes that are completely public and don't need any NextAuth functionality
const FULLY_PUBLIC_ROUTES = [
  '/',
  '/posts',
  '/about',
  '/programs',
  '/faculty',
  '/research',
  '/partnerships',
  '/values',
  '/schools',
  '/unauthorized'
]

export function ConditionalProviders({ children }: ConditionalProvidersProps) {
  const [isClient, setIsClient] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Prevent hydration issues by only rendering on client
  if (!isClient) {
    return <>{children}</>
  }

  // Check if current route is fully public
  const isFullyPublicRoute = FULLY_PUBLIC_ROUTES.some(route => 
    pathname === route || 
    pathname.startsWith(route + '/') ||
    pathname.startsWith('/posts/')
  )

  // For fully public routes, don't load any NextAuth-related providers
  if (isFullyPublicRoute) {
    return <>{children}</>
  }

  // For protected routes, load all authentication providers
  return (
    <ErrorHandlerWrapper>
      <SessionProvider>
        {children}
      </SessionProvider>
    </ErrorHandlerWrapper>
  )
}
