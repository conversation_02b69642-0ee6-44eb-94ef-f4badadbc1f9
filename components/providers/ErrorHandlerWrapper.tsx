'use client'

import { useNextAuthErrorHandler } from '@/hooks/useNextAuthErrorHandler'
import { usePathname } from 'next/navigation'

interface ErrorHandlerWrapperProps {
  children: React.ReactNode
}

// Routes that don't need NextAuth error handling
const PUBLIC_ROUTES = [
  '/',
  '/posts',
  '/about',
  '/programs',
  '/faculty',
  '/research',
  '/partnerships',
  '/values',
  '/schools',
  '/unauthorized'
]

export function ErrorHandlerWrapper({ children }: ErrorHandlerWrapperProps) {
  const pathname = usePathname()

  // Check if current route needs NextAuth error handling
  const isPublicRoute = PUBLIC_ROUTES.some(route =>
    pathname === route ||
    pathname.startsWith(route + '/') ||
    pathname.startsWith('/posts/')
  )

  // Only initialize the NextAuth error handler for routes that need authentication
  if (!isPublicRoute) {
    useNextAuthErrorHandler()
  }

  return <>{children}</>
}
