"use client"

import { Session<PERSON>rovider as NextAuthSessionProvider } from "next-auth/react"
import { useState, useEffect } from "react"
import { AuthErrorBoundary } from "./AuthErrorBoundary"

export function SessionProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Prevent hydration issues by only rendering SessionProvider on client
  if (!isClient) {
    return <>{children}</>
  }

  return (
    <AuthErrorBoundary>
      <NextAuthSessionProvider
        // Add configuration to handle fetch errors gracefully
        refetchInterval={0} // Disable automatic refetching
        refetchOnWindowFocus={false} // Disable refetch on window focus
        refetchWhenOffline={false} // Disable refetch when offline
      >
        {children}
      </NextAuthSessionProvider>
    </AuthErrorBoundary>
  )
}
