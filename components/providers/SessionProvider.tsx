"use client"

import { Session<PERSON>rovider as NextAuthSessionProvider } from "next-auth/react"
import { useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import { AuthErrorBoundary } from "./AuthErrorBoundary"

// Routes that don't need session management
const PUBLIC_ROUTES = [
  '/',
  '/posts',
  '/about',
  '/programs',
  '/faculty',
  '/research',
  '/partnerships',
  '/values',
  '/schools',
  '/unauthorized'
]

export function SessionProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const [isClient, setIsClient] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Prevent hydration issues by only rendering SessionProvider on client
  if (!isClient) {
    return <>{children}</>
  }

  // Check if current route needs session management
  const isPublicRoute = PUBLIC_ROUTES.some(route =>
    pathname === route ||
    pathname.startsWith(route + '/') ||
    pathname.startsWith('/posts/')
  )

  // For public routes, don't wrap with Session<PERSON>rovider to avoid unnecessary auth calls
  if (isPublicRoute) {
    return <>{children}</>
  }

  return (
    <AuthErrorBoundary>
      <NextAuthSessionProvider
        // Add configuration to handle fetch errors gracefully
        refetchInterval={0} // Disable automatic refetching
        refetchOnWindowFocus={false} // Disable refetch on window focus
        refetchWhenOffline={false} // Disable refetch when offline
      >
        {children}
      </NextAuthSessionProvider>
    </AuthErrorBoundary>
  )
}
