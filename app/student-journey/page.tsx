import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { StudentJourneyTimeline } from '@/components/ui/student-journey-timeline'
import { Button } from '@/components/ui/button'
import { ArrowRight, BookOpen, GraduationCap, Users, Calendar, Sparkles, Clock, Target, ChevronDown } from 'lucide-react'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'


export const metadata: Metadata = {
  title: 'Student Journey | Ullens College',
  description: 'Discover the journey of students at Ullens College from application to graduation',
}

export default function JourneyPage() {
  return (
    <>
      <div className="flex min-h-[100dvh] flex-col">
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section */}
          <section className="relative bg-gradient-to-b from-background via-muted/20 to-background py-20 md:py-28 overflow-hidden">
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute right-0 top-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-[100px]" />
              <div className="absolute left-0 bottom-1/4 w-96 h-96 bg-crimson/10 rounded-full blur-[100px]" />
              <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 bg-purple-500/10 rounded-full blur-[80px]" />
            </div>
            
            <div className="container px-4 md:px-6 relative z-10">
              <div className="max-w-4xl mx-auto text-center">
                <Badge className="mb-4 bg-crimson/10 text-crimson hover:bg-crimson/20 transition-colors">
                  <Sparkles className="h-3.5 w-3.5 mr-1" /> Student Experience
                </Badge>
                
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                  Your <span className="bg-gradient-to-r from-crimson to-blue-600 bg-clip-text text-transparent">Journey</span> at Ullens
                </h1>
                
                <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
                  From application to graduation, we're committed to your success every step of the way. Discover what your educational journey looks like at Ullens College.
                </p>
                
                <div className="flex flex-wrap gap-4 justify-center">
                  <Button size="lg" className="shadow-lg hover:shadow-xl transition-all" asChild>
                    <Link href="#timeline" className="gap-2">
                      Explore Your Journey <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button variant="outline" size="lg" className="backdrop-blur-sm bg-background/50" asChild>
                    <Link href="/programs/compare" className="gap-2">
                      Compare Programs <ChevronDown className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
                
                {/* Animated scroll indicator */}
                <div className="hidden md:flex justify-center mt-16">
                  <a 
                    href="#timeline" 
                    className="flex flex-col items-center animate-bounce opacity-70 hover:opacity-100 transition-opacity"
                    aria-label="Scroll down to timeline"
                  >
                    <span className="text-sm text-muted-foreground mb-2">Scroll</span>
                    <div className="w-6 h-10 border-2 border-muted-foreground rounded-full flex justify-center p-1">
                      <div className="w-1.5 h-2.5 bg-muted-foreground rounded-full animate-scroll"></div>
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </section>
          
          {/* Journey Stats */}
          <section className="py-12 md:py-16 bg-background">
            <div className="container px-4 md:px-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-10 max-w-5xl mx-auto">
                <div className="relative group flex flex-col items-center bg-gradient-to-br from-blue-50/50 to-transparent p-8 rounded-xl shadow-md border border-border/40 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                  <div className="absolute inset-0 bg-blue-500/5 opacity-0 group-hover:opacity-100 rounded-xl transition-opacity duration-300"></div>
                  <div className="h-16 w-16 rounded-2xl bg-blue-500/15 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <BookOpen className="h-8 w-8 text-blue-500" />
                  </div>
                  <h3 className="text-3xl font-bold mb-2 group-hover:text-blue-600 transition-colors">25+</h3>
                  <p className="text-muted-foreground text-center">Programs across diverse disciplines</p>
                </div>
                
                <div className="relative group flex flex-col items-center bg-gradient-to-br from-purple-50/50 to-transparent p-8 rounded-xl shadow-md border border-border/40 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                  <div className="absolute inset-0 bg-purple-500/5 opacity-0 group-hover:opacity-100 rounded-xl transition-opacity duration-300"></div>
                  <div className="h-16 w-16 rounded-2xl bg-purple-500/15 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Users className="h-8 w-8 text-purple-500" />
                  </div>
                  <h3 className="text-3xl font-bold mb-2 group-hover:text-purple-600 transition-colors">90%</h3>
                  <p className="text-muted-foreground text-center">Of students receive hands-on experience</p>
                </div>
                
                <div className="relative group flex flex-col items-center bg-gradient-to-br from-crimson/5 to-transparent p-8 rounded-xl shadow-md border border-border/40 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                  <div className="absolute inset-0 bg-crimson/5 opacity-0 group-hover:opacity-100 rounded-xl transition-opacity duration-300"></div>
                  <div className="h-16 w-16 rounded-2xl bg-crimson/15 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <GraduationCap className="h-8 w-8 text-crimson" />
                  </div>
                  <h3 className="text-3xl font-bold mb-2 group-hover:text-crimson transition-colors">95%</h3>
                  <p className="text-muted-foreground text-center">Graduate employment or further education rate</p>
                </div>
              </div>
            </div>
          </section>

          {/* Key Milestones */}
          <section className="py-16 bg-muted/20">
            <div className="container px-4 md:px-6">
              <div className="max-w-5xl mx-auto">
                <div className="flex flex-col items-center text-center mb-12">
                  <Badge className="mb-3 bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 transition-colors">
                    <Clock className="h-3.5 w-3.5 mr-1" /> Key Milestones
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">Your Academic Timeline</h2>
                  <p className="text-muted-foreground text-lg max-w-2xl">
                    Ullens College organizes the academic journey into key milestones to help you stay on track and make the most of your college experience.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                  <div className="bg-background rounded-xl p-5 shadow-sm border border-border/40 flex flex-col items-center text-center">
                    <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold mb-3">1</div>
                    <h3 className="font-bold mb-1">Application</h3>
                    <p className="text-sm text-muted-foreground">Begin your journey by applying to your chosen program</p>
                  </div>
                  
                  <div className="bg-background rounded-xl p-5 shadow-sm border border-border/40 flex flex-col items-center text-center">
                    <div className="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center text-white font-bold mb-3">2</div>
                    <h3 className="font-bold mb-1">First Year</h3>
                    <p className="text-sm text-muted-foreground">Build foundational knowledge and explore your interests</p>
                  </div>
                  
                  <div className="bg-background rounded-xl p-5 shadow-sm border border-border/40 flex flex-col items-center text-center">
                    <div className="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center text-white font-bold mb-3">3</div>
                    <h3 className="font-bold mb-1">Mid-Program</h3>
                    <p className="text-sm text-muted-foreground">Dive deeper into specialized courses and gain practical experience</p>
                  </div>
                  
                  <div className="bg-background rounded-xl p-5 shadow-sm border border-border/40 flex flex-col items-center text-center">
                    <div className="h-10 w-10 rounded-full bg-crimson flex items-center justify-center text-white font-bold mb-3">4</div>
                    <h3 className="font-bold mb-1">Graduation</h3>
                    <p className="text-sm text-muted-foreground">Complete your degree and prepare for your next chapter</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Main Timeline */}
          <section id="timeline" className="py-20 md:py-24 scroll-mt-20 bg-background">
            <div className="container px-4 md:px-6">
              <div className="max-w-5xl mx-auto">
                <div className="mb-16 text-center">
                  <Badge className="mb-3 bg-crimson/10 text-crimson hover:bg-crimson/20 transition-colors">
                    <Target className="h-3.5 w-3.5 mr-1" /> Interactive Timeline
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">The Student Experience</h2>
                  <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                    Our students go through a transformative journey that prepares them for success in their chosen field and personal growth. Explore each stage to learn more about what you can expect.
                  </p>
                </div>
                
                <StudentJourneyTimeline />
                
                <div className="mt-20 bg-gradient-to-br from-background to-blue-50/50 backdrop-blur-sm rounded-2xl border shadow-md p-10 text-center">
                  <Badge className="mb-3">Ready to Begin?</Badge>
                  <h3 className="text-2xl md:text-3xl font-bold mb-4">Start Your Journey at Ullens College</h3>
                  <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
                    Take the first step toward your future at Ullens College. Our admissions team is here to help you navigate the application process and answer any questions you may have.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button size="lg" className="shadow-md" asChild>
                      <Link href="/apply" className="gap-2">
                        Apply Now <ArrowRight className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button variant="outline" size="lg" asChild>
                      <Link href="/contact" className="gap-2">
                        Contact Admissions <Calendar className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </section>
              </main>
      <Footer />
    </div>
    </>
  )
} 