@tailwind base;
@tailwind components;
@tailwind utilities;

/* KaTeX CSS for LaTeX rendering */
@import 'katex/dist/katex.min.css';

/* Enhanced typography for blog posts */
.markdown-content {
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Better LaTeX rendering */
.katex {
  font-size: 1.1em !important;
}

.katex-display {
  margin: 1.5rem 0 !important;
  overflow-x: auto;
  overflow-y: hidden;
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Better focus styles for accessibility */
.markdown-content a:focus,
.markdown-content button:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 2px;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Fluid typography system using clamp() */
  .heading-xl {
    font-size: clamp(2.25rem, 4vw + 1rem, 4.5rem); /* 36px to 72px */
    font-weight: 800;
    line-height: 1.1;
    letter-spacing: -0.02em;
    @apply tracking-tight;
  }

  .heading-lg {
    font-size: clamp(1.875rem, 3vw + 0.75rem, 3.25rem); /* 30px to 52px */
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.01em;
    @apply tracking-tight;
  }

  .heading-md {
    font-size: clamp(1.5rem, 2vw + 0.5rem, 2.5rem); /* 24px to 40px */
    font-weight: 700;
    line-height: 1.3;
    @apply tracking-tight;
  }

  .heading-sm {
    font-size: clamp(1.25rem, 1.5vw + 0.5rem, 1.875rem); /* 20px to 30px */
    font-weight: 600;
    line-height: 1.4;
    @apply tracking-tight;
  }

  .body-lg {
    font-size: clamp(1.125rem, 1vw + 0.75rem, 1.375rem); /* 18px to 22px */
    line-height: 1.6;
  }

  .body-md {
    /* Ensuring minimum 16px for body text */
    font-size: clamp(1rem, 0.75vw + 0.75rem, 1.125rem); /* 16px to 18px */
    line-height: 1.6;
  }

  .body-sm {
    /* Ensuring minimum 14px for small text */
    font-size: clamp(0.875rem, 0.5vw + 0.75rem, 1rem); /* 14px to 16px */
    line-height: 1.6;
  }
  
  /* Computational and mathematical visual elements */
  .bg-grid-pattern {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
                     linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .bg-dot-pattern {
    background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 16px 16px;
  }
  
  .bg-graph-paper {
    background-image: 
      linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px),
      linear-gradient(rgba(0, 0, 0, 0.05) 0.5px, transparent 0.5px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.05) 0.5px, transparent 0.5px);
    background-size: 50px 50px, 50px 50px, 10px 10px, 10px 10px;
  }
  
  .bg-blueprint {
    background-color: #f0f8ff;
    background-image: 
      linear-gradient(rgba(0, 0, 120, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 120, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .math-font {
    font-family: 'Courier New', monospace;
  }
}

@layer base {
  :root {
    /* Logo-based color palette */
    --crimson: 348 65% 48%; /* #c82f48 - Primary brand color */
    --gold: 36 94% 52%; /* #f89c0e - Secondary brand color */
    --dark: 345 5% 13%; /* #231f20 - Dark neutral */
    --light: 30 20% 96%; /* #f8f5f2 - Light background */

    /* System colors */
    --background: var(--light);
    --foreground: var(--dark);
    --card: 0 0% 100%;
    --card-foreground: var(--dark);
    --popover: 0 0% 100%;
    --popover-foreground: var(--dark);
    --primary: var(--crimson);
    --primary-foreground: 0 0% 98%;
    --secondary: var(--gold);
    --secondary-foreground: var(--dark);
    --muted: 30 10% 94%;
    --muted-foreground: 345 5% 35%;
    --accent: var(--gold);
    --accent-foreground: var(--dark);
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 30 10% 90%;
    --input: 30 10% 90%;
    --ring: var(--crimson);
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: var(--crimson);
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    text-rendering: optimizeLegibility;
    font-family: var(--font-inter), Inter, ui-sans-serif, system-ui, sans-serif;
  }

  body {
    @apply bg-background text-foreground font-sans;
    @apply antialiased;
    font-family: var(--font-inter), Inter, ui-sans-serif, system-ui, sans-serif !important;
  }

  /* Ensure all text elements use Inter font */
  *, *::before, *::after {
    font-family: inherit;
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    *, ::before, ::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    .motion-safe\:parallax {
      transform: none !important;
    }
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-sans font-bold tracking-tight;
    font-family: var(--font-inter), Inter, ui-sans-serif, system-ui, sans-serif;
  }

  h1 {
    @apply heading-xl;
  }

  h2 {
    @apply heading-lg;
  }

  h3 {
    @apply heading-md;
  }

  h4 {
    @apply heading-sm;
  }

  p {
    @apply body-md;
    font-family: var(--font-inter), Inter, ui-sans-serif, system-ui, sans-serif;
  }

  /* Custom link styles */
  a.nav-link {
    @apply relative inline-block transition-colors duration-200;
  }

  a.nav-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-crimson transition-all duration-300 ease-in-out;
    transform-origin: left;
  }

  a.nav-link:hover::after,
  a.nav-link:focus::after {
    @apply w-full;
  }

  /* Animated link hover effect */
  .header-link {
    @apply relative inline-block overflow-hidden;
  }

  .header-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-full h-0.5 bg-crimson transform scale-x-0 transition-transform duration-300 ease-out;
    transform-origin: right;
  }

  .header-link:hover::after,
  .header-link:focus::after {
    transform: scaleX(1);
    transform-origin: left;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 ease-in-out;
  }

  .card-hover:hover {
    @apply shadow-lg transform -translate-y-1;
  }

  /* Hide scrollbar for horizontal scrolling */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  /* Responsive spacing utilities */
  .fluid-p {
    padding: clamp(1rem, 3vw, 2rem);
  }

  .fluid-py {
    padding-top: clamp(2rem, 5vw, 4rem);
    padding-bottom: clamp(2rem, 5vw, 4rem);
  }

  .fluid-px {
    padding-left: clamp(1rem, 3vw, 2rem);
    padding-right: clamp(1rem, 3vw, 2rem);
  }

  .fluid-mt {
    margin-top: clamp(1.5rem, 4vw, 3rem);
  }

  .fluid-mb {
    margin-bottom: clamp(1.5rem, 4vw, 3rem);
  }

  .fluid-my {
    margin-top: clamp(1.5rem, 4vw, 3rem);
    margin-bottom: clamp(1.5rem, 4vw, 3rem);
  }

  .fluid-mx {
    margin-left: clamp(1rem, 3vw, 2rem);
    margin-right: clamp(1rem, 3vw, 2rem);
  }

  .fluid-gap {
    gap: clamp(1rem, 3vw, 2rem);
  }

  /* Mobile touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Ensure buttons, inputs, and other UI elements use Inter */
  button, input, textarea, select {
    font-family: var(--font-inter), Inter, ui-sans-serif, system-ui, sans-serif;
  }
}

/* Add these animations to the very end of the file */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out forwards;
}

/* Computer Science specific animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.1;
  }
  25% {
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
  75% {
    opacity: 0.3;
  }
}

@keyframes cursor-blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.cursor-after {
  animation: cursor-blink 1s infinite;
}

@keyframes flow {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(calc(100vh + 100%));
  }
}

/* Enhanced animations for About UEF page */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-gradient-x {
  animation: gradient-x 4s ease infinite;
}

.animate-float {
  animation: float-gentle 6s ease-in-out infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* Custom animations */
.animate-shake {
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
  40%, 60% { transform: translate3d(4px, 0, 0); }
}

/* Enhanced signin page animations */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(200, 47, 72, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(200, 47, 72, 0.6);
  }
}

@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float-gentle {
  animation: float-gentle 6s ease-in-out infinite;
}
