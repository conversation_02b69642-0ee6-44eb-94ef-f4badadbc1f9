'use client'

import React, { useRef, useEffect, useState } from "react"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { Badge } from "@/components/ui/badge"
import { 
  BookOpen, 
  Sparkles, 
  ShieldCheck, 
  Globe, 
  Users,
  ChevronRight,
  ArrowUp,
  CheckCircle2,
  Heart,
  Lightbulb,
  Target,
  Palette
} from "lucide-react"
import Link from "next/link"

export default function ValuesPage() {
  const [activeTab, setActiveTab] = useState("excellence");
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  
  // Enhanced value sections with modern design elements
  const valueSections = [
    { 
      id: "excellence", 
      title: "Excellence and Deep Inquiry", 
      subtitle: "Pursuit of Knowledge",
      icon: <BookOpen className="h-6 w-6" />,
      headline: "We pursue the highest standards in teaching, research, and personal growth.",
      content: [
        "True excellence is rooted in curiosity, rigorous thinking, and a relentless drive to understand the world more deeply. We encourage our community to ask bold questions, challenge assumptions, and seek knowledge that leads to meaningful impact—locally and globally.",
        "We celebrate originality, intellectual courage, and the joy of discovery. We value both the mastery of technology and the wisdom to use it ethically and responsibly."
      ],
      example: "Our faculty-led research initiative brings students and professors together to tackle challenging problems, encouraging deep inquiry and pushing the boundaries of knowledge in their disciplines.",
      primaryColor: "from-blue-500 to-indigo-600",
      accentColor: "bg-blue-50",
      borderColor: "border-blue-200",
      textColor: "text-blue-600",
      hoverBg: "hover:bg-blue-50/70"
    },
    { 
      id: "creativity", 
      title: "Creativity and Interdisciplinary Spirit", 
      subtitle: "Innovation at Intersections",
      icon: <Sparkles className="h-6 w-6" />,
      headline: "We thrive at the intersections—where disciplines meet, ideas collide, and new solutions emerge.",
      content: [
        "We foster an environment where creative problem-solving is second nature, and where students and faculty are empowered to cross boundaries, experiment, and innovate. We believe that the most pressing challenges require not just technical skill, but also imagination, empathy, and the ability to see connections others might miss.",
        "We embrace the unconventional, encourage playful exploration, and see failure as a vital step on the path to growth."
      ],
      example: "Our interdisciplinary projects bring together students from computer science, agriculture, and business to develop technology solutions for sustainable farming practices, blending expertise to solve real-world problems.",
      primaryColor: "from-amber-500 to-orange-600",
      accentColor: "bg-amber-50",
      borderColor: "border-amber-200",
      textColor: "text-amber-600",
      hoverBg: "hover:bg-amber-50/70"
    },
    { 
      id: "integrity", 
      title: "Integrity and Ethical Leadership", 
      subtitle: "Moral Compass",
      icon: <ShieldCheck className="h-6 w-6" />,
      headline: "We hold ourselves to the highest standards of honesty, transparency, and ethical conduct.",
      content: [
        "We believe that technological advancement must be guided by a strong moral compass. Our community is committed to acting with integrity, taking responsibility for our actions, and making decisions that serve the greater good.",
        "We speak openly about our challenges, learn from our mistakes, and strive to build trust in all our relationships."
      ],
      example: "Our Ethics in Technology course integrates ethical considerations into technical education, preparing students to make responsible decisions in their future roles as industry leaders and innovators.",
      primaryColor: "from-crimson to-red-600",
      accentColor: "bg-rose-50",
      borderColor: "border-rose-200",
      textColor: "text-crimson",
      hoverBg: "hover:bg-rose-50/70"
    },
    { 
      id: "global", 
      title: "Global Mindset and Adaptability", 
      subtitle: "Worldwide Perspective",
      icon: <Globe className="h-6 w-6" />,
      headline: "We prepare our graduates to thrive in a rapidly changing, interconnected world.",
      content: [
        "We cultivate cross-cultural understanding, global awareness, and flexibility to adapt to new environments and ideas. We encourage our community to look beyond borders, learn from diverse perspectives, and contribute solutions to global challenges.",
        "We see ourselves as part of a worldwide network of learners, innovators, and changemakers."
      ],
      example: "Our planned study abroad and international collaboration programs will connect students with peers around the world, fostering global perspectives and preparing them for careers in an interconnected global economy.",
      primaryColor: "from-purple-500 to-violet-600",
      accentColor: "bg-purple-50",
      borderColor: "border-purple-200",
      textColor: "text-purple-600",
      hoverBg: "hover:bg-purple-50/70"
    },
    { 
      id: "belonging", 
      title: "Belonging and Community", 
      subtitle: "Inclusive Environment",
      icon: <Users className="h-6 w-6" />,
      headline: "We are committed to building a welcoming, inclusive, and supportive community where everyone can flourish.",
      content: [
        "We value diversity in all its forms and believe that every individual's voice and experience enriches our collective journey. We care for one another's wellbeing—mind, body, and spirit—and strive to create an environment where all feel respected, valued, and empowered to reach their full potential.",
        "We recognize that our strength lies in our unity, and we share the responsibility to use our talents wisely, for the benefit of humanity and the planet."
      ],
      example: "Our community engagement initiatives and student-led diversity programs create a campus where different perspectives are valued and all students feel they belong and can contribute meaningfully.",
      primaryColor: "from-emerald-500 to-green-600",
      accentColor: "bg-emerald-50",
      borderColor: "border-emerald-200",
      textColor: "text-emerald-600",
      hoverBg: "hover:bg-emerald-50/70"
    },
  ];
  
  // Find the currently active section data
  const activeSection = valueSections.find(section => section.id === activeTab) || valueSections[0];
  
  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          
          {/* Enhanced Hero Section */}
          <section className="relative py-20 md:py-28 lg:py-32 overflow-hidden bg-gradient-to-br from-background via-muted/10 to-background">
            {/* Enhanced Background Elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-blue-500/8 to-purple-500/8 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-gradient-to-br from-emerald-500/6 to-teal-500/6 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
              <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-br from-amber-500/8 to-orange-500/8 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
            </div>
            
            <div className="px-6 md:px-8 lg:px-12 relative z-10">
              <div className="max-w-7xl mx-auto text-center">
                
                {/* Enhanced Header with Badge */}
                <div className="inline-flex items-center gap-4 md:gap-6 mb-8 md:mb-12">
                  <div className="w-12 md:w-16 h-[1px] bg-gradient-to-r from-transparent to-muted-foreground/25"></div>
                  <Badge variant="outline" className="px-6 md:px-8 py-3 md:py-4 font-medium tracking-wide border-purple-200/50 text-purple-700 bg-purple-50/40 backdrop-blur-sm">
                    <Heart className="h-3 w-3 mr-2" />
                    Our Values
                  </Badge>
                  <div className="w-12 md:w-16 h-[1px] bg-gradient-to-l from-transparent to-muted-foreground/25"></div>
                </div>
                
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-light mb-8 md:mb-12 tracking-tight">
                  The Principles That{" "}
                  <span className="font-bold bg-gradient-to-r from-crimson via-blue-600 to-purple-600 bg-clip-text text-transparent drop-shadow-sm">
                    Guide Us
                  </span>
                </h1>
                
                <p className="text-lg md:text-xl lg:text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto font-light mb-12 md:mb-16">
                  Five core values that define our character, shape our decisions, and inspire our journey toward excellence in education and innovation.
                </p>

                {/* Values Overview Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 md:gap-8 max-w-6xl mx-auto">
                  {valueSections.map((value, index) => (
                    <div
                      key={value.id}
                      className={`group relative bg-gradient-to-br from-background/80 to-muted/20 rounded-2xl p-6 border border-border/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 cursor-pointer ${value.hoverBg}`}
                      onClick={() => setActiveTab(value.id)}
                      onMouseEnter={() => setHoveredCard(value.id)}
                      onMouseLeave={() => setHoveredCard(null)}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className={`absolute inset-0 bg-gradient-to-br ${value.primaryColor} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300`}></div>
                      
                      <div className="relative">
                        <div className={`h-12 w-12 rounded-xl bg-gradient-to-br ${value.primaryColor} p-1 mb-4 group-hover:scale-110 transition-all duration-300 flex items-center justify-center`}>
                          <div className="w-full h-full bg-white rounded-lg flex items-center justify-center">
                            {React.cloneElement(value.icon, { className: `h-5 w-5 ${value.textColor}` })}
                          </div>
                        </div>
                        
                        <h3 className="text-base font-semibold mb-2 group-hover:text-opacity-90 transition-colors line-clamp-2">
                          {value.title}
                        </h3>
                        
                        <p className={`text-xs ${value.textColor} font-medium opacity-75`}>
                          {value.subtitle}
                        </p>
                        
                        <div className={`w-full h-1 bg-gradient-to-r ${value.primaryColor} rounded-full mt-4 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left`}></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Enhanced Value Details Section */}
          <section className="py-20 md:py-28 bg-gradient-to-b from-background to-muted/15 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-[0.015]">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 25px 25px, #dc2626 1px, transparent 0), 
                                 radial-gradient(circle at 75px 75px, #2563eb 1px, transparent 0)`,
                backgroundSize: '120px 120px'
              }}></div>
            </div>
            
            <div className="px-6 md:px-8 lg:px-12 relative z-10">
              <div className="max-w-7xl mx-auto">
                
                {/* Section Header */}
                <div className="text-center mb-16 md:mb-20">
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-light mb-6 md:mb-8 tracking-tight">
                    Exploring{" "}
                    <span className="font-bold bg-gradient-to-r from-crimson via-blue-600 to-purple-600 bg-clip-text text-transparent">
                      {activeSection.title}
                    </span>
                  </h2>
                  <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed font-light">
                    {activeSection.headline}
                  </p>
                </div>

                {/* Main Content Area */}
                <div className="grid lg:grid-cols-3 gap-12 md:gap-16">
                  
                  {/* Navigation Pills */}
                  <div className="lg:col-span-1">
                    <div className="sticky top-32">
                      <h3 className="text-sm font-semibold uppercase tracking-wider text-muted-foreground mb-6">Our Values</h3>
                      <nav className="space-y-2">
                        {valueSections.map((section) => (
                          <button
                            key={section.id}
                            onClick={() => setActiveTab(section.id)}
                            className={`w-full text-left p-4 rounded-xl transition-all duration-300 group ${
                              activeTab === section.id 
                                ? `bg-gradient-to-r ${activeSection.primaryColor} text-white shadow-lg transform scale-105` 
                                : 'hover:bg-muted/50 text-muted-foreground hover:text-foreground'
                            }`}
                          >
                            <div className="flex items-center gap-3">
                              <div className={`h-8 w-8 rounded-lg flex items-center justify-center transition-all duration-300 ${
                                activeTab === section.id 
                                  ? 'bg-white/20' 
                                  : `${section.accentColor}`
                              }`}>
                                {React.cloneElement(section.icon, { 
                                  className: `h-4 w-4 ${
                                    activeTab === section.id 
                                      ? 'text-white' 
                                      : section.textColor
                                  }`
                                })}
                              </div>
                              <div className="flex-1">
                                <div className={`text-sm font-medium ${
                                  activeTab === section.id ? 'text-white' : 'text-foreground'
                                }`}>
                                  {section.title.split(' ')[0]}
                                </div>
                                <div className={`text-xs ${
                                  activeTab === section.id 
                                    ? 'text-white/80' 
                                    : 'text-muted-foreground'
                                }`}>
                                  {section.subtitle}
                                </div>
                              </div>
                              {activeTab === section.id && (
                                <ChevronRight className="h-4 w-4 text-white" />
                              )}
                            </div>
                          </button>
                        ))}
                      </nav>
                    </div>
                  </div>

                  {/* Content Area */}
                  <div className="lg:col-span-2">
                    <div className="bg-white/80 backdrop-blur-sm rounded-3xl border border-border/30 shadow-xl p-8 md:p-12 transition-all duration-500">
                      
                      {/* Content Header */}
                      <div className="flex items-start gap-6 mb-8">
                        <div className={`h-16 w-16 rounded-2xl bg-gradient-to-br ${activeSection.primaryColor} p-1 flex-shrink-0`}>
                          <div className="w-full h-full bg-white rounded-xl flex items-center justify-center">
                            {React.cloneElement(activeSection.icon, { className: `h-8 w-8 ${activeSection.textColor}` })}
                          </div>
                        </div>
                        <div className="flex-1">
                          <h2 className="text-2xl md:text-3xl font-semibold mb-2">{activeSection.title}</h2>
                          <p className={`text-sm font-medium ${activeSection.textColor} opacity-80`}>
                            {activeSection.subtitle}
                          </p>
                        </div>
                      </div>

                      {/* Content Body */}
                      <div className="space-y-6 mb-8">
                        {activeSection.content.map((paragraph, index) => (
                          <p key={index} className="text-muted-foreground leading-relaxed text-base md:text-lg">
                            {paragraph}
                          </p>
                        ))}
                      </div>
                      
                      {/* Real-world Example */}
                      <div className={`bg-gradient-to-br ${activeSection.primaryColor} bg-opacity-5 rounded-2xl p-6 md:p-8 ${activeSection.borderColor} border`}>
                        <div className="flex items-center gap-3 mb-4">
                          <div className={`h-8 w-8 rounded-lg bg-gradient-to-br ${activeSection.primaryColor} flex items-center justify-center`}>
                            <CheckCircle2 className="h-4 w-4 text-white" />
                          </div>
                          <h3 className="text-lg font-semibold">In Practice</h3>
                        </div>
                        <p className="text-muted-foreground leading-relaxed">
                          {activeSection.example}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Call to Action Section */}
          <section className="py-20 md:py-28 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-emerald-500/5"></div>
            
            <div className="px-6 md:px-8 lg:px-12 relative z-10">
              <div className="max-w-4xl mx-auto text-center">
                <div className="bg-gradient-to-br from-background/80 to-muted/20 rounded-3xl border border-border/30 shadow-xl p-12 md:p-16 backdrop-blur-sm">
                  <h2 className="text-3xl md:text-4xl font-bold mb-6">
                    Join Our <span className="bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">Value-Driven</span> Community
                  </h2>
                  <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
                    These values aren't just words on a page—they're the foundation of everything we do. Join us in building a future guided by excellence, creativity, integrity, global mindset, and belonging.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link href="/about/uef">
                      <button className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                        Learn More About UEF
                      </button>
                    </Link>
                    <Link href="/about/team">
                      <button className="px-8 py-4 border border-blue-500/30 text-blue-600 rounded-full font-medium hover:bg-blue-50 transition-all duration-300">
                        Meet Our Team
                      </button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
} 