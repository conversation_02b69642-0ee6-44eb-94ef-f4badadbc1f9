import React, { use<PERSON>emo, Suspense, lazy } from "react"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  ArrowRight, BookOpen, Network, Code, Layers, LucideIcon, 
  Braces, Database, BrainCircuit, GitBranch, Share2, Sigma,
  FileText, Microscope, PieChart, Building, Atom, HeartPulse, Brain,
  Users, Award, GraduationCap
} from "lucide-react"
import Link from "next/link"

// Lazy load heavy components for better performance
const ForceDirectedGraph = lazy(() => import("@/components/research/ForceDirectedGraph"))
const ComputationalTimeline = lazy(() => import("@/components/research/ComputationalTimeline"))
const AnimatedCodeSnippet = lazy(() => import("@/components/research/AnimatedCodeSnippet"))
const DomainApplications = lazy(() => import("@/components/research/DomainApplications"))
const ResearchFAQ = lazy(() => import("@/components/research/ResearchFAQ"))

export const metadata = {
  title: 'Center of Interdisciplinary Computation | College Name',
  description: 'Explore our research center focused on interdisciplinary computational approaches to solving complex problems.',
}

// Research area type
interface ResearchArea {
  title: string;
  description: string;
  icon: React.ReactNode;
}

// Loading component for Suspense fallbacks
const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-12">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
  </div>
)

// Unified card component for consistent theming
const UnifiedCard = ({ 
  children, 
  className = "", 
  pattern = "dots" 
}: { 
  children: React.ReactNode
  className?: string
  pattern?: "dots" | "grid" | "none"
}) => {
  const patternStyles = {
    dots: "bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px]",
    grid: "bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] bg-[size:20px_20px]",
    none: ""
  }

  return (
    <div className={`bg-white/90 backdrop-blur-sm rounded-xl border border-gray-100 shadow-sm p-8 relative overflow-hidden transition-all hover:shadow-md hover:border-gray-200 ${className}`}>
      {pattern !== "none" && (
        <div className={`absolute inset-0 ${patternStyles[pattern]} opacity-20`}></div>
      )}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export default function ResearchCenterPage() {
  // Memoize research areas for better performance
  const researchAreas: ResearchArea[] = useMemo(() => [
    {
      title: "Computational Science",
      description: "Applying computational techniques to solve complex scientific problems across disciplines.",
      icon: <Braces className="h-5 w-5 text-primary" />
    },
    {
      title: "Data Science & AI",
      description: "Developing new methods for data analysis and artificial intelligence with applications in various fields.",
      icon: <BrainCircuit className="h-5 w-5 text-primary" />
    },
    {
      title: "Interdisciplinary Applications",
      description: "Bridging computational methods with disciplines such as agriculture, business, education, and social sciences.",
      icon: <Share2 className="h-5 w-5 text-primary" />
    },
    {
      title: "Algorithms & Modeling",
      description: "Creating and optimizing algorithms for modeling complex systems across disciplines.",
      icon: <GitBranch className="h-5 w-5 text-primary" />
    },
    {
      title: "Computational Mathematics",
      description: "Developing mathematical foundations for computational approaches to complex problems.",
      icon: <Sigma className="h-5 w-5 text-primary" />
    },
    {
      title: "Data Infrastructure",
      description: "Building systems and tools to support computational research across disciplines.",
      icon: <Database className="h-5 w-5 text-primary" />
    },
  ], []);

  // Memoize network data to prevent re-creation on every render
  const networkData = useMemo(() => ({
    nodes: [
      // Core Computational Methods
      { id: "data-sci", group: 1, label: "Data Science", radius: 16, description: "Extracting insights from large datasets across all disciplines", applications: "Scientific research, business analytics, policy making", techniques: "Statistical analysis, machine learning, visualization" },
      { id: "ml", group: 1, label: "Machine Learning", radius: 15, description: "Algorithms that learn patterns from data in any domain", applications: "Pattern recognition, prediction, automated decision making", techniques: "Neural networks, ensemble methods, deep learning" },
      { id: "simulation", group: 1, label: "Computational Simulation", radius: 17, description: "Mathematical modeling of complex systems across disciplines", applications: "Weather prediction, disease spread, economic modeling", techniques: "Numerical methods, Monte Carlo, finite element analysis" },
      
      // Physical Sciences
      { id: "physics", group: 2, label: "Computational Physics", radius: 14, description: "Using computation to solve complex physics problems", applications: "Particle physics, astrophysics, condensed matter", techniques: "Molecular dynamics, quantum simulations, lattice methods" },
      { id: "climate", group: 2, label: "Climate Science", radius: 15, description: "Computational modeling of Earth's climate systems", applications: "Weather forecasting, climate change research, environmental policy", techniques: "Global circulation models, data assimilation, ensemble forecasting" },
      { id: "chemistry", group: 2, label: "Computational Chemistry", radius: 13, description: "Computer simulation of chemical phenomena", applications: "Drug discovery, material design, catalysis", techniques: "Quantum chemistry, molecular dynamics, DFT calculations" },
      
      // Life Sciences
      { id: "biology", group: 3, label: "Computational Biology", radius: 14, description: "Computational approaches to biological questions", applications: "Genomics, protein structure, evolutionary biology", techniques: "Sequence alignment, phylogenetics, systems biology" },
      { id: "medicine", group: 3, label: "Medical Informatics", radius: 13, description: "Computing in healthcare and medical research", applications: "Diagnosis, treatment planning, epidemiology", techniques: "Medical imaging, electronic health records, precision medicine" },
      { id: "ecology", group: 3, label: "Computational Ecology", radius: 12, description: "Mathematical modeling of ecological systems", applications: "Conservation biology, ecosystem management, biodiversity", techniques: "Population models, spatial analysis, network ecology" },
      
      // Social Sciences & Humanities
      { id: "economics", group: 4, label: "Computational Economics", radius: 13, description: "Mathematical and computational approaches to economic problems", applications: "Market analysis, policy simulation, algorithmic trading", techniques: "Agent-based modeling, econometrics, optimization" },
      { id: "linguistics", group: 4, label: "Computational Linguistics", radius: 12, description: "Computer processing of human language", applications: "Language translation, text analysis, digital humanities", techniques: "Natural language processing, corpus linguistics, semantic analysis" },
      { id: "sociology", group: 4, label: "Computational Social Science", radius: 11, description: "Data-driven approaches to understanding society", applications: "Social network analysis, policy research, digital behavior", techniques: "Network analysis, sentiment analysis, behavioral modeling" },
      
      // Engineering & Applied Sciences
      { id: "engineering", group: 5, label: "Computational Engineering", radius: 14, description: "Computer-aided design and analysis in engineering", applications: "Structural analysis, fluid dynamics, control systems", techniques: "Finite element methods, CFD, optimization algorithms" },
      { id: "materials", group: 5, label: "Materials Science", radius: 12, description: "Computational design and analysis of materials", applications: "New material discovery, nanotechnology, semiconductors", techniques: "Density functional theory, Monte Carlo methods, machine learning" },
      { id: "agriculture", group: 5, label: "Precision Agriculture", radius: 13, description: "Data-driven approaches to farming and food production", applications: "Crop optimization, pest management, sustainable farming", techniques: "Remote sensing, IoT sensors, predictive modeling" },
    ],
    links: [
      // Core computational methods connections
      { source: "data-sci", target: "ml", value: 5, type: "foundational" },
      { source: "data-sci", target: "simulation", value: 4, type: "methodological" },
      { source: "ml", target: "simulation", value: 3, type: "methodological" },
      
      // Physical sciences connections
      { source: "simulation", target: "physics", value: 5, type: "foundational" },
      { source: "simulation", target: "climate", value: 5, type: "foundational" },
      { source: "simulation", target: "chemistry", value: 4, type: "foundational" },
      { source: "physics", target: "climate", value: 4, type: "interdisciplinary" },
      { source: "chemistry", target: "materials", value: 4, type: "applied" },
      { source: "physics", target: "materials", value: 3, type: "theoretical" },
      
      // Life sciences connections
      { source: "ml", target: "biology", value: 4, type: "applied" },
      { source: "data-sci", target: "biology", value: 4, type: "applied" },
      { source: "ml", target: "medicine", value: 4, type: "applied" },
      { source: "data-sci", target: "medicine", value: 4, type: "applied" },
      { source: "biology", target: "medicine", value: 5, type: "interdisciplinary" },
      { source: "simulation", target: "ecology", value: 3, type: "applied" },
      { source: "biology", target: "ecology", value: 4, type: "interdisciplinary" },
      { source: "chemistry", target: "biology", value: 3, type: "interdisciplinary" },
      
      // Social sciences connections
      { source: "data-sci", target: "economics", value: 4, type: "applied" },
      { source: "ml", target: "economics", value: 3, type: "applied" },
      { source: "simulation", target: "economics", value: 4, type: "applied" },
      { source: "ml", target: "linguistics", value: 4, type: "applied" },
      { source: "data-sci", target: "linguistics", value: 3, type: "applied" },
      { source: "data-sci", target: "sociology", value: 4, type: "applied" },
      { source: "ml", target: "sociology", value: 3, type: "applied" },
      
      // Engineering connections
      { source: "simulation", target: "engineering", value: 5, type: "foundational" },
      { source: "ml", target: "engineering", value: 3, type: "applied" },
      { source: "data-sci", target: "agriculture", value: 4, type: "applied" },
      { source: "ml", target: "agriculture", value: 4, type: "applied" },
      { source: "climate", target: "agriculture", value: 4, type: "interdisciplinary" },
      { source: "biology", target: "agriculture", value: 3, type: "interdisciplinary" },
      
      // Cross-domain emerging connections
      { source: "medicine", target: "engineering", value: 3, type: "emerging" },
      { source: "economics", target: "climate", value: 3, type: "emerging" },
      { source: "sociology", target: "ecology", value: 2, type: "emerging" },
    ]
  }), []);

  // Memoize timeline events
  const timelineEvents = useMemo(() => [
    {
      year: 1943,
      title: "COLOSSUS: First Programmable Computer",
      description: "The world's first programmable, electronic, digital computer breaks the Enigma code during WWII, demonstrating computation's power to solve complex real-world problems.",
      relevance: "Proved computational methods could solve previously impossible problems",
      color: "bg-gray-600",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Tommy Flowers", "Max Newman"],
      impact: "Code-breaking capabilities that shortened WWII",
      interdisciplinaryConnection: "Military Strategy + Mathematics + Electronics",
      computationalAdvance: "Sequential logic programming",
      modernLegacy: "Foundation of modern cybersecurity",
      funFact: "Kept secret for 30 years after the war ended",
      sourceUrl: "https://www.tnmoc.org/colossus"
    },
    {
      year: 1946,
      title: "ENIAC: Birth of Modern Computing",
      description: "The Electronic Numerical Integrator and Computer becomes operational, featuring 17,468 vacuum tubes and weighing 30 tons. It performed ballistics calculations 1000x faster than manual methods.",
      relevance: "Established computation as a tool for scientific research",
      color: "bg-amber-600",
      image: "https://images.unsplash.com/photo-1555949963-aa79dcee981c?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["J. Presper Eckert", "John Mauchly", "Betty Snyder", "Marlyn Wescoff"],
      impact: "1000x faster than manual calculations",
      interdisciplinaryConnection: "Physics + Engineering + Mathematics",
      computationalAdvance: "Electronic digital computation",
      modernLegacy: "Template for all modern computers",
      funFact: "The first programmers were six women mathematicians",
      sourceUrl: "https://www.computer.org/digital-library/magazines/an/1996/01/a1012-abs.html"
    },
    {
      year: 1947,
      title: "Simplex Algorithm: Optimization Revolution",
      description: "George Dantzig develops the simplex algorithm for linear programming, revolutionizing optimization problems in economics, engineering, and operations research.",
      relevance: "Foundational method for solving complex optimization problems",
      color: "bg-emerald-600",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["George Dantzig"],
      impact: "Enables solving problems with millions of variables",
      interdisciplinaryConnection: "Mathematics + Economics + Engineering",
      computationalAdvance: "Linear programming and optimization theory",
      modernLegacy: "Resource allocation, supply chain, machine learning",
      funFact: "Dantzig mistook homework problems for exam questions and solved two famous unsolved statistics problems",
      sourceUrl: "https://pubsonline.informs.org/doi/abs/10.1287/opre.50.1.42.17798"
    },
    {
      year: 1950,
      title: "Turing Test: AI Benchmark",
      description: "Alan Turing proposes the Turing Test as a criterion for machine intelligence, establishing the philosophical foundation for artificial intelligence research.",
      relevance: "Defined the goal and evaluation criteria for artificial intelligence",
      color: "bg-purple-600",
      image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Alan Turing"],
      impact: "Framework for evaluating machine intelligence still used today",
      interdisciplinaryConnection: "Computer Science + Philosophy + Cognitive Science",
      computationalAdvance: "Conceptual foundation of AI",
      modernLegacy: "Modern AI evaluation and chatbot development",
      funFact: "Turing predicted that by 2000, machines would fool 30% of judges - remarkably close to reality",
      sourceUrl: "https://academic.oup.com/mind/article/LIX/236/433/986238"
    },
    {
      year: 1957,
      title: "FORTRAN: First High-Level Language",
      description: "IBM releases FORTRAN (Formula Translation), the first widely-used high-level programming language, making computational methods accessible to scientists and engineers.",
      relevance: "Democratized computational research across disciplines",
      color: "bg-blue-600",
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["John Backus", "IBM Programming Team"],
      impact: "Reduced programming time from weeks to hours",
      interdisciplinaryConnection: "Computer Science + Scientific Computing",
      computationalAdvance: "Abstract programming paradigms",
      modernLegacy: "Still used in high-performance scientific computing",
      funFact: "Originally called 'The IBM Mathematical Formula Translating System'",
      sourceUrl: "https://dl.acm.org/doi/10.1145/1039813.1039814"
    },
    {
      year: 1962,
      title: "Quicksort Algorithm: Sorting Revolution",
      description: "Tony Hoare develops the quicksort algorithm, one of the most efficient and widely used sorting algorithms, fundamental to computer science and data processing.",
      relevance: "Essential algorithm for data organization and search",
      color: "bg-indigo-600",
      image: "https://images.unsplash.com/photo-1518186285589-2f7649de83e0?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Tony Hoare"],
      impact: "O(n log n) average complexity revolutionized data sorting",
      interdisciplinaryConnection: "Computer Science + Mathematics + Data Processing",
      computationalAdvance: "Divide-and-conquer algorithmic paradigm",
      modernLegacy: "Foundation of modern database and search systems",
      funFact: "Hoare developed it while trying to sort words for machine translation of Russian",
      sourceUrl: "https://dl.acm.org/doi/10.1145/366622.366644"
    },
    {
      year: 1965,
      title: "Fast Fourier Transform: Signal Processing Breakthrough",
      description: "Cooley and Tukey rediscover the Fast Fourier Transform algorithm, revolutionizing signal processing, image analysis, and scientific computation.",
      relevance: "Enabled efficient analysis of signals and data",
      color: "bg-cyan-600",
      image: "https://images.unsplash.com/photo-1551434678-e076c223a692?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["James Cooley", "John Tukey"],
      impact: "Reduced FFT computation from O(n²) to O(n log n)",
      interdisciplinaryConnection: "Mathematics + Signal Processing + Physics",
      computationalAdvance: "Efficient frequency domain analysis",
      modernLegacy: "Digital audio, image processing, quantum computing",
      funFact: "The algorithm was originally discovered by Gauss in 1805 but forgotten",
      sourceUrl: "https://doi.org/10.1090/S0025-5718-1965-0178586-1"
    },
    {
      year: 1965,
      title: "Moore's Law: Predicting the Future",
      description: "Intel co-founder Gordon Moore observes that transistor density doubles every 18-24 months, predicting an exponential growth in computational power that drives interdisciplinary research possibilities.",
      relevance: "Enabled increasingly sophisticated computational models",
      color: "bg-purple-600",
      image: "https://images.unsplash.com/photo-1518770660439-4636190af475?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Gordon Moore"],
      impact: "10 billion-fold increase in computing power since 1965",
      interdisciplinaryConnection: "Engineering + Economics + Prediction Science",
      computationalAdvance: "Exponential scaling predictions",
      modernLegacy: "Self-fulfilling prophecy driving semiconductor industry",
      funFact: "Moore originally predicted the trend would last only 10 years",
      sourceUrl: "https://newsroom.intel.com/wp-content/uploads/sites/11/2018/05/moores-law-electronics.pdf"
    },
    {
      year: 1969,
      title: "ARPANET: Network Revolution",
      description: "The first packet-switched network connects four universities, laying groundwork for distributed computing and collaborative research across institutions.",
      relevance: "Enabled global collaborative computational research",
      color: "bg-green-600",
      image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["J.C.R. Licklider", "Bob Kahn", "Vint Cerf"],
      impact: "Connected 4 computers, now connects 5+ billion devices",
      interdisciplinaryConnection: "Computer Science + Communications + Global Collaboration",
      computationalAdvance: "Distributed computing architectures",
      modernLegacy: "Internet and cloud computing",
      funFact: "First message was 'LO' - they were trying to type 'LOGIN' but the system crashed",
      sourceUrl: "https://www.darpa.mil/about-us/timeline/arpanet"
    },
    {
      year: 1975,
      title: "Personal Computer Era Begins",
      description: "The Altair 8800 and later Apple II democratize computing power, bringing computational tools to individual researchers and small labs.",
      relevance: "Made computational research accessible to smaller institutions",
      color: "bg-red-600",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Steve Jobs", "Steve Wozniak", "Ed Roberts"],
      impact: "Computing costs dropped from millions to thousands of dollars",
      interdisciplinaryConnection: "Technology + Education + Individual Research",
      computationalAdvance: "Personal computation accessibility",
      modernLegacy: "Desktop scientific computing",
      funFact: "Bill Gates and Paul Allen's first major software was for the Altair 8800"
    },
    {
      year: 1977,
      title: "RSA Cryptography: Secure Communication",
      description: "Rivest, Shamir, and Adleman develop RSA public-key cryptography, enabling secure communication and digital commerce worldwide.",
      relevance: "Foundation of modern digital security and privacy",
      color: "bg-rose-600",
      image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Ron Rivest", "Adi Shamir", "Leonard Adleman"],
      impact: "Enables secure internet transactions worth trillions annually",
      interdisciplinaryConnection: "Mathematics + Computer Science + Economics",
      computationalAdvance: "Public-key cryptography",
      modernLegacy: "Internet security, blockchain, digital signatures",
      funFact: "Based on the difficulty of factoring large prime numbers",
      sourceUrl: "https://dl.acm.org/doi/10.1145/359340.359342"
    },
    {
      year: 1984,
      title: "DNA Sequencing Algorithms",
      description: "Development of computational methods for DNA sequencing analysis revolutionizes biology, introducing computational approaches to life sciences.",
      relevance: "Birth of computational biology and bioinformatics",
      color: "bg-teal-600",
      image: "https://images.unsplash.com/photo-1576086213369-97a306d36557?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Frederick Sanger", "Walter Gilbert", "Leroy Hood"],
      impact: "Enabled Human Genome Project completion",
      interdisciplinaryConnection: "Biology + Computer Science + Medicine",
      computationalAdvance: "Biological sequence analysis",
      modernLegacy: "Personalized medicine and genomics",
      funFact: "The first DNA sequencing took years; now we can sequence a genome in hours"
    },
    {
      year: 1986,
      title: "Backpropagation Algorithm: Neural Network Training",
      description: "Rumelhart, Hinton, and Williams popularize backpropagation for training neural networks, laying groundwork for modern deep learning.",
      relevance: "Enabled practical training of multi-layer neural networks",
      color: "bg-violet-600",
      image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["David Rumelhart", "Geoffrey Hinton", "Ronald Williams"],
      impact: "Made deep learning practically feasible",
      interdisciplinaryConnection: "Computer Science + Neuroscience + Mathematics",
      computationalAdvance: "Efficient neural network training",
      modernLegacy: "Foundation of modern AI and machine learning",
      funFact: "The algorithm was independently discovered multiple times since the 1960s",
      sourceUrl: "https://www.nature.com/articles/323533a0"
    },
    {
      year: 1989,
      title: "World Wide Web: Information Revolution",
      description: "Tim Berners-Lee creates the Web at CERN, transforming how researchers share data, collaborate, and publish findings globally.",
      relevance: "Revolutionized scientific communication and data sharing",
      color: "bg-indigo-600",
      image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Tim Berners-Lee"],
      impact: "1.8 billion websites today, enabling global research collaboration",
      interdisciplinaryConnection: "Computer Science + Information Systems + Global Communication",
      computationalAdvance: "Hypertext and global information sharing",
      modernLegacy: "Modern scientific publishing and collaboration",
      funFact: "Berners-Lee originally called it the 'Information Mesh'",
      sourceUrl: "https://www.w3.org/History/1989/proposal.html"
    },
    {
      year: 1994,
      title: "Shor's Algorithm: Quantum Computing Breakthrough",
      description: "Peter Shor develops a quantum algorithm that can efficiently factor large integers, threatening current cryptography and advancing quantum computing.",
      relevance: "Demonstrated quantum computing's potential and limitations of classical cryptography",
      color: "bg-amber-600",
      image: "https://images.unsplash.com/photo-1635070041078-e363dbe005cb?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Peter Shor"],
      impact: "Exponential speedup over classical algorithms for factoring",
      interdisciplinaryConnection: "Quantum Physics + Computer Science + Cryptography",
      computationalAdvance: "Quantum algorithmic paradigm",
      modernLegacy: "Post-quantum cryptography, quantum computing research",
      funFact: "Sparked the race to build practical quantum computers"
    },
    {
      year: 1997,
      title: "Deep Blue: AI Milestone",
      description: "IBM's Deep Blue defeats world chess champion Garry Kasparov, demonstrating that computational methods can exceed human performance in complex strategic tasks.",
      relevance: "Proved AI could solve problems requiring strategic thinking",
      color: "bg-cyan-600",
      image: "https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Murray Campbell", "Joe Hoane", "Feng-hsiung Hsu"],
      impact: "200 million chess positions evaluated per second",
      interdisciplinaryConnection: "AI + Game Theory + Human Cognition",
      computationalAdvance: "Parallel processing and search algorithms",
      modernLegacy: "Modern AI and machine learning systems",
      funFact: "Deep Blue's victory caused IBM's stock to rise $18 per share overnight"
    },
    {
      year: 2001,
      title: "Human Genome Project Completion",
      description: "International collaboration completes the first human genome sequence using massive computational resources, revealing the 3.1 billion base pairs of human DNA.",
      relevance: "Demonstrated power of computational biology",
      color: "bg-emerald-600",
      image: "https://images.unsplash.com/photo-1583336663277-620dc1996580?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Craig Venter", "Francis Collins", "Eric Lander"],
      impact: "$3.8 billion project, cost to sequence a genome now under $1000",
      interdisciplinaryConnection: "Biology + Computer Science + International Collaboration",
      computationalAdvance: "Large-scale bioinformatics",
      modernLegacy: "Personalized medicine and precision healthcare",
      funFact: "If printed, the human genome would create a stack of paper 200 feet tall"
    },
    {
      year: 2006,
      title: "Cloud Computing: AWS Launch",
      description: "Amazon Web Services democratizes access to powerful computational resources, enabling researchers worldwide to perform large-scale computational analysis.",
      relevance: "Made supercomputing accessible to all researchers",
      color: "bg-orange-600",
      image: "https://images.unsplash.com/photo-1451187580459-43490279c0fa?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Werner Vogels", "Andy Jassy"],
      impact: "Reduced barrier to entry for computational research by 1000x",
      interdisciplinaryConnection: "Computer Science + Economics + Global Research Access",
      computationalAdvance: "On-demand scalable computing",
      modernLegacy: "Modern research infrastructure",
      funFact: "AWS was born from Amazon's need to scale for holiday shopping"
    },
    {
      year: 2009,
      title: "Bitcoin: Blockchain Revolution",
      description: "Satoshi Nakamoto introduces Bitcoin and blockchain technology, creating a new paradigm for distributed computing and trust without central authority.",
      relevance: "Introduced decentralized computational consensus mechanisms",
      color: "bg-yellow-600",
      image: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Satoshi Nakamoto (pseudonym)"],
      impact: "Created $1+ trillion cryptocurrency market and blockchain ecosystem",
      interdisciplinaryConnection: "Cryptography + Economics + Distributed Systems",
      computationalAdvance: "Proof-of-work consensus and distributed ledgers",
      modernLegacy: "Web3, smart contracts, decentralized finance",
      funFact: "Satoshi Nakamoto's true identity remains unknown"
    },
    {
      year: 2012,
      title: "Deep Learning Breakthrough: AlexNet",
      description: "AlexNet wins ImageNet competition by a huge margin, launching the deep learning revolution and transforming computational approaches across all fields.",
      relevance: "Revolutionized pattern recognition and data analysis",
      color: "bg-violet-600",
      image: "https://images.unsplash.com/photo-1647586711330-39abea4260da?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Alex Krizhevsky", "Ilya Sutskever", "Geoffrey Hinton"],
      impact: "Reduced image recognition error rate from 26% to 15%",
      interdisciplinaryConnection: "AI + Computer Vision + Neuroscience",
      computationalAdvance: "Deep neural networks and GPU computing",
      modernLegacy: "Modern AI applications across all fields",
      funFact: "AlexNet had only 8 layers; modern networks can have hundreds"
    },
    {
      year: 2016,
      title: "AlphaGo: Strategic AI Triumph",
      description: "DeepMind's AlphaGo defeats world champion Lee Sedol at Go, demonstrating AI's ability to master intuitive and creative strategic thinking.",
      relevance: "Showed AI could handle intuitive, creative problem-solving",
      color: "bg-rose-600",
      image: "https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Demis Hassabis", "David Silver", "Aja Huang"],
      impact: "10^170 possible Go positions - more than atoms in the observable universe",
      interdisciplinaryConnection: "AI + Game Theory + Human Creativity",
      computationalAdvance: "Monte Carlo tree search + deep reinforcement learning",
      modernLegacy: "Strategic decision-making AI systems",
      funFact: "Go was considered unsolvable by computers until AlphaGo"
    },
    {
      year: 2020,
      title: "AlphaFold: Protein Revolution",
      description: "DeepMind's AlphaFold solves the 50-year-old protein folding problem, predicting 3D protein structures from amino acid sequences with unprecedented accuracy.",
      relevance: "Computational breakthrough solving fundamental biological problem",
      color: "bg-lime-600",
      image: "https://images.unsplash.com/photo-1558021211-6d1403321394?auto=format&fit=crop&q=80&w=500&h=300",
      keyFigures: ["Demis Hassabis", "John Jumper", "Pushmeet Kohli"],
      impact: "Predicted structure of 200+ million proteins - virtually all known proteins",
      interdisciplinaryConnection: "AI + Biology + Medicine + Drug Discovery",
      computationalAdvance: "Transformer architectures for scientific problems",
      modernLegacy: "Accelerated drug discovery and disease understanding",
      funFact: "Solved a problem that took PhD students years to solve for a single protein"
    }
  ], []);

  // Memoize sample research projects data
  const researchProjects = useMemo(() => [
    {
      id: "proj-001",
      title: "Computational Analysis of Climate Impact on Agricultural Patterns",
      description: "This project applies machine learning techniques to analyze how changing climate patterns affect agricultural yields across different regions, helping develop predictive models for future crop planning.",
      image: "https://images.unsplash.com/photo-1586771107445-d3ca888129ce?auto=format&fit=crop&q=80&w=1000",
      tags: ["Climate Science", "Agriculture", "Machine Learning"],
      researchAreas: ["Computational Science", "Data Science & AI"],
      investigators: ["Shishir Raj Adhikari, PhD", "Dr. Emily Rodriguez", "Prof. David Williams"]
    },
    {
      id: "proj-002",
      title: "Network Analysis of Social Media Influence on Public Health Decisions",
      description: "This interdisciplinary project combines social network analysis with computational modeling to understand how information spreads through social media networks and influences public health decisions.",
      image: "https://images.unsplash.com/photo-1614308458127-ce868c22e8e7?auto=format&fit=crop&q=80&w=1000",
      tags: ["Social Networks", "Public Health", "Computational Modeling"],
      researchAreas: ["Data Science & AI", "Interdisciplinary Applications"],
      investigators: ["Dr. Emily Rodriguez", "Prof. James Wilson", "Dr. Lisa Park"]
    },
    {
      id: "proj-003",
      title: "Computational Models for Economic Policy Simulation",
      description: "Developing advanced computational models to simulate the effects of economic policies on various sectors, providing data-driven insights for policy-makers.",
      image: "https://images.unsplash.com/photo-1618044733300-9472054094ee?auto=format&fit=crop&q=80&w=1000",
      tags: ["Economics", "Policy Analysis", "Simulation"],
      researchAreas: ["Algorithms & Modeling", "Computational Mathematics"],
      investigators: ["Prof. Robert Brown", "Dr. Amanda Lee", "Dr. Thomas Martin"]
    }
  ], []);

  // Memoize domain applications data
  const domainApplications = useMemo(() => [
    {
      id: "app-001",
      domain: "Healthcare",
      title: "Predictive Diagnosis & Personalized Medicine",
      description: "Applying computational models to patient data for early disease detection, personalized treatment recommendations, and drug discovery acceleration.",
      icon: <HeartPulse className="h-6 w-6" />,
      color: "bg-red-500",
      examples: [
        {
          title: "AlphaFold Protein Structure Prediction",
          description: "DeepMind's AlphaFold solved the 50-year protein folding problem, predicting 3D structures of over 200 million proteins",
          achievement: "Reduced protein structure prediction time from months to minutes",
          source: "Nature (2021)",
          link: "https://www.nature.com/articles/s41586-021-03819-2"
        },
        {
          title: "IBM Watson for Oncology",
          description: "AI system that analyzes patient data and medical literature to recommend personalized cancer treatments",
          achievement: "Analyzes 1,500+ medical journals and 200+ textbooks in seconds",
          source: "IBM Research",
          link: "https://www.ibm.com/watson-health/oncology-and-genomics"
        },
        {
          title: "Google's Diabetic Retinopathy Detection",
          description: "Deep learning model that detects diabetic retinopathy from retinal photographs with 90%+ accuracy",
          achievement: "Diagnosed diabetic retinopathy with specialist-level accuracy",
          source: "JAMA (2016)",
          link: "https://jamanetwork.com/journals/jama/fullarticle/2588763"
        }
      ],
      technologies: ["Deep Learning", "Computer Vision", "Natural Language Processing", "Genomics"],
      institutions: ["DeepMind", "IBM Research", "Google Health", "Mayo Clinic", "Stanford Medicine"]
    },
    {
      id: "app-002",
      domain: "Environment",
      title: "Climate Modeling & Environmental Prediction",
      description: "Using high-performance computing to model climate patterns, predict environmental changes, and optimize renewable energy systems.",
      icon: <PieChart className="h-6 w-6" />,
      color: "bg-green-500",
      examples: [
        {
          title: "NOAA Global Climate Models",
          description: "Sophisticated computational models predicting global climate patterns up to 100 years in advance",
          achievement: "Process 50+ terabytes of daily observational data",
          source: "NOAA Climate Prediction Center",
          link: "https://www.climate.gov/news-features/understanding-climate/climate-modeling"
        },
        {
          title: "Microsoft's AI for Earth",
          description: "Machine learning platform addressing environmental challenges including deforestation monitoring and species tracking",
          achievement: "Monitored 40+ million acres of forest coverage globally",
          source: "Microsoft AI for Earth",
          link: "https://www.microsoft.com/en-us/ai/ai-for-earth"
        },
        {
          title: "Google's Wind Power Prediction",
          description: "DeepMind's AI system improved wind power prediction accuracy by 36 hours in advance",
          achievement: "Increased wind energy value by roughly 20%",
          source: "DeepMind Blog (2019)",
          link: "https://deepmind.com/blog/article/machine-learning-can-boost-value-wind-energy"
        }
      ],
      technologies: ["Climate Modeling", "Satellite Imagery", "IoT Sensors", "Machine Learning", "Big Data Analytics"],
      institutions: ["NOAA", "NASA", "IPCC", "European Centre for Medium-Range Weather Forecasts", "MIT Climate Portal"]
    },
    {
      id: "app-003",
      domain: "Biology",
      title: "Computational Biology & Genomics",
      description: "Computational approaches to understand protein structures, gene expression, evolutionary biology, and accelerate drug discovery processes.",
      icon: <Microscope className="h-6 w-6" />,
      color: "bg-blue-500",
      examples: [
        {
          title: "Human Genome Project",
          description: "International collaboration that sequenced the entire human genome using computational biology techniques",
          achievement: "Mapped 3.1 billion DNA base pairs, cost reduced from $3B to under $1,000",
          source: "Nature Genetics (2001)",
          link: "https://www.nature.com/articles/ng0901-5"
        },
        {
          title: "CRISPR Gene Editing Optimization",
          description: "Computational tools predict optimal CRISPR targets and minimize off-target effects",
          achievement: "Reduced off-target editing by 90% using predictive algorithms",
          source: "Science (2016)",
          link: "https://science.sciencemag.org/content/353/6305/aaf5573"
        },
        {
          title: "COVID-19 Drug Discovery",
          description: "AI-driven platforms identified potential drug compounds in weeks rather than years during the pandemic",
          achievement: "Screened 6+ billion compounds for COVID-19 treatment potential",
          source: "Nature Machine Intelligence (2020)",
          link: "https://www.nature.com/articles/s42256-020-00236-4"
        }
      ],
      technologies: ["Bioinformatics", "Sequence Analysis", "Molecular Dynamics", "CRISPR Design", "Drug Discovery AI"],
      institutions: ["NIH", "Broad Institute", "Wellcome Sanger Institute", "EMBL-EBI", "Chan Zuckerberg Initiative"]
    },
    {
      id: "app-004",
      domain: "Business",
      title: "Market Analytics & Economic Modeling",
      description: "Computational analysis of market trends, consumer behavior, supply chain optimization, and algorithmic trading for strategic business insights.",
      icon: <Building className="h-6 w-6" />,
      color: "bg-yellow-500",
      examples: [
        {
          title: "Amazon's Recommendation Engine",
          description: "Machine learning algorithms analyze customer behavior to generate personalized product recommendations",
          achievement: "Drives 35% of Amazon's revenue through personalized recommendations",
          source: "McKinsey Global Institute",
          link: "https://www.mckinsey.com/industries/retail/our-insights/how-retailers-can-keep-up-with-consumers"
        },
        {
          title: "JPMorgan's COIN Platform",
          description: "AI system processes legal documents and contracts in seconds, replacing 360,000 hours of manual work annually",
          achievement: "Reduced contract analysis time from hours to seconds",
          source: "JPMorgan Chase Annual Report",
          link: "https://www.jpmorganchase.com/ir/annual-report"
        },
        {
          title: "Walmart's Supply Chain Optimization",
          description: "Predictive analytics optimize inventory management and reduce food waste by predicting demand patterns",
          achievement: "Reduced food waste by 20% and improved inventory turnover by 15%",
          source: "Harvard Business Review (2019)",
          link: "https://hbr.org/2019/01/how-walmart-uses-machine-learning-to-solve-problems-in-real-time"
        }
      ],
      technologies: ["Predictive Analytics", "Algorithmic Trading", "Supply Chain AI", "Customer Analytics", "Financial Modeling"],
      institutions: ["MIT Sloan", "Wharton Analytics", "Stanford Graduate School of Business", "Chicago Booth", "INSEAD"]
    },
    {
      id: "app-005",
      domain: "Physics",
      title: "Quantum Computing & Simulation",
      description: "Simulating quantum phenomena to understand fundamental physics, develop quantum computers, and solve complex optimization problems.",
      icon: <Atom className="h-6 w-6" />,
      color: "bg-purple-500",
      examples: [
        {
          title: "Google's Quantum Supremacy",
          description: "Sycamore quantum processor performed calculation in 200 seconds that would take classical computers 10,000 years",
          achievement: "First demonstration of quantum computational advantage",
          source: "Nature (2019)",
          link: "https://www.nature.com/articles/s41586-019-1666-5"
        },
        {
          title: "CERN's Large Hadron Collider",
          description: "Computational analysis of particle collision data led to discovery of Higgs boson and other fundamental particles",
          achievement: "Processes 50+ petabytes of data annually from particle collisions",
          source: "CERN Scientific Papers",
          link: "https://home.cern/science/physics/higgs-boson"
        },
        {
          title: "IBM's Quantum Network",
          description: "Cloud-based quantum computing platform enabling researchers worldwide to run quantum algorithms",
          achievement: "200+ institutions accessing quantum computers remotely",
          source: "IBM Quantum Network",
          link: "https://www.ibm.com/quantum-computing/network/"
        }
      ],
      technologies: ["Quantum Algorithms", "Quantum Simulation", "Particle Physics Computing", "Quantum Error Correction", "Quantum Machine Learning"],
      institutions: ["IBM Research", "Google Quantum AI", "MIT Center for Quantum Engineering", "Oxford Quantum Computing", "CERN"]
    },
    {
      id: "app-006",
      domain: "Neuroscience",
      title: "Brain Mapping & Neural Networks",
      description: "Computational techniques to map neural connections, understand brain function, and develop brain-computer interfaces.",
      icon: <Brain className="h-6 w-6" />,
      color: "bg-indigo-500",
      examples: [
        {
          title: "Human Connectome Project",
          description: "Large-scale computational mapping of neural connections in the human brain using advanced imaging techniques",
          achievement: "Mapped 100+ billion neural connections with unprecedented detail",
          source: "Nature Methods (2016)",
          link: "https://www.nature.com/articles/nmeth.3902"
        },
        {
          title: "Neuralink Brain-Computer Interface",
          description: "Computational processing of neural signals to enable direct brain control of external devices",
          achievement: "Enabled paralyzed patients to control computers with thought alone",
          source: "Journal of Medical Internet Research",
          link: "https://www.jmir.org/2021/4/e26187/"
        },
        {
          title: "Blue Brain Project",
          description: "Digital reconstruction and simulation of brain circuits using supercomputing infrastructure",
          achievement: "Simulated 31,000 neurons and 40 million synapses in real-time",
          source: "Cell (2015)",
          link: "https://www.cell.com/cell/fulltext/S0092-8674(15)01191-4"
        }
      ],
      technologies: ["fMRI Analysis", "EEG Signal Processing", "Neural Network Modeling", "Brain-Computer Interfaces", "Computational Neuroscience"],
      institutions: ["Human Connectome Project", "Blue Brain Project", "Allen Institute for Brain Science", "MIT McGovern Institute", "Stanford Neurosciences Institute"]
    },
    {
      id: "app-007",
      domain: "Agriculture",
      title: "Precision Agriculture & Food Security",
      description: "Data-driven approaches to optimize crop yields, reduce environmental impact, and ensure global food security through computational modeling.",
      icon: <Layers className="h-6 w-6" />,
      color: "bg-amber-500",
      examples: [
        {
          title: "John Deere's Precision Agriculture",
          description: "AI-powered tractors and drones optimize planting, fertilization, and harvesting using real-time field data",
          achievement: "Increased crop yields by 15% while reducing fertilizer use by 10%",
          source: "IEEE Spectrum (2020)",
          link: "https://spectrum.ieee.org/automaton/robotics/industrial-robots/how-john-deere-plans-to-plant-the-seeds-of-a-digital-farming-revolution"
        },
        {
          title: "Climate Corporation's Climate FieldView",
          description: "Machine learning platform analyzes weather, soil, and crop data to provide personalized farming recommendations",
          achievement: "Used on 100+ million acres globally, improving yields by 5-15%",
          source: "Bayer Crop Science",
          link: "https://www.climate.com/climatefieldview-platform/"
        },
        {
          title: "CGIAR's Crop Modeling Initiative",
          description: "Computational models predict how climate change affects crop production and identify resilient varieties",
          achievement: "Modeled 50+ crops across 100+ countries for climate adaptation",
          source: "Agricultural Systems (2021)",
          link: "https://www.sciencedirect.com/science/article/pii/S0308521X21001475"
        }
      ],
      technologies: ["Remote Sensing", "IoT Sensors", "Crop Modeling", "Weather Analytics", "Genomics-Assisted Breeding"],
      institutions: ["CGIAR", "FAO", "USDA Agricultural Research Service", "Wageningen University", "Cornell Alliance for Science"]
    },
    {
      id: "app-008",
      domain: "Transportation",
      title: "Autonomous Systems & Traffic Optimization",
      description: "Computational approaches to develop self-driving vehicles, optimize traffic flow, and create smart transportation networks.",
      icon: <Network className="h-6 w-6" />,
      color: "bg-cyan-500",
      examples: [
        {
          title: "Waymo's Autonomous Driving",
          description: "AI system processes sensor data to navigate complex urban environments safely and efficiently",
          achievement: "Driven 20+ million autonomous miles with 99.99% safety record",
          source: "Waymo Safety Report",
          link: "https://waymo.com/safety/"
        },
        {
          title: "Singapore's Smart Traffic Management",
          description: "AI-powered traffic light optimization reduces congestion and emissions across the city-state",
          achievement: "Reduced travel time by 25% and emissions by 30% city-wide",
          source: "Land Transport Authority Singapore",
          link: "https://www.lta.gov.sg/content/ltagov/en/industry_innovations/technologies.html"
        },
        {
          title: "Tesla's Autopilot Neural Networks",
          description: "Deep learning system processes video feeds from multiple cameras for autonomous driving capabilities",
          achievement: "Collected 3+ billion miles of real-world driving data",
          source: "Tesla AI Day Presentations",
          link: "https://www.tesla.com/AI"
        }
      ],
      technologies: ["Computer Vision", "Sensor Fusion", "Path Planning", "Traffic Simulation", "Vehicle-to-Everything Communication"],
      institutions: ["MIT CSAIL", "Carnegie Mellon Robotics Institute", "Stanford AI Lab", "Uber ATG", "Aurora Innovation"]
    }
  ], []);

  // Memoize FAQ data
  const faqItems = useMemo(() => [
    {
      question: "What is computational research?",
      answer: "Computational research uses computer algorithms, simulations, and data analysis to solve complex problems that would be difficult or impossible to address through traditional methods alone. It involves developing mathematical models, creating algorithms, and analyzing large datasets to gain insights across various fields.",
      category: "Fundamentals"
    },
    {
      question: "How is interdisciplinary computation different from traditional computational research?",
      answer: "Interdisciplinary computation combines computational approaches with knowledge and methods from multiple disciplines. Unlike traditional computational research that might focus on purely computational problems, interdisciplinary computation addresses complex real-world challenges that span different fields, requiring expertise from various domains working together.",
      category: "Fundamentals"
    },
    {
      question: "What skills do I need to participate in computational research?",
      answer: "Valuable skills include programming (Python, R, Julia), statistical analysis, data visualization, domain expertise in your field, and collaborative abilities. However, we welcome participants at all levels and provide opportunities to develop these skills through our programs.",
      category: "Getting Involved"
    },
    {
      question: "Can undergraduates participate in the center's research?",
      answer: "Absolutely! We offer undergraduate research opportunities, workshops, and courses. Undergraduates can join existing projects or propose their own ideas with faculty mentorship.",
      category: "Getting Involved"
    }
  ], []);

  // Memoize publications data
  const publications = useMemo(() => [
    {
      id: "pub-001",
      title: "Interdisciplinary Approaches to Climate Prediction: Combining Meteorological Data with Agricultural Outcomes",
      authors: [
        { name: "Johnson, S.", affiliation: "Department of Computer Science", isPI: true },
        { name: "Chen, M.", affiliation: "Department of Environmental Science" },
        { name: "Williams, D.", affiliation: "Department of Agriculture" }
      ],
      journal: "Journal of Computational Environmental Science",
      year: 2023,
      doi: "10.1234/jces.2023.001",
      abstract: "This paper presents a novel computational framework that integrates meteorological data with agricultural outcomes to improve climate prediction models. Using machine learning techniques, we demonstrate improved accuracy in predicting how climate changes affect crop yields across different regions.",
      keywords: ["Climate Modeling", "Agricultural Data", "Machine Learning", "Interdisciplinary Methods"],
      image: "https://images.unsplash.com/photo-1586771107445-d3ca888129ce?auto=format&fit=crop&q=80&w=300",
      preprint: "https://arxiv.org/abs/2301.00123"
    },
    {
      id: "pub-002",
      title: "Computational Network Analysis of Social Media Influence on Public Health Behaviors",
      authors: [
        { name: "Rodriguez, E.", affiliation: "Department of Computer Science", isPI: true },
        { name: "Wilson, J.", affiliation: "Department of Sociology" },
        { name: "Park, L.", affiliation: "School of Public Health" }
      ],
      journal: "Computational Social Science Review",
      year: 2022,
      doi: "10.5678/cssr.2022.042",
      abstract: "This study applies computational network analysis to understand how information spreads through social media networks and influences public health decisions. We developed a novel algorithm that identifies key influence patterns in online discussions about vaccination and other public health measures.",
      keywords: ["Network Analysis", "Social Media", "Public Health", "Computational Social Science"],
      preprint: "https://arxiv.org/abs/2209.04567"
    }
  ], []);

  // Complete code snippet data with all three languages
  const codeSnippetTabs = useMemo(() => [
    {
      label: "Python",
      code: `# Interdisciplinary Drought Prediction & Economic Impact Analysis
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

def predict_drought_economic_impact(weather_data, soil_data, crop_prices):
    """
    Integrates meteorology, agronomy, and economics to predict drought impact
    
    This demonstrates true interdisciplinary computation by combining:
    - Climate science: precipitation, temperature, humidity patterns
    - Soil science: moisture content, soil composition, drainage
    - Agricultural science: crop growth models, yield equations
    - Economics: price volatility, market demand, economic losses
    """
    
    # === CLIMATE SCIENCE COMPONENT ===
    # Calculate Palmer Drought Severity Index (PDSI)
    weather_data['pdsi'] = calculate_pdsi(
        weather_data['precipitation'], 
        weather_data['temperature'],
        weather_data['potential_evapotranspiration']
    )
    
    # Compute Standardized Precipitation Index (SPI)
    weather_data['spi_3month'] = calculate_spi(weather_data['precipitation'], window=3)
    weather_data['spi_6month'] = calculate_spi(weather_data['precipitation'], window=6)
    
    # === SOIL & AGRICULTURAL SCIENCE COMPONENT ===
    # Calculate Crop Water Stress Index (CWSI)
    soil_data['cwsi'] = calculate_cwsi(
        soil_data['soil_moisture'],
        soil_data['field_capacity'],
        soil_data['wilting_point']
    )
    
    # === DATA INTEGRATION ===
    merged_data = pd.merge(weather_data, soil_data, 
                          on=['latitude', 'longitude', 'date'], how='inner')
    merged_data = pd.merge(merged_data, crop_prices, 
                          on=['crop_type', 'date'], how='left')
    
    # === FEATURE ENGINEERING ===
    features = ['pdsi', 'spi_3month', 'spi_6month', 'cwsi', 
               'soil_moisture', 'temperature', 'precipitation', 
               'crop_price', 'market_volatility']
    
    X = merged_data[features]
    y = merged_data['economic_loss_per_hectare']
    
    # === MACHINE LEARNING MODEL ===
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y, test_size=0.25, random_state=42, shuffle=False
    )
    
    model = RandomForestRegressor(n_estimators=200, max_depth=15, random_state=42)
    model.fit(X_train, y_train)
    
    predictions = model.predict(X_test)
    
    return {
        'model': model,
        'scaler': scaler,
        'predictions': predictions,
        'feature_importance': dict(zip(features, model.feature_importances_))
    }

def calculate_pdsi(precipitation, temperature, pet):
    """Palmer Drought Severity Index - meteorological drought indicator"""
    water_balance = precipitation - pet
    return np.cumsum(water_balance) / np.std(water_balance)

def calculate_spi(precipitation, window=3):
    """Standardized Precipitation Index - climate science standard"""
    rolling_precip = pd.Series(precipitation).rolling(window=window).sum()
    return (rolling_precip - rolling_precip.mean()) / rolling_precip.std()

def calculate_cwsi(soil_moisture, field_capacity, wilting_point):
    """Crop Water Stress Index - agricultural science metric"""
    available_water = field_capacity - wilting_point
    current_available = soil_moisture - wilting_point
    return 1 - (current_available / available_water)`,
      language: "python"
    },
    {
      label: "R",
      code: `# Interdisciplinary Drought Prediction & Economic Impact Analysis in R
library(tidyverse)
library(randomForest)
library(zoo)  # for rolling calculations
library(scales)  # for data preprocessing

predict_drought_economic_impact <- function(weather_data, soil_data, crop_prices) {
  # Integrates meteorology, agronomy, and economics to predict drought impact
  
  # === CLIMATE SCIENCE COMPONENT ===
  # Calculate Palmer Drought Severity Index (PDSI)
  weather_data <- weather_data %>%
    mutate(
      water_balance = precipitation - potential_evapotranspiration,
      pdsi = cumsum(water_balance) / sd(water_balance, na.rm = TRUE)
    )
  
  # Compute Standardized Precipitation Index (SPI)
  weather_data <- weather_data %>%
    arrange(date) %>%
    mutate(
      precip_3month = rollsum(precipitation, k = 3, fill = NA, align = "right"),
      precip_6month = rollsum(precipitation, k = 6, fill = NA, align = "right"),
      spi_3month = scale(precip_3month)[, 1],
      spi_6month = scale(precip_6month)[, 1]
    )
  
  # === SOIL & AGRICULTURAL SCIENCE COMPONENT ===
  # Calculate Crop Water Stress Index (CWSI)
  soil_data <- soil_data %>%
    mutate(
      available_water = field_capacity - wilting_point,
      current_available = soil_moisture - wilting_point,
      cwsi = 1 - (current_available / available_water),
      cwsi = pmax(0, pmin(1, cwsi))  # Bound between 0 and 1
    )
  
  # Estimate crop yield reduction using FAO crop-water production functions
  ky_values <- list(
    corn = list(vegetative = 0.4, flowering = 1.5, grain_filling = 0.5),
    wheat = list(vegetative = 0.4, flowering = 1.15, grain_filling = 0.4),
    soybeans = list(vegetative = 0.2, flowering = 0.8, grain_filling = 1.0)
  )
  
  # === DATA INTEGRATION ===
  merged_data <- weather_data %>%
    inner_join(soil_data, by = c("latitude", "longitude", "date")) %>%
    left_join(crop_prices, by = c("crop_type", "date"))
  
  # === FEATURE ENGINEERING ===
  feature_cols <- c(
    "pdsi", "spi_3month", "spi_6month", "cwsi", "soil_moisture", 
    "temperature", "precipitation", "crop_price", "market_volatility"
  )
  
  # Remove rows with missing values and scale features
  model_data <- merged_data %>%
    filter(complete.cases(.[feature_cols])) %>%
    mutate(across(all_of(feature_cols), scale))
  
  # === MACHINE LEARNING MODEL ===
  # Split data preserving temporal structure
  set.seed(42)
  n_train <- floor(0.75 * nrow(model_data))
  train_data <- model_data[1:n_train, ]
  test_data <- model_data[(n_train + 1):nrow(model_data), ]
  
  # Train Random Forest model
  formula_str <- paste("economic_loss_per_hectare ~", paste(feature_cols, collapse = " + "))
  model <- randomForest(
    formula = as.formula(formula_str),
    data = train_data,
    ntree = 200,
    importance = TRUE
  )
  
  predictions <- predict(model, test_data)
  feature_importance <- importance(model)[, 1]
  
  return(list(
    model = model,
    predictions = predictions,
    feature_importance = feature_importance
  ))
}`,
      language: "r"
    },
    {
      label: "Julia",
      code: `# Interdisciplinary Drought Prediction & Economic Impact Analysis in Julia
using DataFrames, Statistics
using MLJ, Random
using Pipe: @pipe

# Load machine learning models
RandomForestRegressor = @load RandomForestRegressor pkg=DecisionTree

function predict_drought_economic_impact(weather_data, soil_data, crop_prices)
    # Integrates meteorology, agronomy, and economics to predict drought impact
    
    # === CLIMATE SCIENCE COMPONENT ===
    # Calculate Palmer Drought Severity Index (PDSI)
    weather_data = @pipe weather_data |>
        transform(_, :water_balance => ByRow((p, pet) -> p - pet) => :water_balance;
                 renamecols=false) |>
        transform(_, :pdsi => ByRow(wb -> cumsum(wb) / std(wb)) => :pdsi;
                 renamecols=false)
    
    # Compute Standardized Precipitation Index (SPI)
    function rolling_sum(x, window)
        [i < window ? missing : sum(x[i-window+1:i]) for i in 1:length(x)]
    end
    
    weather_data = @pipe weather_data |>
        sort(_, :date) |>
        transform(_, 
            :precip_3month => ByRow(p -> rolling_sum(p, 3)) => :precip_3month,
            :precip_6month => ByRow(p -> rolling_sum(p, 6)) => :precip_6month;
            renamecols=false)
    
    # === SOIL & AGRICULTURAL SCIENCE COMPONENT ===
    function calculate_cwsi(soil_moisture, field_capacity, wilting_point)
        available_water = field_capacity - wilting_point
        current_available = soil_moisture - wilting_point
        cwsi = 1 - (current_available / available_water)
        return clamp(cwsi, 0.0, 1.0)
    end
    
    soil_data = transform(soil_data, 
        [:soil_moisture, :field_capacity, :wilting_point] => 
        ByRow(calculate_cwsi) => :cwsi;
        renamecols=false)
    
    # === DATA INTEGRATION ===
    merged_data = weather_data |>
        df -> innerjoin(df, soil_data, on=[:latitude, :longitude, :date]) |>
        df -> leftjoin(df, crop_prices, on=[:crop_type, :date])
    
    # === FEATURE ENGINEERING ===
    feature_cols = [
        :pdsi, :spi_3month, :spi_6month, :cwsi, :soil_moisture,
        :temperature, :precipitation, :crop_price, :market_volatility
    ]
    
    # Remove rows with missing values and standardize features
    model_data = dropmissing(merged_data, feature_cols)
    for col in feature_cols
        μ, σ = mean(model_data[!, col]), std(model_data[!, col])
        model_data[!, col] = (model_data[!, col] .- μ) ./ σ
    end
    
    # === MACHINE LEARNING MODEL ===
    Random.seed!(42)
    n_train = floor(Int, 0.75 * nrow(model_data))
    train_data = model_data[1:n_train, :]
    test_data = model_data[n_train+1:end, :]
    
    X_train = select(train_data, feature_cols)
    y_train = train_data[!, :economic_loss_per_hectare]
    X_test = select(test_data, feature_cols)
    
    model = RandomForestRegressor(n_trees=200, max_depth=15, rng=42)
    mach = machine(model, X_train, y_train)
    fit!(mach)
    
    predictions = predict(mach, X_test)
    
    return Dict(
        "model" => mach,
        "predictions" => predictions
    )
end`,
      language: "julia"
    }
  ], []);

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col bg-gradient-to-br from-gray-50 via-white to-blue-50/30">
        <SkipLink />
        <Header />
        
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Simplified Header Section */}
          <section className="bg-gradient-to-br from-blue-50/80 via-white to-purple-50/60 py-16 border-b border-gray-100">
            <div className="container mx-auto px-4 max-w-6xl">
              <div className="max-w-4xl mx-auto text-center">
                                 <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-700 text-sm font-medium mb-6">
                   <Network className="w-4 h-4 mr-2" />
                   <span className="font-mono">Research Center</span>
            </div>
                                 <h1 className="text-4xl md:text-5xl font-bold tracking-tight text-gray-900 mb-6 font-mono">
                  Center of Interdisciplinary Computation
                </h1>
                <div className="h-1 w-32 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mb-6"></div>
                <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                  Bridging computational science with diverse fields to address complex challenges through innovative research and collaboration.
                </p>
                                 <div className="inline-flex items-center px-6 py-3 bg-white/70 backdrop-blur border border-blue-200 rounded-lg font-mono text-sm">
                   <Code className="w-4 h-4 mr-2 text-blue-600" />
                   <code className="text-blue-700 font-mono">function solveInterdisciplinaryProblem(domain, method)</code>
                 </div>
              </div>
            </div>
          </section>

          {/* Main Content with Consistent Spacing */}
          <section className="py-16">
            <div className="container mx-auto px-4 max-w-6xl space-y-16">
              
              {/* Research Focus Areas */}
              <div>
                <div className="text-center mb-12">
                                     <h2 className="text-3xl font-bold text-gray-900 mb-4 font-mono">Research Focus Areas</h2>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    Our research spans multiple domains, connecting computational methods with real-world applications
                  </p>
                </div>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {researchAreas.map((area, index) => (
                    <UnifiedCard key={index} pattern="grid" className="hover:scale-[1.02] transition-transform duration-200">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="h-12 w-12 rounded-lg bg-blue-100 flex items-center justify-center">
                          {area.icon}
                        </div>
                                                 <h3 className="text-xl font-semibold text-gray-900 font-mono">{area.title}</h3>
                      </div>
                                             <p className="text-gray-600 leading-relaxed font-mono text-sm">{area.description}</p>
                    </UnifiedCard>
                  ))}
                </div>
              </div>
              
                             {/* Network Graph - Lazy Loaded */}
               <div>
                 <Suspense fallback={<LoadingSpinner />}>
                   <ForceDirectedGraph data={networkData} width={900} height={500} />
                 </Suspense>
               </div>

              {/* Timeline - Lazy Loaded */}
              <div>
                <Suspense fallback={<LoadingSpinner />}>
                  <ComputationalTimeline events={timelineEvents} />
                </Suspense>
              </div>

               {/* Domain Applications - Lazy Loaded */}
               <div>
                 <Suspense fallback={<LoadingSpinner />}>
                   <DomainApplications applications={domainApplications} />
                 </Suspense>
               </div>

               {/* Code Example - Lazy Loaded */}
               <div>
                 <div className="text-center mb-12">
                   <h2 className="text-3xl font-bold text-gray-900 mb-4 font-mono">Computational Approaches</h2>
                   <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
                     A real-world example demonstrating how computational methods integrate climate science, soil science, 
                     agronomy, and economics to predict drought impacts and economic losses.
                   </p>
                 </div>
                 <Suspense fallback={<LoadingSpinner />}>
                   <AnimatedCodeSnippet 
                     title="Drought Prediction & Economic Impact Analysis" 
                     code={codeSnippetTabs[0].code}
                     language="python"
                     typingSpeed={1}
                     startDelay={50}
                     tabs={codeSnippetTabs}
                   />
                 </Suspense>
               </div>

               {/* FAQ Section - Lazy Loaded */}
               <div>
                 <Suspense fallback={<LoadingSpinner />}>
                   <ResearchFAQ items={faqItems} />
                 </Suspense>
               </div>

              {/* About Section */}
              <UnifiedCard pattern="dots" className="text-center">
                                 <h2 className="text-3xl font-bold text-gray-900 mb-6 font-mono">About the Center</h2>
                <div className="max-w-4xl mx-auto space-y-6 text-lg text-gray-600 leading-relaxed">
                  <p>
                  The Center of Interdisciplinary Computation is dedicated to advancing computational approaches across traditional 
                  disciplinary boundaries. We focus on developing innovative computational methods and applying them to solve 
                  complex problems in various fields including agriculture, business, education, and beyond.
                </p>
                  <p>
                  By bringing together researchers from diverse backgrounds, we aim to foster collaborative innovation and 
                  develop solutions that leverage computational power to address real-world challenges.
                </p>
                <div className="pt-6 border-t border-gray-200 mt-8">
                  <p className="text-base text-gray-600">
                    For inquiries about research collaborations and opportunities:
                  </p>
                  <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700 font-medium">
                    <EMAIL>
                  </a>
                </div>
              </div>
              </UnifiedCard>

              {/* Application Process Section */}
              <div>
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4 font-mono">Join Our Research Community</h2>
                  <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                    The Center of Interdisciplinary Computation welcomes applications from faculty and researchers 
                    passionate about computational approaches to solving complex, real-world problems.
                  </p>
                </div>

                <div className="grid lg:grid-cols-2 gap-8">
                  {/* Faculty/Researcher Application */}
                  <UnifiedCard pattern="dots" className="h-full">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center mx-auto mb-4">
                        <FileText className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-2 font-mono">Faculty & Researchers</h3>
                      <p className="text-gray-600">Application Requirements</p>
                    </div>

                    <div className="space-y-6">
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Required Submissions</h4>
                        <div className="space-y-4">
                          <div className="flex items-start gap-3">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-1">
                              <span className="text-blue-600 font-bold text-sm">1</span>
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900">Detailed Research Proposal</h5>
                              <p className="text-sm text-gray-600">Comprehensive project description with methodology and expected outcomes</p>
                            </div>
                          </div>
                          
                          <div className="flex items-start gap-3">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-1">
                              <span className="text-blue-600 font-bold text-sm">2</span>
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900">5-Year Vision Statement</h5>
                              <p className="text-sm text-gray-600">Long-term research trajectory and interdisciplinary impact goals</p>
                            </div>
                          </div>
                          
                          <div className="flex items-start gap-3">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-1">
                              <span className="text-blue-600 font-bold text-sm">3</span>
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900">Meticulously Crafted Research Plan</h5>
                              <p className="text-sm text-gray-600">Detailed timeline, milestones, and computational methodologies</p>
                            </div>
                          </div>
                          
                          <div className="flex items-start gap-3">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-1">
                              <span className="text-blue-600 font-bold text-sm">4</span>
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900">Funding Strategy</h5>
                              <p className="text-sm text-gray-600">Plan to secure external funding after the first year of CIC support</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </UnifiedCard>

                  {/* CIC Support & Student Program */}
                  <UnifiedCard pattern="grid" className="h-full">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mx-auto mb-4">
                        <Users className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-2 font-mono">CIC Support Package</h3>
                      <p className="text-gray-600">What We Provide</p>
                    </div>

                    <div className="space-y-6">
                      {/* CIC Support */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <Award className="h-5 w-5 text-green-600" />
                          For Selected Researchers
                        </h4>
                        <div className="bg-green-50 rounded-lg p-4 mb-4">
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-700">Student Support</span>
                              <span className="text-sm font-bold text-green-700">2 Students × 1 Year</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-700">Monthly Stipend</span>
                              <span className="text-sm font-bold text-green-700">Rs 10,000 - 15,000</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-700">Meals Provided</span>
                              <span className="text-sm font-bold text-green-700">Lunch & Snacks</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Student Requirements */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <GraduationCap className="h-5 w-5 text-blue-600" />
                          Student Commitments
                        </h4>
                        <div className="bg-blue-50 rounded-lg p-4">
                          <div className="flex items-start gap-3">
                            <div className="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mt-1">
                              <span className="text-white font-bold text-xs">!</span>
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900 mb-1">Education Outreach Requirement</h5>
                              <p className="text-sm text-gray-600">
                                <strong>15% of student time</strong> must be dedicated to education outreach activities, 
                                including workshops, seminars, and community engagement programs.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Application Process */}
                      <div className="pt-4 border-t border-gray-200">
                        <div className="text-center">
                          <p className="text-sm text-gray-600 mb-4">
                            Ready to join our interdisciplinary research community?
                          </p>
                          <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg font-mono">
                            Submit Application <ArrowRight className="ml-2 h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </UnifiedCard>
                </div>

                {/* Additional Information */}
                <UnifiedCard pattern="none" className="bg-gradient-to-r from-gray-50 to-blue-50 border-gray-200 mt-8">
                  <div className="text-center">
                    <h4 className="text-xl font-bold text-gray-900 mb-4 font-mono">Application Timeline & Process</h4>
                    <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                      <div className="text-center">
                        <div className="w-12 h-12 rounded-full bg-blue-600 text-white flex items-center justify-center mx-auto mb-3 font-bold">
                          1
                        </div>
                        <h5 className="font-semibold text-gray-900 mb-2">Submit Proposal</h5>
                        <p className="text-sm text-gray-600">
                          Complete application with all required documents
                        </p>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 rounded-full bg-blue-600 text-white flex items-center justify-center mx-auto mb-3 font-bold">
                          2
                        </div>
                        <h5 className="font-semibold text-gray-900 mb-2">Review Process</h5>
                        <p className="text-sm text-gray-600">
                          Evaluation by interdisciplinary review committee
                        </p>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 rounded-full bg-blue-600 text-white flex items-center justify-center mx-auto mb-3 font-bold">
                          3
                        </div>
                        <h5 className="font-semibold text-gray-900 mb-2">Selection & Start</h5>
                        <p className="text-sm text-gray-600">
                          Begin research with full CIC support package
                        </p>
                      </div>
                    </div>
                  </div>
                </UnifiedCard>
              </div>
              
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
} 