import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { TimelineManager } from "@/components/faculty/TimelineManager"

async function getFacultyTimeline(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      timeline: {
        orderBy: { year: 'desc' }
      }
    }
  })
  return facultyProfile?.timeline || []
}

export default async function TimelinePage() {
  const user = await requireFaculty()
  const timeline = await getFacultyTimeline(user.id)

  return <TimelineManager initialTimeline={timeline} />
}
