import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { ResearchAreasManager } from "@/components/faculty/ResearchAreasManager"

async function getFacultyResearchAreas(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      researchAreas: {
        orderBy: { createdAt: 'desc' }
      }
    }
  })
  return facultyProfile?.researchAreas || []
}

export default async function ResearchAreasPage() {
  const user = await requireFaculty()
  const researchAreas = await getFacultyResearchAreas(user.id)

  return <ResearchAreasManager initialResearchAreas={researchAreas} />
}
