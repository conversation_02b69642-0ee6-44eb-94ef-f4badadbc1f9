import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { DashboardUI } from "@/components/faculty/DashboardUI"

async function getFacultyDashboardData(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      user: {
        include: {
          profile: true
        }
      },
      department: true,
      publications: {
        take: 5,
        orderBy: { year: 'desc' }
      },
      researchProjects: {
        take: 3,
        orderBy: { createdAt: 'desc' }
      },
      classes: {
        take: 3,
        include: {
          course: true
        },
        orderBy: { createdAt: 'desc' }
      },
      posts: {
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          category: {
            select: {
              name: true,
              color: true
            }
          }
        }
      },
      officeHours: {
        where: { isActive: true },
        take: 5
      },
      industryExperience: {
        take: 3,
        orderBy: { startDate: 'desc' }
      },
      skills: {
        take: 10,
        orderBy: [
          { category: 'asc' },
          { skillName: 'asc' }
        ]
      }
    }
  })

  const upcomingBookings = await prisma.officeHourBooking.findMany({
    where: {
      officeHour: {
        facultyId: facultyProfile?.id
      },
      date: {
        gte: new Date()
      },
      status: 'CONFIRMED'
    },
    include: {
      student: {
        select: {
          name: true,
          email: true
        }
      }
    },
    take: 5,
    orderBy: { date: 'asc' }
  })

  return {
    facultyProfile,
    upcomingBookings
  }
}

export default async function FacultyDashboard() {
  const user = await requireFaculty()
  const { facultyProfile, upcomingBookings } = await getFacultyDashboardData(user.id)

  if (!facultyProfile) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Faculty Profile Not Found</CardTitle>
            <CardDescription>
              Your faculty profile hasn't been set up yet. Please contact the administrator.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return <DashboardUI facultyProfile={facultyProfile} upcomingBookings={upcomingBookings} />
}
