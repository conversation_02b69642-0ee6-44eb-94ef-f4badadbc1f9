import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { SkillsManager } from "@/components/faculty/SkillsManager"

async function getFacultySkills(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      skills: {
        orderBy: { createdAt: 'desc' }
      }
    }
  })
  return facultyProfile?.skills || []
}

export default async function SkillsPage() {
  const user = await requireFaculty()
  const skills = await getFacultySkills(user.id)

  return <SkillsManager initialSkills={skills} />
}
