import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { EducationManager } from "@/components/faculty/EducationManager"

async function getFacultyEducation(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      education: {
        orderBy: { year: 'desc' }
      }
    }
  })
  return facultyProfile?.education || []
}

export default async function EducationPage() {
  const user = await requireFaculty()
  const education = await getFacultyEducation(user.id)

  return <EducationManager initialEducation={education} />
}
