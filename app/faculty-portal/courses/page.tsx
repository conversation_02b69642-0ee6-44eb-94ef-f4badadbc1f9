import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { CoursesManager } from "@/components/faculty/CoursesManager"

async function getFacultyCourses(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      classes: {
        include: {
          course: {
            include: {
              department: {
                select: {
                  name: true,
                  slug: true
                }
              }
            }
          }
        },
        orderBy: [
          { year: 'desc' },
          { semester: 'desc' }
        ]
      }
    }
  })
  return facultyProfile?.classes || []
}

export default async function CoursesPage() {
  const user = await requireFaculty()
  const courses = await getFacultyCourses(user.id)

  return <CoursesManager initialCourses={courses} />
}
