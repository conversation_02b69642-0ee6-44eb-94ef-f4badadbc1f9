import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { OfficeHoursManager } from "@/components/faculty/OfficeHoursManager"

async function getFacultyOfficeHours(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      officeHours: {
        where: { isActive: true },
        orderBy: [
          { dayOfWeek: 'asc' },
          { startTime: 'asc' }
        ]
      }
    }
  })
  return facultyProfile?.officeHours || []
}

async function getUpcomingBookings(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId }
  })

  if (!facultyProfile) return []

  const bookings = await prisma.officeHourBooking.findMany({
    where: {
      officeHour: {
        facultyId: facultyProfile.id
      },
      date: {
        gte: new Date()
      },
      status: {
        in: ['PENDING', 'CONFIRMED']
      }
    },
    include: {
      student: {
        select: {
          name: true,
          email: true
        }
      },
      officeHour: {
        select: {
          dayOfWeek: true,
          startTime: true,
          endTime: true,
          location: true
        }
      }
    },
    orderBy: { date: 'asc' },
    take: 10
  })

  return bookings
}

export default async function OfficeHoursPage() {
  const user = await requireFaculty()
  const officeHours = await getFacultyOfficeHours(user.id)
  const upcomingBookings = await getUpcomingBookings(user.id)

  return (
    <OfficeHoursManager 
      initialOfficeHours={officeHours} 
      upcomingBookings={upcomingBookings}
    />
  )
}
