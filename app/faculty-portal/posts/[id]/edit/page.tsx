import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { FacultyPostForm } from "@/components/faculty/FacultyPostForm"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { notFound } from "next/navigation"

async function getPostData(postId: string, userId: string) {
  const [post, categories] = await Promise.all([
    prisma.post.findUnique({
      where: { 
        id: postId,
        authorId: userId // Faculty can only edit their own posts
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                slug: true
              }
            }
          }
        }
      }
    }),
    prisma.category.findMany({
      orderBy: { name: 'asc' }
    })
  ])

  return { post, categories }
}

interface PageProps {
  params: Promise<{ id: string }>
}

export default async function EditFacultyPostPage({ params }: PageProps) {
  // Require faculty access
  const user = await requireFaculty()
  
  const { id } = await params
  const { post, categories } = await getPostData(id, user.id)

  if (!post) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/faculty-portal/posts">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to My Posts
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Post</h1>
          <p className="text-gray-600">Update your blog post or article</p>
        </div>
      </div>

      {/* Post Form */}
      <Card>
        <CardHeader>
          <CardTitle>Post Details</CardTitle>
          <CardDescription>
            Update the information below to modify your post
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FacultyPostForm 
            categories={categories}
            mode="edit"
            post={post}
          />
        </CardContent>
      </Card>
    </div>
  )
}
