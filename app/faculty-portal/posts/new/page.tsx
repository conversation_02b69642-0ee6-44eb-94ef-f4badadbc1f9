import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { FacultyPostForm } from "@/components/faculty/FacultyPostForm"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"

async function getFormData() {
  const categories = await prisma.category.findMany({
    orderBy: { name: 'asc' }
  })

  return { categories }
}

export default async function NewFacultyPostPage() {
  // Require faculty access
  await requireFaculty()
  
  const { categories } = await getFormData()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/faculty-portal/posts">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to My Posts
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create New Post</h1>
          <p className="text-gray-600">Share your knowledge and insights with the community</p>
        </div>
      </div>

      {/* Post Form */}
      <Card>
        <CardHeader>
          <CardTitle>Post Details</CardTitle>
          <CardDescription>
            Fill in the information below to create your post
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FacultyPostForm 
            categories={categories}
            mode="create"
          />
        </CardContent>
      </Card>
    </div>
  )
}
