import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { IndustryExperienceManager } from "@/components/faculty/IndustryExperienceManager"

async function getFacultyIndustryExperience(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      industryExperience: {
        orderBy: { startDate: 'desc' }
      }
    }
  })
  return facultyProfile?.industryExperience || []
}

export default async function IndustryExperiencePage() {
  const user = await requireFaculty()
  const industryExperience = await getFacultyIndustryExperience(user.id)

  return <IndustryExperienceManager initialExperience={industryExperience} />
}
