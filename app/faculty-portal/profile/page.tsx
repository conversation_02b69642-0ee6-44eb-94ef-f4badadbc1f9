import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { Save, User, Clock, BookOpen, Calendar, MapPin, ExternalLink } from "lucide-react"
import { FacultyProfileForm } from "@/components/faculty/FacultyProfileForm"
import { IndustryExperienceForm } from "@/components/faculty/IndustryExperienceForm"
import { SkillsForm } from "@/components/faculty/SkillsForm"
import Link from "next/link"

async function getFacultyProfile(userId: string) {
  return await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      user: {
        include: {
          profile: true
        }
      },
      department: true,
      industryExperience: true,
      skills: true,
      officeHours: {
        where: { isActive: true },
        orderBy: [
          { dayOfWeek: 'asc' },
          { startTime: 'asc' }
        ]
      },
      classes: {
        where: {
          status: 'current'
        },
        include: {
          course: {
            select: {
              code: true,
              name: true,
              credits: true
            }
          }
        },
        take: 5
      }
    }
  })
}

async function getDepartments() {
  return await prisma.department.findMany({
    orderBy: {
      name: 'asc'
    }
  })
}

export default async function FacultyProfilePage() {
  const user = await requireFaculty()
  const facultyProfile = await getFacultyProfile(user.id)
  const departments = await getDepartments()

  if (!facultyProfile) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Faculty Profile Not Found</CardTitle>
            <CardDescription>
              Your faculty profile hasn't been set up yet. Please contact the administrator.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
        <p className="text-gray-600">Manage your faculty profile information</p>
      </div>

      {/* Interactive Profile Form */}
      <FacultyProfileForm
        profile={{
          id: facultyProfile.id,
          bio: facultyProfile.bio,
          title: facultyProfile.title,
          office: facultyProfile.officeLocation,
          officeHours: null, // TODO: Add office hours field to schema
          website: facultyProfile.websiteUrl,
          phone: facultyProfile.user.profile?.phone,
          researchInterests: null // TODO: Add research interests field to schema
        }}
        userEmail={facultyProfile.user.email}
        userName={facultyProfile.user.name || ''}
        currentAvatar={facultyProfile.user.profile?.avatarUrl}
      />

      {/* Industry Experience Form */}
      <div id="industry-experience">
        <IndustryExperienceForm 
          facultyId={facultyProfile.id}
          initialExperiences={facultyProfile.industryExperience}
        />
      </div>

      {/* Skills Form */}
      <div id="skills">
        <SkillsForm
          facultyId={facultyProfile.id}
          initialSkills={facultyProfile.skills}
        />
      </div>

      {/* Office Hours Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                Office Hours
              </CardTitle>
              <CardDescription>
                Your current weekly office hours schedule
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href="/faculty-portal/office-hours">
                <ExternalLink className="w-4 h-4 mr-1" />
                Manage
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {facultyProfile.officeHours && facultyProfile.officeHours.length > 0 ? (
            <div className="space-y-3">
              {facultyProfile.officeHours.slice(0, 3).map((hour) => {
                const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
                const formatTime = (time: string) => {
                  const [h, m] = time.split(':')
                  const hour = parseInt(h)
                  const ampm = hour >= 12 ? 'PM' : 'AM'
                  const hour12 = hour % 12 || 12
                  return `${hour12}:${m} ${ampm}`
                }

                return (
                  <div key={hour.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-4 h-4 text-blue-600" />
                      <span className="font-medium">{days[hour.dayOfWeek]}</span>
                      <span className="text-sm text-gray-600">
                        {formatTime(hour.startTime)} - {formatTime(hour.endTime)}
                      </span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="w-4 h-4 mr-1" />
                      {hour.location}
                    </div>
                  </div>
                )
              })}
              {facultyProfile.officeHours.length > 3 && (
                <p className="text-sm text-gray-500 text-center">
                  +{facultyProfile.officeHours.length - 3} more office hours
                </p>
              )}
            </div>
          ) : (
            <div className="text-center py-6">
              <Clock className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 text-sm">No office hours set up yet</p>
              <Button variant="outline" size="sm" className="mt-2" asChild>
                <Link href="/faculty-portal/office-hours">
                  Set Up Office Hours
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Current Courses Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <BookOpen className="w-5 h-5 mr-2" />
                Current Courses
              </CardTitle>
              <CardDescription>
                Your current teaching assignments
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href="/faculty-portal/courses">
                <ExternalLink className="w-4 h-4 mr-1" />
                View All
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {facultyProfile.classes && facultyProfile.classes.length > 0 ? (
            <div className="space-y-3">
              {facultyProfile.classes.map((courseClass) => (
                <div key={courseClass.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <BookOpen className="w-4 h-4 text-green-600" />
                    <div>
                      <span className="font-medium">
                        {courseClass.course.code}: {courseClass.course.name}
                      </span>
                      {courseClass.section && (
                        <Badge variant="outline" className="ml-2 text-xs">
                          Section {courseClass.section}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    {courseClass.course.credits} credits
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <BookOpen className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 text-sm">No current course assignments</p>
              <p className="text-gray-400 text-xs mt-1">
                Contact your department administrator for course assignments
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Completion</CardTitle>
          <CardDescription>
            Complete your profile to improve visibility
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Basic Information</span>
              <span className="text-sm font-medium text-green-600">Complete</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Biography</span>
              <span className="text-sm font-medium text-yellow-600">
                {facultyProfile.bio ? 'Complete' : 'Incomplete'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Contact Information</span>
              <span className="text-sm font-medium text-green-600">Complete</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Professional Links</span>
              <span className="text-sm font-medium text-yellow-600">
                {facultyProfile.websiteUrl || facultyProfile.scholarId ? 'Complete' : 'Incomplete'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Industry Experience</span>
              <span className="text-sm font-medium text-yellow-600">
                {facultyProfile.industryExperience.length > 0 ? 'Complete' : 'Incomplete'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Skills</span>
              <span className="text-sm font-medium text-yellow-600">
                {facultyProfile.skills.length > 0 ? 'Complete' : 'Incomplete'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Office Hours</span>
              <span className="text-sm font-medium text-yellow-600">
                {facultyProfile.officeHours && facultyProfile.officeHours.length > 0 ? 'Complete' : 'Incomplete'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Course Assignments</span>
              <span className="text-sm font-medium text-blue-600">
                {facultyProfile.classes && facultyProfile.classes.length > 0 ? `${facultyProfile.classes.length} courses` : 'No assignments'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p>• Your profile information will be displayed on the public faculty directory</p>
            <p>• Make sure your biography is professional and highlights your expertise</p>
            <p>• Adding your Google Scholar ID helps showcase your research impact</p>
            <p>• Include industry experience to demonstrate practical applications of your research</p>
            <p>• List key skills to help students understand your areas of expertise</p>
            <p>• Contact the administrator if you need to change your email or department</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
