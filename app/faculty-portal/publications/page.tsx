import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { PublicationsManager } from "@/components/faculty/PublicationsManager"

async function getFacultyPublications(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      publications: {
        orderBy: { year: 'desc' }
      }
    }
  })
  return facultyProfile?.publications || []
}

export default async function PublicationsPage() {
  const user = await requireFaculty()
  const publications = await getFacultyPublications(user.id)

  return <PublicationsManager initialPublications={publications} />
}
