'use client'

import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle, Home, RefreshCw } from 'lucide-react'

const errorMessages: Record<string, { title: string; description: string; action?: string }> = {
  Configuration: {
    title: 'Server Configuration Error',
    description: 'There is a problem with the server configuration. Please contact support.',
  },
  AccessDenied: {
    title: 'Access Denied',
    description: 'You do not have permission to sign in.',
    action: 'Please contact your administrator if you believe this is an error.',
  },
  Verification: {
    title: 'Verification Error',
    description: 'The verification token has expired or has already been used.',
    action: 'Please request a new verification email.',
  },
  Default: {
    title: 'Authentication Error',
    description: 'An error occurred during authentication.',
    action: 'Please try again or contact support if the problem persists.',
  },
  CLIENT_FETCH_ERROR: {
    title: 'Connection Error',
    description: 'Unable to connect to the authentication service. This is usually temporary.',
    action: 'Please check your internet connection and try again.',
  },
}

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error') || 'Default'
  
  const errorInfo = errorMessages[error] || errorMessages.Default

  const handleRetry = () => {
    // Clear any cached auth state and redirect to sign in
    if (typeof window !== 'undefined') {
      localStorage.removeItem('nextauth.message')
      window.location.href = '/auth/signin'
    }
  }

  const handleRefresh = () => {
    if (typeof window !== 'undefined') {
      window.location.reload()
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <img
            src="/UC_logo.svg"
            alt="Ullens College Logo"
            className="mx-auto h-24 w-24"
          />
          <h1 className="mt-6 text-3xl font-bold text-gray-900">
            Authentication Error
          </h1>
        </div>

        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              {errorInfo.title}
            </CardTitle>
            <CardDescription className="text-gray-600">
              {errorInfo.description}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            {errorInfo.action && (
              <Alert>
                <AlertDescription>
                  {errorInfo.action}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-3">
              {error === 'CLIENT_FETCH_ERROR' ? (
                <Button 
                  onClick={handleRefresh} 
                  className="w-full"
                  variant="default"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh Page
                </Button>
              ) : (
                <Button 
                  onClick={handleRetry} 
                  className="w-full"
                  variant="default"
                >
                  Try Again
                </Button>
              )}

              <Button 
                asChild 
                variant="outline" 
                className="w-full"
              >
                <Link href="/">
                  <Home className="mr-2 h-4 w-4" />
                  Return to Homepage
                </Link>
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && (
              <div className="mt-6 p-4 bg-gray-100 rounded-md">
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  Debug Information (Development Only)
                </h4>
                <p className="text-xs text-gray-600 font-mono">
                  Error Code: {error}
                </p>
                <p className="text-xs text-gray-600 mt-1">
                  Check the browser console and server logs for more details.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-sm text-gray-600">
            Need help?{' '}
            <Link 
              href="/contact" 
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
