'use client'

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Calculator, DollarSign, TrendingUp, BarChart, Target, Search, BookOpen, ChevronRight, Save, Download, RefreshCw, Loader2, Heart, Star, ChevronDown, ChevronUp, Filter, BookmarkPlus, Sparkles, Award, TrendingDown, Clock, Zap } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

// Financial Literacy Data
const financialScenarios = [
  {
    id: 1,
    title: "Starting a Tea Shop in Kathmandu",
    description: "Calculate the investment needed and monthly profits for a small tea shop",
    initialInvestment: 500000,
    monthlyRevenue: 80000,
    monthlyExpenses: 45000,
    category: "Business Planning"
  },
  {
    id: 2,
    title: "University Student Budget",
    description: "Plan monthly expenses for a university student in Nepal",
    monthlyRevenue: 25000,
    monthlyExpenses: 23000,
    initialInvestment: 0,
    category: "Personal Finance"
  },
  {
    id: 3,
    title: "Rice Export Business",
    description: "Calculate profits from exporting Nepali rice to international markets",
    initialInvestment: 2000000,
    monthlyRevenue: 450000,
    monthlyExpenses: 320000,
    category: "Agriculture Business"
  }
];

// Business Model Canvas Blocks
const canvasBlocks = [
  { id: 'keyPartners', title: 'Key Partners', color: 'bg-orange-50 border-orange-200', description: 'Who are your key partners and suppliers?' },
  { id: 'keyActivities', title: 'Key Activities', color: 'bg-blue-50 border-blue-200', description: 'What key activities does your value proposition require?' },
  { id: 'keyResources', title: 'Key Resources', color: 'bg-green-50 border-green-200', description: 'What key resources does your value proposition require?' },
  { id: 'valueProposition', title: 'Value Proposition', color: 'bg-red-50 border-red-200', description: 'What value do you deliver to customers?' },
  { id: 'customerRelationships', title: 'Customer Relationships', color: 'bg-purple-50 border-purple-200', description: 'What type of relationship does each customer segment expect?' },
  { id: 'channels', title: 'Channels', color: 'bg-yellow-50 border-yellow-200', description: 'Through which channels do you reach your customers?' },
  { id: 'customerSegments', title: 'Customer Segments', color: 'bg-pink-50 border-pink-200', description: 'For whom are you creating value?' },
  { id: 'costStructure', title: 'Cost Structure', color: 'bg-gray-50 border-gray-200', description: 'What are the most important costs in your business model?' },
  { id: 'revenueStreams', title: 'Revenue Streams', color: 'bg-emerald-50 border-emerald-200', description: 'For what value are customers willing to pay?' }
];

// Startup Dictionary Terms
const startupTerms = [
  {
    term: "MVP (Minimum Viable Product)",
    definition: "A basic version of a product with just enough features to satisfy early customers and provide feedback for future development.",
    example: "A food delivery app starting with just basic ordering and delivery features before adding ratings, promotions, etc.",
    category: "Product Development",
    difficulty: "Beginner",
    relatedTerms: ["Product-Market Fit", "Iteration", "User Feedback"],
    nepaliContext: "Essential for startups in Nepal to test ideas with minimal investment before full development."
  },
  {
    term: "Bootstrapping",
    definition: "Building a business using personal savings or revenue from the business itself, without external funding.",
    example: "Starting a handicraft export business in Nepal using personal savings and reinvesting profits to grow.",
    category: "Funding",
    difficulty: "Beginner",
    relatedTerms: ["Self-funded", "Revenue-based Growth", "Organic Growth"],
    nepaliContext: "Very common approach in Nepal due to limited access to venture capital and angel investors."
  },
  {
    term: "Product-Market Fit",
    definition: "When your product satisfies a strong market demand and customers are willing to pay for it.",
    example: "A mobile banking app that perfectly serves Nepal's unbanked rural population needs.",
    category: "Strategy",
    difficulty: "Intermediate",
    relatedTerms: ["MVP", "Customer Validation", "Market Research"],
    nepaliContext: "Critical for success in Nepal's diverse market segments with varying needs and purchasing power."
  },
  {
    term: "Burn Rate",
    definition: "The rate at which a startup spends money, usually measured monthly.",
    example: "If your startup spends NPR 200,000 per month, that's your burn rate.",
    category: "Finance",
    difficulty: "Beginner",
    relatedTerms: ["Runway", "Cash Flow", "Unit Economics"],
    nepaliContext: "Especially important to monitor in Nepal where funding rounds are smaller and less frequent."
  },
  {
    term: "Pivot",
    definition: "Changing business direction while keeping one foot in what you've learned.",
    example: "A restaurant moving to delivery-only during COVID-19 pandemic.",
    category: "Strategy",
    difficulty: "Intermediate",
    relatedTerms: ["Business Model", "Market Validation", "Adaptability"],
    nepaliContext: "Many Nepali businesses successfully pivoted during COVID-19, showing the importance of flexibility."
  },
  {
    term: "Angel Investor",
    definition: "A wealthy individual who invests their own money in early-stage startups.",
    example: "A successful Nepali businessman investing NPR 5 million in a tech startup.",
    category: "Funding",
    difficulty: "Intermediate",
    relatedTerms: ["Seed Funding", "Equity", "Due Diligence"],
    nepaliContext: "Growing presence in Nepal with successful entrepreneurs becoming angel investors for the next generation."
  },
  {
    term: "Scalability",
    definition: "The ability of a business to grow and handle increased demand without proportional increases in costs.",
    example: "A mobile app that can serve 1000 or 100,000 users with similar infrastructure costs.",
    category: "Growth",
    difficulty: "Advanced",
    relatedTerms: ["Unit Economics", "Technology Stack", "Business Model"],
    nepaliContext: "Key consideration for tech startups looking to expand beyond Nepal's domestic market."
  },
  {
    term: "Customer Acquisition Cost (CAC)",
    definition: "The cost of acquiring a new customer, including marketing and sales expenses.",
    example: "If you spend NPR 10,000 on marketing and get 50 new customers, your CAC is NPR 200.",
    category: "Marketing",
    difficulty: "Intermediate",
    relatedTerms: ["LTV", "Unit Economics", "Marketing Funnel"],
    nepaliContext: "Critical metric for startups in Nepal where marketing budgets are typically constrained."
  },
  {
    term: "Lifetime Value (LTV)",
    definition: "The total revenue a business can expect from a single customer account throughout their relationship.",
    example: "A telecom customer paying NPR 1,000/month for 3 years has an LTV of NPR 36,000.",
    category: "Marketing",
    difficulty: "Intermediate",
    relatedTerms: ["CAC", "Customer Retention", "Churn Rate"],
    nepaliContext: "Helps Nepali businesses understand the long-term value of customer relationships and loyalty programs."
  },
  {
    term: "Venture Capital (VC)",
    definition: "Investment in startups and small businesses that are believed to have long-term growth potential.",
    example: "A VC firm investing NPR 50 million in a Nepali fintech startup for 20% equity.",
    category: "Funding",
    difficulty: "Advanced",
    relatedTerms: ["Series A", "Due Diligence", "Board of Directors"],
    nepaliContext: "Emerging ecosystem in Nepal with increasing interest from regional and international VCs."
  },
  {
    term: "Seed Funding",
    definition: "Initial funding used to begin developing a business idea or startup company.",
    example: "Raising NPR 2 million to develop a prototype and validate the market for an agricultural app.",
    category: "Funding",
    difficulty: "Beginner",
    relatedTerms: ["Angel Investor", "Pre-seed", "Series A"],
    nepaliContext: "Often the first external funding round for Nepali startups, typically ranging from NPR 0.5-5 million."
  },
  {
    term: "Unicorn",
    definition: "A privately held startup company valued at over $1 billion.",
    example: "Gojek from Indonesia became a unicorn by solving transportation and logistics challenges across Southeast Asia.",
    category: "Valuation",
    difficulty: "Advanced",
    relatedTerms: ["Valuation", "IPO", "Decacorn"],
    nepaliContext: "Nepal doesn't have unicorns yet, but several startups are working toward becoming the first."
  },
  {
    term: "Churn Rate",
    definition: "The percentage of customers who stop using your service during a given time period.",
    example: "If you start with 100 customers and 5 stop using your service this month, your monthly churn is 5%.",
    category: "Metrics",
    difficulty: "Intermediate",
    relatedTerms: ["Customer Retention", "LTV", "Customer Success"],
    nepaliContext: "Critical for subscription-based businesses in Nepal like streaming services and SaaS platforms."
  },
  {
    term: "Freemium Model",
    definition: "A business model where basic services are provided free of charge while premium features require payment.",
    example: "Offering basic accounting software for free but charging for advanced features like inventory management.",
    category: "Business Model",
    difficulty: "Intermediate",
    relatedTerms: ["Monetization", "User Acquisition", "Conversion Rate"],
    nepaliContext: "Popular model for software startups in Nepal to build user base before monetizing."
  },
  {
    term: "B2B (Business to Business)",
    definition: "A business model where companies sell products or services to other businesses rather than consumers.",
    example: "A Nepali software company providing inventory management systems to retail stores.",
    category: "Business Model",
    difficulty: "Beginner",
    relatedTerms: ["B2C", "Enterprise Sales", "SaaS"],
    nepaliContext: "Growing segment in Nepal as businesses digitize and need specialized software solutions."
  },
  {
    term: "B2C (Business to Consumer)",
    definition: "A business model where companies sell products or services directly to individual consumers.",
    example: "An e-commerce platform selling clothes directly to customers in Kathmandu.",
    category: "Business Model",
    difficulty: "Beginner",
    relatedTerms: ["B2B", "Consumer Market", "Retail"],
    nepaliContext: "Most common business model in Nepal, especially in retail and service sectors."
  },
  {
    term: "SaaS (Software as a Service)",
    definition: "A software distribution model where applications are hosted by a service provider and made available over the internet.",
    example: "A cloud-based restaurant management system that Nepal restaurants access via monthly subscription.",
    category: "Technology",
    difficulty: "Intermediate",
    relatedTerms: ["Cloud Computing", "Subscription Model", "Recurring Revenue"],
    nepaliContext: "Growing adoption in Nepal as internet infrastructure improves and businesses embrace digital transformation."
  },
  {
    term: "Disruptive Innovation",
    definition: "Innovation that creates a new market and value network, displacing established market leaders.",
    example: "Digital payment systems like eSewa disrupting traditional banking in Nepal.",
    category: "Innovation",
    difficulty: "Advanced",
    relatedTerms: ["Innovation", "Market Disruption", "Blue Ocean"],
    nepaliContext: "Opportunities exist in traditional sectors like agriculture, education, and healthcare in Nepal."
  },
  {
    term: "Lean Startup",
    definition: "A methodology for developing businesses and products that aims to shorten product development cycles.",
    example: "Building a simple version of a delivery app, testing with 50 customers, then iterating based on feedback.",
    category: "Methodology",
    difficulty: "Intermediate",
    relatedTerms: ["MVP", "Build-Measure-Learn", "Pivot"],
    nepaliContext: "Particularly relevant for Nepali entrepreneurs with limited resources who need to validate quickly."
  },
  {
    term: "Exit Strategy",
    definition: "A plan for how business owners and investors will leave a business while maximizing their return.",
    example: "Planning to sell a successful e-commerce startup to a larger company after 5 years of growth.",
    category: "Strategy",
    difficulty: "Advanced",
    relatedTerms: ["IPO", "Acquisition", "Merger"],
    nepaliContext: "Important consideration for startups seeking international investment or planning regional expansion."
  },
  {
    term: "Equity",
    definition: "Ownership stake in a company, typically expressed as a percentage.",
    example: "An investor putting NPR 10 million into a startup for 20% equity ownership.",
    category: "Finance",
    difficulty: "Intermediate",
    relatedTerms: ["Valuation", "Dilution", "Cap Table"],
    nepaliContext: "Understanding equity is crucial for Nepali entrepreneurs when negotiating with investors."
  },
  {
    term: "Valuation",
    definition: "The process of determining the current worth of a company or asset.",
    example: "A two-year-old fintech startup being valued at NPR 100 million based on revenue and growth projections.",
    category: "Finance",
    difficulty: "Advanced",
    relatedTerms: ["Equity", "Due Diligence", "Financial Modeling"],
    nepaliContext: "Challenging in Nepal due to limited comparable companies and different market dynamics."
  },
  {
    term: "Incubator",
    definition: "Organizations that provide support and resources to early-stage startups.",
    example: "Startup incubators in Kathmandu providing mentorship, workspace, and initial funding to tech startups.",
    category: "Ecosystem",
    difficulty: "Beginner",
    relatedTerms: ["Accelerator", "Mentorship", "Startup Ecosystem"],
    nepaliContext: "Growing number of incubators in Nepal supporting entrepreneurship development."
  },
  {
    term: "Accelerator",
    definition: "Programs that provide intensive mentorship and sometimes funding to help startups grow rapidly.",
    example: "A 3-month accelerator program helping Nepali startups refine their business models and connect with investors.",
    category: "Ecosystem",
    difficulty: "Beginner",
    relatedTerms: ["Incubator", "Mentorship", "Demo Day"],
    nepaliContext: "Several accelerators now operate in Nepal, providing crucial support for early-stage startups."
  },
  {
    term: "Product-Led Growth",
    definition: "A business methodology where user acquisition, expansion, and retention are driven by the product itself.",
    example: "A project management tool that users love so much they invite their colleagues to join, driving organic growth.",
    category: "Growth",
    difficulty: "Advanced",
    relatedTerms: ["User Experience", "Viral Coefficient", "Network Effects"],
    nepaliContext: "Effective strategy for software startups in Nepal to grow without large marketing budgets."
  },
  {
    term: "Network Effects",
    definition: "When a product or service becomes more valuable as more people use it.",
    example: "A messaging app becomes more useful as more of your friends and family join the platform.",
    category: "Strategy",
    difficulty: "Advanced",
    relatedTerms: ["Platform Business", "Viral Growth", "User Base"],
    nepaliContext: "Powerful in Nepal's close-knit communities where social connections drive adoption."
  },
  {
    term: "Platform Business Model",
    definition: "A business model that creates value by facilitating exchanges between different user groups.",
    example: "An online marketplace connecting Nepali farmers directly with restaurants and retailers.",
    category: "Business Model",
    difficulty: "Advanced",
    relatedTerms: ["Two-sided Market", "Network Effects", "Marketplace"],
    nepaliContext: "Effective for addressing Nepal's fragmented markets and connecting producers with consumers."
  },
  {
    term: "Agile Development",
    definition: "An iterative approach to software development that emphasizes flexibility and customer collaboration.",
    example: "Developing a mobile app in 2-week sprints, gathering user feedback, and adjusting features accordingly.",
    category: "Technology",
    difficulty: "Intermediate",
    relatedTerms: ["Scrum", "Sprint", "Iteration"],
    nepaliContext: "Increasingly adopted by tech companies in Nepal for faster product development and market responsiveness."
  },
  {
    term: "Digital Transformation",
    definition: "The integration of digital technology into all areas of a business to improve operations and customer value.",
    example: "A traditional Nepali textile company implementing e-commerce, digital inventory, and online customer service.",
    category: "Technology",
    difficulty: "Intermediate",
    relatedTerms: ["Digitalization", "Process Automation", "Customer Experience"],
    nepaliContext: "Major opportunity in Nepal as traditional businesses modernize their operations."
  },
  {
    term: "Market Penetration",
    definition: "The extent to which a product or service is known and used by customers in a particular market.",
    example: "Mobile payment services achieving 60% penetration in urban Nepal but only 15% in rural areas.",
    category: "Marketing",
    difficulty: "Intermediate",
    relatedTerms: ["Market Share", "TAM", "Customer Adoption"],
    nepaliContext: "Understanding penetration rates helps startups identify opportunities in underserved segments."
  }
];

function BusinessToolsContent() {
  const searchParams = useSearchParams()
  const [activeTool, setActiveTool] = useState('financial-literacy')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [selectedDifficulty, setSelectedDifficulty] = useState('All')
  const [expandedTerm, setExpandedTerm] = useState<string | null>(null)
  
  // Financial Calculator States
  const [investment, setInvestment] = useState(500000)
  const [revenue, setRevenue] = useState(80000)
  const [expenses, setExpenses] = useState(45000)
  const [timeHorizon, setTimeHorizon] = useState(12)

  // Business Model Canvas State
  const [canvasData, setCanvasData] = useState({
    keyPartners: '',
    keyActivities: '',
    keyResources: '',
    valueProposition: '',
    customerRelationships: '',
    channels: '',
    customerSegments: '',
    costStructure: '',
    revenueStreams: ''
  })

  useEffect(() => {
    const tool = searchParams.get('tool')
    if (tool && ['financial-literacy', 'business-model', 'startup-dictionary'].includes(tool)) {
      setActiveTool(tool)
    }
  }, [searchParams])

  const calculateFinancials = () => {
    const monthlyProfit = revenue - expenses
    const breakEvenMonths = monthlyProfit > 0 ? investment / monthlyProfit : 0
    const totalProfit = (monthlyProfit * timeHorizon) - investment
    const roi = investment > 0 ? ((totalProfit / investment) * 100).toFixed(1) : '0.0'
    
    return {
      monthlyProfit,
      breakEvenMonths: Math.ceil(breakEvenMonths),
      totalProfit,
      roi
    }
  }

  const updateCanvasBlock = (blockId: string, value: string) => {
    setCanvasData(prev => ({
      ...prev,
      [blockId]: value
    }))
  }

  const exportToPDF = async () => {
    try {
      // Create a temporary container for the canvas
      const element = document.getElementById('business-model-canvas')
      if (!element) return

      // Configure html2canvas options
      const canvas = await html2canvas(element, {
        scale: 2,
        logging: false,
        useCORS: true,
        allowTaint: false,
        backgroundColor: '#ffffff'
      })

      const imgData = canvas.toDataURL('image/png')
      
      // Create PDF with A4 landscape orientation for better canvas display
      const pdf = new jsPDF('landscape', 'mm', 'a4')
      const imgWidth = 297 // A4 landscape width in mm
      const pageHeight = 210 // A4 landscape height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight

      let position = 0

      // Add the canvas image to PDF
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      // Add source reference on a new page
      pdf.addPage()
      pdf.setFontSize(16)
      pdf.text('Business Model Canvas', 20, 30)
      pdf.setFontSize(12)
      pdf.text('Based on the Business Model Canvas framework by Alexander Osterwalder', 20, 50)
      pdf.setFontSize(10)
      pdf.text('Source: Harvard Business Review - "A Better Way to Think About Your Business Model"', 20, 70)
      pdf.text('https://hbr.org/2013/05/a-better-way-to-think-about-yo', 20, 80)
      pdf.text('Generated on: ' + new Date().toLocaleDateString(), 20, 100)

      // Save the PDF
      pdf.save('business-model-canvas.pdf')
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Error generating PDF. Please try again.')
    }
  }

  const filteredTerms = startupTerms.filter(term => {
    const matchesSearch = term.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         term.definition.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         term.example.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'All' || term.category === selectedCategory
    const matchesDifficulty = selectedDifficulty === 'All' || term.difficulty === selectedDifficulty
    return matchesSearch && matchesCategory && matchesDifficulty
  })

  const categories = ['All', ...Array.from(new Set(startupTerms.map(term => term.category)))]
  const difficulties = ['All', 'Beginner', 'Intermediate', 'Advanced']

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-700'
      case 'Intermediate': return 'bg-yellow-100 text-yellow-700'
      case 'Advanced': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const results = calculateFinancials()

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Header Section */}
          <section className="w-full py-16 md:py-20 bg-gradient-to-br from-indigo-50 via-blue-50 to-cyan-50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex items-center gap-3 mb-6">
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/schools/business">
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back to Business School
                    </Link>
                  </Button>
                  <Badge className="bg-indigo-100 text-indigo-700">
                    Interactive Learning Tools
                  </Badge>
                </div>
                
                <div className="text-center">
                  <h1 className="text-4xl md:text-5xl font-bold mb-6">
                    Business <span className="text-indigo-600">Learning Tools</span>
                  </h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    Master essential business concepts through hands-on interactive tools designed for practical learning.
                  </p>
                </div>

                {/* Tool Navigation */}
                <div className="flex flex-wrap justify-center gap-4 mt-8">
                  <Button
                    variant={activeTool === 'financial-literacy' ? 'default' : 'outline'}
                    onClick={() => setActiveTool('financial-literacy')}
                    className="flex items-center gap-2"
                  >
                    <DollarSign className="h-4 w-4" />
                    Financial Literacy
                  </Button>
                  <Button
                    variant={activeTool === 'business-model' ? 'default' : 'outline'}
                    onClick={() => setActiveTool('business-model')}
                    className="flex items-center gap-2"
                  >
                    <BarChart className="h-4 w-4" />
                    Business Model Canvas
                  </Button>
                  <Button
                    variant={activeTool === 'startup-dictionary' ? 'default' : 'outline'}
                    onClick={() => setActiveTool('startup-dictionary')}
                    className="flex items-center gap-2"
                  >
                    <BookOpen className="h-4 w-4" />
                    Startup Dictionary
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* Tool Content */}
          <section className="w-full py-16 md:py-20 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                {/* Financial Literacy Tool */}
                {activeTool === 'financial-literacy' && (
                  <div className="space-y-8">
                    <div className="text-center">
                      <h2 className="text-3xl font-bold mb-4 text-green-800">
                        📊 Financial Literacy Resources
                      </h2>
                      <p className="text-lg text-muted-foreground">
                        Explore curated resources to build your financial knowledge and skills
                      </p>
                    </div>

                    {/* Resource Categories */}
                    <div className="grid lg:grid-cols-2 gap-8">
                      {/* International Resources */}
                      <div className="space-y-6">
                        <div className="text-center">
                          <h3 className="text-2xl font-bold text-blue-800 mb-2">🌍 International Resources</h3>
                          <p className="text-gray-600">World-class financial education platforms</p>
                        </div>

                        {/* Khan Academy */}
                        <Card className="border-blue-200 hover:shadow-xl transition-all duration-300 group">
                          <CardHeader className="bg-gradient-to-br from-blue-50 to-blue-100 border-b border-blue-200">
                            <div className="flex items-center gap-4">
                              <div className="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center shadow-lg">
                                <span className="text-white text-2xl font-bold">🎓</span>
                              </div>
                              <div className="flex-1">
                                <CardTitle className="text-xl text-blue-800">Khan Academy Personal Finance</CardTitle>
                                <CardDescription className="text-blue-600">
                                  Free, comprehensive personal finance courses
                                </CardDescription>
                              </div>
                              <Badge className="bg-blue-100 text-blue-700">Free</Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="p-6">
                            <p className="text-gray-700 mb-4 leading-relaxed">
                              Learn essential personal finance skills including budgeting, saving, investing, taxes, and credit management through interactive lessons and exercises.
                            </p>
                            <div className="space-y-3 mb-6">
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Interactive lessons and practice exercises</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Budgeting and saving strategies</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Investment and retirement planning</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Credit management and taxes</span>
                              </div>
                            </div>
                            <Button 
                              className="w-full bg-blue-600 hover:bg-blue-700 group-hover:shadow-lg transition-all" 
                              asChild
                            >
                              <a 
                                href="https://www.khanacademy.org/college-careers-more/personal-finance" 
                                target="_blank" 
                                rel="noopener noreferrer"
                              >
                                Start Learning
                                <span className="ml-2">→</span>
                              </a>
                            </Button>
                          </CardContent>
                        </Card>

                        {/* Ideapreneur Nepal YouTube */}
                        <Card className="border-red-200 hover:shadow-xl transition-all duration-300 group">
                          <CardHeader className="bg-gradient-to-br from-red-50 to-red-100 border-b border-red-200">
                            <div className="flex items-center gap-4">
                              <div className="w-16 h-16 bg-red-500 rounded-2xl flex items-center justify-center shadow-lg">
                                <span className="text-white text-2xl font-bold">📺</span>
                              </div>
                              <div className="flex-1">
                                <CardTitle className="text-xl text-red-800">Ideapreneur Nepal</CardTitle>
                                <CardDescription className="text-red-600">
                                  YouTube channel for entrepreneurship & finance
                                </CardDescription>
                              </div>
                              <Badge className="bg-red-100 text-red-700">Video</Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="p-6">
                            <p className="text-gray-700 mb-4 leading-relaxed">
                              Entrepreneurship and business finance education tailored for Nepal, featuring practical insights, startup stories, and financial management tips.
                            </p>
                            <div className="space-y-3 mb-6">
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Startup and business finance</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Entrepreneurship stories and insights</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Nepal-focused content</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Video tutorials and interviews</span>
                              </div>
                            </div>
                            <Button 
                              className="w-full bg-red-600 hover:bg-red-700 group-hover:shadow-lg transition-all" 
                              asChild
                            >
                              <a 
                                href="https://www.youtube.com/@IdeapreneurNepal" 
                                target="_blank" 
                                rel="noopener noreferrer"
                              >
                                Watch Videos
                                <span className="ml-2">→</span>
                              </a>
                            </Button>
                          </CardContent>
                        </Card>
                      </div>

                      {/* Nepal-Specific Resources */}
                      <div className="space-y-6">
                        <div className="text-center">
                          <h3 className="text-2xl font-bold text-green-800 mb-2">🇳🇵 Nepal-Specific Resources</h3>
                          <p className="text-gray-600">Local financial education and insights</p>
                        </div>

                        {/* Nepal Rastra Bank */}
                        <Card className="border-green-200 hover:shadow-xl transition-all duration-300 group">
                          <CardHeader className="bg-gradient-to-br from-green-50 to-green-100 border-b border-green-200">
                            <div className="flex items-center gap-4">
                              <div className="w-16 h-16 bg-green-500 rounded-2xl flex items-center justify-center shadow-lg">
                                <span className="text-white text-2xl font-bold">🏛️</span>
                              </div>
                              <div className="flex-1">
                                <CardTitle className="text-xl text-green-800">Nepal Rastra Bank</CardTitle>
                                <CardDescription className="text-green-600">
                                  Official financial literacy resources from Nepal's central bank
                                </CardDescription>
                              </div>
                              <Badge className="bg-green-100 text-green-700">Official</Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="p-6">
                            <p className="text-gray-700 mb-4 leading-relaxed">
                              Comprehensive financial literacy materials in Nepali and English, including guides on banking, digital payments, and financial awareness campaigns.
                            </p>
                            <div className="space-y-3 mb-6">
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Banking and financial services guide</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Digital payment awareness</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Publications in Nepali language</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Financial awareness songs and materials</span>
                              </div>
                            </div>
                            <Button 
                              className="w-full bg-green-600 hover:bg-green-700 group-hover:shadow-lg transition-all" 
                              asChild
                            >
                              <a 
                                href="https://www.nrb.org.np/financial-literacy/" 
                                target="_blank" 
                                rel="noopener noreferrer"
                              >
                                Explore Resources
                                <span className="ml-2">→</span>
                              </a>
                            </Button>
                          </CardContent>
                        </Card>

                        {/* Khatapana */}
                        <Card className="border-purple-200 hover:shadow-xl transition-all duration-300 group">
                          <CardHeader className="bg-gradient-to-br from-purple-50 to-purple-100 border-b border-purple-200">
                            <div className="flex items-center gap-4">
                              <div className="w-16 h-16 bg-purple-500 rounded-2xl flex items-center justify-center shadow-lg">
                                <span className="text-white text-2xl font-bold">📝</span>
                              </div>
                              <div className="flex-1">
                                <CardTitle className="text-xl text-purple-800">Khatapana Blogs</CardTitle>
                                <CardDescription className="text-purple-600">
                                  Personal finance and business insights for Nepal
                                </CardDescription>
                              </div>
                              <Badge className="bg-purple-100 text-purple-700">Blog</Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="p-6">
                            <p className="text-gray-700 mb-4 leading-relaxed">
                              Stay updated with the latest financial trends, policy changes, and economic insights specific to Nepal's market with expert analysis and practical advice.
                            </p>
                            <div className="space-y-3 mb-6">
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Nepal economy and market analysis</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Personal finance tips and strategies</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Business finance and investments</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                <span className="text-sm text-gray-600">Financial policy updates</span>
                              </div>
                            </div>
                            <Button 
                              className="w-full bg-purple-600 hover:bg-purple-700 group-hover:shadow-lg transition-all" 
                              asChild
                            >
                              <a 
                                href="https://khatapana.com/blogs" 
                                target="_blank" 
                                rel="noopener noreferrer"
                              >
                                Read Blogs
                                <span className="ml-2">→</span>
                              </a>
                            </Button>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* Additional Learning Tips */}
                    <div className="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-2xl p-8 border border-indigo-200">
                      <h3 className="text-2xl font-bold text-indigo-800 mb-6 flex items-center gap-3">
                        <span className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center text-white">💡</span>
                        Financial Literacy Learning Path
                      </h3>
                      <div className="grid md:grid-cols-3 gap-6">
                        <div className="bg-white rounded-xl p-6 shadow-lg border border-indigo-100">
                          <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-4">
                            <span className="text-green-600 text-xl">🌱</span>
                          </div>
                          <h4 className="font-bold text-gray-800 mb-2">Start with Basics</h4>
                          <p className="text-sm text-gray-600">
                            Begin with Khan Academy's personal finance fundamentals to build a strong foundation in budgeting, saving, and basic financial concepts.
                          </p>
                        </div>
                        <div className="bg-white rounded-xl p-6 shadow-lg border border-indigo-100">
                          <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-4">
                            <span className="text-blue-600 text-xl">🇳🇵</span>
                          </div>
                          <h4 className="font-bold text-gray-800 mb-2">Learn Local Context</h4>
                          <p className="text-sm text-gray-600">
                            Use Nepal Rastra Bank resources to understand local banking systems, regulations, and financial services specific to Nepal.
                          </p>
                        </div>
                        <div className="bg-white rounded-xl p-6 shadow-lg border border-indigo-100">
                          <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-4">
                            <span className="text-purple-600 text-xl">📈</span>
                          </div>
                          <h4 className="font-bold text-gray-800 mb-2">Stay Updated</h4>
                          <p className="text-sm text-gray-600">
                            Follow Khatapana blogs and Ideapreneur Nepal for current market trends, policy changes, and practical financial advice.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Quick Reference */}
                    <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                        <span className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center text-white text-sm">📚</span>
                        Quick Financial Tips for Students
                      </h3>
                      <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-700">
                        <div className="space-y-2">
                          <p><strong>Start Early:</strong> Begin building financial habits while you're still a student.</p>
                          <p><strong>Budget Basics:</strong> Track income and expenses, even small amounts matter.</p>
                          <p><strong>Emergency Fund:</strong> Save for unexpected expenses, even if it's just NPR 1,000 per month.</p>
                        </div>
                        <div className="space-y-2">
                          <p><strong>Understand Credit:</strong> Learn about loans, interest rates, and responsible borrowing.</p>
                          <p><strong>Digital Literacy:</strong> Master digital payment systems and online banking safely.</p>
                          <p><strong>Invest in Learning:</strong> Your education and skills are your best investment.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Business Model Canvas */}
                {activeTool === 'business-model' && (
                  <div className="space-y-8">
                    <div className="text-center">
                      <h2 className="text-3xl font-bold mb-4 text-blue-800">
                        🎯 Business Model Canvas Builder
                      </h2>
                      <p className="text-lg text-muted-foreground">
                        Design and validate your business model using the proven 9-block framework
                      </p>
                      <p className="text-sm text-gray-600 mt-2">
                        Based on the framework by Alexander Osterwalder. 
                        <a 
                          href="https://hbr.org/2013/05/a-better-way-to-think-about-yo" 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline ml-1"
                        >
                          Learn more from Harvard Business Review
                        </a>
                      </p>
                    </div>

                    {/* Progress Indicator */}
                    <div className="bg-white rounded-2xl p-6 shadow-lg border border-blue-100">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-800">Canvas Completion</h3>
                        <Badge className="bg-blue-100 text-blue-700">
                          {Object.values(canvasData).filter(value => value.trim() !== '').length}/9 Sections
                        </Badge>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div 
                          className="bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full transition-all duration-500"
                          style={{ width: `${(Object.values(canvasData).filter(value => value.trim() !== '').length / 9) * 100}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-gray-600 mt-2">
                        Fill in all sections to create a comprehensive business model
                      </p>
                    </div>

                    {/* Business Model Canvas Grid */}
                    <div id="business-model-canvas" className="bg-white rounded-3xl p-4 md:p-8 shadow-2xl border border-gray-100">
                      {/* Desktop Layout */}
                      <div className="hidden lg:grid grid-cols-5 gap-6 min-h-[700px]">
                        {/* Row 1: Top sections */}
                        <div className="col-span-1">
                          <div className="h-full bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl border-2 border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300 group">
                            <div className="p-4 h-full flex flex-col">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">🤝</span>
                                </div>
                                <h3 className="font-bold text-orange-800 text-sm">Key Partners</h3>
                                {canvasData.keyPartners.trim() && (
                                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <p className="text-xs text-orange-600 mb-3 leading-tight">
                                Who are your key partners and suppliers? What key resources are you acquiring from partners?
                              </p>
                              <Textarea
                                placeholder="• Manufacturing partners&#10;• Technology providers&#10;• Distribution channels&#10;• Strategic alliances"
                                value={canvasData.keyPartners}
                                onChange={(e) => updateCanvasBlock('keyPartners', e.target.value)}
                                className="flex-1 text-sm border-orange-200 focus:border-orange-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="col-span-1">
                          <div className="h-full bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl border-2 border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 group">
                            <div className="p-4 h-full flex flex-col">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">⚡</span>
                                </div>
                                <h3 className="font-bold text-blue-800 text-sm">Key Activities</h3>
                                {canvasData.keyActivities.trim() && (
                                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <p className="text-xs text-blue-600 mb-3 leading-tight">
                                What key activities does your value proposition require?
                              </p>
                              <Textarea
                                placeholder="• Product development&#10;• Manufacturing&#10;• Marketing & sales&#10;• Platform management"
                                value={canvasData.keyActivities}
                                onChange={(e) => updateCanvasBlock('keyActivities', e.target.value)}
                                className="flex-1 text-sm border-blue-200 focus:border-blue-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="col-span-1">
                          <div className="h-full bg-gradient-to-br from-red-50 to-red-100 rounded-2xl border-2 border-red-200 shadow-lg hover:shadow-xl transition-all duration-300 group">
                            <div className="p-4 h-full flex flex-col">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">💎</span>
                                </div>
                                <h3 className="font-bold text-red-800 text-sm">Value Proposition</h3>
                                {canvasData.valueProposition.trim() && (
                                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <p className="text-xs text-red-600 mb-3 leading-tight">
                                What value do you deliver to customers? What customer problem are you solving?
                              </p>
                              <Textarea
                                placeholder="• Unique benefits&#10;• Problems you solve&#10;• Why customers choose you&#10;• Core value delivered"
                                value={canvasData.valueProposition}
                                onChange={(e) => updateCanvasBlock('valueProposition', e.target.value)}
                                className="flex-1 text-sm border-red-200 focus:border-red-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="col-span-1">
                          <div className="h-full bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl border-2 border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 group">
                            <div className="p-4 h-full flex flex-col">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">💬</span>
                                </div>
                                <h3 className="font-bold text-purple-800 text-sm">Customer Relationships</h3>
                                {canvasData.customerRelationships.trim() && (
                                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <p className="text-xs text-purple-600 mb-3 leading-tight">
                                What type of relationship does each customer segment expect?
                              </p>
                              <Textarea
                                placeholder="• Personal assistance&#10;• Self-service&#10;• Automated services&#10;• Communities"
                                value={canvasData.customerRelationships}
                                onChange={(e) => updateCanvasBlock('customerRelationships', e.target.value)}
                                className="flex-1 text-sm border-purple-200 focus:border-purple-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="col-span-1">
                          <div className="h-full bg-gradient-to-br from-pink-50 to-pink-100 rounded-2xl border-2 border-pink-200 shadow-lg hover:shadow-xl transition-all duration-300 group">
                            <div className="p-4 h-full flex flex-col">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-pink-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">👥</span>
                                </div>
                                <h3 className="font-bold text-pink-800 text-sm">Customer Segments</h3>
                                {canvasData.customerSegments.trim() && (
                                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <p className="text-xs text-pink-600 mb-3 leading-tight">
                                For whom are you creating value? Who are your most important customers?
                              </p>
                              <Textarea
                                placeholder="• Target demographics&#10;• Market segments&#10;• Customer archetypes&#10;• User personas"
                                value={canvasData.customerSegments}
                                onChange={(e) => updateCanvasBlock('customerSegments', e.target.value)}
                                className="flex-1 text-sm border-pink-200 focus:border-pink-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>

                        {/* Row 2: Middle section - Key Resources and Channels */}
                        <div className="col-span-1">
                          <div className="h-full bg-gradient-to-br from-green-50 to-green-100 rounded-2xl border-2 border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 group">
                            <div className="p-4 h-full flex flex-col">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">🏗️</span>
                                </div>
                                <h3 className="font-bold text-green-800 text-sm">Key Resources</h3>
                                {canvasData.keyResources.trim() && (
                                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <p className="text-xs text-green-600 mb-3 leading-tight">
                                What key resources does your value proposition require?
                              </p>
                              <Textarea
                                placeholder="• Physical assets&#10;• Intellectual property&#10;• Human resources&#10;• Financial resources"
                                value={canvasData.keyResources}
                                onChange={(e) => updateCanvasBlock('keyResources', e.target.value)}
                                className="flex-1 text-sm border-green-200 focus:border-green-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="col-span-3">
                          <div className="h-full bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl border-2 border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 group">
                            <div className="p-4 h-full flex flex-col">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">📡</span>
                                </div>
                                <h3 className="font-bold text-yellow-800 text-sm">Channels</h3>
                                {canvasData.channels.trim() && (
                                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <p className="text-xs text-yellow-600 mb-3 leading-tight">
                                Through which channels do you reach your customers? How do you deliver your value proposition?
                              </p>
                              <Textarea
                                placeholder="• Online platforms (website, social media, mobile app)&#10;• Physical stores and locations&#10;• Sales team and direct sales&#10;• Partner channels and distributors&#10;• Marketing and advertising channels"
                                value={canvasData.channels}
                                onChange={(e) => updateCanvasBlock('channels', e.target.value)}
                                className="flex-1 text-sm border-yellow-200 focus:border-yellow-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>

                        {/* This column spans with Customer Segments above */}
                        <div className="col-span-1">
                          {/* Empty space or could be used for notes */}
                        </div>

                        {/* Row 3: Bottom sections - Cost Structure and Revenue Streams */}
                        <div className="col-span-2">
                          <div className="h-full bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border-2 border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 group">
                            <div className="p-4 h-full flex flex-col">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-gray-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">💰</span>
                                </div>
                                <h3 className="font-bold text-gray-800 text-sm">Cost Structure</h3>
                                {canvasData.costStructure.trim() && (
                                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <p className="text-xs text-gray-600 mb-3 leading-tight">
                                What are the most important costs in your business model? Which key resources and activities are most expensive?
                              </p>
                              <Textarea
                                placeholder="• Fixed costs (rent, salaries, equipment)&#10;• Variable costs (materials, transaction fees)&#10;• Economies of scale opportunities&#10;• Cost optimization strategies"
                                value={canvasData.costStructure}
                                onChange={(e) => updateCanvasBlock('costStructure', e.target.value)}
                                className="flex-1 text-sm border-gray-200 focus:border-gray-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="col-span-3">
                          <div className="h-full bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl border-2 border-emerald-200 shadow-lg hover:shadow-xl transition-all duration-300 group">
                            <div className="p-4 h-full flex flex-col">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">💵</span>
                                </div>
                                <h3 className="font-bold text-emerald-800 text-sm">Revenue Streams</h3>
                                {canvasData.revenueStreams.trim() && (
                                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <p className="text-xs text-emerald-600 mb-3 leading-tight">
                                For what value are customers willing to pay? How do they prefer to pay? How much does each revenue stream contribute?
                              </p>
                              <Textarea
                                placeholder="• One-time payments (product sales, licensing)&#10;• Recurring revenue (subscriptions, memberships)&#10;• Transaction-based (commissions, fees)&#10;• Advertising and sponsorship revenue&#10;• Freemium and premium models"
                                value={canvasData.revenueStreams}
                                onChange={(e) => updateCanvasBlock('revenueStreams', e.target.value)}
                                className="flex-1 text-sm border-emerald-200 focus:border-emerald-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Mobile/Tablet Layout */}
                      <div className="lg:hidden space-y-6">
                        <div className="text-center mb-6">
                          <Badge className="bg-blue-100 text-blue-700 px-4 py-2">
                            📱 Mobile-Optimized Canvas
                          </Badge>
                          <p className="text-sm text-gray-600 mt-2">
                            Complete each section to build your business model
                          </p>
                        </div>

                        {/* Value Proposition - Start here on mobile */}
                        <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-2xl border-2 border-red-200 shadow-lg">
                          <div className="p-4">
                            <div className="flex items-center gap-3 mb-4">
                              <div className="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                                <span className="text-white text-lg">💎</span>
                              </div>
                              <div className="flex-1">
                                <h3 className="font-bold text-red-800 text-lg">Value Proposition</h3>
                                <p className="text-sm text-red-600">Core value you deliver to customers</p>
                              </div>
                              {canvasData.valueProposition.trim() && (
                                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                              )}
                            </div>
                            <Textarea
                              placeholder="• Unique benefits you provide&#10;• Problems you solve&#10;• Why customers choose you&#10;• Core value delivered"
                              value={canvasData.valueProposition}
                              onChange={(e) => updateCanvasBlock('valueProposition', e.target.value)}
                              className="min-h-[120px] text-sm border-red-200 focus:border-red-400 bg-white/70 resize-none"
                            />
                          </div>
                        </div>

                        {/* Customer Segments */}
                        <div className="bg-gradient-to-br from-pink-50 to-pink-100 rounded-2xl border-2 border-pink-200 shadow-lg">
                          <div className="p-4">
                            <div className="flex items-center gap-3 mb-4">
                              <div className="w-10 h-10 bg-pink-500 rounded-lg flex items-center justify-center">
                                <span className="text-white text-lg">👥</span>
                              </div>
                              <div className="flex-1">
                                <h3 className="font-bold text-pink-800 text-lg">Customer Segments</h3>
                                <p className="text-sm text-pink-600">Who are your target customers?</p>
                              </div>
                              {canvasData.customerSegments.trim() && (
                                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                              )}
                            </div>
                            <Textarea
                              placeholder="• Target demographics&#10;• Market segments&#10;• Customer archetypes&#10;• User personas"
                              value={canvasData.customerSegments}
                              onChange={(e) => updateCanvasBlock('customerSegments', e.target.value)}
                              className="min-h-[120px] text-sm border-pink-200 focus:border-pink-400 bg-white/70 resize-none"
                            />
                          </div>
                        </div>

                        {/* Key Activities & Key Resources - Side by side on tablet */}
                        <div className="grid md:grid-cols-2 gap-4">
                          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl border-2 border-blue-200 shadow-lg">
                            <div className="p-4">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-lg">⚡</span>
                                </div>
                                <div className="flex-1">
                                  <h3 className="font-bold text-blue-800">Key Activities</h3>
                                  <p className="text-xs text-blue-600">Critical activities for success</p>
                                </div>
                                {canvasData.keyActivities.trim() && (
                                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <Textarea
                                placeholder="• Product development&#10;• Manufacturing&#10;• Marketing & sales"
                                value={canvasData.keyActivities}
                                onChange={(e) => updateCanvasBlock('keyActivities', e.target.value)}
                                className="min-h-[100px] text-sm border-blue-200 focus:border-blue-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>

                          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl border-2 border-green-200 shadow-lg">
                            <div className="p-4">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-lg">🏗️</span>
                                </div>
                                <div className="flex-1">
                                  <h3 className="font-bold text-green-800">Key Resources</h3>
                                  <p className="text-xs text-green-600">Essential assets and resources</p>
                                </div>
                                {canvasData.keyResources.trim() && (
                                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <Textarea
                                placeholder="• Physical assets&#10;• Intellectual property&#10;• Human resources"
                                value={canvasData.keyResources}
                                onChange={(e) => updateCanvasBlock('keyResources', e.target.value)}
                                className="min-h-[100px] text-sm border-green-200 focus:border-green-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>

                        {/* Key Partners */}
                        <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl border-2 border-orange-200 shadow-lg">
                          <div className="p-4">
                            <div className="flex items-center gap-3 mb-4">
                              <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                                <span className="text-white text-lg">🤝</span>
                              </div>
                              <div className="flex-1">
                                <h3 className="font-bold text-orange-800 text-lg">Key Partners</h3>
                                <p className="text-sm text-orange-600">Strategic partnerships and suppliers</p>
                              </div>
                              {canvasData.keyPartners.trim() && (
                                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                              )}
                            </div>
                            <Textarea
                              placeholder="• Manufacturing partners&#10;• Technology providers&#10;• Distribution channels&#10;• Strategic alliances"
                              value={canvasData.keyPartners}
                              onChange={(e) => updateCanvasBlock('keyPartners', e.target.value)}
                              className="min-h-[120px] text-sm border-orange-200 focus:border-orange-400 bg-white/70 resize-none"
                            />
                          </div>
                        </div>

                        {/* Customer Relationships & Channels */}
                        <div className="grid md:grid-cols-2 gap-4">
                          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl border-2 border-purple-200 shadow-lg">
                            <div className="p-4">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-lg">💬</span>
                                </div>
                                <div className="flex-1">
                                  <h3 className="font-bold text-purple-800">Customer Relationships</h3>
                                  <p className="text-xs text-purple-600">How you interact with customers</p>
                                </div>
                                {canvasData.customerRelationships.trim() && (
                                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <Textarea
                                placeholder="• Personal assistance&#10;• Self-service&#10;• Automated services"
                                value={canvasData.customerRelationships}
                                onChange={(e) => updateCanvasBlock('customerRelationships', e.target.value)}
                                className="min-h-[100px] text-sm border-purple-200 focus:border-purple-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>

                          <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl border-2 border-yellow-200 shadow-lg">
                            <div className="p-4">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-lg">📡</span>
                                </div>
                                <div className="flex-1">
                                  <h3 className="font-bold text-yellow-800">Channels</h3>
                                  <p className="text-xs text-yellow-600">How you reach customers</p>
                                </div>
                                {canvasData.channels.trim() && (
                                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <Textarea
                                placeholder="• Online platforms&#10;• Physical stores&#10;• Sales team"
                                value={canvasData.channels}
                                onChange={(e) => updateCanvasBlock('channels', e.target.value)}
                                className="min-h-[100px] text-sm border-yellow-200 focus:border-yellow-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>

                        {/* Cost Structure & Revenue Streams */}
                        <div className="grid md:grid-cols-2 gap-4">
                          <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border-2 border-gray-200 shadow-lg">
                            <div className="p-4">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-10 h-10 bg-gray-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-lg">💰</span>
                                </div>
                                <div className="flex-1">
                                  <h3 className="font-bold text-gray-800">Cost Structure</h3>
                                  <p className="text-xs text-gray-600">Key costs and expenses</p>
                                </div>
                                {canvasData.costStructure.trim() && (
                                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <Textarea
                                placeholder="• Fixed costs (rent, salaries)&#10;• Variable costs (materials)&#10;• Cost optimization"
                                value={canvasData.costStructure}
                                onChange={(e) => updateCanvasBlock('costStructure', e.target.value)}
                                className="min-h-[100px] text-sm border-gray-200 focus:border-gray-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>

                          <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl border-2 border-emerald-200 shadow-lg">
                            <div className="p-4">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-10 h-10 bg-emerald-500 rounded-lg flex items-center justify-center">
                                  <span className="text-white text-lg">💵</span>
                                </div>
                                <div className="flex-1">
                                  <h3 className="font-bold text-emerald-800">Revenue Streams</h3>
                                  <p className="text-xs text-emerald-600">How you make money</p>
                                </div>
                                {canvasData.revenueStreams.trim() && (
                                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                )}
                              </div>
                              <Textarea
                                placeholder="• One-time payments&#10;• Recurring revenue&#10;• Transaction-based fees"
                                value={canvasData.revenueStreams}
                                onChange={(e) => updateCanvasBlock('revenueStreams', e.target.value)}
                                className="min-h-[100px] text-sm border-emerald-200 focus:border-emerald-400 bg-white/70 resize-none"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
                      <Button 
                        variant="outline" 
                        onClick={exportToPDF}
                        className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all"
                      >
                        <Download className="mr-2 h-5 w-5" />
                        Export PDF
                      </Button>
                      <Button 
                        variant="outline" 
                        onClick={() => setCanvasData({
                          keyPartners: '', keyActivities: '', keyResources: '', valueProposition: '',
                          customerRelationships: '', channels: '', customerSegments: '', costStructure: '', revenueStreams: ''
                        })}
                        className="bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100 px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all"
                      >
                        <RefreshCw className="mr-2 h-5 w-5" />
                        Clear All
                      </Button>
                    </div>

                    {/* Tips and Guidance */}
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
                      <h3 className="text-lg font-semibold text-blue-800 mb-4 flex items-center gap-2">
                        <span className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">💡</span>
                        Pro Tips for Building Your Canvas
                      </h3>
                      <div className="grid md:grid-cols-2 gap-4 text-sm text-blue-700">
                        <div className="space-y-2">
                          <p><strong>Start with Value Proposition:</strong> Clearly define what unique value you're providing to customers.</p>
                          <p><strong>Identify Customer Segments:</strong> Be specific about who your target customers are.</p>
                          <p><strong>Map Customer Journey:</strong> Think about how customers discover, evaluate, and purchase your offering.</p>
                        </div>
                        <div className="space-y-2">
                          <p><strong>Consider Dependencies:</strong> How do Key Partners, Activities, and Resources connect?</p>
                          <p><strong>Validate Assumptions:</strong> Test your hypotheses with real customer feedback.</p>
                          <p><strong>Iterate Regularly:</strong> Business models evolve - update your canvas as you learn.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Startup Dictionary */}
                {activeTool === 'startup-dictionary' && (
                  <div className="space-y-8">
                    {/* Enhanced Header */}
                    <div className="text-center relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-100 via-blue-100 to-pink-100 rounded-3xl blur-3xl opacity-60"></div>
                      <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-purple-200 shadow-lg">
                        <div className="flex justify-center mb-4">
                          <div className="relative">
                            <div className="w-20 h-20 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                              <BookOpen className="h-10 w-10 text-white" />
                            </div>
                            <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
                              <Sparkles className="h-4 w-4 text-yellow-800" />
                            </div>
                          </div>
                        </div>
                        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-purple-800 via-blue-800 to-pink-800 bg-clip-text text-transparent">
                          Business Dictionary
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                          Master essential business terminology with interactive learning and Nepal-focused examples
                        </p>
                        <div className="flex justify-center items-center gap-6 mt-6">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
                            <Badge className="bg-purple-100 text-purple-700 px-4 py-2">
                              {startupTerms.length} Terms Available
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Search and Filter Panel */}
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl"></div>
                      <div className="relative bg-white/90 backdrop-blur-md rounded-2xl p-8 shadow-xl border border-blue-200">
                        <div className="flex items-center gap-3 mb-6">
                          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                            <Filter className="h-4 w-4 text-white" />
                          </div>
                          <h3 className="text-xl font-bold text-gray-800">Search & Filter</h3>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
                          <div className="md:col-span-6">
                            <Label htmlFor="search" className="text-sm font-semibold mb-3 block text-gray-700">
                              🔍 Search Terms
                            </Label>
                            <div className="relative group">
                              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" />
                              <Input
                                id="search"
                                placeholder="Search terms, definitions, or examples..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-12 pr-4 py-3 text-lg border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                              />
                              {searchTerm && (
                                <button
                                  onClick={() => setSearchTerm('')}
                                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                                >
                                  ✕
                                </button>
                              )}
                            </div>
                          </div>
                          
                          <div className="md:col-span-3">
                            <Label className="text-sm font-semibold mb-3 block text-gray-700">📂 Category</Label>
                            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                              <SelectTrigger className="py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {categories.map((category) => (
                                  <SelectItem key={category} value={category}>
                                    {category}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="md:col-span-3">
                            <Label className="text-sm font-semibold mb-3 block text-gray-700">⚡ Difficulty</Label>
                            <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
                              <SelectTrigger className="py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {difficulties.map((difficulty) => (
                                  <SelectItem key={difficulty} value={difficulty}>
                                    {difficulty}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-4 mt-6">
                          <Button
                            variant="outline"
                            size="lg"
                            onClick={() => {
                              setSearchTerm('')
                              setSelectedCategory('All')
                              setSelectedDifficulty('All')
                            }}
                            className="flex items-center gap-2 px-6 py-3 rounded-xl border-2 border-gray-200 text-gray-700 hover:bg-gray-50 transition-all"
                          >
                            <RefreshCw className="h-5 w-5" />
                            Clear All
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Stats Dashboard */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                      <div className="group relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition-opacity"></div>
                        <div className="relative bg-white rounded-2xl p-6 border border-purple-200 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                          <div className="flex items-center justify-between mb-4">
                            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                              <Target className="h-6 w-6 text-purple-600" />
                            </div>
                            <Badge className="bg-purple-100 text-purple-700">Results</Badge>
                          </div>
                          <div className="text-3xl font-bold text-purple-600 mb-2">{filteredTerms.length}</div>
                          <div className="text-sm text-purple-700 font-medium">Matching Terms</div>
                        </div>
                      </div>

                      <div className="group relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition-opacity"></div>
                        <div className="relative bg-white rounded-2xl p-6 border border-blue-200 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                          <div className="flex items-center justify-between mb-4">
                            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                              <BarChart className="h-6 w-6 text-blue-600" />
                            </div>
                            <Badge className="bg-blue-100 text-blue-700">Topics</Badge>
                          </div>
                          <div className="text-3xl font-bold text-blue-600 mb-2">{categories.length - 1}</div>
                          <div className="text-sm text-blue-700 font-medium">Categories</div>
                        </div>
                      </div>

                      <div className="group relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition-opacity"></div>
                        <div className="relative bg-white rounded-2xl p-6 border border-green-200 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                          <div className="flex items-center justify-between mb-4">
                            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                              <Award className="h-6 w-6 text-green-600" />
                            </div>
                            <Badge className="bg-green-100 text-green-700">Easy</Badge>
                          </div>
                          <div className="text-3xl font-bold text-green-600 mb-2">
                            {startupTerms.filter(t => t.difficulty === 'Beginner').length}
                          </div>
                          <div className="text-sm text-green-700 font-medium">Beginner Terms</div>
                        </div>
                      </div>

                      <div className="group relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition-opacity"></div>
                        <div className="relative bg-white rounded-2xl p-6 border border-orange-200 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                          <div className="flex items-center justify-between mb-4">
                            <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                              <Zap className="h-6 w-6 text-orange-600" />
                            </div>
                            <Badge className="bg-orange-100 text-orange-700">Expert</Badge>
                          </div>
                          <div className="text-3xl font-bold text-orange-600 mb-2">
                            {startupTerms.filter(t => t.difficulty === 'Advanced').length}
                          </div>
                          <div className="text-sm text-orange-700 font-medium">Advanced Terms</div>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Terms Grid */}
                    <div className="space-y-6">
                      {filteredTerms.map((term, index) => (
                        <div key={index} className="group relative">
                          <div className="absolute inset-0 bg-gradient-to-r from-purple-100/50 to-blue-100/50 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                          <Card className="relative border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 bg-white/90 backdrop-blur-sm overflow-hidden">
                            {/* Card Header */}
                            <CardHeader className="bg-gradient-to-r from-purple-50 via-blue-50 to-pink-50 border-b border-purple-100">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                  <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                                    <span className="text-white font-bold text-lg">{term.term.charAt(0)}</span>
                                  </div>
                                  <div>
                                    <CardTitle className="text-2xl text-purple-800 mb-2">{term.term}</CardTitle>
                                    <div className="flex items-center gap-3">
                                      <Badge className={`${getDifficultyColor(term.difficulty)} px-3 py-1 text-sm font-semibold`}>
                                        {term.difficulty === 'Beginner' && '🌱'} 
                                        {term.difficulty === 'Intermediate' && '📈'} 
                                        {term.difficulty === 'Advanced' && '🚀'} 
                                        {term.difficulty}
                                      </Badge>
                                      <Badge className="bg-purple-100 text-purple-700 px-3 py-1">
                                        📂 {term.category}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-3">
                                  <Button
                                    variant="ghost"
                                    size="lg"
                                    onClick={() => setExpandedTerm(expandedTerm === term.term ? null : term.term)}
                                    className="w-12 h-12 rounded-xl text-purple-600 hover:bg-purple-50 transition-all"
                                  >
                                    {expandedTerm === term.term ? 
                                      <ChevronUp className="h-6 w-6" /> : 
                                      <ChevronDown className="h-6 w-6" />
                                    }
                                  </Button>
                                </div>
                              </div>
                            </CardHeader>
                            
                            <CardContent className="p-8">
                              <div className="space-y-6">
                                {/* Definition */}
                                <div className="relative">
                                  <div className="absolute -left-4 top-0 w-1 h-full bg-gradient-to-b from-purple-500 to-blue-500 rounded-full"></div>
                                  <p className="text-lg text-gray-700 leading-relaxed font-medium pl-4">{term.definition}</p>
                                </div>
                                
                                {/* Example */}
                                <div className="bg-gradient-to-br from-blue-50 to-cyan-50 p-6 rounded-2xl border-l-4 border-blue-500 relative overflow-hidden">
                                  <div className="absolute top-2 right-2 text-4xl opacity-10">💼</div>
                                  <div className="flex items-center gap-3 mb-3">
                                    <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                      <span className="text-white font-bold text-sm">💼</span>
                                    </div>
                                    <span className="font-bold text-blue-800 text-lg">Real-World Example</span>
                                  </div>
                                  <p className="text-blue-700 leading-relaxed">{term.example}</p>
                                </div>

                                {/* Nepal Context */}
                                <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-2xl border-l-4 border-green-500 relative overflow-hidden">
                                  <div className="absolute top-2 right-2 text-4xl opacity-10">🇳🇵</div>
                                  <div className="flex items-center gap-3 mb-3">
                                    <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                                      <span className="text-white font-bold text-sm">🇳🇵</span>
                                    </div>
                                    <span className="font-bold text-green-800 text-lg">Nepal Context</span>
                                  </div>
                                  <p className="text-green-700 leading-relaxed">{term.nepaliContext}</p>
                                </div>

                                {/* Expanded Content */}
                                {expandedTerm === term.term && (
                                  <div className="mt-8 pt-6 border-t border-purple-100 space-y-6 animate-fadeIn">
                                    <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-2xl">
                                      <div className="flex items-center gap-3 mb-4">
                                        <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                                          <Target className="h-4 w-4 text-white" />
                                        </div>
                                        <h4 className="font-bold text-purple-800 text-lg">Related Terms</h4>
                                      </div>
                                      <div className="flex flex-wrap gap-3">
                                        {term.relatedTerms.map((relatedTerm, idx) => (
                                          <Badge
                                            key={idx}
                                            variant="outline"
                                            className="bg-white border-purple-200 text-purple-700 cursor-pointer hover:bg-purple-100 transition-all px-4 py-2 text-sm font-semibold rounded-xl"
                                            onClick={() => setSearchTerm(relatedTerm)}
                                          >
                                            🔗 {relatedTerm}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>

                                    <div className="bg-gradient-to-br from-yellow-50 to-orange-50 p-6 rounded-2xl border border-yellow-200">
                                      <div className="flex items-center gap-3 mb-3">
                                        <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                                          <span className="text-white font-bold text-sm">💡</span>
                                        </div>
                                        <h4 className="font-bold text-yellow-800 text-lg">Learning Tip</h4>
                                      </div>
                                      <p className="text-yellow-700 leading-relaxed">
                                        {term.difficulty === 'Beginner' && "🌱 Perfect starting point! This foundational concept will help you understand more complex business ideas. Take time to understand this thoroughly."}
                                        {term.difficulty === 'Intermediate' && "📈 Build on your basics! This concept connects multiple business areas. Consider how it applies to different industries and scenarios."}
                                        {term.difficulty === 'Advanced' && "🚀 Expert-level concept! This requires deep understanding of business fundamentals. Great for strategic thinking and advanced business planning."}
                                      </p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      ))}
                    </div>

                    {/* Enhanced No Results State */}
                    {filteredTerms.length === 0 && (
                      <div className="text-center py-16">
                        <div className="relative mb-8">
                          <div className="w-32 h-32 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                            <Search className="h-16 w-16 text-purple-400" />
                          </div>
                          <div className="absolute -top-2 -right-8 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
                            <span className="text-yellow-800 text-sm">?</span>
                          </div>
                        </div>
                        <h3 className="text-2xl font-bold mb-4 text-gray-800">No terms found</h3>
                        <p className="text-gray-600 mb-8 max-w-md mx-auto">
                          We couldn't find any terms matching your search criteria. Try adjusting your filters or search term.
                        </p>
                        <Button
                          onClick={() => {
                            setSearchTerm('')
                            setSelectedCategory('All')
                            setSelectedDifficulty('All')
                          }}
                          className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all"
                        >
                          <RefreshCw className="mr-2 h-5 w-5" />
                          Clear All Filters
                        </Button>
                      </div>
                    )}

                    {/* Enhanced Learning Path Suggestions */}
                    {filteredTerms.length > 0 && (
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-blue-500/10 to-pink-500/10 rounded-3xl blur-xl"></div>
                        <div className="relative bg-white/90 backdrop-blur-md p-8 rounded-3xl border border-purple-200 shadow-xl">
                          <div className="text-center mb-8">
                            <div className="flex justify-center mb-4">
                              <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <Clock className="h-8 w-8 text-white" />
                              </div>
                            </div>
                            <h3 className="text-3xl font-bold mb-4 bg-gradient-to-r from-purple-800 to-pink-800 bg-clip-text text-transparent">
                              📚 Suggested Learning Path
                            </h3>
                            <p className="text-gray-600 max-w-2xl mx-auto">
                              Follow this structured path to build your business knowledge progressively, from fundamental concepts to advanced strategies.
                            </p>
                          </div>
                          
                          <div className="grid md:grid-cols-3 gap-8">
                            <div className="group relative">
                              <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity"></div>
                              <div className="relative bg-white rounded-2xl p-6 border-2 border-green-200 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2">
                                <div className="flex items-center gap-3 mb-4">
                                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <span className="text-white font-bold">1</span>
                                  </div>
                                  <h4 className="font-bold text-green-800 text-xl">🌱 Start Here</h4>
                                </div>
                                <p className="text-green-700 mb-4 text-sm">Build your foundation with these essential concepts</p>
                                <div className="space-y-2">
                                  {['MVP (Minimum Viable Product)', 'Bootstrapping', 'B2B (Business to Business)'].map((term, idx) => (
                                    <Badge
                                      key={idx}
                                      variant="outline"
                                      className="bg-green-50 border-green-200 text-green-700 cursor-pointer hover:bg-green-100 transition-all block w-full py-2 px-3 rounded-xl"
                                      onClick={() => setSearchTerm(term)}
                                    >
                                      🎯 {term}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                            
                            <div className="group relative">
                              <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity"></div>
                              <div className="relative bg-white rounded-2xl p-6 border-2 border-yellow-200 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2">
                                <div className="flex items-center gap-3 mb-4">
                                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <span className="text-white font-bold">2</span>
                                  </div>
                                  <h4 className="font-bold text-yellow-800 text-xl">📈 Build Skills</h4>
                                </div>
                                <p className="text-yellow-700 mb-4 text-sm">Develop intermediate understanding of business operations</p>
                                <div className="space-y-2">
                                  {['Product-Market Fit', 'Customer Acquisition Cost (CAC)', 'Churn Rate'].map((term, idx) => (
                                    <Badge
                                      key={idx}
                                      variant="outline"
                                      className="bg-yellow-50 border-yellow-200 text-yellow-700 cursor-pointer hover:bg-yellow-100 transition-all block w-full py-2 px-3 rounded-xl"
                                      onClick={() => setSearchTerm(term)}
                                    >
                                      📊 {term}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                            
                            <div className="group relative">
                              <div className="absolute inset-0 bg-gradient-to-br from-red-500/20 to-pink-500/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity"></div>
                              <div className="relative bg-white rounded-2xl p-6 border-2 border-red-200 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2">
                                <div className="flex items-center gap-3 mb-4">
                                  <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <span className="text-white font-bold">3</span>
                                  </div>
                                  <h4 className="font-bold text-red-800 text-xl">🚀 Master Concepts</h4>
                                </div>
                                <p className="text-red-700 mb-4 text-sm">Advanced strategies for business leadership and innovation</p>
                                <div className="space-y-2">
                                  {['Network Effects', 'Platform Business Model', 'Disruptive Innovation'].map((term, idx) => (
                                    <Badge
                                      key={idx}
                                      variant="outline"
                                      className="bg-red-50 border-red-200 text-red-700 cursor-pointer hover:bg-red-100 transition-all block w-full py-2 px-3 rounded-xl"
                                      onClick={() => setSearchTerm(term)}
                                    >
                                      ⚡ {term}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
}

export default function BusinessToolsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-indigo-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-indigo-600" />
          <p className="text-indigo-800">Loading Business Tools...</p>
        </div>
      </div>
    }>
      <BusinessToolsContent />
    </Suspense>
  )
} 