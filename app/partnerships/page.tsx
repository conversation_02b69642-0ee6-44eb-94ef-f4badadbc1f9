import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Building, GraduationCap, Globe, Users, Target, Lightbulb, CheckCircle, ExternalLink, Mail, Phone, Shield, MapPin } from "lucide-react"
import Link from "next/link"

export const metadata = {
  title: 'Partnerships | College Name',
  description: 'Learn about our academic and industry partnerships that enhance the educational experience for our students.',
}

export default function PartnershipsPage() {
  const partnershipStats = [
    { label: "Active Partnerships", value: "1+", description: "Growing network" },
    { label: "Countries", value: "2", description: "Global reach" },
    { label: "Academic Programs", value: "1", description: "Joint offerings" },
    { label: "Future Collaborations", value: "5+", description: "In development" }
  ]

  const currentPartnerships = [
    {
      name: "Kathmandu University",
      country: "Nepal",
      type: "Academic Partnership",
      program: "B.Tech in Cybersecurity",
      status: "Active",
      description: "Joint academic program offering comprehensive cybersecurity education",
      website: "https://ku.edu.np/",
      established: "2024"
    }
  ]

  const upcomingPartnerships = [
    { name: "Tech Industry Leaders", type: "Industry", status: "In Development", region: "International", focus: "Cybersecurity Internships" },
    { name: "Research Institutions", type: "Research", status: "Planning", region: "Regional", focus: "Cybersecurity Research" },
    { name: "Global Universities", type: "Academic", status: "Exploring", region: "International", focus: "Student Exchange" }
  ]

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section with Enhanced Visual Appeal */}
          <section className="relative bg-gradient-to-br from-light via-background to-light/50 py-16 overflow-hidden">
            {/* Enhanced background decorative elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute -top-40 -left-40 w-96 h-96 bg-crimson/10 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute top-1/2 -right-20 w-80 h-80 bg-gold/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
              <div className="absolute bottom-0 left-1/2 w-96 h-96 bg-gradient-to-tr from-crimson/5 to-gold/5 rounded-full blur-3xl"></div>
            </div>
            
            {/* Grid pattern overlay */}
            <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
            
            <div className="container mx-auto px-4 max-w-6xl relative z-10">
              <div className="max-w-4xl mx-auto text-center">
                <Badge variant="outline" className="inline-flex items-center gap-2 px-4 py-2 mb-6 bg-white/80 backdrop-blur-sm border-crimson/20">
                  <Globe className="h-4 w-4 text-crimson" />
                  Global Collaboration Network
                </Badge>
                
                <h1 className="heading-xl text-gray-900 mb-6">
                  Building Bridges Through
                  <span className="block text-transparent bg-gradient-to-r from-crimson to-gold bg-clip-text">
                    Strategic Partnerships
                  </span>
                </h1>
                
                <p className="body-lg text-muted-foreground max-w-3xl mx-auto mb-8">
                  We're forging meaningful partnerships with leading academic institutions and industry pioneers 
                  to create transformative opportunities for our students, faculty, and the global community.
                </p>

                {/* Stats Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
                  {partnershipStats.map((stat, index) => (
                    <div key={index} className="bg-white/80 backdrop-blur-sm rounded-lg border border-white/50 p-4 text-center hover:bg-white/90 transition-all duration-300">
                      <div className="text-2xl font-bold text-crimson mb-1">{stat.value}</div>
                      <div className="text-sm font-medium text-gray-900 mb-1">{stat.label}</div>
                      <div className="text-xs text-muted-foreground">{stat.description}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Current Active Partnerships */}
          <section className="py-16 bg-white">
            <div className="container mx-auto px-4 max-w-6xl">
              <div className="text-center mb-12">
                <Badge variant="outline" className="inline-flex items-center gap-2 px-3 py-1 mb-4 border-green-500/30 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  Active Partnerships
                </Badge>
                <h2 className="heading-lg text-gray-900 mb-4">Our Current Partnerships</h2>
                <p className="body-md text-muted-foreground max-w-3xl mx-auto">
                  We're proud to collaborate with world-class institutions that share our commitment 
                  to educational excellence and innovation.
                </p>
              </div>

              {/* Current Partnership Showcase */}
              <div className="grid gap-8 mb-12">
                {currentPartnerships.map((partnership, index) => (
                  <Card key={index} className="hover:shadow-xl transition-all duration-300 border-l-4 border-l-green-500 bg-gradient-to-r from-green-50/50 to-white">
                    <CardContent className="p-8">
                      <div className="grid lg:grid-cols-3 gap-6 items-center">
                        <div className="lg:col-span-2">
                          <div className="flex items-center gap-3 mb-4">
                            <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center">
                              <GraduationCap className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <h3 className="text-2xl font-bold text-gray-900">{partnership.name}</h3>
                              <div className="flex items-center gap-2 text-muted-foreground">
                                <MapPin className="h-4 w-4" />
                                <span>{partnership.country}</span>
                                <span>•</span>
                                <span>Established {partnership.established}</span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="space-y-3">
                            <div className="flex items-center gap-2">
                              <Badge variant="secondary" className="bg-green-100 text-green-700">
                                {partnership.type}
                              </Badge>
                              <Badge variant="outline" className="border-green-200">
                                <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                                {partnership.status}
                              </Badge>
                            </div>
                            
                            <div className="bg-white/60 rounded-lg p-4 border border-green-100">
                              <div className="flex items-center gap-2 mb-2">
                                <Shield className="h-5 w-5 text-crimson" />
                                <h4 className="font-semibold text-gray-900">{partnership.program}</h4>
                              </div>
                              <p className="text-muted-foreground text-sm">{partnership.description}</p>
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-center lg:text-right">
                          <Link href={partnership.website} target="_blank" rel="noopener noreferrer">
                            <Button variant="outline" className="border-green-500/30 hover:bg-green-50 group">
                              Visit University
                              <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
                </div>
          </section>

          {/* Partnerships in Development */}
          <section className="py-16 bg-gradient-to-b from-light/30 to-background">
            <div className="container mx-auto px-4 max-w-6xl">
              <div className="text-center mb-12">
                <Badge variant="outline" className="inline-flex items-center gap-2 px-3 py-1 mb-4 border-gold/30 text-gold">
                  <Target className="h-4 w-4" />
                  Building Our Network
                </Badge>
                <h2 className="heading-lg text-gray-900 mb-4">Partnerships in Development</h2>
                <p className="body-md text-muted-foreground max-w-3xl mx-auto">
                  We're actively working to establish additional strategic partnerships to expand opportunities 
                  for our students and enhance our academic offerings.
                </p>
              </div>
              
              {/* Upcoming Partnerships */}
              <div className="grid md:grid-cols-3 gap-6 mb-12">
                {upcomingPartnerships.map((partnership, index) => (
                  <Card key={index} className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-gold/30">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant={partnership.status === 'In Development' ? 'default' : 'secondary'} className="text-xs">
                          {partnership.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">{partnership.region}</span>
                      </div>
                      <CardTitle className="text-lg">{partnership.name}</CardTitle>
                      <CardDescription className="space-y-1">
                        <div>{partnership.type} Partnership</div>
                        <div className="text-xs text-muted-foreground">Focus: {partnership.focus}</div>
                      </CardDescription>
                    </CardHeader>
                  </Card>
                ))}
                    </div>
                  </div>
          </section>

          {/* Partnership Types - Enhanced Design */}
          <section className="py-16 bg-white">
            <div className="container mx-auto px-4 max-w-6xl">
              <div className="text-center mb-12">
                <h2 className="heading-lg text-gray-900 mb-4">Partnership Categories</h2>
                <p className="body-md text-muted-foreground max-w-2xl mx-auto">
                  We're developing diverse partnership models to maximize opportunities for our community.
                  </p>
                </div>
                
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-white to-white/80">
                  <CardHeader>
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-crimson to-crimson/80 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      <GraduationCap className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">Academic Partnerships</CardTitle>
                    <CardDescription>
                      Global university networks for exchange programs and collaborative research
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Joint degree programs
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Student exchange programs
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Faculty collaboration
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Shared academic resources
                    </div>
                  </CardContent>
                </Card>

                <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-gold to-gold/80 flex items-center justify-center">
                  <CardHeader>
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-gold to-gold/80 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Building className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">Industry Partnerships</CardTitle>
                    <CardDescription>
                      Strategic alliances with leading companies and organizations
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Cybersecurity internships
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Mentorship programs
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Guest lectures & workshops
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Career development
                    </div>
                  </CardContent>
                </Card>

                <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
                  <CardHeader>
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Lightbulb className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">Research Partnerships</CardTitle>
                    <CardDescription>
                      Collaborative research initiatives and innovation projects
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Cybersecurity research
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Innovation labs
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Technology transfer
                  </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Publication opportunities
                </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </section>

          {/* Partnership Benefits - Enhanced Layout */}
          <section className="py-20 bg-gradient-to-b from-light/30 to-background relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute top-20 -left-20 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl"></div>
              <div className="absolute bottom-20 -right-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl"></div>
            </div>
            
            <div className="container mx-auto px-4 max-w-7xl relative">
              <div className="text-center mb-16">
                <Badge variant="outline" className="inline-flex items-center gap-2 px-4 py-2 mb-6 bg-white/80 backdrop-blur-sm border-primary/20">
                  <Target className="h-4 w-4 text-primary" />
                  Real Impact
                </Badge>
                <h2 className="heading-lg text-gray-900 mb-6">Partnership Impact</h2>
                <p className="body-lg text-muted-foreground max-w-3xl mx-auto mb-8">
                  Our partnerships create tangible value and opportunities across our entire educational ecosystem, 
                  with measurable benefits for students, faculty, and partner organizations.
                </p>
                
                {/* Impact Statistics */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12">
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 text-center border border-white/50 hover:bg-white/90 transition-all duration-300">
                    <div className="text-3xl font-bold text-blue-600 mb-2">100%</div>
                    <div className="text-sm text-muted-foreground">Student Satisfaction</div>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 text-center border border-white/50 hover:bg-white/90 transition-all duration-300">
                    <div className="text-3xl font-bold text-purple-600 mb-2">24/7</div>
                    <div className="text-sm text-muted-foreground">Global Support</div>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 text-center border border-white/50 hover:bg-white/90 transition-all duration-300">
                    <div className="text-3xl font-bold text-green-600 mb-2">50+</div>
                    <div className="text-sm text-muted-foreground">Career Paths</div>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 text-center border border-white/50 hover:bg-white/90 transition-all duration-300">
                    <div className="text-3xl font-bold text-orange-600 mb-2">15+</div>
                    <div className="text-sm text-muted-foreground">Research Areas</div>
                  </div>
                </div>
              </div>
              
              <div className="grid lg:grid-cols-3 gap-8">
                {/* For Students - Enhanced Card */}
                <Card className="group relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-blue-50/30 border-blue-100 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardContent className="p-8 relative">
                    <div className="text-center mb-8">
                      <div className="w-20 h-20 rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <Users className="h-10 w-10 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">For Students</h3>
                      <p className="text-muted-foreground text-sm">Empowering the next generation of cybersecurity professionals</p>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-blue-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Global Learning Opportunities</div>
                          <div className="text-xs text-muted-foreground">Study at Kathmandu University and access international curricula</div>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-blue-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Industry-Ready Skills</div>
                          <div className="text-xs text-muted-foreground">Hands-on cybersecurity training with real-world applications</div>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-blue-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Career Excellence</div>
                          <div className="text-xs text-muted-foreground">Premium placement opportunities in cybersecurity sector</div>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-blue-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Cutting-Edge Resources</div>
                          <div className="text-xs text-muted-foreground">Access to advanced labs and research facilities</div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Student Testimonial */}
                    <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-lg border border-blue-200">
                      <div className="text-xs text-blue-700 font-medium mb-1">Student Perspective</div>
                      <div className="text-sm text-gray-700 italic">"The partnership opens doors to international cybersecurity expertise I never imagined having access to."</div>
                    </div>
                  </CardContent>
                </Card>

                {/* For Faculty - Enhanced Card */}
                <Card className="group relative overflow-hidden bg-gradient-to-br from-purple-50 via-white to-purple-50/30 border-purple-100 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardContent className="p-8 relative">
                    <div className="text-center mb-8">
                      <div className="w-20 h-20 rounded-2xl bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <GraduationCap className="h-10 w-10 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">For Faculty</h3>
                      <p className="text-muted-foreground text-sm">Advancing academic excellence through collaboration</p>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-purple-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Research Collaboration</div>
                          <div className="text-xs text-muted-foreground">Joint cybersecurity research projects with KU experts</div>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-purple-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Professional Growth</div>
                          <div className="text-xs text-muted-foreground">International conferences and development opportunities</div>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-purple-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Global Networks</div>
                          <div className="text-xs text-muted-foreground">Connect with international cybersecurity academia</div>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-purple-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Enhanced Capabilities</div>
                          <div className="text-xs text-muted-foreground">Access to advanced research tools and methodologies</div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Faculty Quote */}
                    <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-purple-100/50 rounded-lg border border-purple-200">
                      <div className="text-xs text-purple-700 font-medium mb-1">Faculty Insight</div>
                      <div className="text-sm text-gray-700 italic">"Collaborating with KU has elevated our research capabilities and opened new frontiers in cybersecurity education."</div>
                    </div>
                  </CardContent>
                </Card>

                {/* For Partners - Enhanced Card */}
                <Card className="group relative overflow-hidden bg-gradient-to-br from-green-50 via-white to-green-50/30 border-green-100 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                  <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardContent className="p-8 relative">
                    <div className="text-center mb-8">
                      <div className="w-20 h-20 rounded-2xl bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <Building className="h-10 w-10 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">For Partners</h3>
                      <p className="text-muted-foreground text-sm">Creating mutual value through strategic collaboration</p>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-green-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Elite Talent Pipeline</div>
                          <div className="text-xs text-muted-foreground">Access to top-tier cybersecurity graduates</div>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-green-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Innovation Hub</div>
                          <div className="text-xs text-muted-foreground">Collaborative research and technology development</div>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-green-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">Global Recognition</div>
                          <div className="text-xs text-muted-foreground">Enhanced brand visibility and educational impact</div>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-white/60 backdrop-blur-sm border border-green-100 hover:bg-white/80 transition-all duration-200">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                  <div>
                          <div className="font-medium text-gray-900 text-sm">Market Intelligence</div>
                          <div className="text-xs text-muted-foreground">Strategic insights into cybersecurity trends and needs</div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Partner Perspective */}
                    <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-green-100/50 rounded-lg border border-green-200">
                      <div className="text-xs text-green-700 font-medium mb-1">Partner Value</div>
                      <div className="text-sm text-gray-700 italic">"This partnership represents the future of cybersecurity education - innovative, practical, and globally connected."</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Bottom Call-to-Action */}
              <div className="text-center mt-16">
                <Card className="inline-block bg-white/90 backdrop-blur-sm border-white/50 shadow-lg">
                  <CardContent className="p-8">
                    <div className="flex items-center gap-4 max-w-md">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-crimson to-gold flex items-center justify-center">
                        <Lightbulb className="h-6 w-6 text-white" />
                      </div>
                      <div className="text-left">
                        <h4 className="font-semibold text-gray-900 mb-1">Ready to Experience the Impact?</h4>
                        <p className="text-sm text-muted-foreground">Join our partnership network and create lasting value together.</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </section>

          {/* Enhanced Contact Section */}
          <section className="py-16 bg-gradient-to-br from-crimson/5 via-background to-gold/5">
            <div className="container mx-auto px-4 max-w-6xl">
              <Card className="bg-white/90 backdrop-blur-sm border-white/50 shadow-xl">
                <CardContent className="p-8 md:p-12">
                  <div className="text-center">
                    <Badge variant="outline" className="inline-flex items-center gap-2 px-3 py-1 mb-4 border-crimson/20 text-crimson">
                      <Users className="h-4 w-4" />
                      Partnership Opportunities
                    </Badge>
                    <h2 className="heading-lg text-gray-900 mb-4">Ready to Partner with Us?</h2>
                    <p className="body-md text-muted-foreground mb-8 max-w-2xl mx-auto">
                      We're excited to explore collaboration opportunities with organizations that share our 
                      commitment to educational excellence and innovation. Let's discuss how we can create 
                      mutual value and impact.
                    </p>
                    
                    <div className="flex items-center justify-center gap-3 text-muted-foreground">
                      <Mail className="h-5 w-5 text-crimson" />
                      <span><EMAIL></span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
} 