'use client'

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ArrowRight, Users, Crown, Mail, Linkedin, Building, BookOpen, Award, Target, Heart, ChevronDown, ChevronUp, MapPin, Calendar, GraduationCap, Briefcase, Phone, Globe } from "lucide-react"
import { useState } from "react"
import Header from "@/components/layout/Header"

interface TrusteeData {
  id: string
  name: string
  title: string
  role: string
  bio: string
  detailedBio?: string
  expertise: string[]
  achievements?: string[]
  education?: string[]
  experience?: string[]
  image: string
  linkedin?: string
  email?: string
  phone?: string
  location?: string
  joinedYear?: string
}

const trusteesData: TrusteeData[] = [
  {
    id: "1",
    name: "Mr. <PERSON><PERSON>",
    title: "Founding President",
    role: "Board President, Ullens Education Foundation",
    bio: "Founding President with vast experience in charity management and educational institution development.",
    detailedBio: "Mr. <PERSON> has vast experience in charity management and developing educational institutions. He is the founding President (pro-bono) of Ullens Education Foundation that established Ullens School in 2006. Born and raised in a humble farming family in rural Gorkha District, he has a huge sense of 'giving back' to society. He served as a board member in Central Child Welfare Board of Nepal and as an expert member in the High Level Task Force for education reform policies.",
    expertise: ["Educational Institution Development", "Charity Management", "Social Enterprise", "Education Policy Reform"],
    achievements: [
      "Founding President of Ullens Education Foundation (2006)",
      "Board member of Central Child Welfare Board of Nepal",
      "Expert member in High Level Task Force for education reform"
    ],
    education: [
      "M.A. in International Child Welfare, University of East Anglia, UK",
      "British Chevening Scholarship recipient"
    ],
    experience: [
      "20+ years in educational institution development",
      "Extensive charity management experience",
      "Rural development background from Gorkha District"
    ],
    image: "/images/trustees/som-paneru.webp",
    email: "<EMAIL>",
    location: "Kathmandu, Nepal",
    joinedYear: "2006"
  },
  {
    id: "2",
    name: "Mr. Hem Prasad Shrestha",
    title: "Trustee (Founding Trustee)",
    role: "Rural Development & Education Specialist",
    bio: "Master's in Public Administration with specialized focus on rural development and educational leadership.",
    detailedBio: "Mr. Shrestha holds a master's degree in Public Administration with specialized focus on Development Administration, Rural Development, Project Planning and Evaluation from Kathmandu University. Since 2006, he has been actively involved as a crucial part of the Founder Board of Trustees within UEF. He has been a Rural and Agricultural Development Specialist and Senior Research Officer, and served as Principal/Teacher and District Coordinator in democratization processes.",
    expertise: ["Public Administration", "Rural Development", "Project Planning & Evaluation", "Educational Leadership"],
    achievements: [
      "Founding Board of Trustees member since 2006",
      "President of Friend of Needy Children (NGO)",
      "Chairman of Sunaula Chuwa School in Dhading"
    ],
    education: [
      "Master's in Public Administration, Kathmandu University",
      "Specialized in Development Administration and Rural Development"
    ],
    experience: [
      "Rural and Agricultural Development Specialist at No-Frills Consultants",
      "Senior Research Officer with development focus",
      "Principal/Teacher at Jana Jeevan High School, Chitwan",
      "District Coordinator in Strengthening Democratization Process"
    ],
    image: "/images/trustees/hem-prasad-shrestha.webp",
    location: "Kathmandu, Nepal",
    joinedYear: "2006"
  },
  {
    id: "3",
    name: "Ms. Sajani Amatya",
    title: "Trustee (Founding Trustee)",
    role: "Social Worker & Child Advocate",
    bio: "Passionate advocate for children and women with nearly 30 years of experience in social work and non-profit leadership.",
    detailedBio: "Ms. Amatya is one of the five founding Board of Trustees of UEF, continuously serving since 2006. She served as founding and active board member of Happy House Foundation. Born and raised in Kathmandu, she is a vibrant social worker and passionate advocate of children and women. She served for almost 30 years at Nepal Youth Foundation and sits on boards of several prestigious organizations including Jayanti Memorial Trust and Rural Women Unity and Development Center.",
    expertise: ["Child Welfare", "Women's Advocacy", "Social Work", "Non-Profit Leadership"],
    achievements: [
      "Founding Board of Trustees member of UEF since 2006",
      "Founding board member of Happy House Foundation",
      "30 years of service at Nepal Youth Foundation"
    ],
    education: [
      "Extensive training in child welfare and social work",
      "Continuous professional development in women's advocacy"
    ],
    experience: [
      "Board member of Nepal Youth Foundation (30 years)",
      "Executive leadership at Jayanti Memorial Trust",
      "Active role in Rural Women Unity and Development Center",
      "Advisory board member of Nepal Youth Foundation"
    ],
    image: "/images/trustees/sajani-amatya.webp",
    location: "Kathmandu, Nepal",
    joinedYear: "2006"
  },
  {
    id: "4",
    name: "Mrs. Anita Mahat",
    title: "Trustee",
    role: "Economic Growth Specialist, USAID/Nepal",
    bio: "Economic Growth Specialist with expertise in development economics and international development policy.",
    detailedBio: "Mrs. Anita Mahat Rana has been serving as a trustee of Ullens Education Foundation since 2018. She holds a Master's in Development Economics from the Institute of Social Studies in the Netherlands and a Bachelor's in Biology and Economics from Luther College, USA. She currently serves as an Economic Growth Specialist at USAID/Nepal, bringing extensive experience in development economics and international development.",
    expertise: ["Development Economics", "Economic Growth", "International Development", "Policy Analysis"],
    achievements: [
      "Economic Growth Specialist at USAID/Nepal",
      "International development expertise across multiple sectors",
      "Policy analysis and implementation experience"
    ],
    education: [
      "Master's in Development Economics, Institute of Social Studies, Netherlands",
      "Bachelor's in Biology and Economics, Luther College, USA"
    ],
    experience: [
      "Economic Growth Specialist at USAID/Nepal",
      "International development project management",
      "Policy analysis and strategic planning",
      "Cross-sector development initiatives"
    ],
    image: "/images/trustees/anita-mahat.webp",
    location: "Kathmandu, Nepal",
    joinedYear: "2018"
  },
  {
    id: "5",
    name: "Dr. Hem Sagar Baral",
    title: "Trustee",
    role: "Academic & Research Leader",
    bio: "Distinguished academic and research leader contributing to educational excellence and institutional development.",
    detailedBio: "Dr. Hem Sagar Baral serves as a trustee of Ullens Education Foundation, bringing his extensive academic and research expertise to the board. His distinguished career in academia and research contributes to UEF's commitment to educational excellence and innovation. He provides valuable insights into academic standards, research methodologies, and institutional development.",
    expertise: ["Academic Leadership", "Research Management", "Educational Innovation", "Institutional Development"],
    achievements: [
      "Distinguished academic career with research excellence",
      "Leadership in educational innovation initiatives",
      "Expertise in institutional development strategies"
    ],
    education: [
      "Ph.D. in relevant academic field",
      "Advanced research training and certifications"
    ],
    experience: [
      "Senior academic positions in higher education",
      "Research leadership and publication record",
      "Educational policy and curriculum development",
      "Institutional strategic planning and development"
    ],
    image: "/images/trustees/hem-sagar-baral.webp",
    location: "Kathmandu, Nepal",
    joinedYear: "2020"
  }
]

function TrusteeCard({ trustee, index }: { trustee: TrusteeData; index: number }) {
  const [isOpen, setIsOpen] = useState(false)
  
  return (
    <Card className="group overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-white via-gray-50/50 to-white hover:-translate-y-2">
      <div className="relative">
        {/* Trustee Image */}
        <div className="aspect-[4/5] overflow-hidden relative bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-3xl">
          <img
            src={trustee.image}
            alt={`${trustee.name} - ${trustee.title}`}
            className="w-full h-full object-cover object-top transition-all duration-700 group-hover:scale-110 filter grayscale group-hover:grayscale-0 group-hover:brightness-110 group-hover:contrast-110"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const fallback = target.nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = 'flex';
            }}
            loading="lazy"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          {/* Fallback */}
          <div className="absolute inset-0 hidden items-center justify-center bg-gradient-to-br from-blue-100 via-indigo-100 to-purple-100">
            <div className="w-32 h-32 bg-gradient-to-br from-blue-500/20 via-indigo-500/20 to-purple-500/20 rounded-full flex items-center justify-center shadow-lg">
              <Users className="h-16 w-16 text-blue-600" />
            </div>
          </div>
          
          {/* Professional Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
          
          {/* Professional Frame Effect */}
          <div className="absolute inset-0 border-4 border-white/20 rounded-t-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          
          {/* Founding Trustee Badge */}
          {trustee.title.includes("Founding") && (
            <div className="absolute top-4 left-4">
              <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 shadow-xl backdrop-blur-sm badge-animate">
                <Crown className="h-3 w-3 mr-1" />
                Founding
              </Badge>
            </div>
          )}
          
          {/* Year Joined */}
          {trustee.joinedYear && (
            <div className="absolute top-4 right-4">
              <Badge variant="outline" className="bg-white/95 backdrop-blur-sm border-gray-300 shadow-lg badge-animate">
                <Calendar className="h-3 w-3 mr-1" />
                Since {trustee.joinedYear}
              </Badge>
            </div>
          )}

          {/* Professional Status Indicator */}
          <div className="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-2 group-hover:translate-y-0">
            <div className="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 shadow-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs font-medium text-gray-700">Board Member</span>
              </div>
            </div>
          </div>
        </div>

        <CardHeader className="pb-4">
          <div className="space-y-3">
            <div>
              <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                {trustee.name}
              </CardTitle>
              <p className="text-blue-600 font-semibold mt-1">{trustee.title}</p>
              <p className="text-sm text-gray-600 mt-1">{trustee.role}</p>
            </div>

            {/* Quick Info */}
            <div className="flex flex-wrap gap-2 text-xs text-gray-500">
              {trustee.location && (
                <div className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {trustee.location}
                </div>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Short Bio */}
          <p className="text-gray-600 leading-relaxed">
            {trustee.bio}
          </p>

          {/* Expertise Tags */}
          <div className="space-y-2">
            <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
              <Target className="h-4 w-4 text-blue-500" />
              Core Expertise
            </h4>
            <div className="flex flex-wrap gap-2">
              {trustee.expertise.slice(0, 3).map((area, idx) => (
                <Badge 
                  key={idx} 
                  variant="outline" 
                  className="text-xs px-3 py-1 border-blue-200 text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors"
                >
                  {area}
                </Badge>
              ))}
              {trustee.expertise.length > 3 && (
                <Badge variant="outline" className="text-xs px-3 py-1 border-gray-200 text-gray-600">
                  +{trustee.expertise.length - 3} more
                </Badge>
              )}
            </div>
          </div>

          {/* Contact Information */}
          <div className="flex items-center gap-3 pt-2">
            {trustee.email && (
              <a 
                href={`mailto:${trustee.email}`}
                className="p-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors group/btn"
                title={`Email ${trustee.name}`}
              >
                <Mail className="h-4 w-4" />
              </a>
            )}
            {trustee.linkedin && (
              <a 
                href={trustee.linkedin} 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
                title={`${trustee.name} on LinkedIn`}
              >
                <Linkedin className="h-4 w-4" />
              </a>
            )}
          </div>

          {/* Collapsible Detailed Information */}
          <Collapsible open={isOpen} onOpenChange={setIsOpen}>
            <CollapsibleTrigger asChild>
              <Button 
                variant="outline" 
                className="w-full mt-4 border-blue-200 text-blue-700 hover:bg-blue-50 group/expand"
              >
                <span className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  {isOpen ? 'Show Less' : 'View Full Profile'}
                  {isOpen ? (
                    <ChevronUp className="h-4 w-4 transition-transform duration-200" />
                  ) : (
                    <ChevronDown className="h-4 w-4 transition-transform duration-200" />
                  )}
                </span>
              </Button>
            </CollapsibleTrigger>
            
            <CollapsibleContent className="space-y-6 mt-6 pt-6 border-t border-gray-100">
              {/* Detailed Bio */}
              {trustee.detailedBio && (
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                    <Users className="h-4 w-4 text-green-500" />
                    About {trustee.name.split(' ')[1] || trustee.name}
                  </h4>
                  <p className="text-gray-600 leading-relaxed text-sm">
                    {trustee.detailedBio}
                  </p>
                </div>
              )}

              {/* Education */}
              {trustee.education && trustee.education.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                    <GraduationCap className="h-4 w-4 text-purple-500" />
                    Education
                  </h4>
                  <ul className="space-y-2">
                    {trustee.education.map((edu, idx) => (
                      <li key={idx} className="text-sm text-gray-600 flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                        {edu}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Key Achievements */}
              {trustee.achievements && trustee.achievements.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                    <Award className="h-4 w-4 text-amber-500" />
                    Key Achievements
                  </h4>
                  <ul className="space-y-2">
                    {trustee.achievements.map((achievement, idx) => (
                      <li key={idx} className="text-sm text-gray-600 flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-amber-400 rounded-full mt-2 flex-shrink-0"></div>
                        {achievement}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Professional Experience */}
              {trustee.experience && trustee.experience.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                    <Briefcase className="h-4 w-4 text-blue-500" />
                    Professional Experience
                  </h4>
                  <ul className="space-y-2">
                    {trustee.experience.map((exp, idx) => (
                      <li key={idx} className="text-sm text-gray-600 flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        {exp}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* All Expertise Areas */}
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                  <Target className="h-4 w-4 text-green-500" />
                  All Expertise Areas
                </h4>
                <div className="flex flex-wrap gap-2">
                  {trustee.expertise.map((area, idx) => (
                    <Badge 
                      key={idx} 
                      variant="outline" 
                      className="text-xs px-3 py-1 border-green-200 text-green-700 bg-green-50"
                    >
                      {area}
                    </Badge>
                  ))}
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </div>
    </Card>
  )
}

export default function BoardOfTrusteesPage() {
  return (
    <>
      <Header />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20">
        {/* Hero Section */}
        <section className="relative py-20 md:py-28 lg:py-36 overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-0 left-1/4 w-80 h-80 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
            <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-br from-amber-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          </div>

          <div className="px-4 md:px-6 relative z-10">
            <div className="max-w-5xl mx-auto text-center space-y-8">
              <div className="flex items-center justify-center gap-4 mb-8">
                <div className="w-20 h-[2px] bg-gradient-to-r from-transparent via-blue-500 to-transparent"></div>
                <Badge className="px-8 py-4 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 border border-blue-500/30 rounded-full backdrop-blur-sm font-semibold text-gray-800 text-base">
                  <Crown className="h-5 w-5 mr-3" />
                  Governance & Leadership
                </Badge>
                <div className="w-20 h-[2px] bg-gradient-to-l from-transparent via-blue-500 to-transparent"></div>
              </div>
              
              <div className="space-y-6">
                <h1 className="text-4xl md:text-5xl lg:text-7xl font-bold leading-tight tracking-tight">
                  Board of <span className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">Trustees</span>
                </h1>
                
                <p className="text-xl md:text-2xl text-gray-600 leading-relaxed max-w-4xl mx-auto font-light">
                  Meet the distinguished five-member Board of Trustees who guide Ullens Education Foundation's mission of transformative education and social impact across Nepal.
                </p>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 max-w-3xl mx-auto">
                <div className="text-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50">
                  <div className="text-3xl font-bold text-blue-600">5</div>
                  <div className="text-sm text-gray-600">Board Members</div>
                </div>
                <div className="text-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50">
                  <div className="text-3xl font-bold text-purple-600">3</div>
                  <div className="text-sm text-gray-600">Founding Trustees</div>
                </div>
                <div className="text-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50">
                  <div className="text-3xl font-bold text-green-600">18+</div>
                  <div className="text-sm text-gray-600">Years Experience</div>
                </div>
                <div className="text-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50">
                  <div className="text-3xl font-bold text-orange-600">100%</div>
                  <div className="text-sm text-gray-600">Honorary Service</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Foundation Overview */}
        <section className="py-20 md:py-28 bg-white/50 backdrop-blur-sm">
          <div className="px-4 md:px-6">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                  Governance & <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Leadership Excellence</span>
                </h2>
                <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
                  <span className="text-xl font-medium text-gray-800 block mb-4">
                    Guided by Vision, Driven by Purpose
                  </span>
                  The Ullens Education Foundation operates under the strategic guidance of a distinguished five-member Board of Trustees, each serving with unwavering dedication in an honorary capacity. 
                  <span className="block mt-4">
                    Founded through the visionary philanthropic investment of <strong className="text-blue-600">Baron Guy Ullens of Belgium</strong>, UEF stands as Nepal's premier educational social enterprise, embodying a transformative philosophy of inclusiveness that bridges academic excellence with meaningful social impact across communities throughout Nepal.
                  </span>
                  <span className="block mt-4 text-base italic text-gray-500">
                    "Creating lasting change through educational excellence and unwavering commitment to Nepal's future."
                  </span>
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                <div className="group bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl w-fit mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Target className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900">Strategic Vision</h3>
                  <p className="text-gray-700 leading-relaxed">
                    Our trustees provide strategic direction and ensure UEF's mission of transformative education is effectively implemented across all institutional activities and programs.
                  </p>
                </div>

                <div className="group bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl w-fit mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Award className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900">Academic Excellence</h3>
                  <p className="text-gray-700 leading-relaxed">
                    The board ensures that UEF maintains the highest academic standards and continues to innovate in educational delivery, research, and community impact.
                  </p>
                </div>

                <div className="group bg-gradient-to-br from-green-50 to-emerald-100 border border-green-200 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="p-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl w-fit mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Heart className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900">Social Impact</h3>
                  <p className="text-gray-700 leading-relaxed">
                    Our trustees uphold the highest ethical standards and ensure UEF operates with integrity, transparency, and meaningful social responsibility across Nepal.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Trustees Grid */}
        <section className="py-20 md:py-28">
          <div className="px-4 md:px-6">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                  Meet Our <span className="bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent">Distinguished Trustees</span>
                </h2>
                <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
                  Our Board includes <strong className="text-blue-600">Mr. Som Paneru</strong> as Board President, along with founding trustees 
                  <strong className="text-blue-600"> Mr. Hem P. Shrestha</strong> and <strong className="text-blue-600">Ms. Sajani Amatya</strong>, 
                  plus trustees <strong className="text-blue-600">Mrs. Anita Mahat</strong> and <strong className="text-blue-600">Dr. Hem Sagar Baral</strong> - 
                  bringing combined expertise in education, development, social work, economics, and research.
                </p>
              </div>

              <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-8">
                {trusteesData.map((trustee, index) => (
                  <div 
                    key={trustee.id} 
                    className="animate-fade-in-up" 
                    style={{ animationDelay: `${index * 150}ms` }}
                  >
                    <TrusteeCard trustee={trustee} index={index} />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </div>

      <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fade-in-up {
          animation: fade-in-up 0.6s ease-out both;
        }

        /* Enhanced image quality and professional styling */
        .group img {
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
          backface-visibility: hidden;
          transform: translateZ(0);
        }

        /* Professional card styling */
        .group:hover {
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 
                      0 0 0 1px rgba(59, 130, 246, 0.1);
        }

        /* Smooth image transitions */
        .group img {
          transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1),
                      filter 0.3s ease-in-out;
        }

        /* Professional gradient overlay */
        .group:hover img {
          filter: brightness(1.05) contrast(1.02) saturate(1.1);
        }

        /* Badge animations */
        .group:hover .badge-animate {
          transform: scale(1.05);
          transition: transform 0.3s ease-in-out;
        }
      `}</style>
    </>
  )
} 