import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { ArrowRight, Users, Target, Heart, Globe, BookOpen, Award, Lightbulb, GraduationCap, Building, Crown } from "lucide-react"

export default function AboutUEFPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      {/* Enhanced Hero Section */}
      <section className="relative py-24 md:py-32 lg:py-40 overflow-hidden bg-gradient-to-br from-background via-muted/5 to-background">
        {/* Enhanced Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-br from-crimson/15 to-pink-500/15 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-0 left-1/4 w-80 h-80 bg-gradient-to-br from-blue-500/15 to-cyan-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/2 left-1/2 w-72 h-72 bg-gradient-to-br from-amber-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          
          {/* Floating Elements */}
          <div className="absolute top-20 left-20 text-crimson/20 animate-float">
            <Building className="h-8 w-8" />
          </div>
          <div className="absolute top-40 right-32 text-blue-500/20 animate-float" style={{ animationDelay: '1s' }}>
            <GraduationCap className="h-6 w-6" />
          </div>
          <div className="absolute bottom-32 right-20 text-amber-500/20 animate-float" style={{ animationDelay: '2s' }}>
            <Globe className="h-7 w-7" />
          </div>
          
          {/* Subtle Grid Pattern */}
          <div className="absolute inset-0 opacity-[0.03]" style={{
            backgroundImage: `
              radial-gradient(circle at 25% 25%, #c82f48 1px, transparent 1px),
              radial-gradient(circle at 75% 75%, #3b82f6 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px, 80px 80px'
          }}></div>
        </div>

        <div className="px-4 md:px-6 relative z-10">
          <div className="max-w-5xl mx-auto text-center space-y-10">
            {/* Enhanced Badge */}
            <div className="flex items-center justify-center gap-6 mb-8 opacity-0 animate-fade-in-up">
              <div className="w-20 h-[2px] bg-gradient-to-r from-transparent via-crimson to-transparent"></div>
              <div className="group px-8 py-4 bg-gradient-to-r from-crimson/20 to-pink-500/20 border-2 border-crimson/50 rounded-full backdrop-blur-md font-semibold flex items-center hover:from-crimson/30 hover:to-pink-500/30 hover:border-crimson/70 transition-all duration-500 shadow-lg hover:shadow-xl hover:shadow-crimson/20">
                <Building className="h-5 w-5 mr-3 text-crimson group-hover:scale-110 transition-transform duration-300" />
                <span className="text-crimson font-bold text-lg">About UEF</span>
              </div>
              <div className="w-20 h-[2px] bg-gradient-to-l from-transparent via-crimson to-transparent"></div>
            </div>
            
            {/* Enhanced Title */}
            <div className="space-y-6 opacity-0 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-black leading-[1.1] tracking-tight">
                Ullens{" "}
                <span className="bg-gradient-to-r from-crimson via-pink-500 to-purple-500 bg-clip-text text-transparent animate-gradient-x">
                  Educational
                </span>
                <br />
                Foundation
              </h1>
              
              <div className="w-24 h-1 bg-gradient-to-r from-crimson to-pink-500 mx-auto rounded-full"></div>
            </div>
            
            {/* Enhanced Subtitle */}
            <p className="text-xl md:text-2xl lg:text-3xl text-muted-foreground leading-relaxed max-w-4xl mx-auto font-light opacity-0 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              <span className="font-medium text-foreground">A Vision for Excellence in Education in Nepal</span>
              <br className="hidden md:block" />
              <span className="text-lg md:text-xl lg:text-2xl mt-2 block">
                Pioneering the future of higher education through innovation, excellence, and a commitment to transforming lives and communities.
              </span>
            </p>


          </div>
        </div>
      </section>

      {/* New History Section with Patrons */}
      <section className="py-20 md:py-32 bg-gradient-to-b from-muted/10 to-background relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 right-0 w-80 h-80 bg-gradient-to-br from-amber-500/15 to-orange-500/15 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-0 left-1/4 w-96 h-96 bg-gradient-to-br from-purple-500/10 to-indigo-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
        </div>

        <div className="px-4 md:px-6 relative z-10">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <div className="text-center mb-20">
              <div className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-2 border-amber-500/30 rounded-full mb-8 backdrop-blur-sm">
                <Heart className="h-6 w-6 text-amber-500" />
                <span className="text-amber-600 font-bold text-lg">Our History</span>
              </div>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-black mb-8 leading-[1.1]">
                A Legacy of <span className="bg-gradient-to-r from-amber-500 via-orange-500 to-red-500 bg-clip-text text-transparent animate-gradient-x">Educational Excellence</span>
              </h2>
              <div className="w-32 h-1 bg-gradient-to-r from-amber-500 to-orange-500 mx-auto mt-8 rounded-full"></div>
            </div>

            {/* Patrons Section */}
            <div className="mb-20">
              <div className="bg-gradient-to-br from-background to-amber-50/50 dark:to-amber-950/20 backdrop-blur-sm border border-amber-500/20 rounded-3xl p-8 md:p-12 shadow-xl hover:shadow-2xl hover:shadow-amber-500/10 transition-all duration-500">
                <div className="text-center mb-12">
                  <div className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-purple-500/10 to-indigo-500/10 border border-purple-500/20 rounded-full mb-6">
                    <Crown className="h-5 w-5 text-purple-500" />
                    <span className="text-purple-600 font-semibold">Our Patrons</span>
                  </div>
                  <h3 className="text-3xl md:text-4xl font-bold mb-4">
                    <span className="bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">Baron Guy Ullens</span> & <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">Myriam Ullens</span>
                  </h3>
                  <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                    The visionary philanthropists whose generous support and belief in Nepal's educational potential made the Ullens Education Foundation possible.
                  </p>
                </div>

                {/* Patron Cards */}
                <div className="grid md:grid-cols-2 gap-8 mb-12">
                  <div className="group bg-gradient-to-br from-purple-50/50 to-indigo-50/50 dark:from-purple-950/20 dark:to-indigo-950/20 rounded-2xl p-6 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 bg-gradient-to-br from-purple-500/15 to-indigo-500/15 rounded-xl">
                        <Crown className="h-8 w-8 text-purple-500" />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-purple-600">Baron Guy Ullens</h4>
                        <p className="text-sm text-muted-foreground">Founding Patron</p>
                      </div>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      Belgian philanthropist and art collector who recognized Nepal's educational potential and provided the foundational support to establish UEF as a transformative educational institution.
                    </p>
                  </div>

                  <div className="group bg-gradient-to-br from-pink-50/50 to-purple-50/50 dark:from-pink-950/20 dark:to-purple-950/20 rounded-2xl p-6 border border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 bg-gradient-to-br from-pink-500/15 to-purple-500/15 rounded-xl">
                        <Heart className="h-8 w-8 text-pink-500" />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-pink-600">Myriam Ullens</h4>
                        <p className="text-sm text-muted-foreground">Co-Patron</p>
                      </div>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      Dedicated supporter of educational initiatives who shares the vision of creating world-class educational opportunities and fostering social transformation in Nepal.
                    </p>
                  </div>
                </div>

                {/* Vision Quote */}
                <div className="bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 rounded-2xl p-8 border border-amber-500/20 relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-amber-500 via-orange-500 to-red-500 rounded-l-2xl"></div>
                  <blockquote className="text-2xl md:text-3xl font-bold text-center mb-6 relative z-10">
                    <span className="bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent">
                      "Nepal can be the Land of Education in South Asia"
                    </span>
                  </blockquote>
                  <cite className="text-center text-muted-foreground block text-lg">- Baron Guy Ullens, Founding Patron</cite>
                </div>
              </div>
            </div>

            {/* Founding Story */}
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Text Content */}
              <div className="space-y-8">
                <div>
                  <div className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-full mb-6">
                    <BookOpen className="h-5 w-5 text-blue-500" />
                    <span className="text-blue-600 font-semibold">The Beginning</span>
                  </div>
                  <h3 className="text-3xl md:text-4xl font-bold mb-6 leading-tight">
                    The Foundation of <span className="bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">Excellence</span>
                  </h3>
                </div>

                <div className="prose prose-lg max-w-none text-muted-foreground leading-relaxed space-y-6">
                  <p>
                    The Ullens Education Foundation was founded out of a deep commitment to create <strong className="text-blue-600">equitable and inclusive educational opportunities</strong> in Nepal, aiming to foster positive societal transformation. Mr. Som Paneru, the current President of the Board of Trustees, recognized the pressing need for a holistic education approach that emphasizes child-centered learning within Nepal's private education system.
                  </p>

                  <p>
                    He aimed to showcase the <strong className="text-green-600">value of diversity in schools</strong>. After successfully managing Happy House for many years, he was eager to establish a school that embraced progressive educational values, offering teachers and staff access to global practices and training.
                  </p>

                  <p>
                    As Nepal transitioned from a period of civil war and conflict, many families chose to remain in Kathmandu for their children's education. However, the quality of education available raised concerns about whether it was truly <strong className="text-purple-600">equipping students to become future leaders</strong> in an increasingly competitive world.
                  </p>

                  <p>
                    In response to this need, and in collaboration with <strong className="text-amber-600">Patron Baron Guy Ullens</strong>, an initial seed fund was established to launch the Ullens Education Foundation as a non-profit organization, adhering to three key principles:
                  </p>
                </div>
              </div>

              {/* Three Principles Card */}
              <div className="space-y-6">
                <div className="bg-gradient-to-br from-background to-blue-50/50 dark:to-blue-950/20 backdrop-blur-sm border border-blue-500/20 rounded-3xl p-8 shadow-xl">
                  <h4 className="text-2xl font-bold mb-8 text-center">
                    <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Founding Principles</span>
                  </h4>
                  
                  <div className="space-y-6">
                    {/* Principle 1 */}
                    <div className="group flex items-start gap-4 p-4 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-2xl border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                      <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-transform duration-300">
                        1
                      </div>
                      <div>
                        <h5 className="font-bold text-blue-600 mb-2">World-Class Education</h5>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                          The school would provide world-class education that meets international standards and prepares students for global opportunities.
                        </p>
                      </div>
                    </div>

                    {/* Principle 2 */}
                    <div className="group flex items-start gap-4 p-4 bg-gradient-to-r from-green-500/5 to-emerald-500/5 rounded-2xl border border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-transform duration-300">
                        2
                      </div>
                      <div>
                        <h5 className="font-bold text-green-600 mb-2">Self-Sufficient Model</h5>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                          It would eventually operate on a self-sufficient model, ensuring long-term sustainability and independence.
                        </p>
                      </div>
                    </div>

                    {/* Principle 3 */}
                    <div className="group flex items-start gap-4 p-4 bg-gradient-to-r from-purple-500/5 to-indigo-500/5 rounded-2xl border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-transform duration-300">
                        3
                      </div>
                      <div>
                        <h5 className="font-bold text-purple-600 mb-2">Social Responsibility</h5>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                          It would uphold a strong sense of social responsibility, giving back to the community and society.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Impact Statement */}
                <div className="bg-gradient-to-r from-amber-500/10 to-orange-500/10 rounded-2xl p-6 border border-amber-500/20">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-gradient-to-br from-amber-500/20 to-orange-500/20 rounded-lg">
                      <Lightbulb className="h-6 w-6 text-amber-500" />
                    </div>
                    <h5 className="font-bold text-amber-600">Legacy Impact</h5>
                  </div>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    These founding principles continue to guide UEF's mission today, ensuring that every initiative contributes to Nepal's educational transformation and social development.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Foundation History Section */}
      <section className="py-20 md:py-32 bg-gradient-to-b from-background to-muted/20">
        <div className="px-4 md:px-6">
          <div className="max-w-7xl mx-auto">
            {/* Enhanced Section Header */}
            <div className="text-center mb-20">
              <div className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-blue-500/10 to-teal-500/10 border border-blue-500/20 rounded-full mb-8">
                <BookOpen className="h-5 w-5 text-blue-500" />
                <span className="text-blue-600 font-semibold">Our Journey</span>
              </div>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-black mb-8 leading-[1.1]">
                Our <span className="bg-gradient-to-r from-blue-600 via-cyan-500 to-teal-600 bg-clip-text text-transparent animate-gradient-x">Foundation</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                From humble beginnings to becoming Nepal's pioneering educational institution, discover the remarkable journey that has shaped thousands of lives.
              </p>
              <div className="w-32 h-1 bg-gradient-to-r from-blue-500 to-teal-500 mx-auto mt-8 rounded-full"></div>
            </div>

            {/* Timeline Layout */}
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-4 md:left-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500/30 via-teal-500/30 to-amber-500/30 transform md:-translate-x-1/2 rounded-full"></div>
              
              <div className="space-y-16">
                {/* Establishment Card */}
                <div className="group relative flex flex-col md:flex-row items-start md:items-center gap-8 opacity-0 animate-fade-in-up">
                  {/* Timeline Dot */}
                  <div className="absolute left-4 md:left-1/2 w-4 h-4 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full border-4 border-background transform md:-translate-x-1/2 shadow-lg group-hover:scale-125 transition-transform duration-300 z-10"></div>
                  
                  {/* Content Card */}
                  <div className="ml-12 md:ml-0 md:w-1/2 md:pr-12">
                    <div className="bg-gradient-to-br from-background to-blue-50/50 dark:to-blue-950/20 backdrop-blur-sm border border-blue-500/20 rounded-3xl p-8 shadow-lg hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-500 group-hover:scale-[1.02] group-hover:border-blue-500/40">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="p-4 bg-gradient-to-br from-blue-500/15 to-cyan-500/15 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                          <Building className="h-12 w-12 text-blue-500" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-blue-500 uppercase tracking-wider">2006</div>
                          <h3 className="text-2xl md:text-3xl font-bold">Foundation Establishment</h3>
                        </div>
                      </div>
                      <p className="text-muted-foreground leading-relaxed text-lg">
                        The Ullens Education Foundation (UEF), established in January 2006 as a non-profit organization, has been at the forefront of creating world-class educational institutions in Nepal. Officially registered at the Company Registrar's Office under number <span className="font-semibold text-blue-600">1/062/063</span>, UEF is dedicated to promoting excellence in education and fostering social responsibility through non-profit ventures.
                      </p>
                      <div className="mt-6 flex flex-wrap gap-2">
                        <span className="px-3 py-1 bg-blue-500/10 text-blue-600 rounded-full text-sm font-medium">Non-Profit</span>
                        <span className="px-3 py-1 bg-teal-500/10 text-teal-600 rounded-full text-sm font-medium">Established 2006</span>
                        <span className="px-3 py-1 bg-cyan-500/10 text-cyan-600 rounded-full text-sm font-medium">Educational Excellence</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Spacer for timeline */}
                  <div className="hidden md:block md:w-1/2"></div>
                </div>

                {/* Ullens School Card */}
                <div className="group relative flex flex-col md:flex-row items-start md:items-center gap-8 opacity-0 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
                  {/* Timeline Dot */}
                  <div className="absolute left-4 md:left-1/2 w-4 h-4 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full border-4 border-background transform md:-translate-x-1/2 shadow-lg group-hover:scale-125 transition-transform duration-300 z-10"></div>
                  
                  {/* Spacer for timeline */}
                  <div className="hidden md:block md:w-1/2"></div>
                  
                  {/* Content Card - Right Side */}
                  <div className="ml-12 md:ml-0 md:w-1/2 md:pl-12">
                    <div className="bg-gradient-to-br from-background to-green-50/50 dark:to-green-950/20 backdrop-blur-sm border border-green-500/20 rounded-3xl p-8 shadow-lg hover:shadow-xl hover:shadow-green-500/10 transition-all duration-500 group-hover:scale-[1.02] group-hover:border-green-500/40">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="p-4 bg-gradient-to-br from-green-500/15 to-emerald-500/15 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                          <GraduationCap className="h-12 w-12 text-green-500" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-green-500 uppercase tracking-wider">Excellence</div>
                          <h3 className="text-2xl md:text-3xl font-bold">Ullens School</h3>
                        </div>
                      </div>
                      <p className="text-muted-foreground leading-relaxed text-lg mb-6">
                        In pursuit of its mission, UEF established Ullens School as a non-profit, independent institution with the vision of becoming a "Center of Excellence in Education." Today, Ullens School operates four key programs across multiple campuses.
                      </p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="bg-green-500/5 rounded-2xl p-5 border border-green-500/20">
                          <h4 className="font-bold text-green-600 mb-3 flex items-center gap-2">
                            <Building className="h-4 w-4" />
                            Campus Locations
                          </h4>
                          <ul className="text-sm text-muted-foreground space-y-2">
                            <li className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                              Khumaltar, Lalitpur
                            </li>
                            <li className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                              Bansbari, Kathmandu
                            </li>
                            <li className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                              UCED Program
                            </li>
                            <li className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                              Nala Outdoor Learning
                            </li>
                          </ul>
                        </div>
                        <div className="bg-blue-500/5 rounded-2xl p-5 border border-blue-500/20">
                          <h4 className="font-bold text-blue-600 mb-3 flex items-center gap-2">
                            <Award className="h-4 w-4" />
                            Impact Highlights
                          </h4>
                          <ul className="text-sm text-muted-foreground space-y-2">
                            <li className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                              1,400+ Students Served
                            </li>
                            <li className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                              Nepal's First IBDP
                            </li>
                            <li className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                              KUSOEd Partnership
                            </li>
                            <li className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                              Education Leadership
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Global Partnerships Card */}
                <div className="group relative flex flex-col md:flex-row items-start md:items-center gap-8 opacity-0 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
                  {/* Timeline Dot */}
                  <div className="absolute left-4 md:left-1/2 w-4 h-4 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full border-4 border-background transform md:-translate-x-1/2 shadow-lg group-hover:scale-125 transition-transform duration-300 z-10"></div>
                  
                  {/* Content Card - Left Side */}
                  <div className="ml-12 md:ml-0 md:w-1/2 md:pr-12">
                    <div className="bg-gradient-to-br from-background to-purple-50/50 dark:to-purple-950/20 backdrop-blur-sm border border-purple-500/20 rounded-3xl p-8 shadow-lg hover:shadow-xl hover:shadow-purple-500/10 transition-all duration-500 group-hover:scale-[1.02] group-hover:border-purple-500/40">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="p-4 bg-gradient-to-br from-purple-500/15 to-indigo-500/15 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                          <Globe className="h-12 w-12 text-purple-500" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-purple-500 uppercase tracking-wider">Recognition</div>
                          <h3 className="text-2xl md:text-3xl font-bold">Global Partnerships</h3>
                        </div>
                      </div>
                      <p className="text-muted-foreground leading-relaxed text-lg">
                        In just over a decade, Ullens School has earned recognition as a leader in progressive education in Nepal and is now one of the most sought-after schools in the country. UEF's commitment to global best practices is reflected in its long-term partnership with the <span className="font-semibold text-purple-600">Bank Street College of Education</span> in New York.
                      </p>
                      <div className="mt-6 flex flex-wrap gap-2">
                        <span className="px-3 py-1 bg-purple-500/10 text-purple-600 rounded-full text-sm font-medium">Global Standards</span>
                        <span className="px-3 py-1 bg-indigo-500/10 text-indigo-600 rounded-full text-sm font-medium">NYC Partnership</span>
                        <span className="px-3 py-1 bg-pink-500/10 text-pink-600 rounded-full text-sm font-medium">Progressive Education</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Spacer for timeline */}
                  <div className="hidden md:block md:w-1/2"></div>
                </div>

                {/* Infrastructure Card */}
                <div className="group relative flex flex-col md:flex-row items-start md:items-center gap-8 opacity-0 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
                  {/* Timeline Dot */}
                  <div className="absolute left-4 md:left-1/2 w-4 h-4 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full border-4 border-background transform md:-translate-x-1/2 shadow-lg group-hover:scale-125 transition-transform duration-300 z-10"></div>
                  
                  {/* Spacer for timeline */}
                  <div className="hidden md:block md:w-1/2"></div>
                  
                  {/* Content Card - Right Side */}
                  <div className="ml-12 md:ml-0 md:w-1/2 md:pl-12">
                    <div className="bg-gradient-to-br from-background to-amber-50/50 dark:to-amber-950/20 backdrop-blur-sm border border-amber-500/20 rounded-3xl p-8 shadow-lg hover:shadow-xl hover:shadow-amber-500/10 transition-all duration-500 group-hover:scale-[1.02] group-hover:border-amber-500/40">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="p-4 bg-gradient-to-br from-amber-500/15 to-orange-500/15 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                          <Target className="h-12 w-12 text-amber-500" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-amber-500 uppercase tracking-wider">Investment</div>
                          <h3 className="text-2xl md:text-3xl font-bold">Infrastructure Development</h3>
                        </div>
                      </div>
                      <p className="text-muted-foreground leading-relaxed text-lg">
                        Guided by a five-member Board of Trustees, UEF provides strategic oversight to all its institutions and programs. Over the past decade, UEF has invested significantly in educational infrastructure, acquiring more than <span className="font-semibold text-amber-600">400 ropanis</span> of land in Kavre district, with an additional <span className="font-semibold text-amber-600">600 ropanis</span> contributed by the local school and community.
                      </p>
                      <div className="mt-6 grid grid-cols-2 gap-4">
                        <div className="text-center p-4 bg-amber-500/5 rounded-xl border border-amber-500/20">
                          <div className="text-2xl font-bold text-amber-600">400+</div>
                          <div className="text-sm text-muted-foreground">Ropanis Acquired</div>
                        </div>
                        <div className="text-center p-4 bg-orange-500/5 rounded-xl border border-orange-500/20">
                          <div className="text-2xl font-bold text-orange-600">600+</div>
                          <div className="text-sm text-muted-foreground">Community Contribution</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Future Vision Card */}
                <div className="group relative flex flex-col md:flex-row items-start md:items-center gap-8 opacity-0 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
                  {/* Timeline Dot */}
                  <div className="absolute left-4 md:left-1/2 w-4 h-4 bg-gradient-to-r from-crimson to-pink-500 rounded-full border-4 border-background transform md:-translate-x-1/2 shadow-lg group-hover:scale-125 transition-transform duration-300 z-10"></div>
                  
                  {/* Content Card - Left Side */}
                  <div className="ml-12 md:ml-0 md:w-1/2 md:pr-12">
                    <div className="bg-gradient-to-br from-background to-crimson-50/50 dark:to-pink-950/20 backdrop-blur-sm border border-crimson/20 rounded-3xl p-8 shadow-lg hover:shadow-xl hover:shadow-crimson/10 transition-all duration-500 group-hover:scale-[1.02] group-hover:border-crimson/40">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="p-4 bg-gradient-to-br from-crimson/15 to-pink-500/15 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                          <Lightbulb className="h-12 w-12 text-crimson" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-crimson uppercase tracking-wider">Future</div>
                          <h3 className="text-2xl md:text-3xl font-bold">Expanding Horizons</h3>
                        </div>
                      </div>
                      <p className="text-muted-foreground leading-relaxed text-lg mb-6">
                        Building on the success of Ullens School, UEF is now embarking on an ambitious new project: <span className="font-semibold text-crimson">Ullens College (UC)</span>, which aims to establish Nepal's first liberal arts college. In October 2020, UEF also launched <span className="font-semibold text-pink-600">Ullens LEAP</span>, a gap year program designed as a bridge to lifelong learning.
                      </p>
                      
                      {/* Vision Quote */}
                      <div className="bg-gradient-to-r from-crimson/10 to-pink-500/10 rounded-2xl p-6 border border-crimson/20 relative overflow-hidden">
                        <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-crimson to-pink-500"></div>
                        <blockquote className="text-lg font-semibold text-crimson italic mb-3 relative z-10">
                          "Nepal can be the Land of Education in South Asia"
                        </blockquote>
                        <cite className="text-sm text-muted-foreground block mb-4">- Guy Ullens, Patron</cite>
                        <p className="text-muted-foreground leading-relaxed text-sm">
                          Inspired by this vision, UEF is dedicated to positioning Nepal as a leading educational hub in the region, promoting world-class education and a strong sense of social responsibility for generations to come.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Spacer for timeline */}
                  <div className="hidden md:block md:w-1/2"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Impact Section */}
      <section className="py-20 md:py-32 bg-gradient-to-br from-muted/30 via-background to-muted/20 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-0 w-96 h-96 bg-gradient-to-br from-emerald-500/10 to-teal-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="px-4 md:px-6 relative z-10">
          <div className="max-w-7xl mx-auto">
            {/* Enhanced Section Header */}
            <div className="text-center mb-20">
              <div className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/20 rounded-full mb-8">
                <Award className="h-5 w-5 text-emerald-500" />
                <span className="text-emerald-600 font-semibold">Our Achievements</span>
              </div>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-black mb-8 leading-[1.1]">
                Our <span className="bg-gradient-to-r from-emerald-600 via-teal-500 to-blue-600 bg-clip-text text-transparent animate-gradient-x">Impact</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                Since our establishment, we have been committed to making a positive difference in education and society, creating lasting impact through measurable achievements.
              </p>
              <div className="w-32 h-1 bg-gradient-to-r from-emerald-500 to-teal-500 mx-auto mt-8 rounded-full"></div>
            </div>

            {/* Enhanced Impact Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Established Metric */}
              <div className="group relative opacity-0 animate-fade-in-up">
                <div className="absolute inset-0 bg-gradient-to-br from-crimson/20 to-pink-500/20 rounded-3xl blur-lg group-hover:blur-2xl transition-all duration-500 opacity-50 group-hover:opacity-70"></div>
                <div className="relative bg-gradient-to-br from-background to-crimson-50/50 dark:to-pink-950/20 backdrop-blur-sm border border-crimson/20 rounded-3xl p-8 hover:border-crimson/40 transition-all duration-500 group-hover:scale-105 hover:shadow-xl hover:shadow-crimson/20">
                  <div className="flex items-center justify-center mb-6">
                    <div className="p-4 bg-gradient-to-br from-crimson/15 to-pink-500/15 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                      <Building className="h-8 w-8 text-crimson" />
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl lg:text-5xl font-black text-crimson mb-3 group-hover:scale-110 transition-transform duration-300">2006</div>
                    <div className="text-muted-foreground font-semibold">Foundation Established</div>
                    <div className="text-xs text-muted-foreground/80 mt-2">Nearly two decades of excellence</div>
                  </div>
                </div>
              </div>

              {/* Students Served Metric */}
              <div className="group relative opacity-0 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 to-green-500/20 rounded-3xl blur-lg group-hover:blur-2xl transition-all duration-500 opacity-50 group-hover:opacity-70"></div>
                <div className="relative bg-gradient-to-br from-background to-emerald-50/50 dark:to-emerald-950/20 backdrop-blur-sm border border-emerald-500/20 rounded-3xl p-8 hover:border-emerald-500/40 transition-all duration-500 group-hover:scale-105 hover:shadow-xl hover:shadow-emerald-500/20">
                  <div className="flex items-center justify-center mb-6">
                    <div className="p-4 bg-gradient-to-br from-emerald-500/15 to-green-500/15 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                      <Users className="h-8 w-8 text-emerald-500" />
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl lg:text-5xl font-black text-emerald-500 mb-3 group-hover:scale-110 transition-transform duration-300">1,400+</div>
                    <div className="text-muted-foreground font-semibold">Students Served</div>
                    <div className="text-xs text-muted-foreground/80 mt-2">Across all campuses & programs</div>
                  </div>
                </div>
              </div>

              {/* Land Infrastructure Metric */}
              <div className="group relative opacity-0 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-3xl blur-lg group-hover:blur-2xl transition-all duration-500 opacity-50 group-hover:opacity-70"></div>
                <div className="relative bg-gradient-to-br from-background to-blue-50/50 dark:to-blue-950/20 backdrop-blur-sm border border-blue-500/20 rounded-3xl p-8 hover:border-blue-500/40 transition-all duration-500 group-hover:scale-105 hover:shadow-xl hover:shadow-blue-500/20">
                  <div className="flex items-center justify-center mb-6">
                    <div className="p-4 bg-gradient-to-br from-blue-500/15 to-cyan-500/15 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                      <Target className="h-8 w-8 text-blue-500" />
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl lg:text-5xl font-black text-blue-500 mb-3 group-hover:scale-110 transition-transform duration-300">1000+</div>
                    <div className="text-muted-foreground font-semibold">Ropanis of Land</div>
                    <div className="text-xs text-muted-foreground/80 mt-2">Strategic infrastructure investment</div>
                  </div>
                </div>
              </div>

              {/* IBDP Achievement Metric */}
              <div className="group relative opacity-0 animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-3xl blur-lg group-hover:blur-2xl transition-all duration-500 opacity-50 group-hover:opacity-70"></div>
                <div className="relative bg-gradient-to-br from-background to-purple-50/50 dark:to-purple-950/20 backdrop-blur-sm border border-purple-500/20 rounded-3xl p-8 hover:border-purple-500/40 transition-all duration-500 group-hover:scale-105 hover:shadow-xl hover:shadow-purple-500/20">
                  <div className="flex items-center justify-center mb-6">
                    <div className="p-4 bg-gradient-to-br from-purple-500/15 to-indigo-500/15 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                      <GraduationCap className="h-8 w-8 text-purple-500" />
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl lg:text-3xl font-black text-purple-500 mb-3 group-hover:scale-110 transition-transform duration-300">Nepal's 1st</div>
                    <div className="text-muted-foreground font-semibold">IBDP Program</div>
                    <div className="text-xs text-muted-foreground/80 mt-2">International Baccalaureate pioneer</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Impact Highlights */}
            <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 opacity-0 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              <div className="text-center p-6 bg-gradient-to-br from-background to-muted/50 rounded-2xl border border-border/50 hover:border-border transition-all duration-300">
                <Globe className="h-8 w-8 text-blue-500 mx-auto mb-4" />
                <h3 className="text-lg font-bold mb-2">Global Partnerships</h3>
                <p className="text-sm text-muted-foreground">Collaborating with international institutions for world-class education</p>
              </div>
              <div className="text-center p-6 bg-gradient-to-br from-background to-muted/50 rounded-2xl border border-border/50 hover:border-border transition-all duration-300">
                <Lightbulb className="h-8 w-8 text-amber-500 mx-auto mb-4" />
                <h3 className="text-lg font-bold mb-2">Innovation Leader</h3>
                <p className="text-sm text-muted-foreground">Pioneering progressive education methods in Nepal</p>
              </div>
              <div className="text-center p-6 bg-gradient-to-br from-background to-muted/50 rounded-2xl border border-border/50 hover:border-border transition-all duration-300">
                <Heart className="h-8 w-8 text-crimson mx-auto mb-4" />
                <h3 className="text-lg font-bold mb-2">Community Impact</h3>
                <p className="text-sm text-muted-foreground">Creating positive change through education and social responsibility</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  )
} 