'use client'

import <PERSON> from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { ArrowRight, Users, Mail, LinkedinIcon, GraduationCap, Award, Globe, Heart, Target, BookOpen, Star, ChevronRight, Crown, Building, MapPin, Calendar, Phone, ExternalLink, Search, Beaker, Microscope, Brain, Atom, Code, Lightbulb, Coffee, Camera, Mountain } from "lucide-react"
import { useState } from "react"

const leadershipTeam = [
  {
    name: "<PERSON><PERSON><PERSON>, PhD",
    position: "Academic Director",
    department: "Ullens College",
    image: "/images/team/shishir-adhikari-color.jpg",
    bio: "<PERSON><PERSON><PERSON> brings a unique blend of rigorous scientific training and practical research experience to his role as Academic Director at Ullens College. With a PhD in Physics from Case Western Reserve University and postdoctoral experience at Harvard Medical School and Dana-Farber Cancer Institute, he is passionate about bridging the gap between advanced research methodologies and undergraduate education.\n\n<PERSON><PERSON><PERSON> specializes in statistical mechanics, biophysics, and machine learning. His research journey has taken him from studying polymer conformations to unraveling cellular adhesion mechanisms, always with an eye toward practical applications.\n\nWhat drives <PERSON><PERSON><PERSON> in his academic leadership role is the belief that students learn best when they engage with real problems. Having mentored students from high school to graduate levels across institutions like Yale, CalTech, and Case Western Reserve University, he understands that meaningful learning happens when theoretical knowledge meets hands-on discovery.\n\nAt Ullens College, Shishir is committed to creating an environment where students don't just consume knowledge but actively participate in the research process. He believes that every student, regardless of their background, has the potential to contribute meaningfully to solving complex problems when given the right guidance and opportunities.\n\nHis approach to education is simple: start with curiosity, build with rigor, and always remember that the best research questions often come from the most unexpected places.\n\nWhen not thinking about academic programs or research problems, Shishir enjoys photography, rock climbing, and cooking—activities that, much like good research, require patience, precision, and a willingness to experiment.",
    education: "PhD Physics, Case Western Reserve University; MS Physics Entrepreneurship Program, Case Western Reserve University; BS Physics with Math & Computer Science Minor, Hiram College",
    expertise: ["Statistical Mechanics", "Biophysics", "Machine Learning", "Research Methodology", "Educational Leadership", "Theory & Computation"],
    email: "<EMAIL>",
    googleScholar: "https://scholar.google.com/citations?user=RzD6tp8AAAAJ&hl=en",
    photography: "https://500px.com/p/sisir?view=photos"
  }
]

const facultyTeam = [
  {
    name: "Dr. James Wilson",
    position: "Professor of Computer Science",
    department: "School of Computer Science",
    image: "/team/placeholder-faculty.jpg",
    specialization: "Artificial Intelligence & Machine Learning",
    education: "PhD Computer Science, MIT",
    experience: "15 years",
    courses: ["Advanced AI", "Machine Learning", "Data Structures"]
  },
  {
    name: "Dr. Maria Rodriguez",
    position: "Professor of Business Studies",
    department: "School of Business",
    image: "/team/placeholder-faculty.jpg",
    specialization: "International Business & Strategy",
    education: "PhD Business Administration, Wharton",
    experience: "12 years",
    courses: ["Strategic Management", "Global Business", "Entrepreneurship"]
  },
  {
    name: "Dr. David Kim",
    position: "Professor of Agriculture",
    department: "School of Agriculture & Climate",
    image: "/team/placeholder-faculty.jpg",
    specialization: "Sustainable Agriculture & Climate Science",
    education: "PhD Agricultural Sciences, UC Davis",
    experience: "18 years",
    courses: ["Climate Smart Agriculture", "Soil Science", "Sustainable Farming"]
  },
  {
    name: "Dr. Emily Thompson",
    position: "Professor of Education",
    department: "School of Education",
    image: "/team/placeholder-faculty.jpg",
    specialization: "Educational Psychology & Pedagogy",
    education: "PhD Educational Psychology, Stanford",
    experience: "14 years",
    courses: ["Educational Psychology", "Teaching Methods", "Assessment Design"]
  },
  {
    name: "Dr. Ahmed Hassan",
    position: "Associate Professor of Computer Science",
    department: "School of Computer Science",
    image: "/team/placeholder-faculty.jpg",
    specialization: "Cybersecurity & Network Systems",
    education: "PhD Information Security, CMU",
    experience: "10 years",
    courses: ["Cybersecurity", "Network Programming", "System Administration"]
  },
  {
    name: "Dr. Lisa Park",
    position: "Assistant Professor of Business",
    department: "School of Business",
    image: "/team/placeholder-faculty.jpg",
    specialization: "Digital Marketing & E-commerce",
    education: "PhD Marketing, Northwestern",
    experience: "8 years",
    courses: ["Digital Marketing", "E-commerce", "Consumer Behavior"]
  }
]

function InteractiveProfile({ leader }: { leader: any }) {
  const [activeTab, setActiveTab] = useState('overview')
  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null)

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Users },
    { id: 'research', label: 'Research', icon: Search },
    { id: 'education', label: 'Education', icon: GraduationCap },
    { id: 'personal', label: 'Beyond Academia', icon: Heart }
  ]

  const researchAreas = [
    { name: 'Non-equilibrium statistical mechanics', icon: Brain, color: 'from-blue-500 to-cyan-500' },
    { name: 'Biophysics', icon: Beaker, color: 'from-emerald-500 to-green-500' },
    { name: 'Machine Learning', icon: Code, color: 'from-purple-500 to-pink-500' },
    { name: 'Research Methodology', icon: Microscope, color: 'from-orange-500 to-red-500' },
    { name: 'Theory & Computation', icon: Atom, color: 'from-indigo-500 to-blue-500' }
  ]

  const personalInterests = [
    { name: 'Photography', icon: Camera, description: 'Capturing moments with precision' },
    { name: 'Rock Climbing', icon: Mountain, description: 'Scaling new heights, literally and figuratively' },
    { name: 'Cooking', icon: Coffee, description: 'Experimenting with flavors and techniques' }
  ]

  const achievements = [
    {
      title: 'Research Excellence',
      description: 'PhD Physics with postdoctoral experience at Harvard Medical School and Dana-Farber Cancer Institute',
      icon: GraduationCap,
      color: 'blue'
    },
    {
      title: 'Academic Leadership',
      description: 'Leading educational innovation and research-based learning at Ullens College',
      icon: Crown,
      color: 'purple'
    },
    {
      title: 'Research Impact',
      description: 'Published research in statistical mechanics, biophysics, and machine learning applications',
      icon: Search,
      color: 'emerald'
    },
    {
      title: 'Mentorship',
      description: 'Mentored students across Yale, CalTech, Case Western Reserve University, and beyond',
      icon: Users,
      color: 'orange'
    }
  ]

  return (
    <div className="group relative opacity-0 animate-fade-in-up max-w-7xl w-full" style={{ animationDelay: '0.1s' }}>
      
      {/* Enhanced Profile Card */}
      <div className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-3xl p-8 md:p-12 shadow-2xl border border-blue-100 relative overflow-hidden">
        
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 right-10 w-32 h-32 bg-blue-500 rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 left-10 w-40 h-40 bg-indigo-500 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-purple-500 rounded-full blur-2xl"></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 items-start relative z-10">
          
          {/* Profile Image Section */}
          <div className="lg:col-span-4 flex justify-center">
            <div className="relative">
              <div className="absolute -inset-4 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl blur opacity-25"></div>
              <div className="relative w-64 h-80 md:w-80 md:h-96 overflow-hidden shadow-2xl border-4 border-white rounded-2xl group">
                <img
                  src={leader.image}
                  alt={leader.name}
                  className="w-full h-full object-cover object-top transition-all duration-700 group-hover:scale-110 filter grayscale group-hover:grayscale-0 group-hover:brightness-110 group-hover:contrast-110"
                  loading="lazy"
                />
                {/* Professional Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
              </div>
            </div>
          </div>

          {/* Profile Content */}
          <div className="lg:col-span-8 space-y-8 text-center lg:text-left">
            
            {/* Header Section */}
            <div className="space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900">{leader.name}</h1>
                <p className="text-xl text-blue-600 font-semibold">{leader.position}</p>
                <p className="text-gray-600 font-medium flex items-center justify-center lg:justify-start gap-2">
                  <Building className="h-4 w-4" />
                  {leader.department}
                </p>
              </div>
              
              {/* Key Highlights */}
              <div className="flex flex-wrap gap-3 justify-center lg:justify-start">
                <Badge className="bg-blue-100 text-blue-700 px-4 py-2">PhD Physics</Badge>
                <Badge className="bg-emerald-100 text-emerald-700 px-4 py-2">Harvard Postdoc</Badge>
                <Badge className="bg-purple-100 text-purple-700 px-4 py-2">Research Scientist</Badge>
                <Badge className="bg-orange-100 text-orange-700 px-4 py-2">Academic Director</Badge>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="flex flex-wrap gap-2 p-2 bg-white/70 backdrop-blur-sm rounded-2xl border border-white/50">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 px-4 py-3 rounded-xl font-medium transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'bg-white text-blue-600 shadow-lg transform scale-105'
                        : 'text-gray-600 hover:text-blue-600 hover:bg-white/50'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                )
              })}
            </div>

            {/* Tab Content */}
            <div className="min-h-[400px]">
              
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div className="space-y-8 animate-fade-in">
                  
                  {/* Bio Content */}
                  <div className="space-y-8">
                    {/* Introduction - Academic Director Role */}
                    <div className="bg-gradient-to-br from-slate-50 to-blue-50/30 rounded-2xl p-8 border border-slate-200/50 relative overflow-hidden">
                      <div className="absolute top-0 right-0 w-32 h-32 bg-blue-400/5 rounded-full blur-2xl"></div>
                      <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-400/5 rounded-full blur-xl"></div>
                      <div className="relative z-10">
                        <p className="text-lg leading-relaxed text-gray-800 text-justify font-medium">
                          {leader.bio.split('\n\n')[0]}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Achievements Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {achievements.map((achievement, idx) => {
                      const Icon = achievement.icon
                      const colorClasses = {
                        blue: 'bg-blue-500',
                        purple: 'bg-purple-500',
                        emerald: 'bg-emerald-500',
                        orange: 'bg-orange-500'
                      }
                      return (
                        <div key={achievement.title} className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 hover:shadow-lg transition-all duration-300">
                          <div className="flex items-center gap-3 mb-3">
                            <div className={`w-10 h-10 ${colorClasses[achievement.color as keyof typeof colorClasses]} rounded-lg flex items-center justify-center`}>
                              <Icon className="h-5 w-5 text-white" />
                            </div>
                            <h4 className="font-bold text-gray-900">{achievement.title}</h4>
                          </div>
                          <p className="text-sm text-gray-600">{achievement.description}</p>
                        </div>
                      )
                    })}
                  </div>

                  {/* Contact Information */}
                  <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-6 text-white">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                        <Mail className="h-5 w-5 text-white" />
                      </div>
                      <h4 className="font-bold text-white text-lg">Connect with Academic Director</h4>
                    </div>
                    <p className="text-blue-100 mb-4 leading-relaxed">
                      For academic inquiries, research collaborations, or educational development opportunities, reach out to our Academic Director.
                    </p>
                    <div className="grid md:grid-cols-3 gap-4">
                      <a href={`mailto:${leader.email}`} className="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                        <Mail className="h-4 w-4 text-blue-200" />
                        <span className="text-sm font-medium text-white">Email</span>
                      </a>
                      {leader.googleScholar && (
                        <a href={leader.googleScholar} target="_blank" rel="noopener noreferrer" className="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                          <BookOpen className="h-4 w-4 text-blue-200" />
                          <span className="text-sm font-medium text-white">Research</span>
                        </a>
                      )}
                      {leader.photography && (
                        <a href={leader.photography} target="_blank" rel="noopener noreferrer" className="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                          <Camera className="h-4 w-4 text-blue-200" />
                          <span className="text-sm font-medium text-white">Portfolio</span>
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Research Tab */}
              {activeTab === 'research' && (
                <div className="space-y-8 animate-fade-in">
                  
                  {/* Research Focus Introduction */}
                  <div className="bg-gradient-to-br from-blue-50/70 to-indigo-50/70 rounded-2xl p-8 border border-blue-100 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-blue-400/5 rounded-full blur-2xl"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-400/5 rounded-full blur-xl"></div>
                    <div className="relative z-10">
                      <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center">
                          <Search className="h-5 w-5 text-white" />
                        </div>
                        Research Excellence
                      </h2>
                      <p className="text-lg leading-relaxed text-gray-800 text-justify font-medium">
                        {leader.bio.split('\n\n')[1]}
                      </p>
                    </div>
                  </div>

                  {/* Research Areas */}
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-6">Research Areas & Expertise</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      {researchAreas.map((area, idx) => {
                        const Icon = area.icon
                        return (
                          <div 
                            key={area.name}
                            className="group p-6 bg-white/70 backdrop-blur-sm rounded-2xl border border-gray-100 hover:border-gray-300 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                            onMouseEnter={() => setHoveredSkill(area.name)}
                            onMouseLeave={() => setHoveredSkill(null)}
                          >
                            <div className="flex items-start gap-4">
                              <div className={`w-12 h-12 bg-gradient-to-r ${area.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                                <Icon className="h-6 w-6 text-white" />
                              </div>
                              <div className="flex-1">
                                <h4 className="font-bold text-gray-900 mb-2 leading-tight">{area.name}</h4>
                                <p className="text-sm text-gray-600">Specialized research focus with extensive experience</p>
                                <div className="mt-3 flex items-center gap-2">
                                  <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${area.color}`}></div>
                                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Core Expertise</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              )}

              {/* Education Tab */}
              {activeTab === 'education' && (
                <div className="space-y-8 animate-fade-in">
                  
                  {/* Educational Philosophy */}
                  <div className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl p-8 border border-amber-200 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-amber-400/5 rounded-full blur-2xl"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-orange-400/5 rounded-full blur-xl"></div>
                    <div className="relative z-10">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-orange-500 rounded-xl flex items-center justify-center">
                          <Lightbulb className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900">Educational Philosophy</h2>
                          <p className="text-amber-700 font-medium">Research-Based Learning Approach</p>
                        </div>
                      </div>
                      <div className="space-y-6">
                        <p className="text-lg leading-relaxed text-gray-800 text-justify font-medium">
                          {leader.bio.split('\n\n')[2]}
                        </p>
                        <p className="text-lg leading-relaxed text-gray-800 text-justify font-medium">
                          {leader.bio.split('\n\n')[3]}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Academic Journey Timeline */}
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                      <GraduationCap className="h-7 w-7 text-blue-600" />
                      Academic Journey
                    </h3>
                    
                    <div className="space-y-6">
                      {/* Education Cards */}
                      <div className="grid gap-6">
                        {leader.education.split(';').map((degree: string, idx: number) => {
                          const institutions = [
                            { 
                              name: 'Case Western Reserve University', 
                              location: 'Cleveland, OH', 
                              color: 'from-blue-600 to-blue-700',
                              bgColor: 'from-blue-50 to-indigo-50',
                              highlight: 'PhD Physics',
                              years: '2015-2020',
                              icon: Brain,
                              thesis: {
                                title: 'Statistical Physics of Cell Adhesion Complexes and Machine Learning',
                                link: 'https://www.proquest.com/openview/a6b449380c849fbe55c03281a9633f10/1?pq-origsite=gscholar&cbl=18750&diss=y'
                              }
                            },
                            { 
                              name: 'Case Western Reserve University', 
                              location: 'Cleveland, OH', 
                              color: 'from-purple-600 to-purple-700',
                              bgColor: 'from-purple-50 to-pink-50',
                              highlight: 'MS Physics Entrepreneurship',
                              years: '2013-2015',
                              icon: Lightbulb,
                              thesis: {
                                title: 'Plexar Imaging: A Startup Determined to Solve the CT Dose Variability Problem',
                                link: 'https://etd.ohiolink.edu/acprod/odb_etd/etd/r/1501/10?clear=10&p10_accession_num=case1374236161'
                              }
                            },
                            { 
                              name: 'Hiram College', 
                              location: 'Hiram, OH', 
                              color: 'from-emerald-600 to-emerald-700',
                              bgColor: 'from-emerald-50 to-green-50',
                              highlight: 'BS Physics',
                              years: '2009-2013',
                              icon: Atom,
                              thesis: {
                                title: 'Impact of solvent density and temperature on the conformation of a Lennard-Jones polymer chain in solution',
                                link: null
                              }
                            }
                          ]
                          const institution = institutions[idx] || institutions[0]
                          const Icon = institution.icon
                          
                          return (
                            <div key={idx} className="group relative">
                              {/* Enhanced background glow */}
                              <div className={`absolute -inset-1 bg-gradient-to-r ${institution.color} rounded-2xl blur-sm opacity-20 group-hover:opacity-30 transition-all duration-500`}></div>
                              
                              <div className={`relative bg-gradient-to-br ${institution.bgColor} backdrop-blur-sm rounded-2xl p-6 border border-white/60 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-1 group-hover:scale-[1.01] overflow-hidden`}>
                                
                                {/* Background pattern */}
                                <div className="absolute inset-0 opacity-5">
                                  <div className={`absolute top-4 right-4 w-24 h-24 bg-gradient-to-r ${institution.color} rounded-full blur-2xl`}></div>
                                  <div className={`absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-r ${institution.color} rounded-full blur-xl`}></div>
                                </div>
                                
                                <div className="relative z-10">
                                  <div className="flex items-start gap-4">
                                    {/* Enhanced Icon Section */}
                                    <div className="flex-shrink-0">
                                      <div className={`w-16 h-16 bg-gradient-to-r ${institution.color} rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg`}>
                                        <Icon className="h-8 w-8 text-white drop-shadow-lg" />
                                      </div>
                                    </div>
                                    
                                    {/* Enhanced Content Section */}
                                    <div className="flex-1 space-y-3">
                                      {/* Header */}
                                      <div className="flex items-start justify-between">
                                        <div className="space-y-1">
                                          <h4 className="text-xl font-bold text-gray-900 leading-tight">{degree.trim()}</h4>
                                          <p className="text-blue-600 font-bold">{institution.name}</p>
                                          <div className="flex items-center gap-3 text-gray-500 text-sm">
                                            <div className="flex items-center gap-1">
                                              <MapPin className="h-3 w-3" />
                                              <span className="font-medium">{institution.location}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                              <Calendar className="h-3 w-3" />
                                              <span className="font-medium">{institution.years}</span>
                                            </div>
                                          </div>
                                        </div>
                                        <Badge className={`bg-gradient-to-r ${institution.color} text-white px-3 py-1 text-sm font-bold shadow-lg`}>
                                          {institution.highlight}
                                        </Badge>
                                      </div>
                                      
                                      {/* Academic Excellence Indicator */}
                                      <div className="flex items-center gap-2 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-white/40">
                                        <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${institution.color} shadow-sm`}></div>
                                        <span className="text-sm font-semibold text-gray-700">Advanced Research & Academic Excellence</span>
                                        <div className="ml-auto">
                                          <Award className="h-3 w-3 text-amber-500" />
                                        </div>
                                      </div>
                                      
                                      {/* Enhanced Thesis Information */}
                                      {institution.thesis && (
                                        <div className="space-y-3 pt-3 border-t border-gray-200/50">
                                          <div className="flex items-center gap-2 mb-2">
                                            <div className="w-6 h-6 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                                              <BookOpen className="h-3 w-3 text-white" />
                                            </div>
                                            <span className="text-sm font-bold text-gray-800">
                                              {idx === 0 ? 'PhD Thesis Research' : idx === 1 ? 'Masters Thesis Research' : 'Undergraduate Research Project'}
                                            </span>
                                          </div>
                                          {institution.thesis.link ? (
                                            <a 
                                              href={institution.thesis.link}
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              className="group/link block p-3 bg-white/80 rounded-lg border border-white/60 hover:border-blue-300 hover:bg-blue-50/80 transition-all duration-300 hover:shadow-md"
                                            >
                                              <p className="text-sm text-gray-800 group-hover/link:text-blue-800 leading-relaxed font-medium mb-2">
                                                {institution.thesis.title}
                                              </p>
                                              <div className="flex items-center gap-2">
                                                <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 rounded-full">
                                                  <ExternalLink className="h-3 w-3 text-blue-600" />
                                                  <span className="text-xs text-blue-600 font-semibold">View Thesis</span>
                                                </div>
                                              </div>
                                            </a>
                                          ) : (
                                            <div className="p-3 bg-white/80 rounded-lg border border-white/60">
                                              <p className="text-sm text-gray-800 leading-relaxed font-medium mb-1">
                                                {institution.thesis.title}
                                              </p>
                                              <div className="flex items-center gap-2">
                                                <div className="px-2 py-1 bg-gray-100 rounded-full">
                                                  <span className="text-xs text-gray-600 font-semibold">Research Project</span>
                                                </div>
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  </div>

                  {/* Research Experience Highlights */}
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                      <Search className="h-7 w-7 text-purple-600" />
                      Research Experience
                    </h3>
                    
                    <div className="max-w-4xl mx-auto">
                      <div className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 rounded-2xl p-8 border border-blue-200 hover:shadow-xl transition-all duration-300">
                        <div className="flex items-center gap-4 mb-6">
                          <div className="w-16 h-16 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-xl flex items-center justify-center">
                            <Beaker className="h-8 w-8 text-white" />
                          </div>
                          <div>
                            <h4 className="text-xl font-bold text-gray-900">Harvard Medical School & Dana-Farber Cancer Institute</h4>
                            <p className="text-blue-600 font-semibold">Postdoctoral Research Fellowship</p>
                            <p className="text-gray-500 text-sm flex items-center gap-1 mt-1">
                              <MapPin className="h-3 w-3" />
                              Boston, MA
                            </p>
                          </div>
                        </div>
                        
                        {/* Research Areas */}
                        <div className="space-y-4">
                          <h5 className="text-sm font-bold text-gray-700 uppercase tracking-wider flex items-center gap-2">
                            <Microscope className="h-4 w-4 text-purple-600" />
                            Research Focus Areas
                          </h5>
                          <div className="grid md:grid-cols-2 gap-3">
                            <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-white/50">
                              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                                <Brain className="h-4 w-4 text-white" />
                              </div>
                              <span className="text-sm font-medium text-gray-700">Circadian rhythm in stem cells</span>
                            </div>
                            
                            <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-white/50">
                              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                                <Code className="h-4 w-4 text-white" />
                              </div>
                              <span className="text-sm font-medium text-gray-700">Graph neural networks</span>
                            </div>
                            
                            <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-white/50">
                              <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                                <Target className="h-4 w-4 text-white" />
                              </div>
                              <span className="text-sm font-medium text-gray-700">Bilevel optimization</span>
                            </div>
                            
                            <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-white/50">
                              <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-green-500 rounded-lg flex items-center justify-center">
                                <Globe className="h-4 w-4 text-white" />
                              </div>
                              <span className="text-sm font-medium text-gray-700">Graph properties</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Academic Impact */}
                  <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 border border-gray-200">
                    <h3 className="text-xl font-bold text-gray-900 mb-6 text-center">Academic Impact & Mentorship</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                      <div className="space-y-3">
                        <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto">
                          <Users className="h-8 w-8 text-white" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900">Multi-Level Mentorship</h4>
                          <p className="text-sm text-gray-600">High school to graduate students</p>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto">
                          <Globe className="h-8 w-8 text-white" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900">Global Experience</h4>
                          <p className="text-sm text-gray-600">Yale, CalTech, Case Western</p>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center mx-auto">
                          <Target className="h-8 w-8 text-white" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900">Research Integration</h4>
                          <p className="text-sm text-gray-600">Theory meets practice</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Personal Tab */}
              {activeTab === 'personal' && (
                <div className="space-y-8 animate-fade-in">
                  
                  {/* Personal Philosophy */}
                  <div className="bg-gradient-to-br from-slate-50 to-purple-50/30 rounded-2xl p-8 border border-slate-200/50 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-purple-400/5 rounded-full blur-2xl"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-pink-400/5 rounded-full blur-xl"></div>
                    <div className="relative z-10">
                      <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Life Beyond the Laboratory</h2>
                      <p className="text-lg leading-relaxed text-gray-800 text-justify font-medium max-w-4xl mx-auto">
                        {leader.bio.split('\n\n')[5]}
                      </p>
                    </div>
                  </div>

                  {/* Personal Values */}
                  <div className="bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 rounded-3xl p-8 border border-pink-200 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-40 h-40 bg-pink-400/10 rounded-full blur-3xl"></div>
                    <div className="absolute bottom-0 left-0 w-32 h-32 bg-purple-400/10 rounded-full blur-2xl"></div>
                    
                    <div className="relative z-10 text-center space-y-6">
                      <div className="flex items-center justify-center gap-3 mb-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-rose-500 to-pink-500 rounded-xl flex items-center justify-center">
                          <Heart className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">Core Values & Approach</h3>
                      </div>
                      
                      <div className="grid md:grid-cols-3 gap-6">
                        <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/50">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Search className="h-6 w-6 text-white" />
                          </div>
                          <h4 className="font-bold text-gray-900 mb-2">Curiosity</h4>
                          <p className="text-sm text-gray-600">Starting every endeavor with genuine curiosity and wonder</p>
                        </div>
                        
                        <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/50">
                          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Target className="h-6 w-6 text-white" />
                          </div>
                          <h4 className="font-bold text-gray-900 mb-2">Rigor</h4>
                          <p className="text-sm text-gray-600">Building upon curiosity with methodical precision and discipline</p>
                        </div>
                        
                        <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/50">
                          <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Lightbulb className="h-6 w-6 text-white" />
                          </div>
                          <h4 className="font-bold text-gray-900 mb-2">Discovery</h4>
                          <p className="text-sm text-gray-600">Embracing unexpected insights and breakthrough moments</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function TeamPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-32 md:py-40 lg:py-48 overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 right-0 w-[500px] h-[500px] bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-0 left-1/4 w-[400px] h-[400px] bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/2 left-1/2 w-[350px] h-[350px] bg-gradient-to-br from-emerald-400/15 to-green-400/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          
          {/* Floating Elements */}
          <div className="absolute top-24 left-24 text-blue-400/30 animate-float">
            <Users className="h-10 w-10" />
          </div>
          <div className="absolute top-48 right-40 text-purple-400/30 animate-float" style={{ animationDelay: '1s' }}>
            <GraduationCap className="h-8 w-8" />
          </div>
          <div className="absolute bottom-40 right-24 text-emerald-400/30 animate-float" style={{ animationDelay: '2s' }}>
            <Award className="h-9 w-9" />
          </div>
        </div>

        <div className="px-4 md:px-6 relative z-10">
          <div className="max-w-6xl mx-auto text-center space-y-12">
            {/* Badge */}
            <div className="flex items-center justify-center gap-8 mb-12 opacity-0 animate-fade-in-up">
              <div className="w-24 h-[3px] bg-gradient-to-r from-transparent via-blue-500 to-transparent rounded-full"></div>
              <div className="group px-10 py-5 bg-white/80 backdrop-blur-xl border-2 border-blue-500/30 rounded-2xl font-semibold flex items-center hover:bg-white/90 hover:border-blue-500/50 transition-all duration-500 shadow-xl hover:shadow-2xl hover:shadow-blue-500/25">
                <Users className="h-6 w-6 mr-4 text-blue-600 group-hover:scale-110 transition-transform duration-300" />
                <span className="text-blue-700 font-bold text-xl">Our Team</span>
              </div>
              <div className="w-24 h-[3px] bg-gradient-to-l from-transparent via-blue-500 to-transparent rounded-full"></div>
            </div>
            
            {/* Title */}
            <div className="space-y-8 opacity-0 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <h1 className="text-6xl md:text-7xl lg:text-8xl font-black leading-[0.9] tracking-tight">
                Meet Our{" "}
                <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient-x">
                  Exceptional
                </span>
                <br />
                <span className="text-5xl md:text-6xl lg:text-7xl">Team</span>
              </h1>
              
              <div className="w-32 h-2 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
            </div>
            
            {/* Subtitle */}
            <p className="text-2xl md:text-3xl lg:text-4xl text-slate-600 leading-relaxed max-w-5xl mx-auto font-light opacity-0 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              <span className="font-semibold text-slate-900">Dedicated Educators & Leaders</span>
              <br className="hidden md:block" />
              <span className="text-xl md:text-2xl lg:text-3xl mt-4 block text-slate-500">
                Committed to excellence in education, research, and transforming lives through innovative learning experiences.
              </span>
            </p>
          </div>
        </div>
      </section>

      {/* Leadership Team Section */}
      <section className="py-24 md:py-32 bg-white/50 backdrop-blur-sm relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-blue-50/50 to-purple-50/30"></div>

        <div className="px-4 md:px-6 relative z-10">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <div className="text-center mb-20">
              <div className="inline-flex items-center gap-4 px-10 py-5 bg-white/80 backdrop-blur-xl border-2 border-blue-500/30 rounded-2xl mb-10 shadow-lg">
                <Target className="h-7 w-7 text-blue-600" />
                <span className="text-blue-700 font-bold text-xl">Academic Leadership</span>
              </div>
              <h2 className="text-5xl md:text-6xl lg:text-7xl font-black mb-10 leading-[1.1]">
                Our <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient-x">Academic</span> Director
              </h2>
              <p className="text-2xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
                Visionary leader guiding Ullens College's mission to transform education through innovative research-based learning and academic excellence.
              </p>
              <div className="w-40 h-2 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-10 rounded-full"></div>
            </div>

            {/* Leadership Cards */}
            <div className="flex justify-center mb-20">
              {leadershipTeam.map((leader, index) => (
                <InteractiveProfile key={leader.name} leader={leader} />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Faculty Team Section */}
      <section className="py-20 md:py-32 bg-gradient-to-b from-background to-muted/20">
        <div className="px-4 md:px-6">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <div className="text-center mb-20">
              <div className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-emerald-500/20 to-green-500/20 border-2 border-emerald-500/30 rounded-full mb-8 backdrop-blur-sm">
                <BookOpen className="h-6 w-6 text-emerald-500" />
                <span className="text-emerald-600 font-bold text-lg">Faculty</span>
              </div>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-black mb-8 leading-[1.1]">
                Our <span className="bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500 bg-clip-text text-transparent animate-gradient-x">Faculty</span> Excellence
              </h2>
              <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                World-class educators and researchers dedicated to inspiring the next generation of leaders and innovators.
              </p>
              <div className="w-32 h-1 bg-gradient-to-r from-emerald-500 to-green-500 mx-auto mt-8 rounded-full"></div>
            </div>

            {/* Faculty Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {facultyTeam.map((faculty, index) => (
                <div key={faculty.name} className="group relative opacity-0 animate-fade-in-up" style={{ animationDelay: `${index * 0.1}s` }}>
                  <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 to-green-500/20 rounded-3xl blur-lg group-hover:blur-2xl transition-all duration-500 opacity-50 group-hover:opacity-70"></div>
                  <div className="relative bg-gradient-to-br from-background to-emerald-50/50 dark:to-emerald-950/20 backdrop-blur-sm border border-emerald-500/20 rounded-3xl p-6 hover:border-emerald-500/40 transition-all duration-500 group-hover:scale-105 hover:shadow-xl hover:shadow-emerald-500/20">
                    {/* Profile Image */}
                    <div className="w-20 h-20 bg-gradient-to-br from-emerald-500/20 to-green-500/20 rounded-full mx-auto mb-4 flex items-center justify-center border-2 border-emerald-500/30 group-hover:scale-110 transition-transform duration-300">
                      <GraduationCap className="h-10 w-10 text-emerald-500" />
                    </div>
                    
                    <div className="text-center mb-4">
                      <h3 className="text-lg font-bold mb-1">{faculty.name}</h3>
                      <p className="text-emerald-600 font-semibold text-sm mb-1">{faculty.position}</p>
                      <p className="text-xs text-muted-foreground">{faculty.department}</p>
                    </div>
                    
                    <div className="space-y-3 text-sm">
                      <div>
                        <h4 className="font-semibold mb-1 flex items-center gap-2">
                          <Star className="h-3 w-3 text-emerald-500" />
                          Specialization
                        </h4>
                        <p className="text-muted-foreground text-xs">{faculty.specialization}</p>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <h4 className="font-semibold mb-1 text-xs">Education</h4>
                          <p className="text-muted-foreground text-xs">{faculty.education}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-1 text-xs">Experience</h4>
                          <p className="text-muted-foreground text-xs">{faculty.experience}</p>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-2 text-xs flex items-center gap-1">
                          <BookOpen className="h-3 w-3 text-green-500" />
                          Courses
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {faculty.courses.map((course) => (
                            <span key={course} className="px-2 py-1 bg-emerald-500/10 text-emerald-600 rounded-full text-xs">
                              {course}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Join Our Team CTA */}
            <div className="mt-20 text-center">
              <div className="bg-gradient-to-br from-background/80 via-muted/20 to-background/80 backdrop-blur-sm rounded-3xl border border-border/30 shadow-xl p-12 max-w-4xl mx-auto">
                <div className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-full mb-6">
                  <Heart className="h-5 w-5 text-purple-500" />
                  <span className="text-purple-600 font-semibold">Join Us</span>
                </div>
                <h3 className="text-3xl md:text-4xl font-bold mb-6">
                  Ready to <span className="bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent">Shape</span> the Future?
                </h3>
                <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                  We're always looking for passionate educators and researchers to join our mission of transforming education in Nepal.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-xl hover:shadow-purple-500/25 transition-all duration-300 hover:scale-105">
                    <Users className="mr-2 h-5 w-5" />
                    View Open Positions
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                  <Button size="lg" variant="outline" className="border-purple-500/30 text-purple-500 hover:bg-purple-500/10 hover:border-purple-500/50">
                    <Mail className="mr-2 h-5 w-5" />
                    Contact HR
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
} 