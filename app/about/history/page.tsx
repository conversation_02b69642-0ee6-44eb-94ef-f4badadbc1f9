import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Clock, Calendar, Milestone, BookOpen, Users, Building, Trophy, Globe } from "lucide-react"

export default function HistoryPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-24 md:py-32 lg:py-40 overflow-hidden bg-gradient-to-br from-background via-muted/10 to-background">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-0 left-1/4 w-80 h-80 bg-gradient-to-br from-purple-500/10 to-indigo-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="px-4 md:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="w-16 h-[1px] bg-gradient-to-r from-transparent via-blue-500 to-transparent"></div>
              <Badge className="px-6 py-3 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/50 rounded-full backdrop-blur-sm font-semibold text-blue-500">
                <Clock className="h-4 w-4 mr-2" />
                Our History
              </Badge>
              <div className="w-16 h-[1px] bg-gradient-to-l from-transparent via-blue-500 to-transparent"></div>
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight">
              The <span className="bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 bg-clip-text text-transparent">Journey</span> of UEF
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground leading-relaxed max-w-3xl mx-auto font-light">
              From vision to reality - the story of how Ullens Educational Foundation came to life and our path toward educational excellence.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
              <Button size="lg" className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-500/90 hover:to-cyan-500/90 shadow-lg hover:shadow-xl hover:shadow-blue-500/25 transition-all duration-300 hover:scale-105">
                <Calendar className="mr-2 h-5 w-5" />
                Timeline
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline" className="border-blue-500/30 text-blue-500 hover:bg-blue-500/10">
                <BookOpen className="mr-2 h-5 w-5" />
                Our Mission
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Foundation Story Section */}
      <section className="py-20 md:py-28 bg-muted/30">
        <div className="px-4 md:px-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                The <span className="bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent">Foundation Story</span>
              </h2>
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                UEF was born from a vision to transform higher education and create leaders who can tackle the world's most pressing challenges.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6">
                <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-lg">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="p-3 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-xl">
                      <Building className="h-8 w-8 text-blue-500" />
                    </div>
                    <h3 className="text-2xl font-bold">The Vision</h3>
                  </div>
                  <p className="text-muted-foreground leading-relaxed text-lg">
                    In the early 2020s, a group of visionary educators and industry leaders recognized the need for a new kind of educational institution. One that would bridge the gap between traditional academic learning and the rapidly evolving demands of the modern world.
                  </p>
                </div>

                <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-lg">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="p-3 bg-gradient-to-br from-purple-500/10 to-indigo-500/10 rounded-xl">
                      <Users className="h-8 w-8 text-purple-500" />
                    </div>
                    <h3 className="text-2xl font-bold">The Founders</h3>
                  </div>
                  <p className="text-muted-foreground leading-relaxed text-lg">
                    Our founding team brought together expertise from technology, agriculture, business, and education. United by a shared commitment to excellence and innovation, they laid the groundwork for what would become UEF.
                  </p>
                </div>
              </div>

              <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-lg">
                <div className="flex items-center gap-4 mb-6">
                  <div className="p-3 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-xl">
                    <Globe className="h-8 w-8 text-green-500" />
                  </div>
                  <h3 className="text-2xl font-bold">Global Perspective</h3>
                </div>
                <p className="text-muted-foreground leading-relaxed text-lg mb-6">
                  From the beginning, UEF was conceived as a globally-minded institution. Our founders understood that tomorrow's challenges - from climate change to technological disruption - require leaders who can think beyond borders and collaborate across cultures.
                </p>
                <p className="text-muted-foreground leading-relaxed text-lg">
                  This global perspective shaped every aspect of our curriculum design, faculty recruitment, and institutional partnerships, ensuring that UEF graduates would be prepared to make an impact anywhere in the world.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-20 md:py-28">
        <div className="px-4 md:px-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                Our <span className="bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">Timeline</span>
              </h2>
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                Key milestones in the development of Ullens Educational Foundation.
              </p>
            </div>

            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-8 md:left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-500 via-purple-500 to-green-500 md:transform md:-translate-x-1/2"></div>

              {/* Timeline Items */}
              <div className="space-y-12">
                {/* 2022 */}
                <div className="relative flex items-center md:justify-center">
                  <div className="absolute left-8 md:left-1/2 w-4 h-4 bg-blue-500 rounded-full border-4 border-background shadow-lg md:transform md:-translate-x-1/2"></div>
                  <div className="ml-16 md:ml-0 md:w-1/2 md:pr-8 md:text-right">
                    <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                      <div className="flex items-center gap-2 mb-3 md:justify-end">
                        <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/30">2022</Badge>
                        <Milestone className="h-5 w-5 text-blue-500" />
                      </div>
                      <h3 className="text-xl font-bold mb-3">Foundation Established</h3>
                      <p className="text-muted-foreground">
                        The Ullens Educational Foundation was officially established with the mission to create a new paradigm in higher education.
                      </p>
                    </div>
                  </div>
                </div>

                {/* 2023 */}
                <div className="relative flex items-center md:justify-center">
                  <div className="absolute left-8 md:left-1/2 w-4 h-4 bg-purple-500 rounded-full border-4 border-background shadow-lg md:transform md:-translate-x-1/2"></div>
                  <div className="ml-16 md:ml-0 md:w-1/2 md:pl-8">
                    <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                      <div className="flex items-center gap-2 mb-3">
                        <Badge className="bg-purple-500/10 text-purple-500 border-purple-500/30">2023</Badge>
                        <BookOpen className="h-5 w-5 text-purple-500" />
                      </div>
                      <h3 className="text-xl font-bold mb-3">Curriculum Development</h3>
                      <p className="text-muted-foreground">
                        Intensive curriculum development phase began, with leading experts designing innovative interdisciplinary programs.
                      </p>
                    </div>
                  </div>
                </div>

                {/* 2024 */}
                <div className="relative flex items-center md:justify-center">
                  <div className="absolute left-8 md:left-1/2 w-4 h-4 bg-green-500 rounded-full border-4 border-background shadow-lg md:transform md:-translate-x-1/2"></div>
                  <div className="ml-16 md:ml-0 md:w-1/2 md:pr-8 md:text-right">
                    <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                      <div className="flex items-center gap-2 mb-3 md:justify-end">
                        <Badge className="bg-green-500/10 text-green-500 border-green-500/30">2024</Badge>
                        <Users className="h-5 w-5 text-green-500" />
                      </div>
                      <h3 className="text-xl font-bold mb-3">Faculty Recruitment</h3>
                      <p className="text-muted-foreground">
                        World-class faculty members from leading institutions joined UEF, bringing diverse expertise and research excellence.
                      </p>
                    </div>
                  </div>
                </div>

                {/* 2025 */}
                <div className="relative flex items-center md:justify-center">
                  <div className="absolute left-8 md:left-1/2 w-4 h-4 bg-crimson rounded-full border-4 border-background shadow-lg md:transform md:-translate-x-1/2"></div>
                  <div className="ml-16 md:ml-0 md:w-1/2 md:pl-8">
                    <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                      <div className="flex items-center gap-2 mb-3">
                        <Badge className="bg-crimson/10 text-crimson border-crimson/30">2025</Badge>
                        <Trophy className="h-5 w-5 text-crimson" />
                      </div>
                      <h3 className="text-xl font-bold mb-3">Official Launch</h3>
                      <p className="text-muted-foreground">
                        Ullens College opens its doors to the first cohort of students, marking the beginning of a new era in education.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Philosophy Section */}
      <section className="py-20 md:py-28 bg-muted/30">
        <div className="px-4 md:px-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                Our <span className="bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent">Educational Philosophy</span>
              </h2>
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                The principles and beliefs that guide our approach to education and institutional development.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div className="p-3 bg-gradient-to-br from-amber-500/10 to-orange-500/10 rounded-xl w-fit mb-6">
                  <BookOpen className="h-8 w-8 text-amber-500" />
                </div>
                <h3 className="text-xl font-bold mb-4">Interdisciplinary Learning</h3>
                <p className="text-muted-foreground leading-relaxed">
                  We believe the most innovative solutions come from connecting ideas across traditional academic boundaries.
                </p>
              </div>

              <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div className="p-3 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-xl w-fit mb-6">
                  <Globe className="h-8 w-8 text-blue-500" />
                </div>
                <h3 className="text-xl font-bold mb-4">Global Perspective</h3>
                <p className="text-muted-foreground leading-relaxed">
                  Our curriculum and community are designed to prepare students for challenges that transcend national borders.
                </p>
              </div>

              <div className="bg-background/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div className="p-3 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-xl w-fit mb-6">
                  <Users className="h-8 w-8 text-green-500" />
                </div>
                <h3 className="text-xl font-bold mb-4">Ethical Leadership</h3>
                <p className="text-muted-foreground leading-relaxed">
                  We emphasize the development of leaders who prioritize integrity, sustainability, and social responsibility.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Future Vision Section */}
      <section className="py-20 md:py-28">
        <div className="px-4 md:px-6">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-gradient-to-br from-background/80 via-muted/20 to-background/80 backdrop-blur-sm rounded-3xl border border-border/30 shadow-xl p-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Looking <span className="bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">Forward</span>
              </h2>
              <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                As we embark on this journey, we remain committed to continuous innovation and adaptation, ensuring UEF evolves with the changing needs of our global community.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/about/uef">
                  <Button size="lg" className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-500/90 hover:to-purple-500/90 shadow-lg hover:shadow-xl hover:shadow-blue-500/25 transition-all duration-300 hover:scale-105">
                    <Building className="mr-2 h-5 w-5" />
                    About UEF
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/about/board-of-trustees">
                  <Button size="lg" variant="outline" className="border-blue-500/30 text-blue-500 hover:bg-blue-500/10">
                    <Users className="mr-2 h-5 w-5" />
                    Meet Our Leadership
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
} 