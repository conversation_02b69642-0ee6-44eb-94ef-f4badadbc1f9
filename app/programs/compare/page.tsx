import { Metadata } from 'next'
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { ProgramComparisonTool } from "@/components/ui/program-comparison-tool"
import { Button } from '@/components/ui/button'
import { ArrowRight, Lightbulb, GraduationCap, Users, Grid, Building2, Layers, BarChart3 } from 'lucide-react'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'

export const metadata: Metadata = {
  title: 'Compare Programs | Ullens College',
  description: 'Compare different programs across our schools side-by-side to find the best fit for your educational goals.',
}

export default function CompareProgramsPage() {
  return (
    <>
      <div className="flex min-h-[100dvh] flex-col">
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section */}
          <section className="relative bg-gradient-to-b from-background to-muted/30 py-16 md:py-24 overflow-hidden">
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute right-0 top-1/4 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl" />
              <div className="absolute left-0 bottom-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl" />
              
              {/* Decorative elements */}
              <div className="hidden md:block absolute top-20 left-[10%] h-24 w-24 border-4 border-blue-300/20 rounded-lg rotate-12 animate-float-slow" />
              <div className="hidden md:block absolute bottom-20 right-[15%] h-16 w-16 border-4 border-purple-300/30 rounded-full animate-float" />
              <div className="hidden md:block absolute top-40 right-[25%] h-8 w-8 bg-indigo-400/10 rounded-full animate-pulse" />
            </div>
            
            <div className="px-4 relative z-10">
              <div className="max-w-4xl mx-auto text-center mb-12">
                <Badge className="mb-4" variant="outline">Schools</Badge>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Find Your Perfect Program
                </h1>
                <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                  Compare our programs side-by-side to discover which one aligns with your interests, career goals, and educational aspirations.
                </p>
              </div>
              
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-5xl mx-auto mt-16 animate-fade-in">
                <div className="flex flex-col items-center bg-background/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-border/50 transition-transform hover:-translate-y-1 duration-300">
                  <div className="h-12 w-12 rounded-full bg-blue-500/15 flex items-center justify-center mb-3">
                    <GraduationCap className="h-5 w-5 text-blue-500" />
                  </div>
                  <h3 className="text-2xl font-bold mb-1">15+</h3>
                  <p className="text-muted-foreground text-center text-sm">Programs</p>
                </div>
                
                <div className="flex flex-col items-center bg-background/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-border/50 transition-transform hover:-translate-y-1 duration-300">
                  <div className="h-12 w-12 rounded-full bg-purple-500/15 flex items-center justify-center mb-3">
                    <Users className="h-5 w-5 text-purple-500" />
                  </div>
                  <h3 className="text-2xl font-bold mb-1">9:1</h3>
                  <p className="text-muted-foreground text-center text-sm">Student to faculty ratio</p>
                </div>
                
                <div className="flex flex-col items-center bg-background/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-border/50 transition-transform hover:-translate-y-1 duration-300">
                  <div className="h-12 w-12 rounded-full bg-indigo-500/15 flex items-center justify-center mb-3">
                    <Building2 className="h-5 w-5 text-indigo-500" />
                  </div>
                  <h3 className="text-2xl font-bold mb-1">4</h3>
                  <p className="text-muted-foreground text-center text-sm">Schools</p>
                </div>
                
                <div className="flex flex-col items-center bg-background/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-border/50 transition-transform hover:-translate-y-1 duration-300">
                  <div className="h-12 w-12 rounded-full bg-crimson/15 flex items-center justify-center mb-3">
                    <Lightbulb className="h-5 w-5 text-crimson" />
                  </div>
                  <h3 className="text-2xl font-bold mb-1">95%</h3>
                  <p className="text-muted-foreground text-center text-sm">Graduate success rate</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Content */}
          <section id="comparison-tool" className="py-16 md:py-24 bg-gradient-to-b from-background to-light/50 relative overflow-hidden">
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute right-0 bottom-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />
              <div className="absolute left-0 top-1/3 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl" />
            </div>
            
            <div className="px-4 relative z-10">
              <div className="max-w-7xl mx-auto">
                <div className="mb-12 text-center">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-500/10 text-blue-600 text-sm font-medium mb-4">
                    <Layers className="h-4 w-4 mr-2" /> Interactive Tool
                  </div>
                  <h2 className="text-3xl md:text-4xl font-bold mb-6">Program Comparison Tool</h2>
                  <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                    Explore and compare our programs based on curriculum, admission requirements, and career opportunities to find the perfect fit for your academic journey.
                  </p>
                </div>
                
                <ProgramComparisonTool />
                
                <div className="mt-16 bg-background/80 backdrop-blur-sm rounded-xl border shadow-md p-8 text-center relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                  <div className="absolute -right-16 -bottom-16 w-48 h-48 bg-blue-500/5 rounded-full blur-3xl"></div>
                  
                  <div className="h-16 w-16 rounded-full bg-blue-500/10 flex items-center justify-center mb-5 mx-auto">
                    <BarChart3 className="h-8 w-8 text-blue-500" />
                  </div>
                  
                  <h3 className="text-2xl font-bold mb-4">Need Personalized Guidance?</h3>
                  <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                    Our academic advisors can help you navigate your options and find the program that best aligns with your goals and interests.
                  </p>
                  <div className="flex flex-wrap gap-4 justify-center">
                    <Button size="lg" className="shadow-md hover:shadow-lg transition-all">
                      Schedule an Appointment <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="lg" asChild className="border-blue-200 text-blue-700 hover:bg-blue-50">
                      <Link href="/apply">
                        Request Information
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  )
} 