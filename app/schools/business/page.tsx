'use client'

import Link from "next/link"
import { ArrowRight, Building, ChevronRight, DollarSign, BarChart, TrendingUp, Briefcase, PieChart, Target, Users, Globe, Leaf, Zap, TrendingDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useParallaxEffect } from "@/components/parallax-effect"
import { PageTransition } from "@/components/ui/page-transition"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LazyImage } from "@/components/ui/lazy-image"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { SkipLink } from "@/components/ui/skip-link"

// Data for Business Programs
const businessPrograms = [
  {
    name: "B.Sc. in Agri Management",
    slug: "bsc-agri-management",
    icon: <Leaf className="h-4 w-4 text-green-600" />,
    description: "Agriculture meets business innovation",
    details: "Combine agricultural knowledge with business acumen to revolutionize Nepal's farming sector through modern management practices, supply chain optimization, and sustainable business models.",
    isNew: true,
    highlight: true
  },
  {
    name: "B.Sc. in Finance",
    slug: "bsc-finance",
    icon: <DollarSign className="h-4 w-4 text-gold" />,
    description: "Financial analysis and management",
    details: "Develop expertise in financial analysis, investment management, corporate finance, and financial planning for careers in banking, investment, and corporate finance.",
    isNew: false,
    highlight: false
  },
  {
    name: "B.Sc. in Marketing",
    slug: "bsc-marketing",
    icon: <Target className="h-4 w-4 text-gold" />,
    description: "Strategic marketing and consumer behavior",
    details: "Learn how to analyze consumer behavior, develop marketing strategies, and create compelling campaigns in our specialized marketing program.",
    isNew: false,
    highlight: false
  },
  {
    name: "B.Sc. in International Business",
    slug: "bsc-international-business",
    icon: <Globe className="h-4 w-4 text-gold" />,
    description: "Global business operations",
    details: "Prepare for a career in the global marketplace by studying international trade, cross-cultural management, and global business strategies.",
    isNew: false,
    highlight: false
  },
];

const additionalPrograms = [
  {
    name: "Business Analytics",
    slug: "bsc-business-analytics",
    description: "Data-driven business decisions",
    details: "Learn to use data analysis techniques to drive strategic business decisions and gain insights from complex business data sets.",
    icon: <BarChart className="h-6 w-6 text-gold" />,
    isNew: false,
    highlight: false
  },
];

const facultyHighlights = [
  {
    name: "Dr. Rajiv Mehta",
    role: "Department Chair, Business Administration",
    image: "/placeholder.svg?height=200&width=200",
    details: "With extensive experience as a business consultant for Fortune 500 companies, Dr. Mehta brings real-world business expertise to our curriculum development and research initiatives.",
  },
  {
    name: "Dr. Michael Chen",
    role: "Associate Professor, Marketing",
    image: "/placeholder.svg?height=200&width=200",
    details: "Award-winning marketing researcher who has published extensively on consumer behavior, digital marketing strategies, and brand management in top academic journals.",
  },
];

const researchAreas = [
  {
    title: "Sustainable Business Practices",
    description: "Research on environmentally and socially responsible business models, ESG investing, and corporate sustainability strategies.",
    link: "/research/sustainable-business",
  },
  {
    title: "Digital Transformation",
    description: "Exploring how businesses can effectively leverage emerging technologies to create value, innovate processes, and gain competitive advantage.",
    link: "/research/digital-transformation",
  },
  {
    title: "Entrepreneurship and Innovation",
    description: "Research on startup ecosystems, venture funding, business model innovation, and factors that drive entrepreneurial success.",
    link: "/research/entrepreneurship",
  },
];

const facilities = [
  {
    name: "Business Case Study Center",
    description: "Interactive learning space where students analyze real-world business scenarios and develop strategic solutions",
    icon: <Briefcase className="h-6 w-6 text-gold" />,
  },
  {
    name: "Financial Trading Room",
    description: "Equipped with real-time financial data terminals and software for investment analysis and portfolio management",
    icon: <DollarSign className="h-6 w-6 text-gold" />,
  },
  {
    name: "Innovation and Entrepreneurship Center",
    description: "Collaborative space for developing business ideas, prototyping products, and connecting with mentors and investors",
    icon: <TrendingUp className="h-6 w-6 text-gold" />,
  },
];

const industryConnections = [
  {
    name: "Investment Banking",
    partners: ["Goldman Sachs", "JP Morgan", "Morgan Stanley"],
  },
  {
    name: "Consulting",
    partners: ["McKinsey", "BCG", "Deloitte"],
  },
  {
    name: "Technology",
    partners: ["Google", "Amazon", "Microsoft"],
  },
];

// Nepal Business Opportunities Data
const nepalBusinessOpportunities = [
  {
    sector: "Agriculture Technology",
    potential: "92%",
    icon: "🌾",
    description: "Transform traditional farming with modern business practices",
    opportunities: ["Farm-to-market platforms", "Agri-tech startups", "Organic certification", "Export management"]
  },
  {
    sector: "Tourism & Hospitality",
    potential: "88%",
    icon: "🏔️",
    description: "Leverage Nepal's natural beauty for business growth",
    opportunities: ["Eco-tourism ventures", "Adventure sports", "Cultural experiences", "Sustainable lodges"]
  },
  {
    sector: "Financial Services",
    potential: "85%",
    icon: "💳",
    description: "Bridge the financial inclusion gap in rural areas",
    opportunities: ["Digital banking", "Microfinance", "Insurance products", "Investment platforms"]
  },
  {
    sector: "Renewable Energy",
    potential: "90%",
    icon: "⚡",
    description: "Harness Nepal's hydroelectric potential",
    opportunities: ["Mini-hydro projects", "Solar installations", "Energy trading", "Green certificates"]
  }
];

// Interactive Business Concepts
const businessConcepts = [
  {
    title: "Supply Chain Management",
    realWorldExample: "From Nepali Tea Gardens to Global Markets",
    description: "Learn how premium Nepali tea travels from Ilam's gardens to international buyers",
    keyLearning: "Logistics optimization can increase farmer profits by 300%",
    icon: "🚚"
  },
  {
    title: "Digital Marketing",
    realWorldExample: "Promoting Nepali Handicrafts Online",
    description: "Discover how traditional craftspeople use social media to reach global customers",
    keyLearning: "Digital presence can expand market reach from local to 50+ countries",
    icon: "📱"
  },
  {
    title: "Financial Planning",
    realWorldExample: "Microfinance in Rural Communities",
    description: "Explore how small loans transform rural entrepreneurship",
    keyLearning: "Strategic financing can lift entire communities out of poverty",
    icon: "💰"
  },
  {
    title: "Sustainable Business",
    realWorldExample: "Eco-friendly Himalayan Trekking",
    description: "Study how tourism businesses balance profit with environmental protection",
    keyLearning: "Sustainability practices can increase customer loyalty by 70%",
    icon: "🌍"
  }
];

// Success Stories from Nepal
const nepalSuccessStories = [
  {
    company: "Tootle",
    sector: "Transportation Tech",
    story: "Revolutionized urban mobility in Nepal",
    impact: "50,000+ daily rides",
    lesson: "Identifying local problems creates billion-dollar opportunities"
  },
  {
    company: "Gorkha Brewery",
    sector: "Beverages",
    story: "Local beer brand competing with international giants",
    impact: "40% market share",
    lesson: "Quality and local identity can beat global brands"
  },
  {
    company: "Daraz Nepal",
    sector: "E-commerce",
    story: "Bringing online shopping to Nepal",
    impact: "2M+ customers",
    lesson: "Adapting global models to local needs drives success"
  }
];

export default function BusinessPage() {
  useParallaxEffect();

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section */}
          <section className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-b from-amber-50 to-background relative overflow-hidden">
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute -right-20 top-20 w-80 h-80 bg-gold/5 rounded-full blur-3xl" />
              <div className="absolute left-1/4 bottom-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />
            </div>

            {/* Upcoming Program Banner */}
            <div className="px-4 md:px-6 relative z-10 mb-8">
              <div className="max-w-4xl mx-auto">
                <div className="bg-gradient-to-r from-gold to-amber-600 text-white p-6 rounded-2xl shadow-lg border border-white/20 backdrop-blur-sm">
                  <div className="flex items-center justify-between flex-wrap gap-4">
                    <div className="flex items-center gap-3">
                      <div className="h-12 w-12 rounded-full bg-white/20 flex items-center justify-center">
                        <Building className="h-6 w-6" />
                      </div>
                      <div>
                        <h2 className="text-xl font-bold">📊 BUSINESS PROGRAMS IN DEVELOPMENT!</h2>
                        <p className="text-white/90">Be among the first to know about Nepal's innovative business education programs</p>
                      </div>
                    </div>
                    <Button 
                      variant="secondary" 
                      className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                    >
                      Express Interest <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <div className="px-4 md:px-6 relative">
              <div className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center">
                <div className="flex flex-col justify-center space-y-6">
                  <div className="flex items-center gap-2">
                    <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                      School of Business
                    </div>
                    <Badge className="bg-amber-500/20 text-amber-700 border-amber-300">
                      Programs in Planning
                    </Badge>
                  </div>
                  <div className="space-y-4">
                    <h1 className="heading-xl">
                      Developing Tomorrow's <span className="text-gold">Business Leaders</span>
                    </h1>
                    <p className="max-w-[600px] text-muted-foreground md:text-xl leading-relaxed">
                      Our School of Business is being designed to provide students with the knowledge, skills, and experiences needed to succeed in the global business environment. <span className="font-semibold text-amber-700">Programs launching soon!</span>
                    </p>
                  </div>
                </div>
                <div className="flex items-center justify-center relative">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-green-500/20 via-emerald-400/15 to-blue-500/10 rounded-full blur-2xl animate-pulse"></div>
                  
                  {/* Enhanced Agricultural Business Image */}
                  <div className="relative w-full max-w-[450px] rounded-2xl overflow-hidden shadow-2xl group">
                    {/* Main Image with Agricultural Theme */}
                    <div className="aspect-square w-full h-full bg-gradient-to-br from-green-100 via-emerald-50 to-green-200 flex items-center justify-center relative overflow-hidden">
                      {/* Background Pattern */}
                      <div className="absolute inset-0 opacity-10">
                        <div className="absolute top-10 left-10 text-6xl text-green-600">🌾</div>
                        <div className="absolute top-20 right-16 text-4xl text-emerald-600">📊</div>
                        <div className="absolute bottom-20 left-16 text-5xl text-green-700">🚚</div>
                        <div className="absolute bottom-16 right-12 text-3xl text-emerald-700">💰</div>
                        <div className="absolute top-1/3 left-1/3 text-4xl text-green-500">📱</div>
                        <div className="absolute bottom-1/3 right-1/4 text-5xl text-emerald-500">🌱</div>
                      </div>
                      
                      {/* Central Content */}
                      <div className="relative z-10 text-center p-8">
                        <div className="w-32 h-32 mx-auto mb-6 bg-gradient-to-br from-green-600 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
                          <Leaf className="h-16 w-16 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-green-800 mb-2">Agri-Business Innovation</h3>
                        <p className="text-green-700 text-lg mb-4">Where Technology Meets Agriculture</p>
                        
                        {/* Feature Icons Grid */}
                        <div className="grid grid-cols-2 gap-4 mt-6">
                          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center">
                            <BarChart className="h-6 w-6 text-green-600 mb-1" />
                            <span className="text-xs text-green-700 font-medium">Analytics</span>
                          </div>
                          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center">
                            <Globe className="h-6 w-6 text-emerald-600 mb-1" />
                            <span className="text-xs text-emerald-700 font-medium">Export</span>
                          </div>
                          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center">
                            <TrendingUp className="h-6 w-6 text-green-600 mb-1" />
                            <span className="text-xs text-green-700 font-medium">Growth</span>
                          </div>
                          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center">
                            <Target className="h-6 w-6 text-emerald-600 mb-1" />
                            <span className="text-xs text-emerald-700 font-medium">Strategy</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Animated Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-green-600/30 via-transparent to-emerald-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    
                    {/* Corner Badges */}
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-green-600 text-white shadow-lg">
                        🌾 Agriculture + Business
                      </Badge>
                    </div>
                    <div className="absolute bottom-4 right-4">
                      <Badge className="bg-emerald-600 text-white shadow-lg">
                        📈 Future Ready
                      </Badge>
                    </div>
                    
                    {/* Floating Stats */}
                    <div className="absolute top-1/4 -right-6 bg-white/90 backdrop-blur-sm rounded-xl p-3 shadow-lg transform rotate-12 group-hover:rotate-6 transition-transform duration-500">
                      <div className="text-center">
                        <div className="text-xl font-bold text-green-600">₹25L+</div>
                        <div className="text-xs text-green-700">Career Potential</div>
                      </div>
                    </div>
                    <div className="absolute bottom-1/4 -left-6 bg-white/90 backdrop-blur-sm rounded-xl p-3 shadow-lg transform -rotate-12 group-hover:-rotate-6 transition-transform duration-500">
                      <div className="text-center">
                        <div className="text-xl font-bold text-emerald-600">65%</div>
                        <div className="text-xs text-emerald-700">Nepal's Workforce</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Department Overview */}
          <section className="w-full py-16 md:py-24 bg-light">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="flex items-center gap-2">
                    <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                      Department Overview
                    </div>
                    <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                      In Development
                    </Badge>
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Building Excellence in Business Education
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our School of Business is being thoughtfully designed to provide comprehensive education that balances theoretical knowledge with practical applications, preparing students for leadership roles in various business domains. <span className="font-semibold text-amber-700">Programs are currently in development phase.</span>
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-gold/10 flex items-center justify-center mb-4">
                      <Briefcase className="h-8 w-8 text-gold" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Industry-Relevant Curriculum</h3>
                    <p className="text-muted-foreground">
                      Our programs are designed in consultation with industry leaders to ensure students gain the skills and knowledge most valued in today's business world.
                    </p>
                  </div>

                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-gold/10 flex items-center justify-center mb-4">
                      <Globe className="h-8 w-8 text-gold" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Global Business Perspective</h3>
                    <p className="text-muted-foreground">
                      Students gain an international outlook through our global business curriculum, exchange programs, and diverse faculty with international experience.
                    </p>
                  </div>

                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-gold/10 flex items-center justify-center mb-4">
                      <PieChart className="h-8 w-8 text-gold" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Experiential Learning</h3>
                    <p className="text-muted-foreground">
                      Through internships, case competitions, and consulting projects, students apply classroom learning to real-world business situations.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Featured Program Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="grid items-center gap-10 lg:grid-cols-2 lg:gap-16 max-w-6xl mx-auto">
                <div className="flex items-center justify-center relative">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-green-500/20 via-emerald-400/15 to-blue-500/10 rounded-full blur-2xl animate-pulse"></div>
                  
                  {/* Enhanced Agricultural Business Image */}
                  <div className="relative w-full max-w-[450px] rounded-2xl overflow-hidden shadow-2xl group">
                    {/* Main Image with Agricultural Theme */}
                    <div className="aspect-square w-full h-full bg-gradient-to-br from-green-100 via-emerald-50 to-green-200 flex items-center justify-center relative overflow-hidden">
                      {/* Background Pattern */}
                      <div className="absolute inset-0 opacity-10">
                        <div className="absolute top-10 left-10 text-6xl text-green-600">🌾</div>
                        <div className="absolute top-20 right-16 text-4xl text-emerald-600">📊</div>
                        <div className="absolute bottom-20 left-16 text-5xl text-green-700">🚚</div>
                        <div className="absolute bottom-16 right-12 text-3xl text-emerald-700">💰</div>
                        <div className="absolute top-1/3 left-1/3 text-4xl text-green-500">📱</div>
                        <div className="absolute bottom-1/3 right-1/4 text-5xl text-emerald-500">🌱</div>
                      </div>
                      
                      {/* Central Content */}
                      <div className="relative z-10 text-center p-8">
                        <div className="w-32 h-32 mx-auto mb-6 bg-gradient-to-br from-green-600 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
                          <Leaf className="h-16 w-16 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-green-800 mb-2">Agri-Business Innovation</h3>
                        <p className="text-green-700 text-lg mb-4">Where Technology Meets Agriculture</p>
                        
                        {/* Feature Icons Grid */}
                        <div className="grid grid-cols-2 gap-4 mt-6">
                          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center">
                            <BarChart className="h-6 w-6 text-green-600 mb-1" />
                            <span className="text-xs text-green-700 font-medium">Analytics</span>
                          </div>
                          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center">
                            <Globe className="h-6 w-6 text-emerald-600 mb-1" />
                            <span className="text-xs text-emerald-700 font-medium">Export</span>
                          </div>
                          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center">
                            <TrendingUp className="h-6 w-6 text-green-600 mb-1" />
                            <span className="text-xs text-green-700 font-medium">Growth</span>
                          </div>
                          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center">
                            <Target className="h-6 w-6 text-emerald-600 mb-1" />
                            <span className="text-xs text-emerald-700 font-medium">Strategy</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Animated Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-green-600/30 via-transparent to-emerald-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    
                    {/* Corner Badges */}
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-green-600 text-white shadow-lg">
                        🌾 Agriculture + Business
                      </Badge>
                    </div>
                    <div className="absolute bottom-4 right-4">
                      <Badge className="bg-emerald-600 text-white shadow-lg">
                        📈 Future Ready
                      </Badge>
                    </div>
                    
                    {/* Floating Stats */}
                    <div className="absolute top-1/4 -right-6 bg-white/90 backdrop-blur-sm rounded-xl p-3 shadow-lg transform rotate-12 group-hover:rotate-6 transition-transform duration-500">
                      <div className="text-center">
                        <div className="text-xl font-bold text-green-600">₹25L+</div>
                        <div className="text-xs text-green-700">Career Potential</div>
                      </div>
                    </div>
                    <div className="absolute bottom-1/4 -left-6 bg-white/90 backdrop-blur-sm rounded-xl p-3 shadow-lg transform -rotate-12 group-hover:-rotate-6 transition-transform duration-500">
                      <div className="text-center">
                        <div className="text-xl font-bold text-emerald-600">65%</div>
                        <div className="text-xs text-emerald-700">Nepal's Workforce</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col justify-center space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <div className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium bg-green-600 text-white">
                        Featured Program
                      </div>
                      <Badge className="bg-orange-100 text-orange-700 border-orange-300">
                        Planning Phase
                      </Badge>
                    </div>
                    <h3 className="heading-md">B.Sc. in Agri Management</h3>
                    <p className="text-muted-foreground text-lg leading-relaxed">
                      Our planned flagship Agri Management program will provide students with a unique combination of agricultural knowledge and business expertise. The curriculum is being designed to integrate sustainable farming practices with modern business principles, complemented by hands-on experiences through field work and industry partnerships. <span className="font-semibold text-amber-700">Program launching soon!</span>
                    </p>
                  </div>
                  <ul className="grid gap-3 mt-2">
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100">
                        <Leaf className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="font-medium">Agricultural Business Foundation (Planned)</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100">
                        <Target className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="font-medium">Supply Chain Optimization (In Development)</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100">
                        <Briefcase className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="font-medium">Agri-Tech Innovation Program (Planned)</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          {/* Program Cards Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="flex items-center gap-2">
                    <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                      Our Programs
                    </div>
                    <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                      Programs in Development
                    </Badge>
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Future Business Education Programs
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Explore our carefully curated business programs designed to prepare you for leadership roles in Nepal's evolving economy. <span className="font-semibold text-amber-700">All programs launching soon.</span>
                    </p>
                  </div>
                </div>

                {/* Program Categories Filter */}
                <div className="flex flex-wrap justify-center gap-3 mb-12">
                  <Button variant="outline" size="sm" className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100">
                    <Globe className="h-4 w-4 mr-2" />
                    All Programs
                  </Button>
                  <Button variant="outline" size="sm" className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100">
                    <Leaf className="h-4 w-4 mr-2" />
                    Agriculture Focus
                  </Button>
                  <Button variant="outline" size="sm" className="bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100">
                    <BarChart className="h-4 w-4 mr-2" />
                    Analytics & Tech
                  </Button>
                  <Button variant="outline" size="sm" className="bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Finance & Growth
                  </Button>
                </div>

                {/* Enhanced Program Cards Grid */}
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mt-12">
                  {[...businessPrograms, ...additionalPrograms].map((program, index) => (
                    <div 
                      key={index} 
                      className="group relative transform transition-all duration-300 hover:scale-105 hover:-translate-y-2"
                    >
                      {/* Card */}
                      <Card className={`h-full border-0 shadow-lg hover:shadow-2xl transition-all duration-500 relative overflow-hidden ${
                        program.highlight 
                          ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 border-2 border-green-300' 
                          : 'bg-gradient-to-br from-white to-gray-50'
                      }`}>
                        
                        {/* Background Pattern */}
                        <div className="absolute inset-0 opacity-5">
                          <div className="absolute top-4 right-4 text-6xl text-gray-400">
                            {program.highlight ? '🌾' : '💼'}
                          </div>
                          <div className="absolute bottom-4 left-4 text-4xl text-gray-300">
                            📚
                          </div>
                        </div>

                        {/* Status Badges */}
                        <div className="absolute top-4 right-4 flex flex-col gap-2 z-10">
                          {program.isNew && (
                            <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg animate-pulse">
                              ⭐ Featured
                            </Badge>
                          )}
                          <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-md">
                             🚀 Upcoming
                          </Badge>
                        </div>

                        {/* Program Icon */}
                        <div className="absolute top-6 left-6 z-10">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center shadow-lg ${
                            program.highlight 
                              ? 'bg-gradient-to-br from-green-600 to-emerald-600' 
                              : 'bg-gradient-to-br from-blue-600 to-indigo-600'
                          }`}>
                            {program.icon}
                          </div>
                        </div>

                        <CardHeader className="pt-20 pb-4 relative z-10">
                          <CardTitle className={`text-xl font-bold leading-tight ${
                            program.highlight ? 'text-green-800' : 'text-gray-800'
                          }`}>
                            {program.name}
                          </CardTitle>
                          {program.description && (
                            <CardDescription className={`text-base font-medium ${
                              program.highlight ? 'text-green-600' : 'text-gray-600'
                            }`}>
                              {program.description}
                            </CardDescription>
                          )}
                        </CardHeader>

                        <CardContent className="pb-6 relative z-10">
                          <p className="text-gray-700 leading-relaxed mb-6 line-clamp-3">
                            {program.details ? program.details : `Comprehensive program covering all aspects of ${program.name}.`}
                          </p>
                          
                          {/* Program Highlights */}
                          <div className="space-y-3 mb-6">
                            <div className="flex items-center gap-2 text-sm">
                              <div className={`w-2 h-2 rounded-full ${
                                program.highlight ? 'bg-green-500' : 'bg-blue-500'
                              }`}></div>
                              <span className="text-gray-600">Industry-Relevant Curriculum</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <div className={`w-2 h-2 rounded-full ${
                                program.highlight ? 'bg-emerald-500' : 'bg-indigo-500'
                              }`}></div>
                              <span className="text-gray-600">Expert Faculty & Mentorship</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <div className={`w-2 h-2 rounded-full ${
                                program.highlight ? 'bg-green-400' : 'bg-blue-400'
                              }`}></div>
                              <span className="text-gray-600">Hands-on Project Experience</span>
                            </div>
                          </div>

                        </CardContent>

                        {/* Hover Effect Overlay */}
                        <div className={`absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-500 ${
                          program.highlight 
                            ? 'bg-gradient-to-br from-green-600 to-emerald-600' 
                            : 'bg-gradient-to-br from-blue-600 to-indigo-600'
                        }`}></div>
                      </Card>

                      {/* Floating Info Card on Hover */}
                      <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transform scale-75 group-hover:scale-100 transition-all duration-300 z-20">
                        <div className="bg-white rounded-lg shadow-xl p-3 border border-gray-200">
                          <div className="text-xs text-gray-600 text-center">
                            <div className="font-semibold text-gray-800">Program Status</div>
                            <div className="text-amber-600">In Development</div>
                            <div className="text-green-600 font-medium">Upcoming</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Agri Management Spotlight */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 relative overflow-hidden">
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute -right-20 top-20 w-80 h-80 bg-green-500/10 rounded-full blur-3xl animate-pulse" />
              <div className="absolute left-1/4 bottom-0 w-96 h-96 bg-emerald-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
            </div>
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <div className="flex items-center justify-center gap-2 mb-4">
                    <Badge className="bg-green-600 text-white px-6 py-2">
                      🌾 Spotlight Program
                    </Badge>
                    <Badge variant="outline" className="bg-white/80 text-green-700 border-green-300">
                      Innovation Focus
                    </Badge>
                  </div>
                  <h2 className="text-4xl md:text-5xl font-bold mb-6 text-green-800">
                    Agri Management: Where <span className="text-emerald-600">Agriculture Meets Business</span>
                  </h2>
                  <p className="text-xl text-green-700 max-w-4xl mx-auto leading-relaxed">
                    Nepal's agricultural sector contributes 25% to GDP and employs 65% of the population. 
                    Our Agri Management program will train the next generation of leaders to revolutionize this vital sector.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-12 items-center">
                  <div className="space-y-8">
                    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                      <h3 className="text-2xl font-bold mb-4 text-green-800">📊 The Opportunity</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-4 bg-green-50 rounded-xl">
                          <div className="text-3xl font-bold text-green-600">65%</div>
                          <div className="text-sm text-green-700">Population in Agriculture</div>
                        </div>
                        <div className="text-center p-4 bg-emerald-50 rounded-xl">
                          <div className="text-3xl font-bold text-emerald-600">25%</div>
                          <div className="text-sm text-emerald-700">GDP Contribution</div>
                        </div>
                        <div className="text-center p-4 bg-green-50 rounded-xl">
                          <div className="text-3xl font-bold text-green-600">300%</div>
                          <div className="text-sm text-green-700">Potential Growth</div>
                        </div>
                        <div className="text-center p-4 bg-emerald-50 rounded-xl">
                          <div className="text-3xl font-bold text-emerald-600">₹2L+</div>
                          <div className="text-sm text-emerald-700">Starting Salaries</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                      <h3 className="text-xl font-bold mb-4 text-green-800 flex items-center gap-2">
                        🎯 What You'll Master
                      </h3>
                      <div className="space-y-3">
                        {[
                          "Agricultural Supply Chain Optimization",
                          "Sustainable Farming Business Models",
                          "Agri-Tech Integration & Innovation",
                          "Rural Finance & Microfinance",
                          "Food Safety & Quality Management",
                          "Export Market Development"
                        ].map((skill, idx) => (
                          <div key={idx} className="flex items-center gap-3">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className="text-green-700">{skill}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <div className="bg-gradient-to-br from-white to-green-50 rounded-2xl p-6 shadow-lg">
                      <h3 className="text-xl font-bold mb-4 text-green-800">🚀 Career Paths</h3>
                      <div className="space-y-4">
                        {[
                          { title: "Agri-Business Manager", salary: "₹8-15L", growth: "High" },
                          { title: "Supply Chain Director", salary: "₹12-25L", growth: "Very High" },
                          { title: "Agri-Tech Entrepreneur", salary: "₹15L+", growth: "Unlimited" },
                          { title: "Export Manager", salary: "₹10-20L", growth: "High" }
                        ].map((career, idx) => (
                          <div key={idx} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div>
                              <div className="font-semibold text-green-800">{career.title}</div>
                              <div className="text-sm text-green-600">{career.salary} annually</div>
                            </div>
                            <Badge className="bg-green-600 text-white">{career.growth}</Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                      <h3 className="text-xl font-bold mb-4 text-green-800">💡 Real Impact</h3>
                      <blockquote className="text-green-700 italic text-lg">
                        "Agri Management graduates will help Nepali farmers increase their income by 300% 
                        through modern supply chains, direct market access, and value-added processing."
                      </blockquote>
                      <cite className="text-green-600 text-sm mt-2 block">- Industry Projection</cite>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Nepal Business Opportunities */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <Badge className="bg-blue-100 text-blue-700 px-6 py-2 mb-4">
                    🇳🇵 Nepal Business Landscape
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-6">
                    Discover <span className="text-blue-600">Untapped Business Opportunities</span> in Nepal
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                    Nepal's emerging economy offers incredible opportunities for innovative business minds. 
                    Explore sectors with massive growth potential.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  {nepalBusinessOpportunities.map((opportunity, index) => (
                    <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="text-3xl">{opportunity.icon}</span>
                            <div>
                              <CardTitle className="text-xl text-blue-800">{opportunity.sector}</CardTitle>
                              <CardDescription className="text-blue-600">{opportunity.description}</CardDescription>
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">{opportunity.potential}</div>
                            <div className="text-xs text-blue-500">Growth Potential</div>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-6">
                        <h4 className="font-semibold mb-3 text-gray-800">Business Opportunities:</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {opportunity.opportunities.map((opp, idx) => (
                            <div key={idx} className="flex items-center gap-2 p-2 bg-blue-50 rounded text-sm">
                              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0"></div>
                              <span className="text-blue-700">{opp}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Interactive Business Concepts */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-purple-50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <Badge className="bg-purple-100 text-purple-700 px-6 py-2 mb-4">
                    📚 Learn by Example
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-6">
                    Business Concepts <span className="text-purple-600">Come Alive</span> with Real Examples
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                    Understanding business theory through real Nepali case studies makes learning engaging and memorable.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  {businessConcepts.map((concept, index) => (
                    <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                      <CardHeader className="bg-gradient-to-br from-purple-50 to-pink-50">
                        <div className="flex items-center gap-3 mb-2">
                          <span className="text-3xl">{concept.icon}</span>
                          <CardTitle className="text-xl text-purple-800">{concept.title}</CardTitle>
                        </div>
                        <CardDescription className="text-purple-600 font-semibold">
                          📖 {concept.realWorldExample}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="p-6 space-y-4">
                        <p className="text-gray-700 leading-relaxed">{concept.description}</p>
                        <div className="bg-gradient-to-r from-purple-100 to-pink-100 p-4 rounded-lg">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-purple-600">💡</span>
                            <span className="font-semibold text-purple-800">Key Learning:</span>
                          </div>
                          <p className="text-purple-700 font-medium">{concept.keyLearning}</p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Nepal Success Stories */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-br from-orange-50 to-red-50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <Badge className="bg-orange-100 text-orange-700 px-6 py-2 mb-4">
                    🏆 Success Stories
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-6">
                    Learn from <span className="text-orange-600">Nepal's Business Champions</span>
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                    These Nepali companies prove that with the right business education and mindset, 
                    local entrepreneurs can compete globally.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8">
                  {nepalSuccessStories.map((story, index) => (
                    <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                      <CardHeader className="bg-gradient-to-br from-orange-50 to-red-50 text-center">
                        <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4 text-white font-bold text-xl">
                          {story.company.charAt(0)}
                        </div>
                        <CardTitle className="text-xl text-orange-800">{story.company}</CardTitle>
                        <CardDescription className="text-orange-600">{story.sector}</CardDescription>
                      </CardHeader>
                      <CardContent className="p-6 space-y-4">
                        <p className="text-gray-700">{story.story}</p>
                        <div className="bg-orange-100 p-3 rounded-lg">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-orange-600">{story.impact}</div>
                            <div className="text-sm text-orange-700">Business Impact</div>
                          </div>
                        </div>
                        <div className="bg-gradient-to-r from-orange-50 to-red-50 p-4 rounded-lg border-l-4 border-orange-500">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-orange-600">🎯</span>
                            <span className="font-semibold text-orange-800">Business Lesson:</span>
                          </div>
                          <p className="text-orange-700 text-sm">{story.lesson}</p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Business Tools Section */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-br from-indigo-50 via-blue-50 to-cyan-50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <Badge className="bg-indigo-100 text-indigo-700 px-6 py-2 mb-4">
                    🛠️ Business Tools
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-6">
                    Interactive <span className="text-indigo-600">Business Tools</span> for Learning
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                    Practice and master essential business concepts with our interactive tools designed 
                    to enhance your learning experience.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8">
                  {/* Financial Literacy Tool */}
                  <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <CardHeader className="bg-gradient-to-br from-green-50 to-emerald-50 relative z-10">
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-emerald-600 rounded-xl flex items-center justify-center">
                          <DollarSign className="h-6 w-6 text-white" />
                        </div>
                        <Badge className="bg-green-100 text-green-700">Interactive</Badge>
                      </div>
                      <CardTitle className="text-xl text-green-800">Financial Literacy Tool</CardTitle>
                      <CardDescription className="text-green-600">
                        Master personal and business finance fundamentals
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-6 relative z-10">
                      <p className="text-gray-700 mb-4">
                        Learn budgeting, investment planning, loan calculations, and financial 
                        decision-making through interactive scenarios.
                      </p>
                      <div className="space-y-2 mb-6">
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-gray-600">Budget Planning Calculator</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                          <span className="text-gray-600">Investment Growth Simulator</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <span className="text-gray-600">Loan & EMI Calculator</span>
                        </div>
                      </div>
                      <Button className="w-full bg-green-600 hover:bg-green-700" asChild>
                        <Link href="/business-tools?tool=financial-literacy">
                          Try Financial Tool
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>

                  {/* Business Model Tool */}
                  <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <CardHeader className="bg-gradient-to-br from-blue-50 to-indigo-50 relative z-10">
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                          <BarChart className="h-6 w-6 text-white" />
                        </div>
                        <Badge className="bg-blue-100 text-blue-700">Builder</Badge>
                      </div>
                      <CardTitle className="text-xl text-blue-800">Business Model Canvas</CardTitle>
                      <CardDescription className="text-blue-600">
                        Design and validate your business ideas visually
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-6 relative z-10">
                      <p className="text-gray-700 mb-4">
                        Create comprehensive business models using the proven Business Model Canvas 
                        framework with guided templates.
                      </p>
                      <div className="space-y-2 mb-6">
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span className="text-gray-600">9-Block Canvas Builder</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                          <span className="text-gray-600">Industry Templates</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <span className="text-gray-600">Validation Checklist</span>
                        </div>
                      </div>
                      <Button className="w-full bg-blue-600 hover:bg-blue-700" asChild>
                        <Link href="/business-tools?tool=business-model">
                          Build Business Model
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>

                  {/* Startup Dictionary */}
                  <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <CardHeader className="bg-gradient-to-br from-purple-50 to-pink-50 relative z-10">
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl flex items-center justify-center">
                          <Target className="h-6 w-6 text-white" />
                        </div>
                        <Badge className="bg-purple-100 text-purple-700">Reference</Badge>
                      </div>
                      <CardTitle className="text-xl text-purple-800">Startup Dictionary</CardTitle>
                      <CardDescription className="text-purple-600">
                        Essential business and startup terminology guide
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-6 relative z-10">
                      <p className="text-gray-700 mb-4">
                        Comprehensive glossary of business, startup, and entrepreneurship terms 
                        with examples and practical applications.
                      </p>
                      <div className="space-y-2 mb-6">
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <span className="text-gray-600">500+ Business Terms</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                          <span className="text-gray-600">Real-world Examples</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                          <span className="text-gray-600">Search & Categories</span>
                        </div>
                      </div>
                      <Button className="w-full bg-purple-600 hover:bg-purple-700" asChild>
                        <Link href="/business-tools?tool=startup-dictionary">
                          Explore Dictionary
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                {/* Tools Overview */}
                <div className="mt-12 text-center">
                  <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-indigo-100">
                    <h3 className="text-2xl font-bold mb-4 text-indigo-800">
                      🎯 Why Use Business Tools?
                    </h3>
                    <div className="grid md:grid-cols-3 gap-6 mt-6">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <span className="text-2xl">🧠</span>
                        </div>
                        <h4 className="font-semibold text-indigo-800 mb-2">Active Learning</h4>
                        <p className="text-sm text-indigo-600">
                          Learn by doing with hands-on interactive experiences
                        </p>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <span className="text-2xl">⚡</span>
                        </div>
                        <h4 className="font-semibold text-blue-800 mb-2">Instant Feedback</h4>
                        <p className="text-sm text-blue-600">
                          Get immediate results and learn from your decisions
                        </p>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 bg-cyan-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <span className="text-2xl">🚀</span>
                        </div>
                        <h4 className="font-semibold text-cyan-800 mb-2">Real-World Ready</h4>
                        <p className="text-sm text-cyan-600">
                          Practice skills you'll use in actual business scenarios
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Research Areas */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-light/50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="flex items-center gap-2">
                    <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                      Research
                    </div>
                    <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                      Future Focus
                    </Badge>
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Business Research Initiatives
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our planned research programs will address contemporary business challenges in Nepal and contribute to global business knowledge through local insights. <span className="font-semibold text-amber-700">Research centers in development.</span>
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                  {researchAreas.map((area, index) => (
                    <div key={index} className="bg-background p-6 rounded-xl border border-border/50 hover:shadow-md transition-all duration-300 relative">
                      <div className="absolute top-4 right-4">
                        <Badge className="bg-amber-100 text-amber-700 text-xs">
                          Planned
                        </Badge>
                      </div>
                      <h3 className="text-xl font-bold mb-3">{area.title}</h3>
                      <p className="text-muted-foreground mb-4">
                        {area.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

        </main>
        <Footer />
      </div>
    </PageTransition>
  );
}
