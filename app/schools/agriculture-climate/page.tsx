'use client'

import Link from "next/link"
import { useState } from "react"
import { ArrowRight, Leaf, ChevronRight, Cloud, SunMedium, Sprout, Droplets, Wind, Trees, Mountain, Thermometer, BarChart3, MapPin, Users, BookOpen, Lightbulb, Star, TrendingUp, Globe, Calendar, Award, Zap, CloudRain, FlaskConical, Microscope, GraduationCap, Eye, EyeOff } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useParallaxEffect } from "@/components/parallax-effect"
import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LazyImage } from "@/components/ui/lazy-image"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { Progress } from "@/components/ui/progress"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { SkipLink } from "@/components/ui/skip-link"

// Nepal Agricultural Data
const nepalAgriStats = [
  { label: "Agriculture contribution to GDP", value: "27%", icon: <BarChart3 className="h-5 w-5" /> },
  { label: "Population dependent on agriculture", value: "66%", icon: <Users className="h-5 w-5" /> },
  { label: "Ecological zones", value: "5", icon: <Mountain className="h-5 w-5" /> },
  { label: "Crop varieties cultivated", value: "300+", icon: <Sprout className="h-5 w-5" /> },
];

// Nepal's Agricultural Innovations
const nepalInnovations = [
  {
    title: "High-Altitude Crop Research",
    description: "Developing climate-resilient varieties for mountain agriculture",
    impact: "Increased yields by 40% in high-altitude regions",
    icon: <Mountain className="h-8 w-8 text-green-600" />,
    details: "Our research focuses on developing crop varieties that can thrive in Nepal's unique high-altitude environments, helping farmers adapt to changing climate conditions."
  },
  {
    title: "Precision Agriculture Systems",
    description: "IoT-based monitoring for smallholder farmers",
    impact: "30% reduction in water usage",
    icon: <Zap className="h-8 w-8 text-blue-600" />,
    details: "Implementing smart sensors and mobile apps to help Nepali farmers optimize irrigation, fertilization, and pest management practices."
  },
  {
    title: "Organic Tea Innovation",
    description: "Sustainable tea cultivation in hill regions",
    impact: "Premium market access for 500+ farmers",
    icon: <Leaf className="h-8 w-8 text-green-600" />,
    details: "Developing organic tea cultivation methods that preserve biodiversity while creating economic opportunities for hill communities."
  },
  {
    title: "Climate-Smart Rice Systems",
    description: "Water-efficient rice cultivation for Terai region",
    impact: "25% increase in productivity",
    icon: <Droplets className="h-8 w-8 text-blue-600" />,
    details: "System of Rice Intensification (SRI) and direct seeding techniques adapted for Nepal's lowland agriculture."
  }
];

// Climate Science Opportunities in Nepal
const climateOpportunities = [
  {
    title: "Himalayan Climate Observatory",
    description: "Monitoring climate change impacts on the world's highest mountains",
    location: "High Himalayas (3,000-5,000m)",
    focus: "Glacial retreat, temperature trends, precipitation patterns",
    research: "Partnership with international climate research institutes"
  },
  {
    title: "Monsoon Research Station",
    description: "Understanding South Asian monsoon dynamics and variability",
    location: "Central Hills (1,000-2,500m)",
    focus: "Rainfall patterns, drought prediction, flood forecasting",
    research: "Real-time weather monitoring and forecasting systems"
  },
  {
    title: "Biodiversity Climate Lab",
    description: "Studying ecosystem responses to climate change",
    location: "Terai Plains (100-1,000m)",
    focus: "Species migration, habitat shifts, conservation strategies",
    research: "Long-term ecological monitoring programs"
  }
];

// Program Highlights
const programHighlights = [
  {
    title: "B.Sc. in Sustainable Agriculture",
    status: "Program in Development",
    duration: "4 years (planned)",
    specializations: ["Organic Farming", "High-Altitude Agriculture", "Precision Farming", "Agroecology"],
    highlights: [
      "Field stations across Nepal's ecological zones (planned)",
      "Industry partnerships with leading agribusinesses",
      "International exchange programs (in development)",
      "Hands-on research from year one"
    ]
  },
  {
    title: "B.Sc. in Climate Science",
    status: "Program in Development", 
    duration: "4 years (planned)",
    specializations: ["Atmospheric Physics", "Climate Modeling", "Environmental Monitoring", "Climate Policy"],
    highlights: [
      "Access to Himalayan research stations (planned)",
      "State-of-the-art climate modeling facilities (in development)",
      "Collaboration with IPCC scientists",
      "Real-world climate impact projects"
    ]
  }
];

// Research Centers
const researchCenters = [
  {
    name: "Center for Himalayan Agriculture",
    focus: "High-altitude farming systems and mountain agriculture research",
    facilities: ["Mountain research stations", "Seed banks", "Climate chambers"],
    partnerships: ["ICIMOD", "Local farming communities", "International research institutes"]
  },
  {
    name: "Nepal Climate Observatory",
    focus: "Long-term climate monitoring and research across Nepal's ecological zones",
    facilities: ["Weather monitoring stations", "Satellite data centers", "Climate modeling labs"],
    partnerships: ["Nepal Department of Meteorology", "USAID", "World Bank Climate Program"]
  },
  {
    name: "Sustainable Innovation Hub",
    focus: "Technology transfer and innovation for smallholder farmers",
    facilities: ["Prototype labs", "Demonstration farms", "Digital agriculture centers"],
    partnerships: ["Local tech companies", "NGOs", "Farmer cooperatives"]
  }
];

// Success Stories
const successStories = [
  {
    title: "Mountain Coffee Revolution",
    description: "Helping farmers transition to high-value coffee cultivation in hill regions",
    impact: "200% increase in farmer income",
    students: "15 students involved",
    image: "/placeholder.svg?height=300&width=400"
  },
  {
    title: "Smart Irrigation Project",
    description: "Implementing sensor-based irrigation systems in drought-prone areas",
    impact: "40% water savings achieved",
    students: "25 students involved",
    image: "/placeholder.svg?height=300&width=400"
  },
  {
    title: "Climate Resilient Seeds",
    description: "Developing drought and flood-resistant crop varieties",
    impact: "10 new varieties released",
    students: "30 students involved",
    image: "/placeholder.svg?height=300&width=400"
  }
];

// District-level ecological region mapping data
const districtEcologicalData = [
  {"district": "Taplejung", "ecological_region": "Mountain"},
  {"district": "Sankhuwasabha", "ecological_region": "Mountain"},
  {"district": "Solukhumbu", "ecological_region": "Mountain"},
  {"district": "Dolakha", "ecological_region": "Mountain"},
  {"district": "Rasuwa", "ecological_region": "Mountain"},
  {"district": "Sindhupalchok", "ecological_region": "Mountain"},
  {"district": "Manang", "ecological_region": "Mountain"},
  {"district": "Mustang", "ecological_region": "Mountain"},
  {"district": "Dolpa", "ecological_region": "Mountain"},
  {"district": "Mugu", "ecological_region": "Mountain"},
  {"district": "Humla", "ecological_region": "Mountain"},
  {"district": "Bajura", "ecological_region": "Mountain"},
  {"district": "Bajhang", "ecological_region": "Mountain"},
  {"district": "Darchula", "ecological_region": "Mountain"},
  {"district": "Jumla", "ecological_region": "Mountain"},
  {"district": "Kalikot", "ecological_region": "Mountain"},
  {"district": "Panchthar", "ecological_region": "Hill"},
  {"district": "Ilam", "ecological_region": "Hill"},
  {"district": "Dhankuta", "ecological_region": "Hill"},
  {"district": "Terhathum", "ecological_region": "Hill"},
  {"district": "Bhojpur", "ecological_region": "Hill"},
  {"district": "Khotang", "ecological_region": "Hill"},
  {"district": "Okhaldhunga", "ecological_region": "Hill"},
  {"district": "Udayapur", "ecological_region": "Hill"},
  {"district": "Sindhuli", "ecological_region": "Hill"},
  {"district": "Ramechhap", "ecological_region": "Hill"},
  {"district": "Kavrepalanchok", "ecological_region": "Hill"},
  {"district": "Bhaktapur", "ecological_region": "Hill"},
  {"district": "Lalitpur", "ecological_region": "Hill"},
  {"district": "Kathmandu", "ecological_region": "Hill"},
  {"district": "Nuwakot", "ecological_region": "Hill"},
  {"district": "Dhading", "ecological_region": "Hill"},
  {"district": "Makwanpur", "ecological_region": "Hill"},
  {"district": "Chitwan", "ecological_region": "Hill"},
  {"district": "Gorkha", "ecological_region": "Hill"},
  {"district": "Lamjung", "ecological_region": "Hill"},
  {"district": "Tanahun", "ecological_region": "Hill"},
  {"district": "Syangja", "ecological_region": "Hill"},
  {"district": "Kaski", "ecological_region": "Hill"},
  {"district": "Parbat", "ecological_region": "Hill"},
  {"district": "Baglung", "ecological_region": "Hill"},
  {"district": "Myagdi", "ecological_region": "Hill"},
  {"district": "Gulmi", "ecological_region": "Hill"},
  {"district": "Arghakhanchi", "ecological_region": "Hill"},
  {"district": "Palpa", "ecological_region": "Hill"},
  {"district": "Pyuthan", "ecological_region": "Hill"},
  {"district": "Rolpa", "ecological_region": "Hill"},
  {"district": "Rukum East", "ecological_region": "Hill"},
  {"district": "Rukum West", "ecological_region": "Hill"},
  {"district": "Salyan", "ecological_region": "Hill"},
  {"district": "Dang", "ecological_region": "Hill"},
  {"district": "Banke", "ecological_region": "Hill"},
  {"district": "Bardiya", "ecological_region": "Hill"},
  {"district": "Surkhet", "ecological_region": "Hill"},
  {"district": "Dailekh", "ecological_region": "Hill"},
  {"district": "Jajarkot", "ecological_region": "Hill"},
  {"district": "Kailali", "ecological_region": "Hill"},
  {"district": "Achham", "ecological_region": "Hill"},
  {"district": "Doti", "ecological_region": "Hill"},
  {"district": "Kanchanpur", "ecological_region": "Terai"},
  {"district": "Kailali", "ecological_region": "Terai"},
  {"district": "Bardiya", "ecological_region": "Terai"},
  {"district": "Banke", "ecological_region": "Terai"},
  {"district": "Dang", "ecological_region": "Terai"},
  {"district": "Pyuthan", "ecological_region": "Terai"},
  {"district": "Kapilvastu", "ecological_region": "Terai"},
  {"district": "Rupandehi", "ecological_region": "Terai"},
  {"district": "Nawalparasi West", "ecological_region": "Terai"},
  {"district": "Nawalparasi East", "ecological_region": "Terai"},
  {"district": "Chitwan", "ecological_region": "Terai"},
  {"district": "Makwanpur", "ecological_region": "Terai"},
  {"district": "Bara", "ecological_region": "Terai"},
  {"district": "Parsa", "ecological_region": "Terai"},
  {"district": "Rautahat", "ecological_region": "Terai"},
  {"district": "Sarlahi", "ecological_region": "Terai"},
  {"district": "Mahottari", "ecological_region": "Terai"},
  {"district": "Dhanusha", "ecological_region": "Terai"},
  {"district": "Siraha", "ecological_region": "Terai"},
  {"district": "Saptari", "ecological_region": "Terai"},
  {"district": "Sunsari", "ecological_region": "Terai"},
  {"district": "Morang", "ecological_region": "Terai"},
  {"district": "Jhapa", "ecological_region": "Terai"}
];

// Enhanced climate zones data with detailed information
const climateZones = [
  {
    id: "mountain",
    name: "Mountain Region",
    color: "#3B82F6",
    districts: districtEcologicalData.filter(d => d.ecological_region === "Mountain").length,
    elevation: "3,000m - 8,848m",
    temperature: "-10°C to 15°C",
    precipitation: "250-1,000mm",
    area: "35% of Nepal",
    population: "7% of total population",
    overview: "The Mountain region encompasses the highest peaks on Earth, including Mount Everest. This region experiences extreme weather conditions and is crucial for understanding climate change impacts on high-altitude ecosystems.",
    ecosystem: {
      vegetation: "Alpine meadows, rhododendron forests, juniper scrublands",
      wildlife: "Snow leopard, blue sheep, musk deer, Himalayan tahr",
      conservation: "Home to 3 National Parks and 2 Conservation Areas",
      threats: "Glacial retreat, temperature rise, habitat fragmentation"
    },
    agriculture: {
      crops: "Barley, buckwheat, potato, apple, medicinal herbs",
      challenges: "Short growing seasons, soil erosion, climate variability",
      innovations: "High-altitude greenhouses, improved seed varieties",
      potential: "Medicinal plants, organic farming, agro-tourism"
    },
    research: {
      focus: "Glaciology, permafrost studies, high-altitude agriculture",
      facilities: "Climate monitoring stations, research outposts",
      opportunities: "Climate modeling, ecosystem monitoring, biodiversity studies",
      partnerships: "ICIMOD, international climate research networks"
    }
  },
  {
    id: "hill",
    name: "Hill Region", 
    color: "#10B981",
    districts: districtEcologicalData.filter(d => d.ecological_region === "Hill").length,
    elevation: "1,000m - 3,000m",
    temperature: "5°C to 25°C",
    precipitation: "1,200-2,500mm",
    area: "42% of Nepal",
    population: "45% of total population",
    overview: "The Hill region forms the middle belt of Nepal, characterized by terraced agriculture and diverse forest ecosystems. This region is critical for understanding monsoon impacts and sustainable agriculture practices.",
    ecosystem: {
      vegetation: "Deciduous forests, pine forests, terraced farmlands",
      wildlife: "Red panda, leopard, barking deer, various bird species",
      conservation: "Multiple community forests and protected areas",
      threats: "Deforestation, landslides, biodiversity loss"
    },
    agriculture: {
      crops: "Rice, maize, wheat, millet, tea, vegetables",
      challenges: "Soil degradation, water management, market access",
      innovations: "Terracing systems, integrated farming, organic methods",
      potential: "Agroforestry, specialty crops, eco-tourism"
    },
    research: {
      focus: "Agroecology, forest management, watershed studies",
      facilities: "Agricultural research stations, forest monitoring plots",
      opportunities: "Sustainable farming, climate adaptation, biodiversity conservation",
      partnerships: "Local communities, agricultural universities, NGOs"
    }
  },
  {
    id: "terai",
    name: "Terai Region",
    color: "#F59E0B",
    districts: districtEcologicalData.filter(d => d.ecological_region === "Terai").length,
    elevation: "60m - 1,000m",
    temperature: "15°C to 40°C", 
    precipitation: "1,000-2,500mm",
    area: "23% of Nepal",
    population: "48% of total population",
    overview: "The Terai plains form Nepal's agricultural heartland, producing most of the country's food grains. This region is vital for food security and understanding lowland climate impacts.",
    ecosystem: {
      vegetation: "Tropical forests, grasslands, agricultural fields",
      wildlife: "Royal Bengal tiger, one-horned rhinoceros, elephants",
      conservation: "Chitwan and Bardiya National Parks",
      threats: "Habitat loss, human-wildlife conflict, pollution"
    },
    agriculture: {
      crops: "Rice, wheat, sugarcane, jute, tobacco, fruits",
      challenges: "Flooding, drought, pest management, soil salinity",
      innovations: "Precision agriculture, water-efficient irrigation",
      potential: "Commercial farming, food processing, agricultural exports"
    },
    research: {
      focus: "Crop science, water management, climate resilience",
      facilities: "Agricultural research centers, meteorological stations",
      opportunities: "Precision farming, climate adaptation, sustainable intensification",
      partnerships: "Government agencies, international development organizations"
    }
  }
];

export default function AgricultureClimatePage() {
  useParallaxEffect();
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedZone, setSelectedZone] = useState<string | null>(null);

  return (
    <>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section with Upcoming Program Banner */}
          <section className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-br from-green-50 via-blue-50 to-yellow-50 relative overflow-hidden">
            {/* Animated Background Elements */}
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute -right-20 top-20 w-80 h-80 bg-green-600/10 rounded-full blur-3xl animate-pulse" />
              <div className="absolute left-1/4 bottom-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
              <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-yellow-400/10 rounded-full blur-3xl animate-pulse delay-2000" />
            </div>
            
            {/* Upcoming Program Banner */}
            <div className="px-4 md:px-6 relative z-10 mb-8">
              <div className="max-w-4xl mx-auto">
                <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-6 rounded-2xl shadow-lg border border-white/20 backdrop-blur-sm">
                  <div className="flex items-center justify-between flex-wrap gap-4">
                    <div className="flex items-center gap-3">
                      <div className="h-12 w-12 rounded-full bg-white/20 flex items-center justify-center">
                        <Star className="h-6 w-6" />
                      </div>
                      <div>
                        <h2 className="text-xl font-bold">🌱 EXCITING NEW PROGRAMS COMING SOON!</h2>
                        <p className="text-white/90">Be among the first to know about Nepal's upcoming Agriculture & Climate Science programs</p>
                      </div>
                    </div>
                    <Button 
                      variant="secondary" 
                      className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                    >
                      Express Interest <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <div className="px-4 md:px-6 relative z-10">
              <div className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center max-w-6xl mx-auto">
                <div className="flex flex-col justify-center space-y-6">
                  <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-green-600/10 to-blue-600/10 text-green-700 text-sm font-medium w-fit backdrop-blur-sm border border-green-200">
                    <Globe className="h-4 w-4 mr-2" />
                    School of Agriculture and Climate Science
                  </div>
                  <div className="space-y-4">
                    <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                      Nepal: The Next 
                      <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600"> Climate Science Hub</span>
                    </h1>
                    <p className="max-w-[600px] text-muted-foreground md:text-xl leading-relaxed">
                      Discover why Nepal's unique geography—from the Himalayas to the Terai—makes it the perfect laboratory for studying climate change, sustainable agriculture, and innovative solutions for a changing world.
                    </p>
                  </div>
                  
                  {/* Nepal Stats */}
                  <div className="grid grid-cols-2 gap-4 py-4">
                    {nepalAgriStats.map((stat, index) => (
                      <div key={index} className="bg-white/80 backdrop-blur-sm p-4 rounded-xl border border-white/50 shadow-sm">
                        <div className="flex items-center gap-2 mb-1">
                          {stat.icon}
                          <span className="text-2xl font-bold text-green-600">{stat.value}</span>
                  </div>
                        <p className="text-sm text-muted-foreground">{stat.label}</p>
                </div>
                    ))}
                  </div>


                </div>
                
                <div className="flex items-center justify-center relative">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-green-600/20 via-blue-500/20 to-yellow-400/20 rounded-full blur-3xl animate-pulse"></div>
                  <div className="relative w-full max-w-[550px] rounded-2xl overflow-hidden shadow-2xl">
                    <img
                      src="/placeholder.svg?height=550&width=550"
                      width={550}
                      height={550}
                      alt="Nepal's diverse agricultural landscape from Himalayas to Terai"
                      className="aspect-square w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-green-600/30 via-transparent to-blue-600/20 mix-blend-multiply"></div>
                    <div className="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm p-4 rounded-xl">
                      <p className="text-sm font-medium text-gray-800">From 60m in Terai to 8,848m in Himalayas - Nepal offers unparalleled climate diversity</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Why Nepal for Climate Science */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <Badge variant="outline" className="mb-4 bg-blue-50 text-blue-700 border-blue-200">
                    <Mountain className="h-3 w-3 mr-1" />
                    Why Nepal?
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                    The Perfect Climate Science Laboratory
                    </h2>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    Nepal's extraordinary geographical diversity makes it an unparalleled natural laboratory for studying climate change impacts and developing innovative solutions.
                    </p>
                </div>

                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-3 mb-8">
                    <TabsTrigger value="overview" className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      Geographic Diversity
                    </TabsTrigger>
                    <TabsTrigger value="research" className="flex items-center gap-2">
                      <Lightbulb className="h-4 w-4" />
                      Research Opportunities
                    </TabsTrigger>
                    <TabsTrigger value="impact" className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Global Impact
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="space-y-8">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300">
                        <CardHeader className="text-center">
                          <div className="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-4">
                            <Mountain className="h-8 w-8 text-blue-600" />
                    </div>
                          <CardTitle>High Himalayas</CardTitle>
                          <CardDescription>3,000m - 8,848m elevation</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground mb-4">
                            Study glacial retreat, permafrost dynamics, and extreme weather patterns in the world's highest mountain range.
                          </p>
                          <div className="space-y-2">
                            <div className="flex justify-between text-xs">
                              <span>Research Potential</span>
                              <span>95%</span>
                  </div>
                            <Progress value={95} className="h-2" />
                  </div>
                        </CardContent>
                      </Card>

                      <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300">
                        <CardHeader className="text-center">
                          <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
                            <Trees className="h-8 w-8 text-green-600" />
                    </div>
                          <CardTitle>Middle Hills</CardTitle>
                          <CardDescription>1,000m - 3,000m elevation</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground mb-4">
                            Explore forest ecosystems, agricultural transitions, and monsoon impacts on biodiversity and farming.
                          </p>
                          <div className="space-y-2">
                            <div className="flex justify-between text-xs">
                              <span>Research Potential</span>
                              <span>88%</span>
                  </div>
                            <Progress value={88} className="h-2" />
                  </div>
                        </CardContent>
                      </Card>

                      <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300">
                        <CardHeader className="text-center">
                          <div className="h-16 w-16 rounded-full bg-yellow-100 flex items-center justify-center mx-auto mb-4">
                            <Sprout className="h-8 w-8 text-yellow-600" />
                    </div>
                          <CardTitle>Terai Plains</CardTitle>
                          <CardDescription>60m - 1,000m elevation</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground mb-4">
                            Investigate lowland agriculture, flood management, and tropical ecosystem responses to climate change.
                          </p>
                          <div className="space-y-2">
                            <div className="flex justify-between text-xs">
                              <span>Research Potential</span>
                              <span>92%</span>
                  </div>
                            <Progress value={92} className="h-2" />
                </div>
                        </CardContent>
                      </Card>
              </div>
                  </TabsContent>

                  <TabsContent value="research" className="space-y-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {climateOpportunities.map((opportunity, index) => (
                        <Card key={index} className="border-0 shadow-md hover:shadow-lg transition-all duration-300">
                          <CardHeader>
                            <CardTitle className="text-lg">{opportunity.title}</CardTitle>
                            <CardDescription>{opportunity.description}</CardDescription>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <div className="flex items-center gap-2 text-sm">
                              <MapPin className="h-4 w-4 text-muted-foreground" />
                              <span>{opportunity.location}</span>
                </div>
                            <div>
                              <p className="text-sm font-medium mb-2">Research Focus:</p>
                              <p className="text-sm text-muted-foreground">{opportunity.focus}</p>
                </div>
                            <div>
                              <p className="text-sm font-medium mb-2">Partnerships:</p>
                              <p className="text-sm text-muted-foreground">{opportunity.research}</p>
              </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="impact" className="space-y-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div className="space-y-6">
                        <h3 className="text-2xl font-bold">Global Climate Research Impact</h3>
                  <div className="space-y-4">
                          <div className="flex items-center justify-between p-4 bg-green-50 rounded-xl">
                            <span className="font-medium">IPCC Report Contributions</span>
                            <Badge className="bg-green-600">12 Studies</Badge>
                    </div>
                          <div className="flex items-center justify-between p-4 bg-blue-50 rounded-xl">
                            <span className="font-medium">International Collaborations</span>
                            <Badge className="bg-blue-600">25+ Universities</Badge>
                  </div>
                          <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-xl">
                            <span className="font-medium">Climate Data Points</span>
                            <Badge className="bg-yellow-600">1M+ Records</Badge>
                      </div>
                          <div className="flex items-center justify-between p-4 bg-purple-50 rounded-xl">
                            <span className="font-medium">Published Research Papers</span>
                            <Badge className="bg-purple-600">200+ Papers</Badge>
                          </div>
                  </div>
                      </div>
                      <div className="bg-gradient-to-br from-green-50 to-blue-50 p-6 rounded-2xl">
                        <h4 className="text-xl font-bold mb-4">Why Nepal Matters for Global Climate Science</h4>
                        <ul className="space-y-3 text-sm">
                          <li className="flex items-start gap-2">
                            <div className="h-5 w-5 rounded-full bg-green-600 flex items-center justify-center mt-0.5">
                              <span className="text-white text-xs">1</span>
                            </div>
                            <span><strong>Water Tower of Asia:</strong> Himalayan glaciers feed 10 major river systems supporting 1.4 billion people</span>
                    </li>
                          <li className="flex items-start gap-2">
                            <div className="h-5 w-5 rounded-full bg-blue-600 flex items-center justify-center mt-0.5">
                              <span className="text-white text-xs">2</span>
                      </div>
                            <span><strong>Climate Sensitivity:</strong> Temperature increases 2x faster than global average in mountains</span>
                    </li>
                          <li className="flex items-start gap-2">
                            <div className="h-5 w-5 rounded-full bg-yellow-600 flex items-center justify-center mt-0.5">
                              <span className="text-white text-xs">3</span>
                      </div>
                            <span><strong>Monsoon Laboratory:</strong> Critical for understanding South Asian weather patterns</span>
                    </li>
                          <li className="flex items-start gap-2">
                            <div className="h-5 w-5 rounded-full bg-purple-600 flex items-center justify-center mt-0.5">
                              <span className="text-white text-xs">4</span>
                      </div>
                            <span><strong>Biodiversity Hotspot:</strong> Rapid ecosystem changes provide early climate indicators</span>
                    </li>
                  </ul>
                  </div>
                </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </section>

          {/* Nepal Climate Zone Explorer */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-blue-50 to-green-50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <Badge variant="outline" className="mb-4 bg-blue-50 text-blue-700 border-blue-200">
                    <MapPin className="h-3 w-3 mr-1" />
                    Interactive District Map
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">
                    Nepal's Ecological Zones
                    </h2>
                  <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                    Click on any district to explore Nepal's three distinct ecological regions and their agricultural potential
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
                  {/* Interactive Map */}
                  <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-xl font-semibold mb-4 text-center">Interactive Nepal District Map</h3>
                    
                    {/* Color Legend */}
                    <div className="flex justify-center mb-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded" style={{ backgroundColor: '#008000' }}></div>
                        <span>Terai</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded" style={{ backgroundColor: '#e9afaf' }}></div>
                        <span>Hill</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded" style={{ backgroundColor: '#b9bafb' }}></div>
                        <span>Mountain</span>
                  </div>
                </div>

                    {/* SVG Map Container */}
                    <div className="flex justify-center">
                      <div 
                        className="relative cursor-pointer border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                        style={{ maxWidth: '500px', width: '100%' }}
                      >
                        {/* Main Nepal District Map */}
                        <img 
                          src="/images/agriculture/Nepal_district_colored.svg"
                          alt="Nepal District Map"
                          className="w-full h-auto"
                          onClick={(e) => {
                            // Create a temporary canvas to get pixel color at click position
                            const img = e.target as HTMLImageElement;
                            const rect = img.getBoundingClientRect();
                            const x = e.clientX - rect.left;
                            const y = e.clientY - rect.top;
                            
                            // Scale coordinates to actual image size
                            const scaleX = img.naturalWidth / img.width;
                            const scaleY = img.naturalHeight / img.height;
                            const actualX = x * scaleX;
                            const actualY = y * scaleY;
                            
                            // Load SVG to detect color at clicked position
                            fetch('/images/agriculture/Nepal_district_colored.svg')
                              .then(response => response.text())
                              .then(svgText => {
                                const parser = new DOMParser();
                                const svgDoc = parser.parseFromString(svgText, 'image/svg+xml');
                                const paths = svgDoc.querySelectorAll('path');
                                
                                // Check each path to see if the click point is inside it
                                paths.forEach((path) => {
                                  const fillColor = path.getAttribute('fill') || '';
                                  
                                  // Create temporary SVG element for hit testing
                                  const tempSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                                  tempSvg.setAttribute('width', '1300');
                                  tempSvg.setAttribute('height', '800');
                                  tempSvg.style.position = 'absolute';
                                  tempSvg.style.top = '-9999px';
                                  document.body.appendChild(tempSvg);
                                  
                                  const tempPath = path.cloneNode(true) as SVGPathElement;
                                  tempSvg.appendChild(tempPath);
                                  
                                  // Check if point is in path
                                  const point = tempSvg.createSVGPoint();
                                  point.x = actualX;
                                  point.y = actualY;
                                  
                                  if (tempPath.isPointInFill && tempPath.isPointInFill(point)) {
                                    // Map colors to ecological zones
                                    let zone = null;
                                    if (fillColor === '#008000') {
                                      zone = 'terai';
                                    } else if (fillColor === '#e9afaf') {
                                      zone = 'hill';
                                    } else if (fillColor === '#b9bafb') {
                                      zone = 'mountain';
                                    }
                                    
                                    if (zone) {
                                      setSelectedZone(zone);
                                    }
                                  }
                                  
                                  document.body.removeChild(tempSvg);
                                });
                              })
                              .catch(err => {
                                console.log('SVG loading failed, using fallback color detection');
                                // Fallback: assume regions based on rough geographic areas
                                const imgHeight = img.height;
                                const clickY = y;
                                
                                if (clickY > imgHeight * 0.7) {
                                  setSelectedZone('terai'); // Bottom region
                                } else if (clickY > imgHeight * 0.3) {
                                  setSelectedZone('hill'); // Middle region  
                                } else {
                                  setSelectedZone('mountain'); // Top region
                                }
                              });
                          }}
                        />
                        
                        {/* Clickable overlay regions for better UX */}
                        <div className="absolute inset-0 pointer-events-none">
                          {/* Visual indicators for regions */}
                          <div 
                            className="absolute top-0 left-0 w-full h-1/3 hover:bg-blue-100 hover:bg-opacity-20 transition-colors pointer-events-auto cursor-pointer"
                            onClick={() => setSelectedZone('mountain')}
                            title="Mountain Region"
                          />
                          <div 
                            className="absolute top-1/3 left-0 w-full h-1/3 hover:bg-orange-100 hover:bg-opacity-20 transition-colors pointer-events-auto cursor-pointer"
                            onClick={() => setSelectedZone('hill')}
                            title="Hill Region"
                          />
                          <div 
                            className="absolute bottom-0 left-0 w-full h-1/3 hover:bg-green-100 hover:bg-opacity-20 transition-colors pointer-events-auto cursor-pointer"
                            onClick={() => setSelectedZone('terai')}
                            title="Terai Region"
                          />
                        </div>
                      </div>
                    </div>

                    <p className="text-center text-sm text-gray-500 mt-4">
                      Click on any district to explore its ecological zone
                    </p>
                  </div>

                  {/* Information Panel */}
                  <div className="space-y-6">
                    {selectedZone ? (
                      <Card className="border-0 shadow-lg">
                        <CardHeader style={{ borderTop: `4px solid ${
                          selectedZone === 'terai' ? '#008000' : 
                          selectedZone === 'hill' ? '#e9afaf' : 
                          '#b9bafb'
                        }` }}>
                          <CardTitle className="flex items-center gap-2">
                            {selectedZone === 'terai' && <Trees className="h-5 w-5 text-green-600" />}
                            {selectedZone === 'hill' && <Mountain className="h-5 w-5 text-orange-600" />}
                            {selectedZone === 'mountain' && <Mountain className="h-5 w-5 text-blue-600" />}
                            {selectedZone === 'terai' ? 'Terai Region' : 
                             selectedZone === 'hill' ? 'Hill Region' : 
                             'Mountain Region'}
                          </CardTitle>
                          <CardDescription>
                            {selectedZone === 'terai' ? 'Low-lying plains ideal for agriculture' : 
                             selectedZone === 'hill' ? 'Middle hills with diverse farming systems' : 
                             'High-altitude regions with unique crops'}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <Tabs defaultValue="overview" className="w-full">
                            <TabsList className="grid w-full grid-cols-4">
                              <TabsTrigger value="overview">Overview</TabsTrigger>
                              <TabsTrigger value="agriculture">Agriculture</TabsTrigger>
                              <TabsTrigger value="climate">Climate</TabsTrigger>
                              <TabsTrigger value="research">Research</TabsTrigger>
                            </TabsList>
                            
                            <TabsContent value="overview" className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div className="text-center p-3 bg-gray-50 rounded-lg">
                                  <div className="text-lg font-semibold text-gray-900">
                                    {selectedZone === 'terai' ? '60-200m' : 
                                     selectedZone === 'hill' ? '200-3,000m' : 
                                     '3,000m+'}
                                  </div>
                                  <div className="text-sm text-gray-600">Elevation</div>
                                </div>
                                <div className="text-center p-3 bg-gray-50 rounded-lg">
                                  <div className="text-lg font-semibold text-gray-900">
                                    {selectedZone === 'terai' ? '17%' : 
                                     selectedZone === 'hill' ? '68%' : 
                                     '15%'}
                                  </div>
                                  <div className="text-sm text-gray-600">Land Area</div>
                                </div>
                              </div>
                              
                              <p className="text-gray-700">
                                {selectedZone === 'terai' ? 
                                  'The Terai is Nepal\'s most fertile region, containing rich alluvial soil deposited by rivers. This region produces most of Nepal\'s food grains and is known for its subtropical climate and flat terrain.' :
                                 selectedZone === 'hill' ? 
                                  'The Hill region is characterized by moderate climate and diverse topography. It\'s home to most of Nepal\'s population and features terraced agriculture, making it ideal for mixed farming systems.' :
                                  'The Mountain region features high-altitude agriculture with unique crops adapted to harsh conditions. This region is crucial for climate research and sustainable mountain agriculture practices.'
                                }
                              </p>
                            </TabsContent>

                            <TabsContent value="agriculture" className="space-y-4">
                              <div className="space-y-3">
                                <h4 className="font-semibold">Major Crops</h4>
                                <div className="flex flex-wrap gap-2">
                                  {selectedZone === 'terai' ? 
                                    ['Rice', 'Wheat', 'Maize', 'Sugarcane', 'Jute'].map(crop => (
                                      <Badge key={crop} variant="secondary">{crop}</Badge>
                                    )) :
                                   selectedZone === 'hill' ?
                                    ['Rice', 'Maize', 'Millet', 'Barley', 'Potato'].map(crop => (
                                      <Badge key={crop} variant="secondary">{crop}</Badge>
                                    )) :
                                    ['Barley', 'Potato', 'Apple', 'Yak products', 'Medicinal herbs'].map(crop => (
                                      <Badge key={crop} variant="secondary">{crop}</Badge>
                                    ))
                                  }
                                </div>

                                <h4 className="font-semibold mt-4">Agricultural Challenges</h4>
                                <ul className="list-disc list-inside text-sm space-y-1 text-gray-700">
                                  {(selectedZone === 'terai' ? [
                                    'Flooding during monsoon',
                                    'Pest management',
                                    'Water management',
                                    'Climate change impacts'
                                  ] : selectedZone === 'hill' ? [
                                    'Soil erosion on slopes',
                                    'Limited mechanization',
                                    'Water scarcity',
                                    'Market access'
                                  ] : [
                                    'Extreme weather conditions',
                                    'Short growing seasons',
                                    'Limited transportation',
                                    'High altitude stress'
                                  ]).map((challenge, idx) => (
                                    <li key={idx}>{challenge}</li>
                                  ))}
                                </ul>
                              </div>
                            </TabsContent>

                            <TabsContent value="climate" className="space-y-4">
                              <div className="grid grid-cols-1 gap-4">
                                <div className="p-3 bg-gray-50 rounded-lg">
                                  <div className="flex items-center gap-2 mb-2">
                                    <Thermometer className="h-4 w-4 text-red-500" />
                                    <span className="font-medium">Temperature</span>
                                  </div>
                                  <div className="text-sm text-gray-700">
                                    {selectedZone === 'terai' ? '20-30°C year-round' : 
                                     selectedZone === 'hill' ? '10-25°C seasonal variation' : 
                                     '-10 to 15°C extreme variation'}
                                  </div>
                                </div>

                                <div className="p-3 bg-gray-50 rounded-lg">
                                  <div className="flex items-center gap-2 mb-2">
                                    <CloudRain className="h-4 w-4 text-blue-500" />
                                    <span className="font-medium">Precipitation</span>
                                  </div>
                                  <div className="text-sm text-gray-700">
                                    {selectedZone === 'terai' ? '1,500-2,500mm annually' : 
                                     selectedZone === 'hill' ? '1,200-2,200mm annually' : 
                                     '500-1,000mm annually'}
                                  </div>
                                </div>

                                <div className="p-3 bg-gray-50 rounded-lg">
                                  <div className="flex items-center gap-2 mb-2">
                                    <Wind className="h-4 w-4 text-gray-500" />
                                    <span className="font-medium">Climate Type</span>
                                  </div>
                                  <div className="text-sm text-gray-700">
                                    {selectedZone === 'terai' ? 'Tropical to subtropical' : 
                                     selectedZone === 'hill' ? 'Temperate' : 
                                     'Alpine to arctic'}
                                  </div>
                                </div>
                              </div>
                            </TabsContent>

                            <TabsContent value="research" className="space-y-4">
                              <div className="space-y-3">
                                <h4 className="font-semibold flex items-center gap-2">
                                  <FlaskConical className="h-4 w-4" />
                                  Research Opportunities
                                </h4>
                                <ul className="list-disc list-inside text-sm space-y-1 text-gray-700">
                                  {(selectedZone === 'terai' ? [
                                    'Sustainable rice production systems',
                                    'Climate-smart agriculture',
                                    'Integrated pest management',
                                    'Precision farming technologies'
                                  ] : selectedZone === 'hill' ? [
                                    'Terraced agriculture optimization',
                                    'Agroforestry systems',
                                    'Soil conservation techniques',
                                    'Mixed farming systems'
                                  ] : [
                                    'High-altitude crop adaptation',
                                    'Climate change impacts',
                                    'Alpine ecosystem conservation',
                                    'Traditional knowledge systems'
                                  ]).map((research, idx) => (
                                    <li key={idx}>{research}</li>
                                  ))}
                                </ul>

                                <h4 className="font-semibold flex items-center gap-2 mt-4">
                                  <GraduationCap className="h-4 w-4" />
                                  Study Programs
                                </h4>
                                <div className="grid grid-cols-1 gap-2">
                                  {['Field Research Stations', 'Climate Monitoring', 'Crop Trials', 'Community Partnerships'].map((program, idx) => (
                                    <div key={idx} className="p-2 bg-blue-50 rounded border text-sm">
                                      {program}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </TabsContent>
                          </Tabs>
                        </CardContent>
                      </Card>
                    ) : (
                      <Card className="border-0 shadow-lg">
                        <CardContent className="p-8 text-center">
                          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">
                            Select a District
                          </h3>
                          <p className="text-gray-600">
                            Click on any district in the map to explore detailed information about its ecological zone, climate, agriculture, and research opportunities.
                          </p>
                        </CardContent>
                      </Card>
                    )}

                    {/* Quick Zone Summary Cards */}
                    <div className="grid grid-cols-1 gap-4">
                      {[
                        { 
                          id: 'terai', 
                          name: 'Terai Region', 
                          color: '#008000', 
                          districts: '20 Districts',
                          icon: Trees 
                        },
                        { 
                          id: 'hill', 
                          name: 'Hill Region', 
                          color: '#e9afaf', 
                          districts: '41 Districts',
                          icon: Mountain 
                        },
                        { 
                          id: 'mountain', 
                          name: 'Mountain Region', 
                          color: '#b9bafb', 
                          districts: '16 Districts',
                          icon: Mountain 
                        }
                      ].map((zone) => {
                        const Icon = zone.icon;
                        return (
                          <Card 
                            key={zone.id}
                            className={`cursor-pointer border-0 shadow-md hover:shadow-lg transition-all duration-300 ${
                              selectedZone === zone.id ? 'ring-2 ring-offset-2' : ''
                            }`}
                            style={{ 
                              borderTop: `4px solid ${zone.color}`,
                              '--ring-color': selectedZone === zone.id ? zone.color : 'transparent'
                            } as React.CSSProperties}
                            onClick={() => setSelectedZone(zone.id)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <Icon className="h-5 w-5" style={{ color: zone.color }} />
                                  <div>
                                    <div className="font-medium">{zone.name}</div>
                                    <div className="text-sm text-gray-600">{zone.districts}</div>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <ChevronRight className="h-4 w-4 text-gray-400" />
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Nepal's Agricultural Innovations */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-green-50 to-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <Badge variant="outline" className="mb-4 bg-green-50 text-green-700 border-green-200">
                    <Lightbulb className="h-3 w-3 mr-1" />
                    Innovation in Action
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                    Agricultural Innovations Transforming Nepal
                    </h2>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    See how our students and faculty are developing cutting-edge solutions for Nepal's unique agricultural challenges.
                    </p>
                  </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {nepalInnovations.map((innovation, index) => (
                    <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
                      <CardHeader className="bg-gradient-to-r from-green-50 to-blue-50 group-hover:from-green-100 group-hover:to-blue-100 transition-all duration-300">
                        <div className="flex items-start gap-4">
                          <div className="h-16 w-16 rounded-xl bg-white shadow-md flex items-center justify-center">
                            {innovation.icon}
                          </div>
                          <div className="flex-1">
                            <CardTitle className="text-xl">{innovation.title}</CardTitle>
                            <CardDescription className="text-base">{innovation.description}</CardDescription>
                          </div>
                        </div>
                        </CardHeader>
                      <CardContent className="p-6">
                  <div className="space-y-4">
                          <div className="bg-green-50 p-4 rounded-xl border border-green-200">
                            <p className="text-sm font-medium text-green-800 mb-1">Impact Achieved:</p>
                            <p className="text-lg font-bold text-green-600">{innovation.impact}</p>
                          </div>
                          <p className="text-muted-foreground">{innovation.details}</p>
                          <Button variant="outline" className="w-full group-hover:bg-green-50 group-hover:border-green-300 transition-all duration-300">
                            Learn More About This Innovation
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Button>
                        </div>
                        </CardContent>
                      </Card>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Interactive Program Explorer */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <Badge variant="outline" className="mb-4 bg-blue-50 text-blue-700 border-blue-200">
                    <Calendar className="h-3 w-3 mr-1" />
                    Programs in Development
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                    Choose Your Future Path to Impact
                    </h2>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    Our planned comprehensive programs will prepare students to address Nepal's agricultural and climate challenges while contributing to global sustainability.
                    </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {programHighlights.map((program, index) => (
                    <Card key={index} className="border-0 shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden">
                      <CardHeader className="bg-gradient-to-r from-green-600 to-blue-600 text-white">
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-2xl">{program.title}</CardTitle>
                            <CardDescription className="text-green-100">{program.status}</CardDescription>
                          </div>
                          <Badge className="bg-white/20 text-white hover:bg-white/30">
                            {program.duration}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="p-6 space-y-6">
                        <div>
                          <h4 className="font-semibold mb-3">Specializations:</h4>
                          <div className="flex flex-wrap gap-2">
                            {program.specializations.map((spec, idx) => (
                              <Badge key={idx} variant="outline" className="bg-green-50 border-green-200 text-green-700">
                                {spec}
                              </Badge>
                            ))}
                  </div>
                </div>

                        <div>
                          <h4 className="font-semibold mb-3">Program Highlights:</h4>
                          <ul className="space-y-2">
                            {program.highlights.map((highlight, idx) => (
                              <li key={idx} className="flex items-start gap-2 text-sm">
                                <div className="h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mt-0.5">
                                  <div className="h-2 w-2 rounded-full bg-green-600"></div>
                    </div>
                                <span>{highlight}</span>
                              </li>
                            ))}
                          </ul>
                    </div>


                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Success Stories Carousel */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-green-50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <Badge variant="outline" className="mb-4 bg-yellow-50 text-yellow-700 border-yellow-200">
                    <Award className="h-3 w-3 mr-1" />
                    Success Stories
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                    Students Making Real Impact
                    </h2>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    See how our students are already creating positive change in Nepal's agricultural and climate sectors.
                    </p>
                </div>

                <Carousel className="w-full max-w-5xl mx-auto">
                  <CarouselContent>
                    {successStories.map((story, index) => (
                      <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3">
                        <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden h-full">
                      <div className="h-48 overflow-hidden">
                        <img 
                              src={story.image} 
                              alt={story.title}
                              className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                          <CardContent className="p-6 space-y-4">
                            <h3 className="text-xl font-bold">{story.title}</h3>
                            <p className="text-muted-foreground text-sm">{story.description}</p>
                            <div className="flex justify-between items-center pt-2">
                              <div className="text-sm">
                                <span className="font-medium text-green-600">{story.impact}</span>
                      </div>
                              <div className="text-sm text-muted-foreground">
                                {story.students}
                    </div>
                    </div>
                          </CardContent>
                        </Card>
                      </CarouselItem>
                  ))}
                  </CarouselContent>
                  <CarouselPrevious />
                  <CarouselNext />
                </Carousel>
              </div>
            </div>
          </section>

          {/* Research Centers */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <Badge variant="outline" className="mb-4 bg-blue-50 text-blue-700 border-blue-200">
                    <BookOpen className="h-3 w-3 mr-1" />
                    Research Infrastructure
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                    World-Class Research Centers
                    </h2>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    Our dedicated research facilities provide students with hands-on experience in cutting-edge agricultural and climate science research.
                    </p>
                </div>

                <Accordion type="single" collapsible className="w-full space-y-4">
                  {researchCenters.map((center, index) => (
                    <AccordionItem key={index} value={`center-${index}`} className="border-0 bg-light rounded-xl shadow-sm hover:shadow-md transition-all duration-300">
                      <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 text-left">
                          <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-green-600 to-blue-600 flex items-center justify-center">
                            <BookOpen className="h-6 w-6 text-white" />
                      </div>
                          <div>
                            <h3 className="text-xl font-bold">{center.name}</h3>
                            <p className="text-muted-foreground text-sm">{center.focus}</p>
                    </div>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="px-6 pb-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                          <div>
                            <h4 className="font-semibold mb-3 text-green-600">Facilities:</h4>
                            <ul className="space-y-2">
                              {center.facilities.map((facility, idx) => (
                                <li key={idx} className="flex items-center gap-2 text-sm">
                                  <div className="h-2 w-2 rounded-full bg-green-600"></div>
                                  <span>{facility}</span>
                                </li>
                              ))}
                            </ul>
                </div>
                          <div>
                            <h4 className="font-semibold mb-3 text-blue-600">Key Partnerships:</h4>
                            <ul className="space-y-2">
                              {center.partnerships.map((partnership, idx) => (
                                <li key={idx} className="flex items-center gap-2 text-sm">
                                  <div className="h-2 w-2 rounded-full bg-blue-600"></div>
                                  <span>{partnership}</span>
                                </li>
                              ))}
                            </ul>
                      </div>
                    </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            </div>
          </section>

          {/* Call to Action */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-br from-green-600 via-blue-600 to-green-700 relative overflow-hidden">
            <div className="absolute inset-0 bg-[url('/placeholder.svg?height=600&width=1200')] opacity-10 bg-cover bg-center"></div>
            <div className="px-4 md:px-6 relative z-10">
              <div className="max-w-4xl mx-auto text-center text-white">
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                  Be Part of Nepal's Agricultural Revolution
                </h2>
                <p className="text-xl md:text-2xl mb-8 text-green-100">
                  Join the next generation of leaders addressing food security, climate change, and sustainable development in the Himalayas and beyond.
                    </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20">
                    <div className="text-3xl font-bold mb-2">₹12L+</div>
                    <div className="text-green-100">Average Starting Salary</div>
                    </div>
                  <div className="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20">
                    <div className="text-3xl font-bold mb-2">95%</div>
                    <div className="text-green-100">Job Placement Rate</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20">
                    <div className="text-3xl font-bold mb-2">50+</div>
                    <div className="text-green-100">Industry Partners</div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" variant="outline" className="border-white/30 text-white hover:bg-white/10 bg-white/5 backdrop-blur-sm">
                    Contact Us
                  </Button>
                </div>

                <div className="mt-8 text-green-100">
                  <p className="text-sm">
                    Be the first to know when applications open and receive program updates
                  </p>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  );
} 