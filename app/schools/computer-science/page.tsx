'use client'

import Link from "next/link"
import { ArrowRight, Lock, Code, ChevronRight, Database, Server, Network, Cpu } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useParallaxEffect } from "@/components/parallax-effect"
import { PageTransition } from "@/components/ui/page-transition"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LazyImage } from "@/components/ui/lazy-image"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"

import { SkipLink } from "@/components/ui/skip-link"
import CybersecurityTimeline from "@/components/education/CybersecurityTimeline"

// Data for Computer Science Programs
const csFeaturedProgram = {
  name: "B.Tech in Cybersecurity",
  slug: "btech-cybersecurity",
  title: "B.Tech in Cybersecurity",
  details: "The B.Tech in Cybersecurity at Ullens College is designed to cultivate technically proficient professionals who are ready to tackle the evolving challenges of the digital world. Our program stands out by offering a robust foundation in computer science, ensuring that graduates are not only experts in cybersecurity but also possess the versatility to excel across the broader spectrum of computing disciplines.",
  extendedDetails: "At the heart of our curriculum is a deep commitment to technical excellence. Students gain hands-on experience with the latest tools, technologies, and methodologies in cybersecurity, preparing them to defend against real-world threats and secure critical digital infrastructure. This technical rigor is underpinned by a comprehensive computer science backbone, covering essential areas such as programming, algorithms, data structures, networks, and systems.",
  careerFlexibility: "This strong foundation empowers our graduates with the flexibility to pursue diverse career paths. While the program specializes in cybersecurity, the breadth and depth of computer science knowledge open doors to fields such as artificial intelligence, data science, software engineering, cloud computing, and beyond. Our alumni are equipped not just to respond to today's security challenges, but to innovate and lead in any area of computer science they choose.",
  communityMessage: "By joining our B.Tech in Cybersecurity, students become part of a forward-thinking community that values technical mastery, adaptability, and lifelong learning—qualities essential for success in the rapidly changing landscape of technology.",
  points: [
    { text: "Technical Excellence with Computer Science Foundation", icon: <Code className="h-4 w-4 text-teal-400" /> },
    { text: "Hands-on Experience with Latest Security Tools", icon: <Lock className="h-4 w-4 text-red-400" /> },
    { text: "Career Flexibility Across Computing Disciplines", icon: <ArrowRight className="h-4 w-4 text-violet-400" /> },
    { text: "Real-world Threat Defense & Infrastructure Security", icon: <Server className="h-4 w-4 text-emerald-400" /> },
  ],
};

const csProgramCards = [
  {
    name: "Computer Science",
    slug: "btech-cs",
    description: "Core fundamentals and advanced topics",
    details: "Our Computer Science program covers algorithms, data structures, software engineering, and more. Students develop strong problem-solving skills applicable across industries.",
    upcoming: true,
  },
  {
    name: "Artificial Intelligence",
    slug: "btech-ai-ml",
    description: "Machine learning and AI applications",
    details: "Study machine learning, neural networks, and AI applications. Work on real-world projects and prepare for careers in this rapidly growing field.",
    upcoming: true,
  },
];



const facultyHighlights = [
  {
    name: "Dr. Maya Patel",
    role: "Department Chair, Cybersecurity",
    image: "/placeholder.svg?height=200&width=200",
    details: "With over 15 years of industry experience at leading tech companies, Dr. Patel brings practical knowledge and cutting-edge research to our cybersecurity program.",
  },
  {
    name: "Dr. Arun Sharma",
    role: "Professor, Artificial Intelligence",
    image: "/placeholder.svg?height=200&width=200",
    details: "An award-winning researcher in deep learning and computer vision with publications in top AI conferences and collaborations with tech giants.",
  },
  {
    name: "Dr. Samuel Chen",
    role: "Associate Professor, Data Science",
    image: "/placeholder.svg?height=200&width=200", 
    details: "Specializing in big data analytics and applied machine learning with a focus on solving real-world problems through data-driven approaches.",
  },
];

const labFacilities = [
  {
    name: "Cybersecurity Lab",
    description: "State-of-the-art facility for network security, penetration testing, and digital forensics",
    icon: <Lock className="h-6 w-6 text-red-400" />,
  },
  {
    name: "AI & ML Lab",
    description: "Equipped with high-performance GPUs for deep learning research and applications",
    icon: <Cpu className="h-6 w-6 text-teal-400" />,
  },
  {
    name: "Data Science Center",
    description: "Specialized tools and infrastructure for big data processing and visualization",
    icon: <Database className="h-6 w-6 text-violet-400" />,
  },
];

export default function ComputerScienceSchoolPage() {
  useParallaxEffect();

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section */}
          <section className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-br from-stone-900 via-red-900/80 to-red-800 relative overflow-hidden">
            {/* Animated Code Background */}
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute top-10 left-10 text-emerald-400/15 font-mono text-xs animate-pulse">
                <pre>{`function solve(problem) {
  return innovation();
}`}</pre>
              </div>
              <div className="absolute top-32 right-20 text-sky-400/15 font-mono text-xs animate-pulse" style={{animationDelay: '1s'}}>
                <pre>{`class Future {
  constructor() {
    this.possibilities = ∞;
  }
}`}</pre>
              </div>
              <div className="absolute bottom-20 left-1/4 text-violet-400/15 font-mono text-xs animate-pulse" style={{animationDelay: '2s'}}>
                <pre>{`while(learning) {
  skills++;
  opportunities.expand();
}`}</pre>
              </div>
              {/* Tech Grid Pattern */}
              <div className="absolute inset-0 bg-grid-pattern opacity-3"></div>
            </div>
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center">
                <div className="flex flex-col justify-center space-y-6">
                  <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-red-600/15 to-red-700/15 backdrop-blur-sm border border-red-400/20 text-red-200 text-sm font-medium w-fit font-mono">
                    <Code className="h-4 w-4 mr-2" />
                    School.of.ComputerScience();
                  </div>
                  <div className="space-y-6">
                    <h1 className="heading-xl text-white">
                      <span className="bg-gradient-to-r from-red-300 via-red-400 to-rose-300 bg-clip-text text-transparent">
                        Debugging Reality,
                      </span>
                      <br />
                      <span className="text-white">
                        Coding the <span className="text-red-300 font-mono">Future</span>
                      </span>
                    </h1>
                    <div className="space-y-4">
                      <p className="max-w-[600px] text-stone-200 md:text-xl leading-relaxed">
                        Where algorithms meet innovation, and every line of code shapes tomorrow's digital landscape.
                      </p>
                      <div className="bg-stone-800/40 backdrop-blur-sm rounded-lg p-4 border border-stone-600/30">
                        <p className="text-red-200 font-mono text-sm">
                          <span className="text-emerald-300">{'>'}</span> npm install future-career<br />
                          <span className="text-emerald-300">{'>'}</span> Initializing your tech journey...<br />
                          <span className="text-amber-300">✓</span> <span className="text-stone-300">Passion for technology detected</span><br />
                          <span className="text-amber-300">✓</span> <span className="text-stone-300">Innovation mindset loaded</span><br />
                          <span className="text-emerald-300">✓</span> <span className="text-stone-300">Ready to change the world!</span>
                        </p>
                      </div>
                    </div>
                  </div>

                </div>
                <div className="flex items-center justify-center relative">
                  {/* Holographic Effect Background */}
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-red-500/10 via-teal-500/10 to-violet-500/10 rounded-full blur-3xl animate-pulse"></div>
                  
                  {/* Interactive Code Editor Mockup */}
                  <div className="relative w-full max-w-[550px] bg-stone-900 rounded-2xl overflow-hidden shadow-2xl shadow-red-500/10 border border-stone-600">
                    {/* Editor Header */}
                    <div className="bg-stone-800 p-3 border-b border-stone-600 flex items-center gap-2">
                      <div className="flex gap-2">
                        <div className="w-3 h-3 rounded-full bg-red-400"></div>
                        <div className="w-3 h-3 rounded-full bg-amber-400"></div>
                        <div className="w-3 h-3 rounded-full bg-emerald-400"></div>
                      </div>
                      <div className="text-stone-400 text-xs font-mono ml-4">future_builder.cpp</div>
                    </div>
                    
                    {/* Code Content */}
                    <div className="p-6 font-mono text-sm">
                      <div className="space-y-2">
                        <div className="text-violet-300">#include {'<'}iostream{'>'}</div>
                        <div className="text-violet-300">#include {'<'}future{'>'}</div>
                        <div className="text-stone-400">// Building tomorrow's tech leaders</div>
                        <div className="mt-4">
                          <span className="text-sky-300">class</span> <span className="text-emerald-300">Student</span> <span className="text-stone-200">{'{'}</span>
                        </div>
                        <div className="ml-4 text-stone-200">
                          <span className="text-sky-300">public:</span>
                        </div>
                        <div className="ml-8 space-y-1">
                          <div className="text-stone-200">
                            <span className="text-sky-300">void</span> <span className="text-amber-300">learn</span>() {'{'} 
                            <span className="text-teal-300 animate-pulse cursor-after">|</span>
                          </div>
                          <div className="ml-4 text-emerald-300">knowledge++;</div>
                          <div className="ml-4 text-emerald-300">skills.expand();</div>
                          <div className="ml-4 text-emerald-300">future.unlock();</div>
                          <div className="text-stone-200">{'}'}</div>
                        </div>
                        <div className="text-stone-200">{'}'};</div>
                        
                        <div className="mt-4 text-sky-300">
                          <span className="text-sky-300">int</span> <span className="text-amber-300">main</span>() {'{'} 
                        </div>
                        <div className="ml-4 space-y-1">
                          <div className="text-stone-200">Student you;</div>
                          <div className="text-stone-200">you.learn();</div>
                          <div className="text-emerald-300">
                            <span className="text-sky-300">return</span> SUCCESS;
                          </div>
                        </div>
                        <div className="text-stone-200">{'}'}</div>
                      </div>
                    </div>
                    
                    {/* Terminal Output */}
                    <div className="bg-stone-950 p-4 border-t border-stone-600">
                      <div className="text-emerald-300 font-mono text-xs">
                        <div>$ ./future_builder</div>
                        <div className="text-teal-300">Compiling dreams into reality...</div>
                        <div className="text-emerald-300">✓ Success! Welcome to Computer Science.</div>
                        <div className="text-amber-300 animate-pulse">Ready for input...</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Department Overview */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-stone-900 via-stone-800 to-stone-900 relative overflow-hidden">
            {/* Tech Grid Background */}
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-teal-500/8 to-red-500/8 rounded-full blur-3xl"></div>
            {/* Floating Code Elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute top-16 left-16 text-emerald-400/10 font-mono text-xs animate-pulse">
                <pre>{`const education = {
  quality: "excellence",
  innovation: true
};`}</pre>
              </div>
              <div className="absolute bottom-20 right-20 text-red-400/10 font-mono text-xs animate-pulse" style={{animationDelay: '2s'}}>
                <pre>{`function buildFuture() {
  return skills.compile();
}`}</pre>
              </div>
            </div>
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-6 text-center mb-16">
                  <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-teal-600/15 to-red-600/15 backdrop-blur-sm border border-teal-400/20 text-teal-200 text-sm font-medium font-mono">
                    <Database className="h-4 w-4 mr-2" />
                    System.Initialize("DepartmentOverview");
                  </div>
                  <div className="space-y-6">
                    <h2 className="heading-lg bg-gradient-to-r from-red-300 via-red-400 to-rose-300 bg-clip-text text-transparent">
                      Excellence in Computing Education and Research
                    </h2>
                    <p className="text-stone-200 text-lg leading-relaxed max-w-4xl mx-auto">
                      Our School of Computer Science is committed to providing a comprehensive education that balances 
                      <span className="font-mono bg-red-800/20 px-2 py-1 rounded text-red-200 mx-1">theoretical foundations</span> 
                      with 
                      <span className="font-mono bg-teal-800/20 px-2 py-1 rounded text-teal-200 mx-1">practical skills</span>, 
                      preparing students for successful careers in the rapidly evolving tech industry.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {/* Algorithm Card */}
                  <div className="group bg-stone-800/40 backdrop-blur-sm border border-red-500/20 hover:border-red-400/30 p-8 rounded-2xl shadow-xl hover:shadow-2xl hover:shadow-red-500/10 transition-all duration-300 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-red-400/10 to-rose-400/10 rounded-full blur-xl"></div>
                    <div className="relative">
                      <div className="h-16 w-16 rounded-xl bg-gradient-to-br from-red-600 to-red-700 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg shadow-red-500/15">
                        <Code className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-xl font-bold mb-3 text-white">Innovative Curriculum</h3>
                      <p className="text-stone-200 mb-4">
                        Our curriculum combines core computer science principles with emerging technologies and industry best practices.
                      </p>
                      <div className="font-mono text-xs text-red-200 bg-stone-900/30 rounded p-2 border border-red-500/15">
                        <span className="text-stone-400">// Core subjects</span><br />
                        algorithms.optimize();<br />
                        dataStructures.master();<br />
                        futureSkills.unlock();
                      </div>
                    </div>
                  </div>
                  
                  {/* Infrastructure Card */}
                  <div className="group bg-stone-800/40 backdrop-blur-sm border border-teal-500/20 hover:border-teal-400/30 p-8 rounded-2xl shadow-xl hover:shadow-2xl hover:shadow-teal-500/10 transition-all duration-300 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-teal-400/10 to-cyan-400/10 rounded-full blur-xl"></div>
                    <div className="relative">
                      <div className="h-16 w-16 rounded-xl bg-gradient-to-br from-teal-600 to-teal-700 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg shadow-teal-500/15">
                        <Server className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-xl font-bold mb-3 text-white">State-of-the-Art Labs</h3>
                      <p className="text-stone-200 mb-4">
                        Modern labs equipped with the latest hardware and software tools for hands-on learning and research.
                      </p>
                      <div className="font-mono text-xs text-teal-200 bg-stone-900/30 rounded p-2 border border-teal-500/15">
                        <span className="text-stone-400">// Infrastructure specs</span><br />
                        GPUs: "RTX 4090 x 10"<br />
                        RAM: "128GB per node"<br />
                        Network: "10Gbps";
                      </div>
                    </div>
                  </div>

                  {/* Network Card */}
                  <div className="group bg-stone-800/40 backdrop-blur-sm border border-violet-500/20 hover:border-violet-400/30 p-8 rounded-2xl shadow-xl hover:shadow-2xl hover:shadow-violet-500/10 transition-all duration-300 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-violet-400/10 to-purple-400/10 rounded-full blur-xl"></div>
                    <div className="relative">
                      <div className="h-16 w-16 rounded-xl bg-gradient-to-br from-violet-600 to-violet-700 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg shadow-violet-500/15">
                        <Network className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-xl font-bold mb-3 text-white">Industry Network</h3>
                      <p className="text-stone-200 mb-4">
                        Strong partnerships with leading tech companies for internships, projects, and employment opportunities.
                      </p>
                      <div className="font-mono text-xs text-violet-200 bg-stone-900/30 rounded p-2 border border-violet-500/15">
                        <span className="text-stone-400">// Partnership array</span><br />
                        companies.include([<br />
                        &nbsp;&nbsp;"Google", "Meta", "AWS"<br />
                        ]);
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Featured Program Section */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-br from-stone-900 via-teal-900/60 to-slate-900 relative overflow-hidden">
            {/* Cybersecurity Code Background */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute top-10 left-10 text-emerald-400/15 font-mono text-xs animate-pulse">
                <pre>{`class CyberDefender {
  protect(data) {
    return encrypt(data);
  }
}`}</pre>
              </div>
              <div className="absolute bottom-20 right-10 text-teal-400/15 font-mono text-xs animate-pulse" style={{animationDelay: '1.5s'}}>
                <pre>{`firewall.enable();
threat.detect();
security.maximize();`}</pre>
              </div>
              <div className="absolute top-1/2 left-1/4 text-violet-400/15 font-mono text-xs animate-pulse" style={{animationDelay: '0.8s'}}>
                <pre>{`if (threat.detected) {
  response.immediate();
}`}</pre>
              </div>
            </div>
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="grid items-center gap-10 lg:grid-cols-2 lg:gap-16 max-w-6xl mx-auto">
                <div className="flex items-center justify-center relative order-2 lg:order-1">
                  {/* Holographic Security Dashboard */}
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-red-500/8 via-teal-500/8 to-violet-500/8 rounded-full blur-3xl animate-pulse"></div>
                  
                  {/* Security Dashboard Mockup */}
                  <div className="relative w-full max-w-[450px] bg-stone-900 rounded-2xl overflow-hidden shadow-2xl shadow-teal-500/10 border border-stone-600">
                    {/* Dashboard Header */}
                    <div className="bg-stone-800 p-3 border-b border-stone-600 flex items-center gap-3">
                      <div className="flex gap-2">
                        <div className="w-3 h-3 rounded-full bg-red-400 animate-pulse"></div>
                        <div className="w-3 h-3 rounded-full bg-amber-400"></div>
                        <div className="w-3 h-3 rounded-full bg-emerald-400"></div>
                      </div>
                      <div className="text-stone-400 text-xs font-mono ml-2">CyberDefense Dashboard v2.1</div>
                    </div>
                    
                    {/* Security Stats */}
                    <div className="p-6 space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-red-800/20 border border-red-600/30 rounded-lg p-3">
                          <div className="text-red-300 text-xs font-mono">THREATS BLOCKED</div>
                          <div className="text-red-200 text-xl font-bold">1,247</div>
                        </div>
                        <div className="bg-emerald-800/20 border border-emerald-600/30 rounded-lg p-3">
                          <div className="text-emerald-300 text-xs font-mono">SYSTEMS SECURE</div>
                          <div className="text-emerald-200 text-xl font-bold">100%</div>
                        </div>
                      </div>
                      
                      {/* Live Terminal */}
                      <div className="bg-stone-950 rounded p-3 border border-stone-600">
                        <div className="text-emerald-300 font-mono text-xs">
                          <div className="text-stone-400">$ ./security_monitor</div>
                          <div className="text-teal-300">[INFO] Scanning network interfaces...</div>
                          <div className="text-emerald-300">✓ All systems operational</div>
                          <div className="text-amber-300 animate-pulse">⚡ Real-time protection active</div>
                        </div>
                      </div>
                      
                      {/* Security Metrics */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-xs text-stone-300">
                          <span>Firewall Status</span>
                          <span className="text-emerald-300">ACTIVE</span>
                        </div>
                        <div className="w-full bg-stone-700 rounded-full h-1">
                          <div className="bg-emerald-500 h-1 rounded-full w-full"></div>
                        </div>
                        
                        <div className="flex justify-between text-xs text-stone-300 mt-3">
                          <span>Intrusion Detection</span>
                          <span className="text-teal-300">MONITORING</span>
                        </div>
                        <div className="w-full bg-stone-700 rounded-full h-1">
                          <div className="bg-teal-500 h-1 rounded-full w-5/6"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col justify-center space-y-6 order-1 lg:order-2">
                  <div className="flex items-center gap-2">
                    <div className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-teal-600/15 to-red-600/15 backdrop-blur-sm border border-teal-400/20 text-teal-200 text-sm font-medium font-mono">
                      <Lock className="h-4 w-4 mr-2" />
                      Featured Program
                    </div>
                    <Badge className="bg-red-600/20 text-red-200 border-red-400/30">
                      Available Now
                    </Badge>
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg bg-gradient-to-r from-red-300 via-teal-300 to-violet-300 bg-clip-text text-transparent">
                      {csFeaturedProgram.title}
                    </h2>
                    <p className="text-stone-200 text-lg leading-relaxed">
                      {csFeaturedProgram.details}
                    </p>
                    <p className="text-stone-300 leading-relaxed">
                      {csFeaturedProgram.extendedDetails}
                    </p>
                  </div>

                  {/* Technical Excellence Section */}
                  <div className="bg-stone-800/30 backdrop-blur-sm rounded-xl p-6 border border-teal-500/20">
                    <h4 className="font-semibold text-teal-200 mb-3 flex items-center gap-2">
                      <Code className="h-5 w-5 text-teal-300" />
                      Technical Excellence & Innovation
                    </h4>
                    <p className="text-stone-200 leading-relaxed mb-4">
                      {csFeaturedProgram.extendedDetails}
                    </p>
                    <div className="bg-stone-900/50 rounded p-3 font-mono text-xs">
                      <div className="text-teal-300">
                        <span className="text-stone-400">// Core technical stack</span><br />
                        <span className="text-sky-300">technologies:</span> [<br />
                        &nbsp;&nbsp;<span className="text-emerald-300">"Cryptography"</span>, <span className="text-emerald-300">"Network Security"</span>,<br />
                        &nbsp;&nbsp;<span className="text-emerald-300">"Ethical Hacking"</span>, <span className="text-emerald-300">"Digital Forensics"</span><br />
                        ]
                      </div>
                    </div>
                  </div>

                  {/* Career Flexibility Section */}
                  <div className="bg-stone-800/30 backdrop-blur-sm rounded-xl p-6 border border-emerald-500/20">
                    <h4 className="font-semibold text-emerald-200 mb-3 flex items-center gap-2">
                      <ArrowRight className="h-5 w-5 text-emerald-300" />
                      Limitless Career Opportunities
                    </h4>
                    <p className="text-stone-200 leading-relaxed mb-4">
                      {csFeaturedProgram.careerFlexibility}
                    </p>
                    <div className="bg-stone-900/50 rounded p-3 font-mono text-xs">
                      <div className="text-emerald-300">
                        <span className="text-stone-400">// Career paths available</span><br />
                        <span className="text-sky-300">const</span> <span className="text-stone-100">paths</span> = [<br />
                        &nbsp;&nbsp;<span className="text-amber-300">"Cybersecurity"</span>, <span className="text-amber-300">"AI/ML"</span>, <span className="text-amber-300">"Cloud"</span>,<br />
                        &nbsp;&nbsp;<span className="text-amber-300">"Data Science"</span>, <span className="text-amber-300">"Software Dev"</span><br />
                        ];
                      </div>
                    </div>
                  </div>

                  {/* Key Highlights */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-white">Program Highlights</h4>
                    <ul className="grid gap-3 sm:grid-cols-2">
                      {csFeaturedProgram.points.map((point, index) => (
                        <li key={index} className="flex items-center gap-3 p-3 rounded-lg bg-stone-800/30 border border-stone-600/30 hover:bg-stone-800/50 transition-colors backdrop-blur-sm">
                          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-red-500/15 to-teal-500/15 border border-red-500/20 flex-shrink-0">
                            {point.icon}
                          </div>
                          <span className="font-medium text-sm text-stone-200">{point.text}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Community Message */}
                  <div className="bg-gradient-to-r from-violet-900/20 to-red-900/20 rounded-xl p-6 border border-violet-500/20 backdrop-blur-sm">
                    <div className="flex items-start gap-3">
                      <div className="text-violet-300 mt-1">
                        <Lock className="h-5 w-5" />
                      </div>
                      <p className="text-stone-200 font-medium italic leading-relaxed">
                        {csFeaturedProgram.communityMessage}
                      </p>
                    </div>
                  </div>

                  {/* CTA Section */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-2">
                    <Button asChild variant="outline" className="border-teal-400/60 text-teal-100 hover:bg-teal-400/20 hover:border-teal-400/80 hover:text-white backdrop-blur-sm bg-stone-800/40">
                      <Link href="#cybersecurity-timeline">
                        <Database className="mr-2 h-4 w-4" />
                        View Curriculum Timeline
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Cybersecurity Program Timeline */}
          <section id="cybersecurity-timeline" className="w-full py-16 md:py-24 bg-gradient-to-b from-stone-900 via-slate-800 to-stone-900 relative overflow-hidden">
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="max-w-7xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-6 text-center mb-16">
                  <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-violet-500/15 to-teal-500/15 backdrop-blur-sm border border-violet-400/20 text-violet-200 text-sm font-medium font-mono">
                    <Database className="h-4 w-4 mr-2" />
                    Timeline.render("cybersecurity");
                  </div>
                  <div className="space-y-6">
                    <h2 className="heading-lg bg-gradient-to-r from-violet-300 via-teal-300 to-emerald-300 bg-clip-text text-transparent">
                      <span className="font-mono text-sm text-violet-300 block mb-2">curriculum.loadPath("/cybersecurity");</span>
                      Academic Roadmap: Cybersecurity Engineering
                    </h2>
                    
                    {/* ASCII-style header */}
                    <div className="bg-stone-900 rounded-lg p-6 max-w-4xl mx-auto font-mono text-sm border border-emerald-400/30 mb-6">
                      <div className="text-emerald-300">
                        <div className="text-center mb-4">
                          <span className="text-teal-300">╔══════════════════════════════════════════════════════════════╗</span><br />
                          <span className="text-teal-300">║</span> <span className="text-white font-bold">CYBERSECURITY PROGRAM ARCHITECTURE v2.0</span> <span className="text-teal-300">║</span><br />
                          <span className="text-teal-300">╠══════════════════════════════════════════════════════════════╣</span><br />
                          <span className="text-teal-300">║</span> <span className="text-amber-300">Status:</span> <span className="text-emerald-300">[ACTIVE]</span> <span className="text-amber-300">| Duration:</span> <span className="text-violet-300">4 Years</span> <span className="text-amber-300">| Credits:</span> <span className="text-red-300">120+</span> <span className="text-teal-300">║</span><br />
                          <span className="text-teal-300">╚══════════════════════════════════════════════════════════════╝</span>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                          <div className="bg-sky-900/20 border border-sky-500/20 rounded p-2">
                            <span className="text-sky-300">└─ Year 1</span><br />
                            <span className="text-stone-300 ml-3">Foundation</span>
                          </div>
                          <div className="bg-violet-900/20 border border-violet-500/20 rounded p-2">
                            <span className="text-violet-300">└─ Year 2</span><br />
                            <span className="text-stone-300 ml-3">Core Security</span>
                          </div>
                          <div className="bg-teal-900/20 border border-teal-500/20 rounded p-2">
                            <span className="text-teal-300">└─ Year 3</span><br />
                            <span className="text-stone-300 ml-3">Advanced Topics</span>
                          </div>
                          <div className="bg-red-900/20 border border-red-500/20 rounded p-2">
                            <span className="text-red-300">└─ Year 4</span><br />
                            <span className="text-stone-300 ml-3">Specialization</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <p className="text-stone-200 text-lg leading-relaxed max-w-4xl mx-auto mb-6">
                      Navigate through our systematically designed curriculum architecture. Each node represents a critical milestone in your cybersecurity engineering journey.
                    </p>
                    
                    {/* Interactive Terminal */}
                    <div className="bg-stone-900 rounded-lg p-4 max-w-3xl mx-auto font-mono text-sm border border-emerald-400/30">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-3 h-3 rounded-full bg-red-400"></div>
                        <div className="w-3 h-3 rounded-full bg-amber-400"></div>
                        <div className="w-3 h-3 rounded-full bg-emerald-400"></div>
                        <span className="text-stone-400 text-xs ml-4">cybersec-curriculum@ullens:~$</span>
                      </div>
                      <div className="space-y-1">
                        <div className="text-emerald-300">
                          <span className="text-stone-400">$</span> <span className="text-teal-300">./curriculum</span> <span className="text-amber-300">--interactive</span> <span className="text-violet-300">--program=cybersecurity</span>
                        </div>
                        <div className="text-teal-300">Initializing curriculum browser...</div>
                        <div className="text-emerald-300">✓ Loading course dependencies</div>
                        <div className="text-emerald-300">✓ Parsing semester structure</div>
                        <div className="text-emerald-300">✓ Mapping career pathways</div>
                        <div className="text-amber-300 animate-pulse">▶ Click any timeline node to explore details</div>
                      </div>
                    </div>
                  </div>
                </div>
                <CybersecurityTimeline />
              </div>
            </div>
          </section>

          {/* Program Cards Section */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-stone-900 via-slate-800 to-stone-900 relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute inset-0 bg-dot-pattern opacity-3"></div>
            </div>
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-6 text-center mb-16">
                  <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-teal-500/15 to-violet-500/15 backdrop-blur-sm border border-teal-400/20 text-teal-200 text-sm font-medium font-mono">
                    <Cpu className="h-4 w-4 mr-2" />
                    Programs.loadAll();
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg bg-gradient-to-r from-teal-300 via-sky-300 to-violet-300 bg-clip-text text-transparent">
                      Comprehensive Computing Education
                    </h2>
                    <p className="text-stone-200 text-lg leading-relaxed max-w-3xl mx-auto">
                      Explore our diverse range of programs designed to prepare you for the most in-demand careers in technology.
                    </p>
                    {/* Terminal Style Navigation */}
                    <div className="bg-stone-900 rounded-lg p-4 max-w-md mx-auto font-mono text-sm">
                      <div className="text-emerald-300">
                        <span className="text-stone-400">$ ls programs/</span><br />
                        <span className="text-teal-300">cybersecurity/</span><br />
                        <span className="text-stone-400">// More programs coming soon...</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-2 lg:max-w-4xl lg:mx-auto mt-12">
                  {csProgramCards.map((card, index) => (
                    <div key={index} className={`block card-hover ${card.upcoming ? 'cursor-default' : ''}`}>
                      <Card className={`h-full border-0 shadow-xl transition-all duration-300 hover:shadow-2xl relative overflow-hidden ${
                        card.upcoming 
                          ? 'bg-slate-800/50 backdrop-blur-sm border border-slate-600/50 hover:border-slate-500/50' 
                          : 'bg-slate-800/50 backdrop-blur-sm border border-slate-600/50'
                      }`}>
                        {card.upcoming && (
                          <div className="absolute top-4 right-4 bg-gradient-to-r from-orange-500 to-amber-500 text-white px-3 py-1 rounded-full text-xs font-medium font-mono">
                            Coming Soon
                          </div>
                        )}
                        <CardHeader>
                          <CardTitle className={`text-xl ${card.upcoming ? 'text-slate-300' : 'text-white'}`}>
                            {card.name}
                          </CardTitle>
                          {card.description && (
                            <CardDescription className={card.upcoming ? 'text-slate-400' : 'text-slate-300'}>
                              {card.description}
                            </CardDescription>
                          )}
                        </CardHeader>
                        <CardContent>
                          <p className={`leading-relaxed ${card.upcoming ? 'text-slate-400' : 'text-slate-300'}`}>
                            {card.upcoming 
                              ? `Exciting ${card.name} program launching soon. Stay tuned for updates on enrollment and curriculum details.`
                              : (card.details || `Explore various aspects of ${card.name}.`)
                            }
                          </p>
                          {card.upcoming && (
                            <div className="mt-4 bg-slate-900 rounded p-3 font-mono text-xs">
                              <div className="text-orange-400">
                                <span className="text-slate-500">// Program status</span><br />
                                <span className="text-blue-400">const</span> <span className="text-white">program</span> = {'{'}<br />
                                <span className="ml-4 text-yellow-400">name</span>: <span className="text-green-400">"{card.name}"</span>,<br />
                                <span className="ml-4 text-yellow-400">status</span>: <span className="text-orange-400">"Coming Soon"</span><br />
                                {'}'};
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Lab Facilities */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-slate-900 via-indigo-950 to-slate-900 relative overflow-hidden">
            {/* Tech Background Elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
              <div className="absolute top-20 left-20 text-cyan-400/10 font-mono text-xs animate-pulse">
                <pre>{`class Laboratory {
  deploy(innovation) {
    return this.technology.latest();
  }
}`}</pre>
              </div>
              <div className="absolute bottom-16 right-16 text-purple-400/10 font-mono text-xs animate-pulse" style={{animationDelay: '1.5s'}}>
                <pre>{`const facilities = {
  equipment: "cutting-edge",
  access: "24/7"
};`}</pre>
              </div>
            </div>
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 backdrop-blur-sm border border-cyan-400/30 text-cyan-300 text-sm font-medium font-mono">
                    <Server className="h-4 w-4 mr-2" />
                    Facilities.initialize();
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                      State-of-the-Art Labs & Infrastructure
                    </h2>
                    <p className="text-slate-300 text-lg leading-relaxed max-w-3xl mx-auto">
                      Our modern labs and facilities provide students with hands-on experience using the latest technologies and tools.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {labFacilities.map((lab, index) => (
                    <div key={index} className="bg-slate-800/50 backdrop-blur-sm border border-slate-600/50 hover:border-slate-500/50 p-8 rounded-xl flex flex-col items-center text-center group transition-all duration-300 hover:shadow-xl hover:shadow-cyan-500/10">
                      <div className="h-16 w-16 rounded-full bg-gradient-to-br from-cyan-500/20 to-purple-500/20 border border-cyan-500/30 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <div className="text-cyan-400">
                          {lab.icon}
                        </div>
                      </div>
                      <h3 className="text-xl font-bold mb-3 text-white">{lab.name}</h3>
                      <p className="text-slate-300">{lab.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 relative overflow-hidden">
            {/* Animated Code Elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute top-20 left-10 text-cyan-400/20 font-mono text-sm animate-pulse">
                <pre>{`if (passion && dedication) {
  return success;
}`}</pre>
              </div>
              <div className="absolute bottom-20 right-10 text-green-400/20 font-mono text-sm animate-pulse" style={{animationDelay: '1s'}}>
                <pre>{`while (learning) {
  opportunities++;
}`}</pre>
              </div>
            </div>
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="max-w-5xl mx-auto bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl shadow-2xl overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-2">
                  <div className="p-8 md:p-12 flex flex-col justify-center">
                    <div className="mb-6">
                      <div className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-400/30 text-cyan-300 text-sm font-medium font-mono mb-4">
                        <ArrowRight className="h-4 w-4 mr-2" />
                        Execute.futureBuilder();
                      </div>
                      <h2 className="text-3xl font-bold mb-4 text-white">
                        Ready to <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">Compile</span> Your Future?
                      </h2>
                      <p className="text-slate-300 mb-6 leading-relaxed">
                        Join our School of Computer Science and start your journey toward a rewarding career in technology. 
                        <span className="font-mono bg-slate-700 px-2 py-1 rounded text-cyan-300 mx-1">Applications.open = true;</span>
                      </p>
                    </div>
                    
                    {/* Interactive Terminal */}
                    <div className="bg-black rounded-lg p-4 mb-6 border border-slate-600">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-3 h-3 rounded-full bg-red-500"></div>
                        <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <span className="text-slate-400 text-xs font-mono ml-2">student@ullens:~$</span>
                      </div>
                      <div className="font-mono text-sm">
                        <div className="text-green-400">
                          <span className="text-green-400">{'>'}</span> git clone https://github.com/your-future.git<br />
                          <span className="text-cyan-400">Cloning into 'your-future'...</span><br />
                          <span className="text-green-400">✓</span> <span className="text-slate-400">Remote: Counting dreams... done.</span><br />
                          <span className="text-green-400">✓</span> <span className="text-slate-400">Receiving opportunities... 100%</span><br />
                          <span className="text-yellow-400 animate-pulse">Ready to push your first commit!</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button className="bg-gradient-to-r from-cyan-600 to-purple-600 hover:from-cyan-700 hover:to-purple-700 shadow-lg shadow-cyan-500/25 border-0">
                        <Code className="mr-2 h-4 w-4" />
                        Apply Now
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Code Editor Mockup */}
                  <div className="hidden lg:block relative bg-slate-900 border-l border-slate-700">
                    <div className="p-6">
                      <div className="bg-slate-800 rounded-lg overflow-hidden">
                        <div className="bg-slate-700 px-4 py-2 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-red-500"></div>
                          <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                          <div className="w-2 h-2 rounded-full bg-green-500"></div>
                          <span className="text-slate-400 text-xs font-mono ml-4">success_story.py</span>
                        </div>
                        <div className="p-4 font-mono text-sm space-y-1">
                          <div className="text-purple-400"># Your success story starts here</div>
                          <div className="text-blue-400">class <span className="text-green-400">YourJourney</span>:</div>
                          <div className="ml-4 text-blue-400">def <span className="text-yellow-400">__init__</span>(self):</div>
                          <div className="ml-8 text-slate-300">self.skills = []</div>
                          <div className="ml-8 text-slate-300">self.passion = <span className="text-green-400">True</span></div>
                          <div className="ml-8 text-slate-300">self.future = <span className="text-cyan-400">"Unlimited"</span></div>
                          <div className="ml-4 text-blue-400">def <span className="text-yellow-400">study_at_ullens</span>(self):</div>
                          <div className="ml-8 text-slate-300">self.skills.extend([</div>
                          <div className="ml-12 text-green-400">"Problem Solving",</div>
                          <div className="ml-12 text-green-400">"Critical Thinking",</div>
                          <div className="ml-12 text-green-400">"Innovation"</div>
                          <div className="ml-8 text-slate-300">])</div>
                          <div className="ml-8 text-blue-400">return <span className="text-green-400">"Success Guaranteed"</span></div>
                          <div className="mt-2 text-slate-500"># Ready to run your program?</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
              </main>
      <Footer />
    </div>
    </PageTransition>
  );
} 