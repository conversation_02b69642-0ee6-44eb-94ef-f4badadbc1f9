'use client'

import Link from "next/link"
import { ArrowRight, BookOpen, ChevronRight, GraduationCap, Pencil, School, Presentation, Brain, Users, Globe, Award, Target, Lightbulb, Heart, Star, CheckCircle, Calendar, MapPin, Phone, Mail } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useParallaxEffect } from "@/components/parallax-effect"
import { PageTransition } from "@/components/ui/page-transition"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LazyImage } from "@/components/ui/lazy-image"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { SkipLink } from "@/components/ui/skip-link"

// Data for UCED Programs
const ucedPrograms = [
  { 
    name: "Transformational Leadership Training", 
    slug: "transformational-leadership", 
    icon: <Target className="h-6 w-6 text-blue-500" />,
    description: "Leadership development for educators",
    details: "Comprehensive training program designed to develop transformational leadership skills for educators and school administrators, focusing on vision-building and inspirational leadership.",
    duration: "6 months",
    format: "Hybrid",
    highlights: ["Vision Building", "Team Leadership", "Change Management"]
  },
  { 
    name: "Progressive Education Certification", 
    slug: "progressive-education", 
    icon: <Lightbulb className="h-6 w-6 text-emerald-500" />,
    description: "Student-centered teaching approaches",
    details: "Learn progressive educational methodologies that nurture holistic student development through student-centered learning and innovative pedagogical approaches.",
    duration: "4 months",
    format: "In-person",
    highlights: ["Student-Centered Learning", "Holistic Development", "Innovation"]
  },
  { 
    name: "Subject-Specific Workshops", 
    slug: "subject-workshops", 
    icon: <BookOpen className="h-6 w-6 text-purple-500" />,
    description: "Specialized subject training",
    details: "Targeted workshops designed to help teachers incorporate progressive pedagogy into specific subject areas and level-based instruction planning and delivery.",
    duration: "2-4 weeks",
    format: "Flexible",
    highlights: ["Subject Expertise", "Pedagogy Integration", "Lesson Planning"]
  },
  { 
    name: "Classroom Management & Student-Centered Learning", 
    slug: "classroom-management", 
    icon: <Users className="h-6 w-6 text-orange-500" />,
    description: "Effective classroom leadership",
    details: "Training programs focused on effective classroom management techniques while fostering student-centered learning environments that promote engagement and growth.",
    duration: "3 months",
    format: "Blended",
    highlights: ["Engagement Strategies", "Behavior Management", "Learning Environment"]
  },
];

const trainingAudience = [
  {
    name: "Government School Teachers",
    description: "Professional development for public education teachers",
    details: "Specialized training programs designed to enhance the capacity of government school teachers in progressive education methodologies and transformational leadership.",
    icon: <School className="h-8 w-8 text-blue-500" />,
    count: "500+",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200"
  },
  {
    name: "Private Institution Educators",
    description: "Capacity building for private school teachers",
    details: "Comprehensive training programs tailored for educators in private institutions, focusing on innovative teaching practices and educational leadership.",
    icon: <GraduationCap className="h-8 w-8 text-emerald-500" />,
    count: "300+",
    bgColor: "bg-emerald-50",
    borderColor: "border-emerald-200"
  },
  {
    name: "School Leaders & Administrators",
    description: "Leadership development for education professionals",
    details: "Advanced training in transformational leadership, school management, and educational vision-building for current and aspiring school administrators.",
    icon: <Award className="h-8 w-8 text-purple-500" />,
    count: "150+",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200"
  },
  {
    name: "Aspiring Teachers",
    description: "Foundation training for future educators",
    details: "Foundational programs for individuals aspiring to become teachers or school administrators, providing essential skills and progressive education principles.",
    icon: <Heart className="h-8 w-8 text-rose-500" />,
    count: "200+",
    bgColor: "bg-rose-50",
    borderColor: "border-rose-200"
  },
];

const impactStats = [
  {
    number: "1,150+",
    label: "Educators Trained",
    icon: <GraduationCap className="h-8 w-8 text-blue-500" />
  },
  {
    number: "15+",
    label: "Training Programs",
    icon: <BookOpen className="h-8 w-8 text-emerald-500" />
  },
  {
    number: "5",
    label: "Years of Excellence",
    icon: <Award className="h-8 w-8 text-purple-500" />
  },
  {
    number: "98%",
    label: "Satisfaction Rate",
    icon: <Star className="h-8 w-8 text-orange-500" />
  }
];

export default function EducationPage() {
  useParallaxEffect();

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section - Enhanced */}
          <section className="w-full py-20 md:py-28 lg:py-36 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute -right-20 top-20 w-96 h-96 bg-blue-500/8 rounded-full blur-3xl animate-pulse" />
              <div className="absolute left-1/4 bottom-0 w-[500px] h-[500px] bg-indigo-500/6 rounded-full blur-3xl" />
              <div className="absolute top-1/2 left-1/2 w-80 h-80 bg-purple-500/5 rounded-full blur-2xl" />
            </div>
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="max-w-4xl mx-auto">
                <div className="flex flex-col justify-center space-y-8 text-center">
                  <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-500/10 to-indigo-500/10 text-blue-600 text-sm font-semibold mx-auto border border-blue-200/50">
                    <Target className="h-4 w-4 mr-2" />
                    Ullens Center for Educator Development
                  </div>
                  <div className="space-y-6">
                    <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                      Transforming <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">Education</span> Through Teacher Excellence
                    </h1>
                    <p className="text-gray-600 text-lg md:text-xl leading-relaxed max-w-4xl mx-auto">
                      UCED is the dedicated teacher training unit of Ullens Education Foundation, enhancing professional capacity of educators through transformational leadership and progressive education.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* About UCED Section - Enhanced */}
          <section className="w-full py-20 md:py-28 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500 rounded-full blur-3xl"></div>
              <div className="absolute bottom-40 right-20 w-40 h-40 bg-indigo-500 rounded-full blur-3xl"></div>
              <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-purple-500 rounded-full blur-2xl"></div>
            </div>
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="flex flex-col items-center justify-center space-y-6 text-center mb-20">
                  <Badge variant="outline" className="px-6 py-3 text-blue-600 border-blue-200 bg-white shadow-sm">
                    <BookOpen className="h-4 w-4 mr-2" />
                    About UCED
                  </Badge>
                  <div className="space-y-6">
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                      Ullens Center for <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">Educator Development</span>
                    </h2>
                    <p className="text-gray-600 text-xl leading-relaxed max-w-4xl mx-auto">
                      The dedicated teacher training unit of Ullens Education Foundation, transforming education through progressive teaching methodologies and transformational leadership development.
                    </p>
                  </div>
                </div>

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20">
                  {/* Left Column - Story & Mission */}
                  <div className="space-y-8">
                    {/* Our Story */}
                    <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                          <Heart className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">Our Story</h3>
                      </div>
                      <div className="space-y-4 text-gray-600 leading-relaxed">
                        <p>
                          Ullens Center for Educator Development (UCED) operates in collaboration with <strong className="text-blue-600">Kathmandu University</strong>, serving educators both within the Ullens School network and across Nepal's broader teaching community.
                        </p>
                        <p>
                          We are committed to enhancing professional capacity through high-quality training in transformational leadership and progressive education, empowering teachers to create meaningful impact in their classrooms and communities.
                        </p>
                      </div>
                    </div>

                    {/* Mission & Vision */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200">
                        <div className="h-10 w-10 rounded-lg bg-blue-500 flex items-center justify-center mb-4">
                          <Target className="h-5 w-5 text-white" />
                        </div>
                        <h4 className="text-lg font-bold text-gray-900 mb-3">Our Mission</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">
                          To provide transformational leadership training and progressive education that empowers educators to foster student-centered learning environments.
                        </p>
                      </div>
                      
                      <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border border-emerald-200">
                        <div className="h-10 w-10 rounded-lg bg-emerald-500 flex items-center justify-center mb-4">
                          <Lightbulb className="h-5 w-5 text-white" />
                        </div>
                        <h4 className="text-lg font-bold text-gray-900 mb-3">Our Vision</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">
                          To be the leading center for educator development, creating a network of transformational leaders who drive educational excellence across Nepal.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Key Features & Timeline */}
                  <div className="space-y-8">
                    {/* Key Features */}
                    <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                          <Star className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">What Makes Us Unique</h3>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-start gap-3">
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-gray-900">Academic Accreditation</h4>
                            <p className="text-sm text-gray-600">All programs accredited by Kathmandu University School of Education</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-gray-900">Flexible Learning</h4>
                            <p className="text-sm text-gray-600">Short-term and long-term programs tailored to educator needs</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-gray-900">Progressive Methodology</h4>
                            <p className="text-sm text-gray-600">Grounded in philosophical foundations of progressive teaching</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-gray-900">Holistic Development</h4>
                            <p className="text-sm text-gray-600">Focus on nurturing complete student growth and development</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Impact Timeline */}
                    <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-3xl p-8 border border-indigo-100">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 flex items-center justify-center">
                          <Calendar className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">Our Journey</h3>
                      </div>
                      <div className="space-y-6">
                        <div className="flex gap-4">
                          <div className="flex flex-col items-center">
                            <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
                            <div className="w-px h-8 bg-blue-200"></div>
                          </div>
                          <div className="pb-6">
                            <h4 className="font-semibold text-gray-900">Established Partnership</h4>
                            <p className="text-sm text-gray-600">Collaboration with Kathmandu University began</p>
                          </div>
                        </div>
                        <div className="flex gap-4">
                          <div className="flex flex-col items-center">
                            <div className="h-3 w-3 bg-emerald-500 rounded-full"></div>
                            <div className="w-px h-8 bg-emerald-200"></div>
                          </div>
                          <div className="pb-6">
                            <h4 className="font-semibold text-gray-900">Program Accreditation</h4>
                            <p className="text-sm text-gray-600">All training programs received academic accreditation</p>
                          </div>
                        </div>
                        <div className="flex gap-4">
                          <div className="flex flex-col items-center">
                            <div className="h-3 w-3 bg-purple-500 rounded-full"></div>
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">Expanding Impact</h4>
                            <p className="text-sm text-gray-600">Serving educators across Nepal</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Core Values */}
                <div className="space-y-12">
                  <div className="text-center">
                    <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Core Values</h3>
                    <p className="text-gray-600 text-lg max-w-3xl mx-auto">
                      The principles that guide our approach to educator development and transformational learning.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div className="group text-center p-8 rounded-3xl bg-white shadow-lg border border-blue-100 hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                      <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <Target className="h-8 w-8 text-white" />
                      </div>
                      <h4 className="text-xl font-bold mb-4 text-gray-900">Transformational Leadership</h4>
                      <p className="text-gray-600 leading-relaxed">
                        Developing visionary leaders who inspire and guide educational transformation through innovative practices and progressive methodologies.
                      </p>
                    </div>
                    
                    <div className="group text-center p-8 rounded-3xl bg-white shadow-lg border border-emerald-100 hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                      <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <Lightbulb className="h-8 w-8 text-white" />
                      </div>
                      <h4 className="text-xl font-bold mb-4 text-gray-900">Progressive Education</h4>
                      <p className="text-gray-600 leading-relaxed">
                        Fostering student-centered learning environments that nurture holistic development through innovative pedagogical approaches.
                      </p>
                    </div>

                    <div className="group text-center p-8 rounded-3xl bg-white shadow-lg border border-purple-100 hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                      <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <Award className="h-8 w-8 text-white" />
                      </div>
                      <h4 className="text-xl font-bold mb-4 text-gray-900">Academic Excellence</h4>
                      <p className="text-gray-600 leading-relaxed">
                        Maintaining the highest standards of academic rigor and relevance through university-accredited training programs.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Leadership Section - Enhanced */}
          <section className="w-full py-20 md:py-28 bg-white relative overflow-hidden">
            <div className="px-4 md:px-6">
              <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="flex flex-col items-center justify-center space-y-6 text-center mb-16">
                  <Badge variant="outline" className="px-6 py-3 text-blue-600 border-blue-200 bg-blue-50 shadow-sm">
                    <Users className="h-4 w-4 mr-2" />
                    Leadership
                  </Badge>
                  <div className="space-y-4">
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                      Meet Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">Director</span>
                    </h2>
                    <p className="text-gray-600 text-xl leading-relaxed max-w-3xl mx-auto">
                      Visionary leadership driving educational transformation through progressive methodologies and transformational practice.
                    </p>
                  </div>
                </div>

                {/* Director Profile */}
                <div className="max-w-6xl mx-auto">
                  <div className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-3xl p-8 md:p-12 shadow-xl border border-blue-100 relative overflow-hidden">
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-10 right-10 w-32 h-32 bg-blue-500 rounded-full blur-3xl"></div>
                      <div className="absolute bottom-10 left-10 w-40 h-40 bg-indigo-500 rounded-full blur-3xl"></div>
                    </div>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 items-center relative z-10">
                      {/* Profile Image */}
                      <div className="lg:col-span-4 flex justify-center">
                        <div className="relative">
                          <div className="absolute -inset-4 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl blur opacity-25"></div>
                          <div className="relative w-64 h-80 md:w-80 md:h-96 overflow-hidden shadow-2xl border-4 border-white rounded-2xl group">
                            <img
                              src="/sanjay-adhikari.webp"
                              alt="Sanjaya Adhikari - Director, UCED"
                              className="w-full h-full object-cover object-top transition-all duration-700 group-hover:scale-110 filter grayscale group-hover:grayscale-0 group-hover:brightness-110 group-hover:contrast-110"
                            />
                            {/* Professional Overlay */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                          </div>
                        </div>
                      </div>

                      {/* Profile Content */}
                      <div className="lg:col-span-8 space-y-8 text-center lg:text-left">
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <h3 className="text-3xl md:text-4xl font-bold text-gray-900">Sanjaya Adhikari</h3>
                            <p className="text-xl text-blue-600 font-semibold">Director, Ullens Center for Educator Development (UCED)</p>
                          </div>
                          
                          {/* Key Highlights */}
                          <div className="flex flex-wrap gap-3 justify-center lg:justify-start">
                            <Badge className="bg-blue-100 text-blue-700 px-4 py-2">20 Years Experience</Badge>
                            <Badge className="bg-emerald-100 text-emerald-700 px-4 py-2">PhD Scholar</Badge>
                            <Badge className="bg-purple-100 text-purple-700 px-4 py-2">Published Author</Badge>
                          </div>
                        </div>

                        {/* Bio Content */}
                        <div className="space-y-6 text-gray-700 leading-relaxed">
                          <p className="text-lg">
                            Sanjaya Adhikari is a dedicated educator with <strong className="text-blue-600">20 years of experience</strong> as a teacher, academic leader, and teacher trainer. He has been an integral part of Ullens School since 2014, where he currently serves as the Director of the Ullens Center for Educator Development (UCED).
                          </p>
                          
                          <p>
                            Driven by a passion for progressive education, Sanjaya believes in providing students with opportunities to explore learning beyond the traditional classroom setting. He is committed to fostering student success both academically and in life, and actively involves students in research work, believing that big ideas are born from research.
                          </p>
                          
                          <p>
                            Sanjaya is currently a <strong className="text-indigo-600">PhD scholar in Educational Leadership at Kathmandu University</strong>. He holds an M.Phil in Education and has presented research papers at numerous national and international educational conferences and seminars. His eleven years at Ullens School have solidified his commitment to equity, celebrating diversity, and promoting active participation in education.
                          </p>
                          
                          <p>
                            An advocate for integrating art-based activities into teaching, Sanjaya also plays a key role in conducting seminars and Model United Nations (MUN) sessions. He is deeply passionate about literature and is an <strong className="text-purple-600">accomplished author</strong>, with a novel, several short stories, poems, and essays to his name, as well as numerous articles related to education, teaching, and learning.
                          </p>
                        </div>

                        {/* Achievements Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-blue-100">
                            <div className="flex items-center gap-3 mb-3">
                              <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                <GraduationCap className="h-5 w-5 text-white" />
                              </div>
                              <h4 className="font-bold text-gray-900">Academic Excellence</h4>
                            </div>
                            <p className="text-sm text-gray-600">PhD scholar in Educational Leadership, M.Phil in Education, extensive research presentations</p>
                          </div>
                          
                          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-emerald-100">
                            <div className="flex items-center gap-3 mb-3">
                              <div className="w-10 h-10 bg-emerald-500 rounded-lg flex items-center justify-center">
                                <BookOpen className="h-5 w-5 text-white" />
                              </div>
                              <h4 className="font-bold text-gray-900">Published Author</h4>
                            </div>
                            <p className="text-sm text-gray-600">Novel, short stories, poems, essays, and educational articles contributing to literature and pedagogy</p>
                          </div>
                        </div>

                        {/* Contact Information */}
                        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-6 text-white">
                          <div className="flex items-center gap-3 mb-4">
                            <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                              <Mail className="h-5 w-5 text-white" />
                            </div>
                            <h4 className="font-bold text-white text-lg">Get in Touch with UCED</h4>
                          </div>
                          <p className="text-blue-100 mb-4 leading-relaxed">
                            For inquiries about our training programs, partnerships, or educational development opportunities, reach out to our team.
                          </p>
                          <div className="flex items-center gap-3">
                            <Mail className="h-5 w-5 text-blue-200" />
                            <a 
                              href="mailto:<EMAIL>" 
                              className="text-white font-medium hover:text-blue-200 transition-colors underline decoration-blue-300 underline-offset-4"
                            >
                              <EMAIL>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Who We Serve Section - Enhanced */}
          <section className="w-full py-20 md:py-28 bg-gradient-to-br from-gray-50 to-blue-50">
            <div className="px-4 md:px-6">
              <div className="max-w-7xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-6 text-center mb-16">
                  <Badge variant="outline" className="px-4 py-2 text-blue-600 border-blue-200 bg-white">
                    Who We Serve
                  </Badge>
                  <div className="space-y-4">
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900">
                      Educators Across Nepal
                    </h2>
                    <p className="text-gray-600 text-lg leading-relaxed max-w-3xl mx-auto">
                      UCED serves a diverse community of educators, from government school teachers to private institution educators, school leaders, and aspiring teaching professionals.
                    </p>
                  </div>
                </div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4 mt-16">
                  {trainingAudience.map((audience, index) => (
                    <div key={index} className={`group ${audience.bgColor} ${audience.borderColor} border-2 p-8 rounded-3xl flex flex-col items-center text-center hover:shadow-xl transition-all duration-300 hover:-translate-y-2`}>
                      <div className="h-20 w-20 rounded-2xl bg-white shadow-lg flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        {audience.icon}
                      </div>
                      <h3 className="text-lg font-bold mb-3 text-gray-900">{audience.name}</h3>
                      <p className="text-sm text-gray-600 mb-4 font-medium">{audience.description}</p>
                      <p className="text-xs text-gray-500 leading-relaxed">{audience.details}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Training Programs Section - Enhanced */}
          <section className="w-full py-20 md:py-28 bg-white">
            <div className="px-4 md:px-6">
              <div className="max-w-7xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-6 text-center mb-16">
                  <Badge variant="outline" className="px-4 py-2 text-blue-600 border-blue-200 bg-blue-50">
                    Training Programs
                  </Badge>
                  <div className="space-y-4">
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900">
                      Comprehensive Educator Development
                    </h2>
                    <p className="text-gray-600 text-lg leading-relaxed max-w-3xl mx-auto">
                      Our training programs combine short-term and long-term approaches, all designed to align instructional practices with progressive educational methodologies.
                    </p>
                  </div>
                </div>

                <div className="grid gap-8 md:grid-cols-2 mt-16">
                  {ucedPrograms.map((program, index) => (
                    <div key={index} className="group bg-white border border-gray-200 rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
                      <div className="p-8">
                        <div className="flex items-start gap-4 mb-6">
                          <div className="h-14 w-14 rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            {program.icon}
                          </div>
                          <div className="flex-1">
                            <h3 className="text-xl font-bold text-gray-900 mb-2">{program.name}</h3>
                            <p className="text-gray-600 text-sm font-medium">{program.description}</p>
                          </div>
                        </div>
                        
                        <p className="text-gray-600 leading-relaxed mb-6">
                          {program.details}
                        </p>
                        
                        <div className="flex flex-wrap gap-4 mb-6">
                          <div className="flex items-center gap-2 bg-blue-50 px-3 py-1 rounded-full">
                            <Calendar className="h-4 w-4 text-blue-500" />
                            <span className="text-sm font-medium text-blue-700">{program.duration}</span>
                          </div>
                          <div className="flex items-center gap-2 bg-emerald-50 px-3 py-1 rounded-full">
                            <MapPin className="h-4 w-4 text-emerald-500" />
                            <span className="text-sm font-medium text-emerald-700">{program.format}</span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <h4 className="text-sm font-semibold text-gray-900">Key Highlights:</h4>
                          <div className="flex flex-wrap gap-2">
                            {program.highlights.map((highlight, idx) => (
                              <span key={idx} className="inline-flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-md text-xs font-medium text-gray-700">
                                <CheckCircle className="h-3 w-3 text-green-500" />
                                {highlight}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      <div className="px-8 pb-8">
                        <Button className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-300">
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Partnership Section - Enhanced */}
          <section className="w-full py-20 md:py-28 bg-gradient-to-br from-blue-50 to-indigo-50">
            <div className="px-4 md:px-6">
              <div className="max-w-7xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-6 text-center mb-16">
                  <Badge variant="outline" className="px-4 py-2 text-blue-600 border-blue-200 bg-white">
                    Academic Partnership
                  </Badge>
                  <div className="space-y-4">
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900">
                      Collaboration with Kathmandu University
                    </h2>
                    <p className="text-gray-600 text-lg leading-relaxed max-w-3xl mx-auto">
                      UCED operates in collaboration with Kathmandu University, ensuring our training programs meet the highest academic standards and provide meaningful professional development.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mt-16">
                  <div className="group bg-white p-10 rounded-3xl border border-blue-100 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                    <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                      <GraduationCap className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold mb-4 text-gray-900">Kathmandu University School of Education</h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      All UCED training programs are academically accredited by the Kathmandu University School of Education, ensuring both rigor and relevance in educator development.
                    </p>
                    <div className="flex items-center gap-2 text-blue-600 font-medium">
                      <CheckCircle className="h-5 w-5" />
                      Academically Accredited
                    </div>
                  </div>
                  
                  <div className="group bg-white p-10 rounded-3xl border border-emerald-100 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                    <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                      <School className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold mb-4 text-gray-900">Ullens School Network</h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      UCED serves educators within the Ullens School network (Khumaltar and Kathmandu) while extending our reach to the broader teaching community across Nepal.
                    </p>
                    <div className="flex items-center gap-2 text-emerald-600 font-medium">
                      <CheckCircle className="h-5 w-5" />
                      Network of Excellence
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  );
} 