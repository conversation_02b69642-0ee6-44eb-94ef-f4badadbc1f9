import React from 'react'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Faculty Directory | College Name',
  description: 'Meet our distinguished faculty members who bring expertise and passion to our academic programs. Browse by department, research area, or search for specific expertise.',
  keywords: 'faculty directory, professors, academic staff, college faculty, researchers, higher education',
  openGraph: {
    title: 'Faculty Directory | College Name',
    description: 'Explore our diverse faculty members and their research areas. Connect with leading experts across all academic departments.',
    type: 'website',
    url: '/faculty',
    images: [
      {
        url: '/images/faculty-directory-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Faculty Directory'
      }
    ]
  }
}

export default function FacultyLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
} 