import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for research areas
const researchAreaSchema = z.object({
  areaName: z.string().min(1, 'Research area name is required'),
  description: z.string().optional()
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = researchAreaSchema.parse(body)

    // Verify the research area belongs to the current faculty member
    const researchArea = await prisma.facultyResearchArea.findFirst({
      where: {
        id: params.id,
        faculty: {
          userId: session.user.id
        }
      }
    })

    if (!researchArea) {
      return NextResponse.json({ error: 'Research area not found' }, { status: 404 })
    }

    // Update research area
    const updatedResearchArea = await prisma.facultyResearchArea.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        updatedAt: new Date()
      }
    })

    return NextResponse.json({
      message: 'Research area updated successfully',
      researchArea: updatedResearchArea
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error updating research area:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the research area belongs to the current faculty member
    const researchArea = await prisma.facultyResearchArea.findFirst({
      where: {
        id: params.id,
        faculty: {
          userId: session.user.id
        }
      }
    })

    if (!researchArea) {
      return NextResponse.json({ error: 'Research area not found' }, { status: 404 })
    }

    // Delete research area
    await prisma.facultyResearchArea.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      message: 'Research area deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting research area:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
