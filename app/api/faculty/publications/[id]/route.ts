import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for publications
const publicationSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  authors: z.array(z.string()).min(1, 'At least one author is required'),
  journal: z.string().min(1, 'Journal/Conference is required'),
  year: z.number().min(1900).max(2030),
  citationCount: z.number().min(0).default(0),
  link: z.string().url().optional().or(z.literal('')),
  abstract: z.string().optional(),
  tags: z.array(z.string()).default([])
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = publicationSchema.parse(body)

    // Verify the publication belongs to the current faculty member
    const publication = await prisma.facultyPublication.findFirst({
      where: {
        id: params.id,
        faculty: {
          userId: session.user.id
        }
      }
    })

    if (!publication) {
      return NextResponse.json({ error: 'Publication not found' }, { status: 404 })
    }

    // Update publication
    const updatedPublication = await prisma.facultyPublication.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        updatedAt: new Date()
      }
    })

    return NextResponse.json({
      message: 'Publication updated successfully',
      publication: updatedPublication
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error updating publication:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the publication belongs to the current faculty member
    const publication = await prisma.facultyPublication.findFirst({
      where: {
        id: params.id,
        faculty: {
          userId: session.user.id
        }
      }
    })

    if (!publication) {
      return NextResponse.json({ error: 'Publication not found' }, { status: 404 })
    }

    // Delete publication
    await prisma.facultyPublication.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      message: 'Publication deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting publication:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
