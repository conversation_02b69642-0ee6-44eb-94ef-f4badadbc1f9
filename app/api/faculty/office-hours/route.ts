import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for office hours
const officeHourSchema = z.object({
  dayOfWeek: z.number().min(0).max(6), // 0 = Sunday, 6 = Saturday
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  location: z.string().min(1, 'Location is required'),
  notes: z.string().optional(),
  isActive: z.boolean().default(true)
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        officeHours: {
          where: { isActive: true },
          orderBy: [
            { dayOfWeek: 'asc' },
            { startTime: 'asc' }
          ]
        }
      }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    return NextResponse.json({ officeHours: facultyProfile.officeHours })
  } catch (error) {
    console.error('Error fetching office hours:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = officeHourSchema.parse(body)

    // Get faculty profile
    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    // Validate time range
    const startTime = new Date(`2000-01-01T${validatedData.startTime}:00`)
    const endTime = new Date(`2000-01-01T${validatedData.endTime}:00`)
    
    if (startTime >= endTime) {
      return NextResponse.json({ 
        error: 'End time must be after start time' 
      }, { status: 400 })
    }

    // Check for overlapping office hours on the same day
    const existingOfficeHours = await prisma.officeHour.findMany({
      where: {
        facultyId: facultyProfile.id,
        dayOfWeek: validatedData.dayOfWeek,
        isActive: true
      }
    })

    for (const existing of existingOfficeHours) {
      const existingStart = new Date(`2000-01-01T${existing.startTime}:00`)
      const existingEnd = new Date(`2000-01-01T${existing.endTime}:00`)
      
      // Check for overlap
      if (
        (startTime >= existingStart && startTime < existingEnd) ||
        (endTime > existingStart && endTime <= existingEnd) ||
        (startTime <= existingStart && endTime >= existingEnd)
      ) {
        return NextResponse.json({ 
          error: 'Office hours overlap with existing schedule' 
        }, { status: 400 })
      }
    }

    // Create new office hour
    const officeHour = await prisma.officeHour.create({
      data: {
        ...validatedData,
        facultyId: facultyProfile.id
      }
    })

    return NextResponse.json(officeHour, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error creating office hour:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
