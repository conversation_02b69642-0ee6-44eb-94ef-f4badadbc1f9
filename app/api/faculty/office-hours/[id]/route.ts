import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for office hours
const officeHourSchema = z.object({
  dayOfWeek: z.number().min(0).max(6), // 0 = Sunday, 6 = Saturday
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  location: z.string().min(1, 'Location is required'),
  notes: z.string().optional(),
  isActive: z.boolean().default(true)
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = officeHourSchema.parse(body)

    // Verify the office hour belongs to the current faculty member
    const officeHour = await prisma.officeHour.findFirst({
      where: {
        id: params.id,
        faculty: {
          userId: session.user.id
        }
      }
    })

    if (!officeHour) {
      return NextResponse.json({ error: 'Office hour not found' }, { status: 404 })
    }

    // Validate time range
    const startTime = new Date(`2000-01-01T${validatedData.startTime}:00`)
    const endTime = new Date(`2000-01-01T${validatedData.endTime}:00`)
    
    if (startTime >= endTime) {
      return NextResponse.json({ 
        error: 'End time must be after start time' 
      }, { status: 400 })
    }

    // Check for overlapping office hours on the same day (excluding current one)
    const existingOfficeHours = await prisma.officeHour.findMany({
      where: {
        facultyId: officeHour.facultyId,
        dayOfWeek: validatedData.dayOfWeek,
        isActive: true,
        id: { not: params.id }
      }
    })

    for (const existing of existingOfficeHours) {
      const existingStart = new Date(`2000-01-01T${existing.startTime}:00`)
      const existingEnd = new Date(`2000-01-01T${existing.endTime}:00`)
      
      // Check for overlap
      if (
        (startTime >= existingStart && startTime < existingEnd) ||
        (endTime > existingStart && endTime <= existingEnd) ||
        (startTime <= existingStart && endTime >= existingEnd)
      ) {
        return NextResponse.json({ 
          error: 'Office hours overlap with existing schedule' 
        }, { status: 400 })
      }
    }

    // Update office hour
    const updatedOfficeHour = await prisma.officeHour.update({
      where: { id: params.id },
      data: validatedData
    })

    return NextResponse.json(updatedOfficeHour)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error updating office hour:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the office hour belongs to the current faculty member
    const officeHour = await prisma.officeHour.findFirst({
      where: {
        id: params.id,
        faculty: {
          userId: session.user.id
        }
      },
      include: {
        _count: {
          select: {
            bookings: {
              where: {
                status: 'CONFIRMED',
                date: {
                  gte: new Date()
                }
              }
            }
          }
        }
      }
    })

    if (!officeHour) {
      return NextResponse.json({ error: 'Office hour not found' }, { status: 404 })
    }

    // Check if there are future bookings
    if (officeHour._count.bookings > 0) {
      return NextResponse.json({ 
        error: 'Cannot delete office hour with future bookings. Please cancel bookings first.' 
      }, { status: 400 })
    }

    // Delete office hour
    await prisma.officeHour.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Office hour deleted successfully' })
  } catch (error) {
    console.error('Error deleting office hour:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
