import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for profile updates
const profileUpdateSchema = z.object({
  bio: z.string().optional(),
  title: z.string().optional(),
  office: z.string().optional(), // Maps to officeLocation
  officeHours: z.string().optional(), // Will be stored in UserProfile
  website: z.string().url().optional().or(z.literal('')), // Maps to websiteUrl
  phone: z.string().optional(), // Will be stored in UserProfile
  researchInterests: z.string().optional(), // Will be stored in UserProfile bio
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        department: true,
        publications: {
          orderBy: { year: 'desc' }
        },
        researchAreas: true,
        education: {
          orderBy: { year: 'desc' }
        },
        timeline: {
          orderBy: { year: 'desc' }
        }
      }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    return NextResponse.json({ profile: facultyProfile })
  } catch (error) {
    console.error('Error fetching faculty profile:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    console.log('Received update request:', body)
    
    // Extract facultyId from the request if provided
    const { facultyId, ...updateData } = body
    
    // Validate the update data
    const validatedData = profileUpdateSchema.parse(updateData)

    // First check if the faculty profile exists
    const existingProfile = await prisma.facultyProfile.findFirst({
      where: { 
        userId: session.user.id,
        ...(facultyId ? { id: facultyId } : {})
      }
    })

    if (!existingProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    console.log('Found existing profile:', existingProfile.id)

    // Prepare data for faculty profile update
    const facultyProfileData: any = {}
    if (validatedData.bio !== undefined) facultyProfileData.bio = validatedData.bio
    if (validatedData.title !== undefined) facultyProfileData.title = validatedData.title
    if (validatedData.office !== undefined) facultyProfileData.officeLocation = validatedData.office
    if (validatedData.website !== undefined) facultyProfileData.websiteUrl = validatedData.website

    // Prepare data for user profile update
    const userProfileData: any = {}
    if (validatedData.phone !== undefined) userProfileData.phone = validatedData.phone
    if (validatedData.researchInterests !== undefined) userProfileData.bio = validatedData.researchInterests

    console.log('Faculty profile update data:', facultyProfileData)
    console.log('User profile update data:', userProfileData)

    // Use transaction to update both profiles
    const result = await prisma.$transaction(async (tx) => {
      // Update faculty profile
      const updatedFacultyProfile = await tx.facultyProfile.update({
        where: { id: existingProfile.id },
        data: {
          ...facultyProfileData,
          updatedAt: new Date()
        },
        include: {
          department: true,
          user: {
            include: {
              profile: true
            }
          }
        }
      })

      // Update or create user profile if needed
      if (Object.keys(userProfileData).length > 0) {
        await tx.userProfile.upsert({
          where: { userId: session.user.id },
          update: userProfileData,
          create: {
            userId: session.user.id,
            ...userProfileData
          }
        })
      }

      return updatedFacultyProfile
    })

    console.log('Profile updated successfully')

    return NextResponse.json({
      message: 'Profile updated successfully',
      profile: result
    })
  } catch (error) {
    console.error('Error updating faculty profile:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({ 
      error: 'Internal server error', 
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
