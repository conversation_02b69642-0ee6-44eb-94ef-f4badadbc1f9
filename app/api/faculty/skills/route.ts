import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for skills
const skillSchema = z.object({
  id: z.string().optional(),
  skillName: z.string().min(1, 'Skill name is required'),
  proficiency: z.string().optional().nullable(),
  category: z.string().optional().nullable(),
})

const requestSchema = z.object({
  facultyId: z.string(),
  skills: z.array(skillSchema)
})

// Get all skills for a faculty member
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || !['FACULTY', 'COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Extract faculty ID from query params
    const { searchParams } = new URL(request.url)
    const facultyId = searchParams.get('facultyId')

    if (!facultyId) {
      return NextResponse.json({ error: 'Faculty ID is required' }, { status: 400 })
    }

    // Check if this is the faculty member's own data or an admin request
    if (session.user.role === 'FACULTY') {
      const facultyProfile = await prisma.facultyProfile.findFirst({
        where: { userId: session.user.id }
      })

      if (!facultyProfile || facultyProfile.id !== facultyId) {
        return NextResponse.json({ error: 'Unauthorized to access this faculty profile' }, { status: 403 })
      }
    }

    const skills = await prisma.facultySkill.findMany({
      where: { facultyId },
      orderBy: [
        { category: 'asc' },
        { skillName: 'asc' }
      ]
    })

    return NextResponse.json(skills)
  } catch (error) {
    console.error('Error fetching skills:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Create or update skills
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || !['FACULTY', 'COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate request data
    const validatedData = requestSchema.parse(body)
    const { facultyId, skills } = validatedData

    // Check if this is the faculty member's own data or an admin request
    if (session.user.role === 'FACULTY') {
      const facultyProfile = await prisma.facultyProfile.findFirst({
        where: { userId: session.user.id }
      })

      if (!facultyProfile || facultyProfile.id !== facultyId) {
        return NextResponse.json({ error: 'Unauthorized to modify this faculty profile' }, { status: 403 })
      }
    }

    // Check if faculty profile exists
    const existingProfile = await prisma.facultyProfile.findUnique({
      where: { id: facultyId }
    })

    if (!existingProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    // Use transaction to delete existing and create new entries
    const result = await prisma.$transaction(async (tx) => {
      // Delete all existing entries for this faculty
      await tx.facultySkill.deleteMany({
        where: { facultyId }
      })

      // Create new entries
      const createdSkills = []
      for (const skill of skills) {
        const { id, ...skillData } = skill
        const created = await tx.facultySkill.create({
          data: {
            ...skillData,
            facultyId
          }
        })
        createdSkills.push(created)
      }

      return createdSkills
    })

    return NextResponse.json({
      message: 'Skills updated successfully',
      skills: result
    })
  } catch (error) {
    console.error('Error updating skills:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({ 
      error: 'Internal server error', 
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 