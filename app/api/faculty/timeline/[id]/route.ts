import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for timeline events
const timelineSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  year: z.string().min(1, 'Year is required'),
  description: z.string().min(1, 'Description is required'),
  type: z.enum(['EDUCATION', 'POSITION', 'AWARD', 'PUBLICATION'])
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = timelineSchema.parse(body)

    // Verify the timeline event belongs to the current faculty member
    const timelineEvent = await prisma.facultyTimeline.findFirst({
      where: {
        id: params.id,
        faculty: {
          userId: session.user.id
        }
      }
    })

    if (!timelineEvent) {
      return NextResponse.json({ error: 'Timeline event not found' }, { status: 404 })
    }

    // Update timeline event
    const updatedTimelineEvent = await prisma.facultyTimeline.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        updatedAt: new Date()
      }
    })

    return NextResponse.json({
      message: 'Timeline event updated successfully',
      timelineEvent: updatedTimelineEvent
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error updating timeline event:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the timeline event belongs to the current faculty member
    const timelineEvent = await prisma.facultyTimeline.findFirst({
      where: {
        id: params.id,
        faculty: {
          userId: session.user.id
        }
      }
    })

    if (!timelineEvent) {
      return NextResponse.json({ error: 'Timeline event not found' }, { status: 404 })
    }

    // Delete timeline event
    await prisma.facultyTimeline.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      message: 'Timeline event deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting timeline event:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
