import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { writeFile } from 'fs/promises'
import { join } from 'path'
import { mkdir } from 'fs/promises'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the faculty profile to ensure it exists
    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        user: {
          include: {
            profile: true
          }
        }
      }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    // Process the multipart form data
    const formData = await request.formData()
    const photo = formData.get('photo') as File
    
    if (!photo) {
      return NextResponse.json({ error: 'No photo provided' }, { status: 400 })
    }

    // Validate file size (5MB max)
    const MAX_SIZE = 5 * 1024 * 1024 // 5MB
    if (photo.size > MAX_SIZE) {
      return NextResponse.json({ 
        error: 'Photo exceeds maximum size of 5MB' 
      }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(photo.type)) {
      return NextResponse.json({ 
        error: 'Only JPG, PNG, or WebP images are allowed' 
      }, { status: 400 })
    }

    // Create a unique filename
    const timestamp = Date.now()
    const fileExtension = photo.type.split('/')[1]
    const fileName = `faculty_${facultyProfile.id}_${timestamp}.${fileExtension}`
    
    // Ensure the uploads directory exists
    const uploadsDir = join(process.cwd(), 'public', 'uploads', 'faculty')
    await mkdir(uploadsDir, { recursive: true })
    
    // Create file path
    const filePath = join(uploadsDir, fileName)
    const publicPath = `/uploads/faculty/${fileName}`
    
    // Save the file
    const buffer = Buffer.from(await photo.arrayBuffer())
    await writeFile(filePath, buffer)
    
    console.log(`File saved to ${filePath}`)

    // Update the user profile with the new avatar URL
    const userProfile = await prisma.userProfile.upsert({
      where: { userId: session.user.id },
      update: { avatarUrl: publicPath },
      create: {
        userId: session.user.id,
        avatarUrl: publicPath
      }
    })

    return NextResponse.json({
      message: 'Photo uploaded successfully',
      avatarUrl: publicPath
    })
  } catch (error) {
    console.error('Error uploading photo:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
