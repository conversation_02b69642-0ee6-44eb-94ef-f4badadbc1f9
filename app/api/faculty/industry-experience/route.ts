import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for industry experience
const industryExperienceSchema = z.object({
  id: z.string().optional(),
  company: z.string().min(1, 'Company name is required'),
  position: z.string().min(1, 'Position is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
})

const requestSchema = z.object({
  facultyId: z.string(),
  experiences: z.array(industryExperienceSchema)
})

// Get all industry experiences for a faculty member
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || !['FACULTY', 'COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Extract faculty ID from query params
    const { searchParams } = new URL(request.url)
    const facultyId = searchParams.get('facultyId')

    if (!facultyId) {
      return NextResponse.json({ error: 'Faculty ID is required' }, { status: 400 })
    }

    // Check if this is the faculty member's own data or an admin request
    if (session.user.role === 'FACULTY') {
      const facultyProfile = await prisma.facultyProfile.findFirst({
        where: { userId: session.user.id }
      })

      if (!facultyProfile || facultyProfile.id !== facultyId) {
        return NextResponse.json({ error: 'Unauthorized to access this faculty profile' }, { status: 403 })
      }
    }

    const experiences = await prisma.facultyIndustryExperience.findMany({
      where: { facultyId },
      orderBy: [
        { startDate: 'desc' }
      ]
    })

    return NextResponse.json(experiences)
  } catch (error) {
    console.error('Error fetching industry experiences:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Create or update industry experiences
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || !['FACULTY', 'COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate request data
    const validatedData = requestSchema.parse(body)
    const { facultyId, experiences } = validatedData

    // Check if this is the faculty member's own data or an admin request
    if (session.user.role === 'FACULTY') {
      const facultyProfile = await prisma.facultyProfile.findFirst({
        where: { userId: session.user.id }
      })

      if (!facultyProfile || facultyProfile.id !== facultyId) {
        return NextResponse.json({ error: 'Unauthorized to modify this faculty profile' }, { status: 403 })
      }
    }

    // Check if faculty profile exists
    const existingProfile = await prisma.facultyProfile.findUnique({
      where: { id: facultyId }
    })

    if (!existingProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    // Use transaction to delete existing and create new entries
    const result = await prisma.$transaction(async (tx) => {
      // Delete all existing entries for this faculty
      await tx.facultyIndustryExperience.deleteMany({
        where: { facultyId }
      })

      // Create new entries
      const createdExperiences = []
      for (const exp of experiences) {
        const { id, ...expData } = exp
        const created = await tx.facultyIndustryExperience.create({
          data: {
            ...expData,
            facultyId
          }
        })
        createdExperiences.push(created)
      }

      return createdExperiences
    })

    return NextResponse.json({
      message: 'Industry experiences updated successfully',
      experiences: result
    })
  } catch (error) {
    console.error('Error updating industry experiences:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({ 
      error: 'Internal server error', 
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 