import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'

// In-memory store for rate limiting (in production, use Redis or database)
const loginAttempts = new Map<string, { count: number; lastAttempt: number; blockedUntil?: number }>()

const MAX_ATTEMPTS = 3
const BLOCK_DURATION = 15 * 60 * 1000 // 15 minutes
const ATTEMPT_WINDOW = 60 * 60 * 1000 // 1 hour

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return request.ip || 'unknown'
}

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    const now = Date.now()
    
    // Get current attempt data
    const attemptData = loginAttempts.get(clientIP) || { count: 0, lastAttempt: 0 }
    
    // Check if currently blocked
    if (attemptData.blockedUntil && attemptData.blockedUntil > now) {
      return NextResponse.json({
        success: false,
        blocked: true,
        message: 'IP temporarily blocked due to too many failed login attempts',
        blockedUntil: attemptData.blockedUntil,
        timeRemaining: Math.ceil((attemptData.blockedUntil - now) / 1000)
      }, { status: 429 })
    }
    
    // Reset if block period has expired
    if (attemptData.blockedUntil && attemptData.blockedUntil <= now) {
      loginAttempts.delete(clientIP)
      return NextResponse.json({
        success: true,
        blocked: false,
        attempts: 0,
        message: 'Rate limit reset'
      })
    }
    
    // Reset attempts if last attempt was more than 1 hour ago
    if (now - attemptData.lastAttempt > ATTEMPT_WINDOW) {
      attemptData.count = 0
    }
    
    return NextResponse.json({
      success: true,
      blocked: false,
      attempts: attemptData.count,
      maxAttempts: MAX_ATTEMPTS,
      message: 'Rate limit check passed'
    })
    
  } catch (error) {
    console.error('Rate limit check error:', error)
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { success } = await request.json()
    const clientIP = getClientIP(request)
    const now = Date.now()
    
    if (success) {
      // Reset on successful login
      loginAttempts.delete(clientIP)
      return NextResponse.json({
        success: true,
        message: 'Login attempts reset'
      })
    } else {
      // Increment failed attempts
      const attemptData = loginAttempts.get(clientIP) || { count: 0, lastAttempt: 0 }
      
      // Reset if last attempt was more than 1 hour ago
      if (now - attemptData.lastAttempt > ATTEMPT_WINDOW) {
        attemptData.count = 0
      }
      
      attemptData.count++
      attemptData.lastAttempt = now
      
      // Block if max attempts reached
      if (attemptData.count >= MAX_ATTEMPTS) {
        attemptData.blockedUntil = now + BLOCK_DURATION
      }
      
      loginAttempts.set(clientIP, attemptData)
      
      return NextResponse.json({
        success: true,
        attempts: attemptData.count,
        maxAttempts: MAX_ATTEMPTS,
        blocked: attemptData.count >= MAX_ATTEMPTS,
        blockedUntil: attemptData.blockedUntil,
        message: attemptData.count >= MAX_ATTEMPTS 
          ? 'IP blocked due to too many failed attempts'
          : `Failed attempt recorded. ${MAX_ATTEMPTS - attemptData.count} attempts remaining`
      })
    }
    
  } catch (error) {
    console.error('Rate limit update error:', error)
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 })
  }
}

// Cleanup old entries periodically (in production, use a proper job scheduler)
setInterval(() => {
  const now = Date.now()
  for (const [ip, data] of loginAttempts.entries()) {
    if (data.blockedUntil && data.blockedUntil <= now) {
      loginAttempts.delete(ip)
    } else if (now - data.lastAttempt > ATTEMPT_WINDOW * 2) {
      loginAttempts.delete(ip)
    }
  }
}, 5 * 60 * 1000) // Clean up every 5 minutes
