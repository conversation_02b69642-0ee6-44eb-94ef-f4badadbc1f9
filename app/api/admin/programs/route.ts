import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { revalidatePath } from 'next/cache'

// Validation schema for creating/updating programs
const programSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  slug: z.string().min(1, 'Slug is required'),
  departmentId: z.string().min(1, 'Department is required'),
  degreeType: z.string().min(1, 'Degree type is required'),
  duration: z.number().min(1, 'Duration must be at least 1 year').max(10, 'Duration cannot exceed 10 years'),
  description: z.string().optional(),
  imageUrl: z.string().url().optional().or(z.literal('')),
  isActive: z.boolean().default(true),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const departmentId = searchParams.get('departmentId') || ''
    const degreeType = searchParams.get('degreeType') || ''
    const isActive = searchParams.get('isActive')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { degreeType: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (departmentId) {
      where.departmentId = departmentId
    }

    if (degreeType) {
      where.degreeType = degreeType
    }

    if (isActive !== null && isActive !== '') {
      where.isActive = isActive === 'true'
    }

    const [programs, total] = await Promise.all([
      prisma.program.findMany({
        where,
        include: {
          department: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          }
        },
        orderBy: {
          name: 'asc'
        },
        skip,
        take: limit
      }),
      prisma.program.count({ where })
    ])

    return NextResponse.json({
      programs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching programs:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = programSchema.parse(body)

    // Check if name or slug already exists
    const existingProgram = await prisma.program.findFirst({
      where: {
        OR: [
          { name: validatedData.name },
          { slug: validatedData.slug }
        ]
      }
    })

    if (existingProgram) {
      return NextResponse.json({ 
        error: existingProgram.name === validatedData.name 
          ? 'Program name already exists' 
          : 'Program slug already exists' 
      }, { status: 400 })
    }

    // Verify department exists
    const department = await prisma.department.findUnique({
      where: { id: validatedData.departmentId }
    })

    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 400 })
    }

    // Create program
    const program = await prisma.program.create({
      data: validatedData,
      include: {
        department: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    })

    // Revalidate programs pages
    revalidatePath('/admin/programs')
    revalidatePath('/programs')

    return NextResponse.json(program, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error creating program:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
