import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const table = searchParams.get('table')
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '20')
    const search = searchParams.get('search') || ''
    const sortColumn = searchParams.get('sortColumn') || ''
    const sortDirection = searchParams.get('sortDirection') || 'asc'

    if (!table) {
      return NextResponse.json(
        { success: false, error: 'Table name is required' },
        { status: 400 }
      )
    }

    // Validate table name to prevent SQL injection
    const validTableResult = await prisma.$queryRaw<Array<{ table_name: string }>>`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = ${table}
      AND table_type = 'BASE TABLE';
    `

    if (validTableResult.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Table not found' },
        { status: 404 }
      )
    }

    // Get column information for the table
    const columnResult = await prisma.$queryRaw<Array<{
      column_name: string
      data_type: string
      is_nullable: string
      column_default: string | null
    }>>`
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = ${table}
      ORDER BY ordinal_position;
    `

    const columns = columnResult.map(col => ({
      name: col.column_name,
      type: col.data_type,
      nullable: col.is_nullable === 'YES',
      default: col.column_default
    }))

    // Build the query
    const offset = (page - 1) * pageSize
    let whereClause = ''
    let orderClause = ''

    // Add search functionality (search across all text columns)
    if (search) {
      const textColumns = columns
        .filter(col => 
          col.type.includes('text') || 
          col.type.includes('varchar') || 
          col.type.includes('char')
        )
        .map(col => col.name)

      if (textColumns.length > 0) {
        const searchConditions = textColumns
          .map(col => `"${col}"::text ILIKE '%${search.replace(/'/g, "''")}%'`)
          .join(' OR ')
        whereClause = `WHERE ${searchConditions}`
      }
    }

    // Add sorting
    if (sortColumn && columns.some(col => col.name === sortColumn)) {
      const direction = sortDirection.toLowerCase() === 'desc' ? 'DESC' : 'ASC'
      orderClause = `ORDER BY "${sortColumn}" ${direction}`
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as count FROM "${table}" ${whereClause}`
    const countResult = await prisma.$queryRawUnsafe(countQuery)
    const totalCount = Number((countResult as any)[0]?.count || 0)

    // Get paginated data
    const dataQuery = `
      SELECT * FROM "${table}" 
      ${whereClause} 
      ${orderClause} 
      LIMIT ${pageSize} OFFSET ${offset}
    `
    const rows = await prisma.$queryRawUnsafe(dataQuery)

    return NextResponse.json({
      success: true,
      rows: rows as Record<string, any>[],
      totalCount,
      columns,
      pagination: {
        page,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    })

  } catch (error) {
    console.error('Table data error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch table data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 