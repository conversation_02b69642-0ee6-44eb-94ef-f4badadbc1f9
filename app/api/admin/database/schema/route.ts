import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// Enhanced table metadata
const tableMetadata: Record<string, {
  displayName: string
  description: string
  category: string
  icon: string
  primaryKey?: string
}> = {
  'users': {
    displayName: 'Users',
    description: 'User accounts and authentication data',
    category: 'Core',
    icon: 'Users',
    primaryKey: 'id'
  },
  'posts': {
    displayName: 'Posts',
    description: 'Blog posts and content articles',
    category: 'Content',
    icon: 'FileText',
    primaryKey: 'id'
  },
  'faculty_profiles': {
    displayName: 'Faculty Profiles',
    description: 'Faculty member profiles and information',
    category: 'Academic',
    icon: 'GraduationCap',
    primaryKey: 'id'
  },
  'departments': {
    displayName: 'Departments',
    description: 'Academic departments and divisions',
    category: 'Academic',
    icon: 'Building2',
    primaryKey: 'id'
  },
  'programs': {
    displayName: 'Programs',
    description: 'Academic degree programs',
    category: 'Academic',
    icon: 'BookOpen',
    primaryKey: 'id'
  },
  'courses': {
    displayName: 'Courses',
    description: 'Individual courses and subjects',
    category: 'Academic',
    icon: 'Calendar',
    primaryKey: 'id'
  },
  'post_categories': {
    displayName: 'Post Categories',
    description: 'Content categorization system',
    category: 'Content',
    icon: 'Tag',
    primaryKey: 'id'
  },
  'user_profiles': {
    displayName: 'User Profiles',
    description: 'Extended user profile information',
    category: 'Core',
    icon: 'User',
    primaryKey: 'id'
  },
  'accounts': {
    displayName: 'OAuth Accounts',
    description: 'Third-party authentication accounts',
    category: 'Auth',
    icon: 'Key',
    primaryKey: 'id'
  },
  'sessions': {
    displayName: 'User Sessions',
    description: 'Active user sessions',
    category: 'Auth',
    icon: 'Clock',
    primaryKey: 'id'
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all table names from the database
    const tableResult = await prisma.$queryRaw<Array<{ table_name: string }>>`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `

    const tables = []

    for (const table of tableResult) {
      const tableName = table.table_name

      try {
        // Get enhanced column information
        const columnResult = await prisma.$queryRaw<Array<{
          column_name: string
          data_type: string
          is_nullable: string
          column_default: string | null
          character_maximum_length: number | null
          numeric_precision: number | null
          numeric_scale: number | null
        }>>`
          SELECT
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length,
            numeric_precision,
            numeric_scale
          FROM information_schema.columns
          WHERE table_schema = 'public'
          AND table_name = ${tableName}
          ORDER BY ordinal_position;
        `

        // Get primary key information
        const primaryKeyResult = await prisma.$queryRaw<Array<{
          column_name: string
        }>>`
          SELECT kcu.column_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
          WHERE tc.table_schema = 'public'
            AND tc.table_name = ${tableName}
            AND tc.constraint_type = 'PRIMARY KEY';
        `

        // Get foreign key information
        const foreignKeyResult = await prisma.$queryRaw<Array<{
          column_name: string
          foreign_table_name: string
          foreign_column_name: string
        }>>`
          SELECT
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
          JOIN information_schema.constraint_column_usage ccu
            ON ccu.constraint_name = tc.constraint_name
          WHERE tc.table_schema = 'public'
            AND tc.table_name = ${tableName}
            AND tc.constraint_type = 'FOREIGN KEY';
        `

        // Get record count
        const countResult = await prisma.$queryRawUnsafe(`SELECT COUNT(*) as count FROM "${tableName}"`)
        const count = Number((countResult as any)[0]?.count || 0)

        // Get table size information
        const sizeResult = await prisma.$queryRaw<Array<{
          size_bytes: bigint
        }>>`
          SELECT pg_total_relation_size(quote_ident(${tableName})) as size_bytes;
        `

        const primaryKeys = primaryKeyResult.map(pk => pk.column_name)
        const foreignKeys = foreignKeyResult.reduce((acc, fk) => {
          acc[fk.column_name] = {
            table: fk.foreign_table_name,
            column: fk.foreign_column_name
          }
          return acc
        }, {} as Record<string, { table: string; column: string }>)

        // Format columns with enhanced metadata
        const columns = columnResult.map(col => ({
          name: col.column_name,
          type: col.data_type,
          nullable: col.is_nullable === 'YES',
          default: col.column_default,
          maxLength: col.character_maximum_length,
          precision: col.numeric_precision,
          scale: col.numeric_scale,
          isPrimary: primaryKeys.includes(col.column_name),
          isForeignKey: col.column_name in foreignKeys,
          foreignKey: foreignKeys[col.column_name] || null
        }))

        const metadata = tableMetadata[tableName] || {
          displayName: tableName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          description: `Database table: ${tableName}`,
          category: 'Other',
          icon: 'Database'
        }

        tables.push({
          name: tableName,
          ...metadata,
          count,
          columns,
          primaryKeys,
          foreignKeys: Object.keys(foreignKeys),
          size: Number(sizeResult[0]?.size_bytes || 0),
          lastModified: new Date().toISOString()
        })
      } catch (error) {
        console.error(`Error processing table ${tableName}:`, error)
        // Skip tables that cause errors but continue with others
        const metadata = tableMetadata[tableName] || {
          displayName: tableName,
          description: `Database table: ${tableName}`,
          category: 'Other',
          icon: 'Database'
        }

        tables.push({
          name: tableName,
          ...metadata,
          count: 0,
          columns: [],
          primaryKeys: [],
          foreignKeys: [],
          size: 0,
          lastModified: new Date().toISOString(),
          error: 'Failed to load table information'
        })
      }
    }

    // Calculate database statistics
    const totalRecords = tables.reduce((sum, table) => sum + table.count, 0)
    const totalColumns = tables.reduce((sum, table) => sum + table.columns.length, 0)
    const totalSize = tables.reduce((sum, table) => sum + table.size, 0)
    const avgRecordsPerTable = Math.round(totalRecords / tables.length) || 0
    const categories = [...new Set(tables.map(t => t.category))]

    return NextResponse.json({
      success: true,
      tables: tables.sort((a, b) => a.displayName.localeCompare(b.displayName)),
      statistics: {
        totalTables: tables.length,
        totalRecords,
        totalColumns,
        totalSize,
        avgRecordsPerTable,
        categories,
        lastUpdated: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Database schema error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch database schema',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}