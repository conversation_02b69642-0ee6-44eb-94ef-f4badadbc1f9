import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json()

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Query is required and must be a string' },
        { status: 400 }
      )
    }

    const trimmedQuery = query.trim()

    // Basic safety checks
    const dangerousPatterns = [
      /DROP\s+DATABASE/i,
      /DROP\s+SCHEMA/i,
      /TRUNCATE\s+TABLE/i,
      /ALTER\s+TABLE.*DROP/i,
      /DELETE\s+FROM.*WHERE\s*$/i, // DELETE without WHERE clause
      /UPDATE.*SET.*WHERE\s*$/i,   // UPDATE without WHERE clause
    ]

    const isDangerous = dangerousPatterns.some(pattern => pattern.test(trimmedQuery))
    
    if (isDangerous) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Query contains potentially dangerous operations that are not allowed',
          hint: 'Avoid DROP DATABASE, DROP SCHEMA, TRUNCATE, or DELETE/UPDATE without WHERE clauses'
        },
        { status: 403 }
      )
    }

    // Limit query length
    if (trimmedQuery.length > 10000) {
      return NextResponse.json(
        { success: false, error: 'Query is too long (max 10,000 characters)' },
        { status: 400 }
      )
    }

    const startTime = Date.now()

    try {
      // Execute the query
      const result = await prisma.$queryRawUnsafe(trimmedQuery)
      const executionTime = Date.now() - startTime

      // Determine if this was a SELECT query or a modification query
      const isSelectQuery = /^\s*SELECT/i.test(trimmedQuery)
      
      if (isSelectQuery) {
        return NextResponse.json({
          success: true,
          data: result as any[],
          executionTime,
          queryType: 'SELECT'
        })
      } else {
        // For INSERT, UPDATE, DELETE queries
        return NextResponse.json({
          success: true,
          rowsAffected: Array.isArray(result) ? result.length : 0,
          executionTime,
          queryType: 'MODIFICATION',
          message: 'Query executed successfully'
        })
      }

    } catch (queryError) {
      const executionTime = Date.now() - startTime
      
      return NextResponse.json({
        success: false,
        error: queryError instanceof Error ? queryError.message : 'Query execution failed',
        executionTime,
        query: trimmedQuery.substring(0, 200) + (trimmedQuery.length > 200 ? '...' : '')
      })
    }

  } catch (error) {
    console.error('Query API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process query request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 