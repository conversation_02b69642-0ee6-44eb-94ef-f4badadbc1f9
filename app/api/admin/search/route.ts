import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''
    const type = searchParams.get('type') || 'all' // all, users, faculty, content, departments, programs, courses
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        results: {
          users: [],
          faculty: [],
          content: [],
          departments: [],
          programs: [],
          courses: [],
          total: 0
        }
      })
    }

    const searchResults: any = {
      users: [],
      faculty: [],
      content: [],
      departments: [],
      programs: [],
      courses: [],
      total: 0
    }

    // Search Users
    if (type === 'all' || type === 'users') {
      const users = await prisma.user.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
            { profile: { 
              OR: [
                { firstName: { contains: query, mode: 'insensitive' } },
                { lastName: { contains: query, mode: 'insensitive' } },
                { bio: { contains: query, mode: 'insensitive' } }
              ]
            }}
          ]
        },
        include: {
          profile: {
            select: {
              firstName: true,
              lastName: true,
              avatarUrl: true
            }
          }
        },
        take: limit,
        orderBy: { createdAt: 'desc' }
      })

      searchResults.users = users.map(user => ({
        id: user.id,
        type: 'user',
        title: user.name || `${user.profile?.firstName || ''} ${user.profile?.lastName || ''}`.trim(),
        subtitle: user.email,
        description: `${user.role.replace('_', ' ')} • ${user.status}`,
        url: `/admin/users/${user.id}/edit`,
        avatar: user.profile?.avatarUrl,
        metadata: {
          role: user.role,
          status: user.status,
          createdAt: user.createdAt
        }
      }))
    }

    // Search Faculty
    if (type === 'all' || type === 'faculty') {
      const faculty = await prisma.facultyProfile.findMany({
        where: {
          OR: [
            { user: { name: { contains: query, mode: 'insensitive' } } },
            { user: { email: { contains: query, mode: 'insensitive' } } },
            { title: { contains: query, mode: 'insensitive' } },
            { bio: { contains: query, mode: 'insensitive' } },
            { department: { name: { contains: query, mode: 'insensitive' } } },
            { researchAreas: { some: { areaName: { contains: query, mode: 'insensitive' } } } }
          ]
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              profile: {
                select: {
                  avatarUrl: true
                }
              }
            }
          },
          department: {
            select: {
              name: true
            }
          },
          researchAreas: {
            select: {
              areaName: true
            },
            take: 3
          }
        },
        take: limit,
        orderBy: { createdAt: 'desc' }
      })

      searchResults.faculty = faculty.map(f => ({
        id: f.id,
        type: 'faculty',
        title: f.user.name || 'Unknown Faculty',
        subtitle: `${f.title} • ${f.department.name}`,
        description: f.researchAreas.map(r => r.areaName).join(', '),
        url: `/admin/faculty/${f.id}/edit`,
        avatar: f.user.profile?.avatarUrl,
        metadata: {
          department: f.department.name,
          researchAreas: f.researchAreas.length,
          userId: f.user.id
        }
      }))
    }

    // Search Content/Posts
    if (type === 'all' || type === 'content') {
      const posts = await prisma.post.findMany({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { content: { contains: query, mode: 'insensitive' } },
            { excerpt: { contains: query, mode: 'insensitive' } },
            { category: { name: { contains: query, mode: 'insensitive' } } },
            { author: { name: { contains: query, mode: 'insensitive' } } }
          ]
        },
        include: {
          author: {
            select: {
              name: true,
              email: true
            }
          },
          category: {
            select: {
              name: true
            }
          }
        },
        take: limit,
        orderBy: { createdAt: 'desc' }
      })

      searchResults.content = posts.map(post => ({
        id: post.id,
        type: 'content',
        title: post.title,
        subtitle: `By ${post.author.name} • ${post.category.name}`,
        description: post.excerpt || post.content.substring(0, 150) + '...',
        url: `/admin/posts/${post.id}/edit`,
        metadata: {
          status: post.status,
          viewCount: post.viewCount,
          publishedAt: post.publishedAt,
          author: post.author.name
        }
      }))
    }

    // Search Departments
    if (type === 'all' || type === 'departments') {
      const departments = await prisma.department.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } }
          ]
        },
        include: {
          _count: {
            select: {
              faculty: true,
              programs: true,
              courses: true
            }
          }
        },
        take: limit,
        orderBy: { createdAt: 'desc' }
      })

      searchResults.departments = departments.map(dept => ({
        id: dept.id,
        type: 'department',
        title: dept.name,
        subtitle: 'Department',
        description: dept.description || `${dept._count.faculty} faculty, ${dept._count.programs} programs, ${dept._count.courses} courses`,
        url: `/admin/departments/${dept.id}/edit`,
        metadata: {
          facultyCount: dept._count.faculty,
          programCount: dept._count.programs,
          courseCount: dept._count.courses
        }
      }))
    }

    // Search Programs
    if (type === 'all' || type === 'programs') {
      const programs = await prisma.program.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { degreeType: { contains: query, mode: 'insensitive' } },
            { department: { name: { contains: query, mode: 'insensitive' } } }
          ]
        },
        include: {
          department: {
            select: {
              name: true
            }
          }
        },
        take: limit,
        orderBy: { createdAt: 'desc' }
      })

      searchResults.programs = programs.map(program => ({
        id: program.id,
        type: 'program',
        title: program.name,
        subtitle: `${program.degreeType} • ${program.department.name}`,
        description: program.description || `${program.duration} duration`,
        url: `/admin/programs/${program.id}/edit`,
        metadata: {
          degreeType: program.degreeType,
          duration: program.duration,
          department: program.department.name,
          isActive: program.isActive
        }
      }))
    }

    // Search Courses
    if (type === 'all' || type === 'courses') {
      const courses = await prisma.course.findMany({
        where: {
          OR: [
            { code: { contains: query, mode: 'insensitive' } },
            { name: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { department: { name: { contains: query, mode: 'insensitive' } } }
          ]
        },
        include: {
          department: {
            select: {
              name: true
            }
          },
          _count: {
            select: {
              classes: true
            }
          }
        },
        take: limit,
        orderBy: { createdAt: 'desc' }
      })

      searchResults.courses = courses.map(course => ({
        id: course.id,
        type: 'course',
        title: `${course.code} - ${course.name}`,
        subtitle: `${course.credits} credits • ${course.department.name}`,
        description: course.description || `${course._count.classes} classes offered`,
        url: `/admin/courses/${course.id}/edit`,
        metadata: {
          code: course.code,
          credits: course.credits,
          department: course.department.name,
          classCount: course._count.classes,
          isActive: course.isActive
        }
      }))
    }

    // Calculate total results
    searchResults.total = Object.values(searchResults).reduce((sum: number, results: any) => {
      return sum + (Array.isArray(results) ? results.length : 0)
    }, 0)

    return NextResponse.json({
      success: true,
      query,
      results: searchResults
    })

  } catch (error) {
    console.error('Search error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
