import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import { UserRole, UserStatus } from '@prisma/client'

// Validation schema for updating users
const updateUserSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Valid email is required').optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
  role: z.enum(['STUDENT', 'FACULTY', 'COLLEGE_ADMIN', 'SYS_ADMIN']).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED']).optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: params.id },
      include: {
        profile: {
          select: {
            firstName: true,
            lastName: true,
            phone: true
          }
        },
        facultyProfile: {
          select: {
            title: true,
            department: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Remove password from response
    const { password, ...userWithoutPassword } = user

    return NextResponse.json(userWithoutPassword)
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateUserSchema.parse(body)

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Prevent users from modifying their own role/status unless they're SYS_ADMIN
    if (session.user.id === params.id && session.user.role !== 'SYS_ADMIN') {
      if (validatedData.role || validatedData.status) {
        return NextResponse.json({ 
          error: 'Cannot modify your own role or status' 
        }, { status: 403 })
      }
    }

    // Check if email is being changed and if it already exists
    if (validatedData.email && validatedData.email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: validatedData.email }
      })

      if (emailExists) {
        return NextResponse.json({ error: 'Email already exists' }, { status: 400 })
      }
    }

    // Update in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Prepare user update data
      const userUpdateData: any = {}
      
      if (validatedData.name) userUpdateData.name = validatedData.name
      if (validatedData.email) userUpdateData.email = validatedData.email
      if (validatedData.role) userUpdateData.role = validatedData.role as UserRole
      if (validatedData.status) userUpdateData.status = validatedData.status as UserStatus
      
      // Hash password if provided
      if (validatedData.password) {
        userUpdateData.password = await bcrypt.hash(validatedData.password, 12)
      }

      // Update user
      const user = await tx.user.update({
        where: { id: params.id },
        data: userUpdateData
      })

      // Update or create user profile if profile fields are provided
      if (validatedData.firstName !== undefined || validatedData.lastName !== undefined || validatedData.phone !== undefined) {
        await tx.userProfile.upsert({
          where: { userId: params.id },
          update: {
            ...(validatedData.firstName !== undefined && { firstName: validatedData.firstName }),
            ...(validatedData.lastName !== undefined && { lastName: validatedData.lastName }),
            ...(validatedData.phone !== undefined && { phone: validatedData.phone })
          },
          create: {
            userId: params.id,
            firstName: validatedData.firstName,
            lastName: validatedData.lastName,
            phone: validatedData.phone
          }
        })
      }

      return user
    })

    // Remove password from response
    const { password, ...userWithoutPassword } = result

    return NextResponse.json(userWithoutPassword)
  } catch (error) {
    console.error('Error updating user:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error', 
        details: error.errors 
      }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Prevent users from deleting themselves
    if (session.user.id === params.id) {
      return NextResponse.json({ 
        error: 'Cannot delete your own account' 
      }, { status: 403 })
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id },
      include: {
        facultyProfile: {
          include: {
            _count: {
              select: {
                classes: true,
                publications: true,
                researchProjects: true
              }
            }
          }
        },
        posts: {
          select: { id: true }
        }
      }
    })

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check for dependencies
    const hasActiveDependencies = 
      (existingUser.facultyProfile && (
        existingUser.facultyProfile._count.classes > 0 ||
        existingUser.facultyProfile._count.publications > 0 ||
        existingUser.facultyProfile._count.researchProjects > 0
      )) ||
      existingUser.posts.length > 0

    if (hasActiveDependencies) {
      return NextResponse.json({ 
        error: 'Cannot delete user with active dependencies (classes, publications, posts, etc.)',
        suggestion: 'Consider deactivating the user instead'
      }, { status: 400 })
    }

    // Delete user (this will cascade to related data)
    await prisma.user.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'User deleted successfully' })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
