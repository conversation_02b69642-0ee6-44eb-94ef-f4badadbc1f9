import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import { generateStrongPassword } from '@/lib/utils'
import { sendEmail } from '@/lib/email'

// Schema for password reset
const resetPasswordSchema = z.object({
  // Option 1: Provide a specific new password
  newPassword: z.string().min(6, 'Password must be at least 6 characters').optional(),
  
  // Option 2: Generate a random password
  generateRandom: z.boolean().optional(),
  
  // Option to send email notification
  sendEmail: z.boolean().optional().default(false)
})

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const body = await request.json()
    const validatedData = resetPasswordSchema.parse(body)

    // Determine the new password
    let newPassword: string

    if (validatedData.newPassword) {
      // Use provided password
      newPassword = validatedData.newPassword
    } else if (validatedData.generateRandom) {
      // Generate a strong random password
      newPassword = generateStrongPassword()
    } else {
      return NextResponse.json({ 
        error: 'Either provide a new password or request a random one' 
      }, { status: 400 })
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 12)

    // Update the user's password
    await prisma.user.update({
      where: { id: params.id },
      data: { password: hashedPassword }
    })

    // Send email notification if requested
    if (validatedData.sendEmail && existingUser.email) {
      try {
        await sendEmail({
          to: existingUser.email,
          subject: 'Your Password Has Been Reset',
          html: `
            <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #4F46E5;">Password Reset Notification</h2>
              <p>Hello ${existingUser.name || 'User'},</p>
              <p>Your password has been reset by an administrator.</p>
              ${validatedData.generateRandom ? `
                <div style="background-color: #F3F4F6; padding: 15px; border-radius: 5px; margin: 20px 0;">
                  <p style="margin-top: 0;"><strong>Your temporary password:</strong> ${newPassword}</p>
                  <p style="margin-bottom: 0;">Please log in and change this password immediately for security reasons.</p>
                </div>
              ` : `
                <p>If you requested this password reset, no further action is needed.</p>
                <p>If you did not request this password reset, please contact the administrator immediately.</p>
              `}
              <p style="margin-top: 30px; color: #6B7280; font-size: 14px;">This is an automated message, please do not reply.</p>
            </div>
          `
        })
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError)
        // We don't want to fail the whole operation if just the email fails
      }
    }

    // If it was a random password, return it so it can be shown to the admin
    if (validatedData.generateRandom) {
      return NextResponse.json({ 
        message: 'Password reset successfully',
        tempPassword: newPassword,
        emailSent: validatedData.sendEmail,
        user: {
          id: existingUser.id,
          name: existingUser.name,
          email: existingUser.email
        }
      })
    }

    return NextResponse.json({ 
      message: 'Password reset successfully',
      emailSent: validatedData.sendEmail,
      user: {
        id: existingUser.id,
        name: existingUser.name,
        email: existingUser.email
      }
    })
  } catch (error) {
    console.error('Error resetting password:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error', 
        details: error.errors 
      }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 