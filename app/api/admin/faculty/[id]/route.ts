import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { revalidatePath } from 'next/cache'

// Validation schema for updating faculty
const updateFacultySchema = z.object({
  // User details
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Valid email is required').optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED']).optional(),

  // Faculty profile details
  title: z.string().min(1, 'Title is required').optional(),
  departmentId: z.string().min(1, 'Department is required').optional(),
  officeLocation: z.string().optional(),
  websiteUrl: z.string().url().optional().or(z.literal('')),
  scholarId: z.string().optional(),
  bio: z.string().optional(),

  // User profile details
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
            createdAt: true,
            profile: {
              select: {
                firstName: true,
                lastName: true,
                phone: true
              }
            }
          }
        },
        department: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            publications: true,
            researchAreas: true,
            classes: true,
            officeHours: true
          }
        }
      }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty not found' }, { status: 404 })
    }

    return NextResponse.json(facultyProfile)
  } catch (error) {
    console.error('Error fetching faculty:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate the request body
    const body = await request.json()
    console.log('Received update request for faculty ID:', params.id, 'with data:', body)
    
    try {
      const validatedData = updateFacultySchema.parse(body)
      
      // Check if faculty exists
      const existingFaculty = await prisma.facultyProfile.findUnique({
        where: { id: params.id },
        include: { user: true }
      })

      if (!existingFaculty) {
        console.error('Faculty not found:', params.id)
        return NextResponse.json({ error: 'Faculty not found' }, { status: 404 })
      }

      console.log('Found existing faculty:', existingFaculty.id, 'with user ID:', existingFaculty.userId)

      // Check if email is being changed and if it already exists
      if (validatedData.email && validatedData.email !== existingFaculty.user.email) {
        const existingUser = await prisma.user.findUnique({
          where: { email: validatedData.email }
        })

        if (existingUser && existingUser.id !== existingFaculty.userId) {
          return NextResponse.json({ error: 'Email already exists' }, { status: 400 })
        }
      }

      // Check if department exists (if being changed)
      if (validatedData.departmentId) {
        const department = await prisma.department.findUnique({
          where: { id: validatedData.departmentId }
        })

        if (!department) {
          console.error('Department not found:', validatedData.departmentId)
          return NextResponse.json({ error: 'Department not found' }, { status: 400 })
        }
      }

      // Update in transaction
      console.log('Starting update transaction')
      const result = await prisma.$transaction(async (tx) => {
        // Update user if user fields are provided
        if (validatedData.name || validatedData.email || validatedData.status) {
          console.log('Updating user record')
          const userData = {
            ...(validatedData.name !== undefined && { name: validatedData.name }),
            ...(validatedData.email !== undefined && { email: validatedData.email }),
            ...(validatedData.status !== undefined && { status: validatedData.status })
          }
          console.log('User update data:', userData)
          
          await tx.user.update({
            where: { id: existingFaculty.userId },
            data: userData
          })
        }

        // Update or create user profile if profile fields are provided
        if (validatedData.firstName !== undefined || validatedData.lastName !== undefined || validatedData.phone !== undefined) {
          console.log('Updating user profile')
          const profileData = {
            ...(validatedData.firstName !== undefined && { firstName: validatedData.firstName }),
            ...(validatedData.lastName !== undefined && { lastName: validatedData.lastName }),
            ...(validatedData.phone !== undefined && { phone: validatedData.phone })
          }
          console.log('Profile update data:', profileData)
          
          await tx.userProfile.upsert({
            where: { userId: existingFaculty.userId },
            update: profileData,
            create: {
              userId: existingFaculty.userId,
              ...profileData
            }
          })
        }

        // Update faculty profile
        console.log('Updating faculty profile')
        const facultyData = {
          ...(validatedData.title !== undefined && { title: validatedData.title }),
          ...(validatedData.departmentId !== undefined && { departmentId: validatedData.departmentId }),
          ...(validatedData.officeLocation !== undefined && { officeLocation: validatedData.officeLocation }),
          ...(validatedData.websiteUrl !== undefined && { websiteUrl: validatedData.websiteUrl }),
          ...(validatedData.scholarId !== undefined && { scholarId: validatedData.scholarId }),
          ...(validatedData.bio !== undefined && { bio: validatedData.bio })
        }
        console.log('Faculty profile update data:', facultyData)
        
        const facultyProfile = await tx.facultyProfile.update({
          where: { id: params.id },
          data: facultyData,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                status: true,
                createdAt: true,
                profile: true
              }
            },
            department: {
              select: {
                id: true,
                name: true
              }
            }
          }
        })

        return facultyProfile
      })

      console.log('Update successful:', result)

      // Revalidate faculty pages
      revalidatePath('/faculty')
      revalidatePath('/admin/faculty')
      
      return NextResponse.json(result)
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        console.error('Validation error:', validationError.errors)
        return NextResponse.json({
          error: 'Validation error',
          details: validationError.errors
        }, { status: 400 })
      }
      throw validationError
    }
  } catch (error) {
    console.error('Error updating faculty:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if faculty exists
    const existingFaculty = await prisma.facultyProfile.findUnique({
      where: { id: params.id },
      include: {
        user: true,
        _count: {
          select: {
            classes: true,
            officeHours: true,
            researchProjects: true
          }
        }
      }
    })

    if (!existingFaculty) {
      return NextResponse.json({ error: 'Faculty not found' }, { status: 404 })
    }

    // Prevent admins from deleting themselves
    if (existingFaculty.userId === session.user.id) {
      return NextResponse.json({ error: 'You cannot delete your own account' }, { status: 400 })
    }

    // Delete related data and faculty profile
    await prisma.$transaction([
      // Delete related data first
      prisma.officeHourBooking.deleteMany({ 
        where: { officeHour: { facultyId: params.id } } 
      }),
      prisma.officeHour.deleteMany({ 
        where: { facultyId: params.id } 
      }),
      prisma.facultyPublication.deleteMany({ 
        where: { facultyId: params.id } 
      }),
      prisma.facultyResearchArea.deleteMany({ 
        where: { facultyId: params.id } 
      }),
      prisma.facultyEducation.deleteMany({ 
        where: { facultyId: params.id } 
      }),
      prisma.facultyTimeline.deleteMany({ 
        where: { facultyId: params.id } 
      }),
      prisma.facultyCVDocument.deleteMany({ 
        where: { facultyId: params.id } 
      }),
      prisma.courseClass.deleteMany({ 
        where: { facultyId: params.id } 
      }),
      // Delete research projects (handle project tags and requirements first)
      ...existingFaculty._count.researchProjects > 0 ? [
        prisma.projectRequirement.deleteMany({
          where: { project: { facultyId: params.id } }
        }),
        prisma.projectTag.deleteMany({
          where: { project: { facultyId: params.id } }
        }),
        prisma.researchProject.deleteMany({ 
          where: { facultyId: params.id } 
        })
      ] : [],
      
      // Delete faculty profile
      prisma.facultyProfile.delete({ where: { id: params.id } }),
      
      // Delete user (includes cascade to userProfile)
      prisma.user.delete({ where: { id: existingFaculty.userId } })
    ])

    // Revalidate faculty pages
    revalidatePath('/faculty')
    revalidatePath('/admin/faculty')

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting faculty:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
