import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { revalidatePath } from 'next/cache'

// Validation schema for updating courses
const courseUpdateSchema = z.object({
  code: z.string().min(1, 'Course code is required'),
  name: z.string().min(1, 'Course name is required'),
  description: z.string().optional(),
  credits: z.number().min(1, 'Credits must be at least 1').max(10, 'Credits cannot exceed 10'),
  departmentId: z.string().min(1, 'Department is required'),
  isActive: z.boolean().default(true),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const course = await prisma.course.findUnique({
      where: { id },
      include: {
        department: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        _count: {
          select: {
            classes: true
          }
        },
        classes: {
          include: {
            faculty: {
              include: {
                user: {
                  select: {
                    name: true,
                    email: true
                  }
                }
              }
            }
          },
          orderBy: {
            year: 'desc'
          },
          take: 5
        }
      }
    })

    if (!course) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 })
    }

    return NextResponse.json(course)
  } catch (error) {
    console.error('Error fetching course:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = courseUpdateSchema.parse(body)

    // Check if course exists
    const existingCourse = await prisma.course.findUnique({
      where: { id }
    })

    if (!existingCourse) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 })
    }

    // Check if course code conflicts with other courses
    const conflictingCourse = await prisma.course.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          { code: validatedData.code }
        ]
      }
    })

    if (conflictingCourse) {
      return NextResponse.json({ 
        error: 'Course code already exists' 
      }, { status: 400 })
    }

    // Verify department exists
    const department = await prisma.department.findUnique({
      where: { id: validatedData.departmentId }
    })

    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 400 })
    }

    // Update course
    const course = await prisma.course.update({
      where: { id },
      data: validatedData,
      include: {
        department: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        _count: {
          select: {
            classes: true
          }
        }
      }
    })

    // Revalidate courses pages
    revalidatePath('/admin/courses')
    revalidatePath('/courses')

    return NextResponse.json(course)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error updating course:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if course exists
    const existingCourse = await prisma.course.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            classes: true
          }
        }
      }
    })

    if (!existingCourse) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 })
    }

    // Check for dependencies (course classes)
    if (existingCourse._count.classes > 0) {
      return NextResponse.json({ 
        error: `Cannot delete course. It has ${existingCourse._count.classes} associated class(es). Please remove or reassign the classes first.` 
      }, { status: 400 })
    }

    // Delete course
    await prisma.course.delete({
      where: { id }
    })

    // Revalidate courses pages
    revalidatePath('/admin/courses')
    revalidatePath('/courses')

    return NextResponse.json({ message: 'Course deleted successfully' })
  } catch (error) {
    console.error('Error deleting course:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
