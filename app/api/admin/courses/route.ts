import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { revalidatePath } from 'next/cache'

// Validation schema for creating/updating courses
const courseSchema = z.object({
  code: z.string().min(1, 'Course code is required'),
  name: z.string().min(1, 'Course name is required'),
  description: z.string().optional(),
  credits: z.number().min(1, 'Credits must be at least 1').max(10, 'Credits cannot exceed 10'),
  departmentId: z.string().min(1, 'Department is required'),
  isActive: z.boolean().default(true),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const departmentId = searchParams.get('departmentId') || ''
    const credits = searchParams.get('credits')
    const isActive = searchParams.get('isActive')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (search) {
      where.OR = [
        { code: { contains: search, mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (departmentId) {
      where.departmentId = departmentId
    }

    if (credits) {
      where.credits = parseInt(credits)
    }

    if (isActive !== null && isActive !== '') {
      where.isActive = isActive === 'true'
    }

    const [courses, total] = await Promise.all([
      prisma.course.findMany({
        where,
        include: {
          department: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          _count: {
            select: {
              classes: true
            }
          }
        },
        orderBy: {
          code: 'asc'
        },
        skip,
        take: limit
      }),
      prisma.course.count({ where })
    ])

    return NextResponse.json({
      courses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching courses:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = courseSchema.parse(body)

    // Check if course code already exists
    const existingCourse = await prisma.course.findUnique({
      where: { code: validatedData.code }
    })

    if (existingCourse) {
      return NextResponse.json({ 
        error: 'Course code already exists' 
      }, { status: 400 })
    }

    // Verify department exists
    const department = await prisma.department.findUnique({
      where: { id: validatedData.departmentId }
    })

    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 400 })
    }

    // Create course
    const course = await prisma.course.create({
      data: validatedData,
      include: {
        department: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        _count: {
          select: {
            classes: true
          }
        }
      }
    })

    // Revalidate courses pages
    revalidatePath('/admin/courses')
    revalidatePath('/courses')

    return NextResponse.json(course, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error creating course:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
