import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for updating departments
const updateDepartmentSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  slug: z.string().min(1, 'Slug is required').optional(),
  description: z.string().optional(),
  headFacultyId: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const department = await prisma.department.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            faculty: true,
            programs: true,
            courses: true
          }
        },
        faculty: {
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            }
          }
        },
        programs: {
          select: {
            id: true,
            name: true,
            degreeType: true,
            isActive: true
          }
        },
        courses: {
          select: {
            id: true,
            code: true,
            name: true,
            credits: true,
            isActive: true
          }
        }
      }
    })

    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 })
    }

    return NextResponse.json(department)
  } catch (error) {
    console.error('Error fetching department:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateDepartmentSchema.parse(body)

    // Check if department exists
    const existingDepartment = await prisma.department.findUnique({
      where: { id: params.id }
    })

    if (!existingDepartment) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 })
    }

    // Check if name or slug already exists (if being changed)
    if (validatedData.name || validatedData.slug) {
      const conflictingDepartment = await prisma.department.findFirst({
        where: {
          AND: [
            { id: { not: params.id } },
            {
              OR: [
                ...(validatedData.name ? [{ name: validatedData.name }] : []),
                ...(validatedData.slug ? [{ slug: validatedData.slug }] : [])
              ]
            }
          ]
        }
      })

      if (conflictingDepartment) {
        return NextResponse.json({ 
          error: conflictingDepartment.name === validatedData.name 
            ? 'Department name already exists' 
            : 'Department slug already exists' 
        }, { status: 400 })
      }
    }

    // Check if head faculty exists (if provided)
    if (validatedData.headFacultyId) {
      const faculty = await prisma.facultyProfile.findUnique({
        where: { id: validatedData.headFacultyId }
      })

      if (!faculty) {
        return NextResponse.json({ error: 'Head faculty not found' }, { status: 400 })
      }
    }

    const department = await prisma.department.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        _count: {
          select: {
            faculty: true,
            programs: true,
            courses: true
          }
        }
      }
    })

    return NextResponse.json(department)
  } catch (error) {
    console.error('Error updating department:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error', 
        details: error.errors 
      }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if department exists
    const existingDepartment = await prisma.department.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            faculty: true,
            programs: true,
            courses: true
          }
        }
      }
    })

    if (!existingDepartment) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 })
    }

    // Check for dependencies
    const hasActiveDependencies = 
      existingDepartment._count.faculty > 0 ||
      existingDepartment._count.programs > 0 ||
      existingDepartment._count.courses > 0

    if (hasActiveDependencies) {
      return NextResponse.json({ 
        error: 'Cannot delete department with active faculty, programs, or courses',
        dependencies: {
          faculty: existingDepartment._count.faculty,
          programs: existingDepartment._count.programs,
          courses: existingDepartment._count.courses
        }
      }, { status: 400 })
    }

    // Delete department
    await prisma.department.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Department deleted successfully' })
  } catch (error) {
    console.error('Error deleting department:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
