import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { revalidatePath } from 'next/cache'

// Validation schema for updating course assignments
const classAssignmentUpdateSchema = z.object({
  courseId: z.string().min(1, 'Course is required'),
  facultyId: z.string().min(1, 'Faculty is required'),
  semester: z.enum(['Spring', 'Summer', 'Fall'], {
    errorMap: () => ({ message: 'Semester must be Spring, Summer, or Fall' })
  }),
  year: z.number().min(2020, 'Year must be 2020 or later').max(2030, 'Year cannot exceed 2030'),
  section: z.string().optional(),
  maxEnrollment: z.number().min(1, 'Max enrollment must be at least 1').max(500, 'Max enrollment cannot exceed 500'),
  schedule: z.object({
    days: z.array(z.string()).min(1, 'At least one day must be selected'),
    time: z.string().min(1, 'Time is required'),
    location: z.string().min(1, 'Location is required')
  }),
  syllabusUrl: z.string().url().optional().or(z.literal('')),
  description: z.string().optional(),
  status: z.enum(['upcoming', 'current', 'past']).default('upcoming')
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const courseClass = await prisma.courseClass.findUnique({
      where: { id },
      include: {
        course: {
          include: {
            department: {
              select: {
                name: true,
                slug: true
              }
            }
          }
        },
        faculty: {
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            },
            department: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    if (!courseClass) {
      return NextResponse.json({ error: 'Course assignment not found' }, { status: 404 })
    }

    return NextResponse.json(courseClass)
  } catch (error) {
    console.error('Error fetching course assignment:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = classAssignmentUpdateSchema.parse(body)

    // Check if course assignment exists
    const existingAssignment = await prisma.courseClass.findUnique({
      where: { id }
    })

    if (!existingAssignment) {
      return NextResponse.json({ error: 'Course assignment not found' }, { status: 404 })
    }

    // Check if course exists and is active
    const course = await prisma.course.findUnique({
      where: { id: validatedData.courseId },
      include: {
        department: {
          select: {
            name: true
          }
        }
      }
    })

    if (!course || !course.isActive) {
      return NextResponse.json({ error: 'Course not found or inactive' }, { status: 400 })
    }

    // Check if faculty exists
    const faculty = await prisma.facultyProfile.findUnique({
      where: { id: validatedData.facultyId },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    if (!faculty) {
      return NextResponse.json({ error: 'Faculty not found' }, { status: 400 })
    }

    // Check for duplicate assignment (same course, faculty, semester, year, section) excluding current
    const duplicateAssignment = await prisma.courseClass.findFirst({
      where: {
        courseId: validatedData.courseId,
        facultyId: validatedData.facultyId,
        semester: validatedData.semester,
        year: validatedData.year,
        section: validatedData.section || null,
        id: { not: id }
      }
    })

    if (duplicateAssignment) {
      return NextResponse.json({ 
        error: 'This course assignment already exists for the specified semester and section' 
      }, { status: 400 })
    }

    // Update course assignment
    const updatedCourseClass = await prisma.courseClass.update({
      where: { id },
      data: {
        ...validatedData,
        syllabusUrl: validatedData.syllabusUrl || null,
        description: validatedData.description || null,
        section: validatedData.section || null
      },
      include: {
        course: {
          include: {
            department: {
              select: {
                name: true,
                slug: true
              }
            }
          }
        },
        faculty: {
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            },
            department: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    // Revalidate relevant pages
    revalidatePath('/admin/classes')
    revalidatePath('/faculty-portal/courses')

    return NextResponse.json(updatedCourseClass)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error updating course assignment:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if course assignment exists
    const existingAssignment = await prisma.courseClass.findUnique({
      where: { id }
    })

    if (!existingAssignment) {
      return NextResponse.json({ error: 'Course assignment not found' }, { status: 404 })
    }

    // Delete course assignment
    await prisma.courseClass.delete({
      where: { id }
    })

    // Revalidate relevant pages
    revalidatePath('/admin/classes')
    revalidatePath('/faculty-portal/courses')

    return NextResponse.json({ message: 'Course assignment deleted successfully' })
  } catch (error) {
    console.error('Error deleting course assignment:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
