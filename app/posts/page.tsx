'use client'

import <PERSON> from "next/link"
import { ArrowRight, Search, Tag, Calendar, FileText } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useState } from "react"

// Placeholder data - in a real app, this would come from a CMS or database
// Consider moving this to a shared lib/data.ts file if used in multiple places
const allPosts = [
  {
    id: "1",
    slug: "welcome-to-our-new-blog",
    title: "Welcome to Our New Blog!",
    date: "October 26, 2023",
    excerpt: "We're excited to launch our new blog. Stay tuned for updates, insights, and stories from our vibrant community.",
    category: "College News",
    author: "College Admin",
    tags: ["announcement", "blog", "community"],
    imageUrl: "/images/campus/main-building.jpg",
    isFeatured: true,
  },
  {
    id: "2",
    slug: "annual-tech-symposium-2023",
    title: "Highlights from the Annual Tech Symposium 2023",
    date: "October 24, 2023",
    excerpt: "Our annual Tech Symposium was a massive success, featuring groundbreaking talks and innovative student projects showcasing the future of technology.",
    category: "Events",
    author: "Tech Department",
    tags: ["event", "technology", "students", "innovation"],
    imageUrl: "/placeholder.svg?height=400&width=600",
  },
  {
    id: "3",
    slug: "faculty-spotlight-dr-jane-doe",
    title: "Faculty Spotlight: Dr. Jane Doe on AI Ethics",
    date: "October 20, 2023",
    excerpt: "Dr. Jane Doe from our Computer Science department shares her valuable insights on the future of AI ethics and its implications across industries.",
    category: "Faculty Insights",
    author: "Research Team",
    tags: ["ai", "ethics", "faculty", "computer science"],
    imageUrl: "/placeholder.svg?height=400&width=600",
  },
  {
    id: "4",
    slug: "new-sustainability-initiative",
    title: "Launching Our Campus-Wide Sustainability Initiative",
    date: "October 18, 2023",
    excerpt: "Learn about our new green initiatives aimed at making our campus more environmentally friendly and sustainable for future generations.",
    category: "College News",
    author: "Sustainability Committee",
    tags: ["sustainability", "campus", "environment", "initiative"],
    imageUrl: "/placeholder.svg?height=400&width=600",
  },
  {
    id: "5",
    slug: "student-achievements-robotics-competition",
    title: "Students Shine at National Robotics Competition",
    date: "October 15, 2023",
    excerpt: "Our robotics team secured top honors at the national competition, showcasing their exceptional skills and dedication. Read about their journey!",
    category: "Student Achievements",
    author: "Student Affairs",
    tags: ["students", "robotics", "competition", "achievements"],
    imageUrl: "/placeholder.svg?height=400&width=600",
  },
]

// Get all unique categories
const categories = Array.from(new Set(allPosts.map(post => post.category)));

export default function AllPostsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeCategory, setActiveCategory] = useState("all");

  // Filter posts based on search term and active category
  const filteredPosts = allPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = activeCategory === "all" || post.category === activeCategory;

    return matchesSearch && matchesCategory;
  });

  // Get featured post if any
  const featuredPost = allPosts.find(post => post.isFeatured);

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col bg-background">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1 py-10 md:py-16" tabIndex={-1}>
          <div className="container mx-auto px-4 md:px-6">
            <div className="mb-12 text-center">
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl text-primary">
                News, Events & Insights
              </h1>
              <p className="mt-4 text-xl text-muted-foreground max-w-2xl mx-auto">
                Explore the latest updates, stories, and announcements from our college community.
              </p>
            </div>

            {/* Search and filter */}
            <div className="mb-8 space-y-4">
              <div className="relative max-w-lg mx-auto">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search posts..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <Tabs defaultValue="all" className="w-full" onValueChange={setActiveCategory}>
                <TabsList className="w-full max-w-3xl mx-auto justify-start overflow-x-auto py-2">
                  <TabsTrigger value="all" className="rounded-full">All</TabsTrigger>
                  {categories.map(category => (
                    <TabsTrigger
                      key={category}
                      value={category}
                      className="rounded-full"
                    >
                      {category}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>

            {/* Featured post */}
            {featuredPost && activeCategory === "all" && searchTerm === "" && (
              <div className="mb-12">
                <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Featured Post
                </h2>
                <div className="bg-card rounded-xl overflow-hidden shadow-lg">
                  <div className="md:flex">
                    <div className="md:w-1/2">
                      <img
                        src={featuredPost.imageUrl || "/images/campus/main-building.jpg"}
                        alt={featuredPost.title}
                        className="h-64 md:h-full w-full object-cover"
                      />
                    </div>
                    <div className="p-6 md:w-1/2 flex flex-col justify-between">
                      <div>
                        <Badge className="mb-2">{featuredPost.category}</Badge>
                        <h3 className="text-2xl font-bold mb-3">
                          <Link href={`/posts/${featuredPost.slug}`} className="hover:text-primary transition-colors">
                            {featuredPost.title}
                          </Link>
                        </h3>
                        <p className="text-muted-foreground mb-4">{featuredPost.excerpt}</p>
                      </div>
                      <div className="mt-4">
                        <div className="flex items-center text-sm text-muted-foreground mb-4">
                          <Calendar className="mr-2 h-4 w-4" />
                          <span>{featuredPost.date}</span>
                        </div>
                        <Button asChild>
                          <Link href={`/posts/${featuredPost.slug}`}>
                            Read Full Article <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* All posts */}
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {filteredPosts.map((post) => (
                <Card key={post.id} className="flex flex-col overflow-hidden rounded-lg shadow-lg border-0 transition-all duration-300 hover:shadow-xl">
                  <div className="h-48 overflow-hidden">
                    <img
                      src={post.imageUrl || "/placeholder.svg?height=400&width=600"}
                      alt={post.title}
                      className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                    />
                  </div>
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline" className="text-xs font-medium">{post.category}</Badge>
                      <span className="text-xs text-muted-foreground">{post.date}</span>
                    </div>
                    <CardTitle className="line-clamp-2">
                      <Link href={`/posts/${post.slug}`} className="hover:text-primary transition-colors">
                        {post.title}
                      </Link>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex-grow">
                    <p className="text-sm text-muted-foreground line-clamp-3">{post.excerpt}</p>
                  </CardContent>
                  <CardFooter className="flex flex-col items-start gap-4 pt-4 border-t">
                    <div className="flex flex-wrap gap-2">
                      {post.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {post.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">+{post.tags.length - 3}</Badge>
                      )}
                    </div>
                    <Link
                      href={`/posts/${post.slug}`}
                      className="text-sm font-medium text-primary hover:underline flex items-center"
                    >
                      Read More <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </CardFooter>
                </Card>
              ))}
            </div>

            {/* Empty state */}
            {filteredPosts.length === 0 && (
              <div className="text-center py-12">
                <h3 className="text-xl font-medium mb-2">No posts found</h3>
                <p className="text-muted-foreground mb-6">Try adjusting your search or filter criteria</p>
                <Button onClick={() => {setSearchTerm(""); setActiveCategory("all");}}>
                  Reset Filters
                </Button>
              </div>
            )}

            {/* Newsletter subscription */}
            <div className="mt-16 bg-muted rounded-xl p-8 text-center">
              <h2 className="text-2xl font-bold mb-2">Stay Updated</h2>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Subscribe to our newsletter to receive the latest posts and updates directly in your inbox.
              </p>
              <form className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-grow"
                  required
                />
                <Button type="submit">Subscribe</Button>
              </form>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
}