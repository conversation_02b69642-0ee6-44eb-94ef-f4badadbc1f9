import Link from "next/link"
import { ArrowRight, Search, Tag, Calendar, FileText } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { PostsPageClient } from "@/components/posts/PostsPageClient"
import { prisma } from "@/lib/prisma"
import { PostStatus } from "@prisma/client"

async function getPostsData() {
  const [posts, categories] = await Promise.all([
    prisma.post.findMany({
      where: { status: PostStatus.PUBLISHED },
      include: {
        author: {
          select: {
            name: true,
            email: true
          }
        },
        category: {
          select: {
            name: true,
            slug: true,
            color: true
          }
        },
        tags: {
          include: {
            tag: {
              select: {
                name: true,
                slug: true
              }
            }
          }
        }
      },
      orderBy: {
        publishedAt: 'desc'
      }
    }),
    prisma.category.findMany({
      include: {
        _count: {
          select: {
            posts: {
              where: {
                status: PostStatus.PUBLISHED
              }
            }
          }
        }
      },
      orderBy: { name: 'asc' }
    })
  ])

  return { posts, categories }
}

export default async function AllPostsPage() {
  const { posts, categories } = await getPostsData()

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col bg-background">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1 py-10 md:py-16" tabIndex={-1}>
          <PostsPageClient posts={posts} categories={categories} />
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
}