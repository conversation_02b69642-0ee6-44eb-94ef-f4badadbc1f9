import Link from "next/link"
import { <PERSON><PERSON>ef<PERSON> } from "lucide-react"
import Head<PERSON> from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import BlogPostContent from "./blog-post-content"

// Placeholder data - in a real app, this would come from a CMS or database
// This should be the same data source as in app/posts/page.tsx
const allPosts = [
  {
    id: "1",
    slug: "welcome-to-our-new-blog",
    title: "Welcome to Our New Blog!",
    date: "October 26, 2023",
    excerpt: "We're excited to launch our new blog. Stay tuned for updates, insights, and stories from our vibrant community.",
    content: "<p>This is the full content of our first blog post. We plan to share regular updates about college life, academic achievements, upcoming events, and much more.</p><p>Our goal is to create a platform for communication and engagement with students, faculty, alumni, and prospective applicants. We believe that a strong online presence is key to fostering a connected and informed community.</p><h2>What to Expect</h2><ul><li>Latest college news and announcements</li><li>Highlights from campus events</li><li>Faculty research and publications</li><li>Student achievements and stories</li><li>Important academic dates and deadlines</li></ul><p>We encourage you to subscribe to our newsletter (coming soon!) to get the latest posts delivered directly to your inbox. You can also follow us on our social media channels.</p><p>Thank you for visiting, and we look forward to sharing our journey with you!</p>",
    category: "College News",
    author: "College Admin",
    authorAvatar: "/placeholder.svg?height=100&width=100",
    authorBio: "The College Admin team manages all official communications for the institution.",
    readingTime: "3 min read",
    viewCount: 1245,
    tags: ["announcement", "blog", "community"],
    imageUrl: "/placeholder.svg?height=600&width=1200", // Placeholder image
    imageAlt: "Abstract representation of a blog post"
  },
  {
    id: "2",
    slug: "annual-tech-symposium-2023",
    title: "Highlights from the Annual Tech Symposium 2023",
    date: "October 24, 2023",
    excerpt: "Our annual Tech Symposium was a massive success, featuring groundbreaking talks and innovative student projects showcasing the future of technology.",
    content: "<p>The Annual Tech Symposium 2023 brought together students, faculty, and industry leaders to discuss the latest trends and innovations in technology. The event featured keynote speeches, panel discussions, and hands-on workshops.</p><h3>Key Highlights:</h3><ul><li>Keynote by Dr. Alex Chen on Quantum Computing.</li><li>Panel discussion on AI in Healthcare.</li><li>Student project showcase with over 50 entries.</li><li>Networking opportunities with tech companies.</li></ul><p>We extend our heartfelt thanks to all speakers, participants, and organizers for making this event a grand success. The energy and enthusiasm were palpable, and we are already looking forward to next year's symposium!</p>",
    category: "Events",
    author: "Tech Department",
    authorAvatar: "/images/faculty/default-avatar.svg",
    authorBio: "The Technology Department promotes innovation and technological advancement across campus.",
    readingTime: "4 min read",
    viewCount: 892,
    tags: ["event", "technology", "students", "innovation", "symposium"],
    imageUrl: "/images/campus/research-facilities.jpg", // Placeholder image
    imageAlt: "People at a tech conference"

  },
  // Add more posts here, including the faculty spotlight and others from allPosts
    {
    id: "3",
    slug: "faculty-spotlight-dr-jane-doe",
    title: "Faculty Spotlight: Dr. Jane Doe on AI Ethics",
    date: "October 20, 2023",
    excerpt: "Dr. Jane Doe from our Computer Science department shares her valuable insights on the future of AI ethics and its implications across industries.",
    content: "<p>In this faculty spotlight, we sit down with Dr. Jane Doe, a leading researcher in Artificial Intelligence and Ethics from our School of Computer Science. Dr. Doe discusses the pressing ethical challenges posed by rapid advancements in AI, from algorithmic bias to autonomous decision-making.</p><blockquote><p>\"As AI becomes more integrated into our daily lives, it's crucial that we proactively address the ethical frameworks that govern its development and deployment.\" - Dr. Jane Doe</p></blockquote><p>Dr. Doe also shared information about her upcoming research paper on accountable AI systems and a new course she is developing on AI Ethics for undergraduate students.</p>",
    category: "Faculty Insights",
    author: "Research Team",
    authorAvatar: "/placeholder.svg?height=100&width=100",
    authorBio: "The Research Team highlights groundbreaking work being done by our faculty members.",
    readingTime: "5 min read",
    viewCount: 765,
    tags: ["ai", "ethics", "faculty", "computer science", "research"],
    imageUrl: "/placeholder.svg?height=600&width=1200", // Placeholder image
    imageAlt: "Portrait of Dr. Jane Doe"
  },
  {
    id: "4",
    slug: "new-sustainability-initiative",
    title: "Launching Our Campus-Wide Sustainability Initiative",
    date: "October 18, 2023",
    excerpt: "Learn about our new green initiatives aimed at making our campus more environmentally friendly and sustainable for future generations.",
    content: "<p>Today, we are proud to announce the launch of our comprehensive sustainability initiative that will transform our campus operations over the next five years. This initiative represents our commitment to environmental stewardship and responsible resource management.</p><h2>Key Components</h2><ul><li>Installation of solar panels on major campus buildings</li><li>Implementation of a campus-wide recycling and composting program</li><li>Transition to energy-efficient lighting and appliances</li><li>Development of sustainable transportation options</li></ul><p>These efforts will not only reduce our carbon footprint but also serve as educational opportunities for our students. We believe that by modeling sustainable practices, we can inspire the next generation of environmental leaders.</p>",
    category: "College News",
    author: "Sustainability Committee",
    authorAvatar: "/placeholder.svg?height=100&width=100",
    authorBio: "The Sustainability Committee works to make our campus greener and more environmentally friendly.",
    readingTime: "4 min read",
    viewCount: 612,
    tags: ["sustainability", "campus", "environment", "initiative"],
    imageUrl: "/placeholder.svg?height=600&width=1200",
    imageAlt: "Green campus initiative illustration"
  },
  {
    id: "5",
    slug: "student-achievements-robotics-competition",
    title: "Students Shine at National Robotics Competition",
    date: "October 15, 2023",
    excerpt: "Our robotics team secured top honors at the national competition, showcasing their exceptional skills and dedication. Read about their journey!",
    content: "<p>We are thrilled to announce that our student robotics team, the TechTitans, has won first place at the National Collegiate Robotics Competition. This prestigious event featured teams from over 50 universities across the country.</p><h2>The Winning Project</h2><p>The TechTitans developed an autonomous rescue robot designed to navigate difficult terrain and identify individuals in need of assistance during disaster scenarios. Their innovative use of machine learning algorithms for object recognition set their project apart from the competition.</p><h2>Team Members</h2><ul><li>Sarah Johnson (Team Lead, Senior, Electrical Engineering)</li><li>Michael Chen (Software Developer, Junior, Computer Science)</li><li>Aisha Patel (Hardware Engineer, Senior, Mechanical Engineering)</li><li>David Rodriguez (AI Specialist, Sophomore, Computer Science)</li></ul><p>This achievement represents countless hours of hard work, collaboration, and problem-solving. We are incredibly proud of what these students have accomplished and look forward to seeing what they do next.</p>",
    category: "Student Achievements",
    author: "Student Affairs",
    authorAvatar: "/placeholder.svg?height=100&width=100",
    authorBio: "Student Affairs highlights the accomplishments and activities of our amazing students.",
    readingTime: "6 min read",
    viewCount: 934,
    tags: ["students", "robotics", "competition", "achievements"],
    imageUrl: "/placeholder.svg?height=600&width=1200",
    imageAlt: "Students with robotics project"
  },
];

// Function to fetch post data by slug (simulated)
async function getPostBySlug(slug: string) {
  // In a real app, you would fetch this from a database or CMS
  return allPosts.find(post => post.slug === slug);
}

// Function to get related posts based on tags and category
function getRelatedPosts(currentPost: any, count = 3) {
  // Filter out the current post and get posts with matching tags or category
  return allPosts
    .filter(post => post.id !== currentPost.id)
    .sort((a, b) => {
      // Count matching tags
      const aMatchingTags = a.tags.filter((tag: string) => currentPost.tags.includes(tag)).length;
      const bMatchingTags = b.tags.filter((tag: string) => currentPost.tags.includes(tag)).length;

      // Prioritize by matching tags, then by category match
      if (aMatchingTags !== bMatchingTags) {
        return bMatchingTags - aMatchingTags; // More matching tags first
      }

      // If same number of matching tags, check category match
      const aMatchesCategory = a.category === currentPost.category ? 1 : 0;
      const bMatchesCategory = b.category === currentPost.category ? 1 : 0;

      return bMatchesCategory - aMatchesCategory;
    })
    .slice(0, count);
}

export default function PostPage({ params }: { params: { slug: string } }) {
  // Get post data
  const post = allPosts.find(post => post.slug === params.slug);

  if (!post) {
    // Handle post not found, e.g., redirect to 404 page or show a message
    return (
        <PageTransition transitionType="fade" duration={300}>
            <div className="flex min-h-screen flex-col">
                <SkipLink />
                <Header />
                <main id="main-content" className="flex-1 py-10 md:py-16" tabIndex={-1}>
                    <div className="px-4 md:px-6 text-center">
                        <h1 className="text-3xl font-bold">Post not found</h1>
                        <p className="mt-4">
                            Sorry, we couldn't find the post you were looking for.
                        </p>
                        <Button asChild className="mt-8">
                            <Link href="/posts">Back to all posts</Link>
                        </Button>
                    </div>
                </main>
                <Footer />
            </div>
        </PageTransition>
    );
  }

  // Get related posts
  const relatedPosts = getRelatedPosts(post);

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col bg-background">
        <SkipLink />
        <Header />

        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Back navigation */}
          <div className="container mx-auto px-4 md:px-6 pt-8">
            <Button variant="outline" size="sm" asChild>
              <Link href="/posts" className="inline-flex items-center text-muted-foreground hover:text-primary">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to all posts
              </Link>
            </Button>
          </div>

          {/* Client component with all interactive elements */}
          <BlogPostContent post={post} relatedPosts={relatedPosts} />
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
}

// This function is needed for Next.js to know which slugs to pre-render at build time if using SSG.
// If you are fetching data dynamically (SSR or client-side), you might not need this specific function,
// or it would fetch from your actual data source.
export async function generateStaticParams() {
    // In a real app, fetch all post slugs from your CMS/database
    return allPosts.map(post => ({
        slug: post.slug,
    }));
}