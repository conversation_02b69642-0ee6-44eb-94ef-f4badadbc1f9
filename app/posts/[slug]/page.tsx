import Link from "next/link"
import { Arrow<PERSON>ef<PERSON> } from "lucide-react"
import Head<PERSON> from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import BlogPostContent from "./blog-post-content"
import { prisma } from "@/lib/prisma"
import { PostStatus } from "@prisma/client"
import { notFound } from "next/navigation"

// Function to fetch post data by slug from database
async function getPostBySlug(slug: string) {
  const post = await prisma.post.findUnique({
    where: {
      slug,
      status: PostStatus.PUBLISHED
    },
    include: {
      author: {
        select: {
          name: true,
          email: true,
          profile: {
            select: {
              avatarUrl: true,
              bio: true
            }
          }
        }
      },
      category: {
        select: {
          name: true,
          slug: true,
          color: true
        }
      },
      tags: {
        include: {
          tag: {
            select: {
              name: true,
              slug: true
            }
          }
        }
      }
    }
  })

  if (!post) return null

  // Increment view count
  await prisma.post.update({
    where: { id: post.id },
    data: { viewCount: { increment: 1 } }
  })

  return post
}

// Function to get related posts based on tags and category
async function getRelatedPosts(currentPost: any, count = 3) {
  // Extract valid tag IDs, filtering out any undefined values
  const tagIds = currentPost.tags
    ?.map((t: any) => t.tag?.id)
    .filter((id: any) => id !== undefined && id !== null) || []

  // Build the OR condition - always include category match
  const orConditions: any[] = [
    { categoryId: currentPost.categoryId }
  ]

  // Only add tag condition if we have valid tag IDs
  if (tagIds.length > 0) {
    orConditions.push({
      tags: {
        some: {
          tagId: {
            in: tagIds
          }
        }
      }
    })
  }

  const relatedPosts = await prisma.post.findMany({
    where: {
      AND: [
        { status: PostStatus.PUBLISHED },
        { id: { not: currentPost.id } },
        {
          OR: orConditions
        }
      ]
    },
    include: {
      author: {
        select: {
          name: true,
          email: true
        }
      },
      category: {
        select: {
          name: true,
          slug: true,
          color: true
        }
      }
    },
    take: count,
    orderBy: {
      publishedAt: 'desc'
    }
  })

  return relatedPosts
}

interface PageProps {
  params: Promise<{ slug: string }>
}

export default async function PostPage({ params }: PageProps) {
  const { slug } = await params

  // Get post data from database
  const post = await getPostBySlug(slug)

  if (!post) {
    // Handle post not found, e.g., redirect to 404 page or show a message
    return (
        <PageTransition transitionType="fade" duration={300}>
            <div className="flex min-h-screen flex-col">
                <SkipLink />
                <Header />
                <main id="main-content" className="flex-1 py-10 md:py-16" tabIndex={-1}>
                    <div className="px-4 md:px-6 text-center">
                        <h1 className="text-3xl font-bold">Post not found</h1>
                        <p className="mt-4">
                            Sorry, we couldn't find the post you were looking for.
                        </p>
                        <Button asChild className="mt-8">
                            <Link href="/posts">Back to all posts</Link>
                        </Button>
                    </div>
                </main>
                <Footer />
            </div>
        </PageTransition>
    );
  }

  // Get related posts
  const relatedPosts = await getRelatedPosts(post);

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col bg-background">
        <SkipLink />
        <Header />

        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Back navigation */}
          <div className="container mx-auto px-4 md:px-6 pt-8">
            <Button variant="outline" size="sm" asChild>
              <Link href="/posts" className="inline-flex items-center text-muted-foreground hover:text-primary">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to all posts
              </Link>
            </Button>
          </div>

          {/* Client component with all interactive elements */}
          <BlogPostContent post={post} relatedPosts={relatedPosts} />
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
}

// Generate static params for published posts
export async function generateStaticParams() {
    const posts = await prisma.post.findMany({
        where: { status: PostStatus.PUBLISHED },
        select: { slug: true }
    });

    return posts.map(post => ({
        slug: post.slug,
    }));
}