import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { DepartmentForm } from "@/components/admin/DepartmentForm"

async function getFaculty() {
  return await prisma.facultyProfile.findMany({
    select: {
      id: true,
      title: true,
      user: {
        select: {
          name: true
        }
      }
    },
    orderBy: {
      user: {
        name: 'asc'
      }
    }
  })
}

export default async function NewDepartmentPage() {
  // Require admin access
  await requireAdmin()
  
  const faculty = await getFaculty()

  return (
    <DepartmentForm faculty={faculty} />
  )
}
