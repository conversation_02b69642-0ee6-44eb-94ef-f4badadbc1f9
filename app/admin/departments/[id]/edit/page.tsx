import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { DepartmentForm } from "@/components/admin/DepartmentForm"
import { notFound } from "next/navigation"

async function getDepartmentData(id: string) {
  const department = await prisma.department.findUnique({
    where: { id },
    select: {
      id: true,
      name: true,
      slug: true,
      description: true,
      headFacultyId: true
    }
  })

  if (!department) {
    notFound()
  }

  return department
}

async function getFaculty() {
  return await prisma.facultyProfile.findMany({
    select: {
      id: true,
      title: true,
      user: {
        select: {
          name: true
        }
      }
    },
    orderBy: {
      user: {
        name: 'asc'
      }
    }
  })
}

export default async function EditDepartmentPage({ params }: { params: Promise<{ id: string }> }) {
  // Require admin access
  await requireAdmin()

  const { id } = await params

  const [department, faculty] = await Promise.all([
    getDepartmentData(id),
    getFaculty()
  ])

  const initialData = {
    name: department.name,
    slug: department.slug,
    description: department.description || '',
    headFacultyId: department.headFacultyId || '',
  }

  return (
    <DepartmentForm
      departmentId={id}
      initialData={initialData}
      faculty={faculty}
    />
  )
}
