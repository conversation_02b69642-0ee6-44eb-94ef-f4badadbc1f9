import { prisma } from "@/lib/prisma"
import { requireAdmin } from "@/lib/auth-utils"
import { DepartmentsClient } from "@/components/admin/DepartmentsClient"

async function getDepartmentsData() {
  const departments = await prisma.department.findMany({
    include: {
      _count: {
        select: {
          faculty: true,
          programs: true,
          courses: true
        }
      },
      faculty: {
        take: 3, // Get first 3 faculty for preview
        include: {
          user: {
            select: {
              name: true,
              email: true
            }
          }
        }
      },
      programs: {
        take: 3, // Get first 3 programs for preview
        select: {
          name: true,
          degreeType: true
        }
      }
    },
    orderBy: {
      name: 'asc'
    }
  })

  const totalFaculty = departments.reduce((sum, dept) => sum + dept._count.faculty, 0)
  const totalPrograms = departments.reduce((sum, dept) => sum + dept._count.programs, 0)
  const totalCourses = departments.reduce((sum, dept) => sum + dept._count.courses, 0)

  return {
    departments,
    totalDepartments: departments.length,
    totalFaculty,
    totalPrograms,
    totalCourses
  }
}

export default async function DepartmentsPage() {
  // Require admin access
  await requireAdmin()

  const data = await getDepartmentsData()

  return (
    <DepartmentsClient
      departments={data.departments}
      totalDepartments={data.totalDepartments}
      totalFaculty={data.totalFaculty}
      totalPrograms={data.totalPrograms}
      totalCourses={data.totalCourses}
    />
  )
}
