import { Suspense } from 'react'
import { DatabaseDebugClient } from '@/components/admin/DatabaseDebugClient'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Database, AlertTriangle, Shield, Zap } from 'lucide-react'
import './database-debug.css'

export default function DatabaseDebugPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="space-y-8 p-6">
        {/* Enhanced Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-white shadow-2xl">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="rounded-xl bg-white/20 p-3 backdrop-blur-sm">
                    <Database className="h-8 w-8" />
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold tracking-tight">Database Explorer</h1>
                    <p className="text-blue-100 text-lg">
                      Advanced database debugging and exploration tool
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 rounded-full bg-amber-500/20 px-4 py-2 backdrop-blur-sm">
                  <Shield className="h-5 w-5 text-amber-200" />
                  <span className="text-sm font-medium text-amber-100">Admin Access</span>
                </div>
                <div className="flex items-center space-x-2 rounded-full bg-green-500/20 px-4 py-2 backdrop-blur-sm">
                  <Zap className="h-5 w-5 text-green-200" />
                  <span className="text-sm font-medium text-green-100">Live Data</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Decorative elements */}
          <div className="absolute -right-20 -top-20 h-40 w-40 rounded-full bg-white/10"></div>
          <div className="absolute -bottom-16 -left-16 h-32 w-32 rounded-full bg-white/5"></div>
        </div>

        {/* Enhanced Warning Card */}
        <Card className="border-amber-200/50 bg-gradient-to-r from-amber-50 to-orange-50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-amber-800">
              <div className="rounded-lg bg-amber-100 p-2 mr-3">
                <AlertTriangle className="h-5 w-5 text-amber-600" />
              </div>
              Security Notice
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="rounded-lg bg-amber-100/50 p-4">
              <p className="text-sm text-amber-800 leading-relaxed">
                <strong>⚠️ Production Environment:</strong> This tool provides direct database access. 
                All operations are logged and monitored. Use responsibly and always backup data before modifications.
              </p>
              <div className="mt-3 flex items-center space-x-4 text-xs text-amber-700">
                <span className="flex items-center">
                  <div className="mr-1 h-2 w-2 rounded-full bg-green-500"></div>
                  Read Operations: Safe
                </span>
                <span className="flex items-center">
                  <div className="mr-1 h-2 w-2 rounded-full bg-yellow-500"></div>
                  Write Operations: Monitored
                </span>
                <span className="flex items-center">
                  <div className="mr-1 h-2 w-2 rounded-full bg-red-500"></div>
                  Dangerous Operations: Blocked
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Loading State */}
        <Suspense fallback={
          <Card className="border-0 bg-white/70 backdrop-blur-sm shadow-xl">
            <CardContent className="flex items-center justify-center py-16">
              <div className="text-center space-y-4">
                <div className="relative">
                  <Database className="h-12 w-12 text-blue-500 animate-pulse mx-auto" />
                  <div className="absolute inset-0 rounded-full border-4 border-blue-200 border-t-blue-500 animate-spin"></div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-gray-900">Initializing Database Explorer</h3>
                  <p className="text-sm text-gray-600">Connecting to database and loading schema...</p>
                </div>
                <div className="flex justify-center space-x-1">
                  <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce"></div>
                  <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
              </div>
            </CardContent>
          </Card>
        }>
          <DatabaseDebugClient />
        </Suspense>
      </div>
    </div>
  )
} 