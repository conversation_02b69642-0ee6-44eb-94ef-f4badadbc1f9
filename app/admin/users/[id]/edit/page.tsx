import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { UserForm } from "@/components/admin/UserForm"
import { notFound } from "next/navigation"

async function getUserData(id: string) {
  const user = await prisma.user.findUnique({
    where: { id },
    include: {
      profile: {
        select: {
          firstName: true,
          lastName: true,
          phone: true
        }
      }
    }
  })

  if (!user) {
    notFound()
  }

  return user
}

export default async function EditUserPage({ params }: { params: { id: string } }) {
  // Require admin access
  await requireAdmin()
  
  const user = await getUserData(params.id)

  const initialData = {
    name: user.name || '',
    email: user.email,
    role: user.role,
    status: user.status,
    firstName: user.profile?.firstName || '',
    lastName: user.profile?.lastName || '',
    phone: user.profile?.phone || '',
  }

  return (
    <UserForm 
      userId={params.id}
      initialData={initialData}
    />
  )
}
