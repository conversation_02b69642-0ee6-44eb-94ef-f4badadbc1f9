import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { FileText, Plus, Search, Filter, MoreHorizontal, Eye, Calendar, User } from "lucide-react"
import { prisma } from "@/lib/prisma"
import { requireAdmin } from "@/lib/auth-utils"
import { PostStatus } from "@prisma/client"
import Link from "next/link"

async function getPostsData() {
  const [posts, categories, statusStats] = await Promise.all([
    prisma.post.findMany({
      include: {
        author: {
          select: {
            name: true,
            email: true
          }
        },
        category: {
          select: {
            name: true,
            color: true
          }
        },
        tags: {
          include: {
            tag: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 50 // Limit for performance
    }),
    prisma.category.findMany({
      include: {
        _count: {
          select: {
            posts: true
          }
        }
      }
    }),
    // Count posts by status
    Promise.all([
      prisma.post.count({ where: { status: PostStatus.PUBLISHED } }),
      prisma.post.count({ where: { status: PostStatus.DRAFT } }),
      prisma.post.count({ where: { status: PostStatus.ARCHIVED } })
    ])
  ])

  const [publishedCount, draftCount, archivedCount] = statusStats

  return {
    posts,
    categories,
    totalPosts: posts.length,
    statusStats: {
      PUBLISHED: publishedCount,
      DRAFT: draftCount,
      ARCHIVED: archivedCount
    },
    totalViews: posts.reduce((sum, post) => sum + post.viewCount, 0)
  }
}

function getStatusColor(status: PostStatus) {
  switch (status) {
    case PostStatus.PUBLISHED:
      return 'bg-green-100 text-green-800'
    case PostStatus.DRAFT:
      return 'bg-yellow-100 text-yellow-800'
    case PostStatus.ARCHIVED:
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export default async function ContentManagementPage() {
  // Require admin access
  await requireAdmin()
  
  const data = await getPostsData()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Content Management</h1>
          <p className="text-gray-600">Manage blog posts, news articles, and content</p>
        </div>
        <Button asChild>
          <Link href="/admin/posts/new">
            <Plus className="w-4 h-4 mr-2" />
            Create Post
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalPosts}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Published</CardTitle>
            <FileText className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.statusStats.PUBLISHED}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
            <FileText className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.statusStats.DRAFT}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalViews.toLocaleString()}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Content Library</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.posts.map((post) => (
              <div key={post.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <FileText className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">{post.title}</h3>
                    <p className="text-sm text-gray-500 line-clamp-2">{post.excerpt || 'No excerpt available'}</p>
                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                      <span className="flex items-center">
                        <User className="w-3 h-3 mr-1" />
                        {post.author.name || post.author.email}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {new Date(post.createdAt).toLocaleDateString()}
                      </span>
                      <span className="flex items-center">
                        <Eye className="w-3 h-3 mr-1" />
                        {post.viewCount} views
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Badge 
                    className="text-xs"
                    style={{ 
                      backgroundColor: post.category.color || '#e5e7eb',
                      color: '#374151'
                    }}
                  >
                    {post.category.name}
                  </Badge>
                  
                  <Badge className={getStatusColor(post.status)}>
                    {post.status}
                  </Badge>
                  
                  {post.featured && (
                    <Badge variant="outline" className="text-xs">
                      Featured
                    </Badge>
                  )}
                  
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
            
            {data.posts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No posts found</p>
                <Button asChild className="mt-4">
                  <Link href="/admin/posts/new">Create First Post</Link>
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Categories Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Content Categories</CardTitle>
          <CardDescription>Posts organized by category</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.categories.map((category) => (
              <div key={category.id} className="p-4 border rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: category.color || '#e5e7eb' }}
                  />
                  <h4 className="font-medium">{category.name}</h4>
                </div>
                <p className="text-2xl font-bold text-blue-600">{category._count.posts}</p>
                <p className="text-sm text-gray-500">posts</p>
              </div>
            ))}
            
            {data.categories.length === 0 && (
              <div className="col-span-full text-center py-4 text-gray-500">
                <p>No categories found</p>
                <Button variant="outline" size="sm" className="mt-2">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Category
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common content management tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/posts/new" className="flex flex-col items-center space-y-2">
                <FileText className="w-8 h-8 text-blue-600" />
                <div className="text-center">
                  <h3 className="font-medium">New Post</h3>
                  <p className="text-sm text-gray-500">Create a new article</p>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/categories" className="flex flex-col items-center space-y-2">
                <FileText className="w-8 h-8 text-green-600" />
                <div className="text-center">
                  <h3 className="font-medium">Manage Categories</h3>
                  <p className="text-sm text-gray-500">Organize content</p>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/posts?status=draft" className="flex flex-col items-center space-y-2">
                <FileText className="w-8 h-8 text-yellow-600" />
                <div className="text-center">
                  <h3 className="font-medium">Review Drafts</h3>
                  <p className="text-sm text-gray-500">Pending content</p>
                </div>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
