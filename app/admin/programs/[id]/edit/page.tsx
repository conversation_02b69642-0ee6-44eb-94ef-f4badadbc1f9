import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { ProgramForm } from "@/components/admin/ProgramForm"
import { notFound } from "next/navigation"

async function getProgramData(id: string) {
  const program = await prisma.program.findUnique({
    where: { id },
    include: {
      department: {
        select: {
          id: true,
          name: true,
          slug: true
        }
      }
    }
  })

  if (!program) {
    notFound()
  }

  return program
}

async function getDepartments() {
  return await prisma.department.findMany({
    select: {
      id: true,
      name: true,
      slug: true
    },
    orderBy: {
      name: 'asc'
    }
  })
}

export default async function EditProgramPage({ params }: { params: Promise<{ id: string }> }) {
  // Require admin access
  await requireAdmin()
  
  const { id } = await params
  
  const [program, departments] = await Promise.all([
    getProgramData(id),
    getDepartments()
  ])

  const initialData = {
    name: program.name,
    slug: program.slug,
    departmentId: program.departmentId,
    degreeType: program.degreeType,
    duration: program.duration,
    description: program.description || '',
    imageUrl: program.imageUrl || '',
    isActive: program.isActive,
  }

  return (
    <ProgramForm 
      programId={id}
      initialData={initialData}
      departments={departments}
    />
  )
}
