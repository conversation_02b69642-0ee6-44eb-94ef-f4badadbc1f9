import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { ProgramForm } from "@/components/admin/ProgramForm"

async function getDepartments() {
  return await prisma.department.findMany({
    select: {
      id: true,
      name: true,
      slug: true
    },
    orderBy: {
      name: 'asc'
    }
  })
}

export default async function NewProgramPage() {
  // Require admin access
  await requireAdmin()
  
  const departments = await getDepartments()

  return <ProgramForm departments={departments} />
}
