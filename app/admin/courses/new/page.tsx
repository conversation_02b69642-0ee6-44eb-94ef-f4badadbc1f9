import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { CourseForm } from "@/components/admin/CourseForm"

async function getDepartments() {
  return await prisma.department.findMany({
    select: {
      id: true,
      name: true,
      slug: true
    },
    orderBy: {
      name: 'asc'
    }
  })
}

export default async function NewCoursePage() {
  // Require admin access
  await requireAdmin()
  
  const departments = await getDepartments()

  return <CourseForm departments={departments} />
}
