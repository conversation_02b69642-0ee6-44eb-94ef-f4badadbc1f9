import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { CourseForm } from "@/components/admin/CourseForm"
import { notFound } from "next/navigation"

async function getCourseData(id: string) {
  const course = await prisma.course.findUnique({
    where: { id },
    include: {
      department: {
        select: {
          id: true,
          name: true,
          slug: true
        }
      },
      _count: {
        select: {
          classes: true
        }
      }
    }
  })

  if (!course) {
    notFound()
  }

  return course
}

async function getDepartments() {
  return await prisma.department.findMany({
    select: {
      id: true,
      name: true,
      slug: true
    },
    orderBy: {
      name: 'asc'
    }
  })
}

export default async function EditCoursePage({ params }: { params: Promise<{ id: string }> }) {
  // Require admin access
  await requireAdmin()
  
  const { id } = await params
  
  const [course, departments] = await Promise.all([
    getCourseData(id),
    getDepartments()
  ])

  const initialData = {
    code: course.code,
    name: course.name,
    description: course.description || '',
    credits: course.credits,
    departmentId: course.departmentId,
    isActive: course.isActive,
  }

  return (
    <CourseForm 
      courseId={id}
      initialData={initialData}
      departments={departments}
    />
  )
}
