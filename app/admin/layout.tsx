import { requireAdmin } from "@/lib/auth-utils"
import { AdminSidebar } from "@/components/admin/AdminSidebar"
import { AdminHeader } from "@/components/admin/AdminHeader"
import { Breadcrumbs } from "@/components/admin/Breadcrumbs"

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Require admin role to access this layout
  const user = await requireAdmin()

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <AdminSidebar user={user} />
      
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader user={user} />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gradient-to-br from-gray-50 to-gray-100 p-6">
          <Breadcrumbs />
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
