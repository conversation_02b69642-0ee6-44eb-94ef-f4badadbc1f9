import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { ClassAssignmentForm } from "@/components/admin/ClassAssignmentForm"

async function getFormData() {
  const [courses, faculty] = await Promise.all([
    prisma.course.findMany({
      where: { isActive: true },
      include: {
        department: {
          select: {
            name: true,
            slug: true
          }
        }
      },
      orderBy: [
        { department: { name: 'asc' } },
        { code: 'asc' }
      ]
    }),
    prisma.facultyProfile.findMany({
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        },
        department: {
          select: {
            name: true,
            slug: true
          }
        }
      },
      orderBy: {
        user: {
          name: 'asc'
        }
      }
    })
  ])

  return { courses, faculty }
}

export default async function NewClassAssignmentPage() {
  await requireAdmin()
  const { courses, faculty } = await getFormData()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Assign Course to Faculty</h1>
        <p className="text-gray-600">Create a new course assignment for a faculty member</p>
      </div>
      
      <ClassAssignmentForm courses={courses} faculty={faculty} />
    </div>
  )
}
