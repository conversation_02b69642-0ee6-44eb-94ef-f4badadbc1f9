import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Users, Plus, Calendar, BookOpen, User, MapPin, Clock, Edit, Trash2 } from "lucide-react"
import { prisma } from "@/lib/prisma"
import { requireAdmin } from "@/lib/auth-utils"
import Link from "next/link"

async function getClassesData() {
  const [classes, courses, faculty, departments] = await Promise.all([
    prisma.courseClass.findMany({
      include: {
        course: {
          include: {
            department: {
              select: {
                name: true,
                slug: true
              }
            }
          }
        },
        faculty: {
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            },
            department: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: [
        { year: 'desc' },
        { semester: 'desc' },
        { course: { code: 'asc' } }
      ]
    }),
    prisma.course.count({
      where: { isActive: true }
    }),
    prisma.facultyProfile.count(),
    prisma.department.findMany({
      include: {
        _count: {
          select: {
            courses: true,
            faculty: true
          }
        }
      }
    })
  ])

  // Group classes by status
  const currentYear = new Date().getFullYear()
  const currentMonth = new Date().getMonth() + 1
  
  const groupedClasses = classes.reduce((acc, cls) => {
    let status = 'upcoming'
    if (cls.year < currentYear || (cls.year === currentYear && cls.semester === 'Fall' && currentMonth >= 8)) {
      status = cls.year === currentYear ? 'current' : 'past'
    } else if (cls.year === currentYear && cls.semester === 'Spring' && currentMonth >= 1 && currentMonth <= 5) {
      status = 'current'
    }
    
    if (!acc[status]) acc[status] = []
    acc[status].push(cls)
    return acc
  }, {} as Record<string, typeof classes>)

  return {
    classes,
    groupedClasses,
    totalClasses: classes.length,
    activeCourses: courses,
    totalFaculty: faculty,
    departments,
    stats: {
      current: groupedClasses.current?.length || 0,
      upcoming: groupedClasses.upcoming?.length || 0,
      past: groupedClasses.past?.length || 0
    }
  }
}

export default async function ClassesManagementPage() {
  await requireAdmin()
  const data = await getClassesData()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Course Assignments</h1>
          <p className="text-gray-600">Manage course assignments to faculty members</p>
        </div>
        <Button asChild>
          <Link href="/admin/classes/new">
            <Plus className="w-4 h-4 mr-2" />
            Assign Course
          </Link>
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalClasses}</div>
            <p className="text-xs text-muted-foreground">
              Across all semesters
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Classes</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.stats.current}</div>
            <p className="text-xs text-muted-foreground">
              Active this semester
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Courses</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activeCourses}</div>
            <p className="text-xs text-muted-foreground">
              Ready for assignment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Faculty Members</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalFaculty}</div>
            <p className="text-xs text-muted-foreground">
              Available for assignments
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Classes by Status */}
      <div className="space-y-6">
        {['current', 'upcoming', 'past'].map((status) => {
          const classes = data.groupedClasses[status] || []
          if (classes.length === 0) return null

          return (
            <Card key={status}>
              <CardHeader>
                <CardTitle className="flex items-center capitalize">
                  <Calendar className="w-5 h-5 mr-2" />
                  {status} Classes
                  <Badge variant="secondary" className="ml-2">
                    {classes.length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {classes.map((cls) => (
                    <div key={cls.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="font-semibold text-lg">
                              {cls.course.code}: {cls.course.name}
                            </h3>
                            <Badge variant="outline">
                              {cls.course.credits} credits
                            </Badge>
                            {cls.section && (
                              <Badge variant="secondary">
                                Section {cls.section}
                              </Badge>
                            )}
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div className="flex items-center">
                              <User className="w-4 h-4 mr-2" />
                              <span>{cls.faculty.user.name}</span>
                            </div>
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-2" />
                              <span>{cls.semester} {cls.year}</span>
                            </div>
                            <div className="flex items-center">
                              <Users className="w-4 h-4 mr-2" />
                              <span>{cls.enrollmentCount}/{cls.maxEnrollment} students</span>
                            </div>
                          </div>

                          {cls.description && (
                            <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                              {cls.description}
                            </p>
                          )}
                        </div>
                        
                        <div className="flex space-x-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <Link href={`/admin/classes/${cls.id}/edit`}>
                              <Edit className="w-4 h-4 mr-1" />
                              Edit
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common course assignment tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/classes/new" className="flex flex-col items-center space-y-2">
                <Plus className="w-8 h-8 text-blue-600" />
                <div className="text-center">
                  <h3 className="font-medium">New Assignment</h3>
                  <p className="text-sm text-gray-500">Assign course to faculty</p>
                </div>
              </Link>
            </Button>

            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/courses" className="flex flex-col items-center space-y-2">
                <BookOpen className="w-8 h-8 text-green-600" />
                <div className="text-center">
                  <h3 className="font-medium">Manage Courses</h3>
                  <p className="text-sm text-gray-500">Course catalog</p>
                </div>
              </Link>
            </Button>

            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/faculty" className="flex flex-col items-center space-y-2">
                <Users className="w-8 h-8 text-purple-600" />
                <div className="text-center">
                  <h3 className="font-medium">Manage Faculty</h3>
                  <p className="text-sm text-gray-500">Faculty directory</p>
                </div>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
