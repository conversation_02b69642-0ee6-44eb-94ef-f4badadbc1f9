import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { ClassAssignmentForm } from "@/components/admin/ClassAssignmentForm"
import { notFound } from "next/navigation"

async function getClassData(classId: string) {
  const [classData, courses, faculty] = await Promise.all([
    prisma.courseClass.findUnique({
      where: { id: classId },
      include: {
        course: {
          include: {
            department: {
              select: {
                name: true,
                slug: true
              }
            }
          }
        },
        faculty: {
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            },
            department: {
              select: {
                name: true,
                slug: true
              }
            }
          }
        }
      }
    }),
    prisma.course.findMany({
      where: { isActive: true },
      include: {
        department: {
          select: {
            name: true,
            slug: true
          }
        }
      },
      orderBy: [
        { department: { name: 'asc' } },
        { code: 'asc' }
      ]
    }),
    prisma.facultyProfile.findMany({
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        },
        department: {
          select: {
            name: true,
            slug: true
          }
        }
      },
      orderBy: {
        user: {
          name: 'asc'
        }
      }
    })
  ])

  if (!classData) {
    notFound()
  }

  return { classData, courses, faculty }
}

interface PageProps {
  params: Promise<{ id: string }>
}

export default async function EditClassAssignmentPage({ params }: PageProps) {
  await requireAdmin()
  const { id } = await params
  const { classData, courses, faculty } = await getClassData(id)

  const initialData = {
    courseId: classData.courseId,
    facultyId: classData.facultyId,
    semester: classData.semester as 'Spring' | 'Summer' | 'Fall',
    year: classData.year,
    section: classData.section || '',
    maxEnrollment: classData.maxEnrollment,
    schedule: classData.schedule as {
      days: string[]
      time: string
      location: string
    },
    syllabusUrl: classData.syllabusUrl || '',
    description: classData.description || '',
    status: classData.status as 'upcoming' | 'current' | 'past'
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Edit Course Assignment</h1>
        <p className="text-gray-600">
          Modify the course assignment for {classData.course.code}: {classData.course.name}
        </p>
      </div>
      
      <ClassAssignmentForm 
        courses={courses} 
        faculty={faculty} 
        classId={id}
        initialData={initialData}
      />
    </div>
  )
}
