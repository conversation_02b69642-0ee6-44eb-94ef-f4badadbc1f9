import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { FacultyForm } from "@/components/admin/FacultyForm"
import { notFound } from "next/navigation"

async function getFacultyData(id: string) {
  const faculty = await prisma.facultyProfile.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          status: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              phone: true
            }
          }
        }
      },
      department: {
        select: {
          id: true,
          name: true
        }
      }
    }
  })

  if (!faculty) {
    notFound()
  }

  return faculty
}

async function getDepartments() {
  return await prisma.department.findMany({
    select: {
      id: true,
      name: true
    },
    orderBy: {
      name: 'asc'
    }
  })
}

// Define the page component properly for Next.js 15
export default async function EditFacultyPage({ params }: { params: { id: string } }) {
  // Require admin access
  await requireAdmin()
  
  // Properly handle params in Next.js 15
  // The whole params object needs to be properly resolved
  const resolvedParams = await Promise.resolve(params);
  const facultyId = resolvedParams.id;
  
  // Now we can safely use facultyId
  const [faculty, departments] = await Promise.all([
    getFacultyData(facultyId),
    getDepartments()
  ])

  const initialData = {
    name: faculty.user.name || '',
    email: faculty.user.email,
    title: faculty.title,
    departmentId: faculty.departmentId,
    officeLocation: faculty.officeLocation || '',
    websiteUrl: faculty.websiteUrl || '',
    scholarId: faculty.scholarId || '',
    bio: faculty.bio || '',
    firstName: faculty.user.profile?.firstName || '',
    lastName: faculty.user.profile?.lastName || '',
    phone: faculty.user.profile?.phone || '',
    status: faculty.user.status,
  }

  return (
    <FacultyForm 
      facultyId={facultyId}
      initialData={initialData}
      departments={departments} 
    />
  )
}
