import { requireAdmin } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { FacultyForm } from "@/components/admin/FacultyForm"

async function getDepartments() {
  return await prisma.department.findMany({
    select: {
      id: true,
      name: true
    },
    orderBy: {
      name: 'asc'
    }
  })
}

export default async function NewFacultyPage() {
  // Require admin access
  await requireAdmin()
  
  const departments = await getDepartments()

  return (
    <FacultyForm departments={departments} />
  )
}
