# College Website Database Design

This document provides a comprehensive overview of the database schema used in the college website application.

## Table of Contents
- [Overview](#overview)
- [Core User Management](#core-user-management)
- [Academic Structure](#academic-structure)
- [Faculty Profiles](#faculty-profiles)
- [Course Management](#course-management)
- [Office Hours & Scheduling](#office-hours--scheduling)
- [Research Projects](#research-projects)
- [Content Management](#content-management)
- [Entity Relationship Diagram](#entity-relationship-diagram)

## Overview

The database is designed to support a comprehensive college website with faculty profiles, course management, research project listings, and content management. PostgreSQL is used as the database provider, and <PERSON><PERSON><PERSON> serves as the ORM.

## Core User Management

### User
Central user entity that stores authentication and basic user information.

| Field         | Type         | Description                                  |
|---------------|--------------|----------------------------------------------|
| id            | String (cuid) | Primary identifier                          |
| email         | String       | User's email (unique)                        |
| emailVerified | DateTime?    | When email was verified                      |
| name          | String?      | User's display name                          |
| image         | String?      | Profile image URL                            |
| password      | String?      | Hashed password for credential auth          |
| role          | UserRole     | User role (STUDENT, FACULTY, COLLEGE_ADMIN, SYS_ADMIN) |
| status        | UserStatus   | Account status (ACTIVE, INACTIVE, PENDING, SUSPENDED) |
| createdAt     | DateTime     | When record was created                      |
| updatedAt     | DateTime     | When record was last updated                 |

**Relations:**
- One-to-many with Account
- One-to-many with Session
- One-to-one with UserProfile
- One-to-one with FacultyProfile (for faculty users)
- One-to-many with Post (author)
- One-to-many with OfficeHourBooking (student)

### UserProfile
Extended profile information for any user type.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| userId      | String       | Foreign key to User               |
| firstName   | String?      | User's first name                 |
| lastName    | String?      | User's last name                  |
| phone       | String?      | Contact phone number              |
| avatarUrl   | String?      | Profile picture URL               |
| bio         | String?      | Biography text                    |
| dateOfBirth | DateTime?    | Date of birth                     |
| address     | String?      | Street address                    |
| city        | String?      | City                              |
| state       | String?      | State/province                    |
| zipCode     | String?      | Postal code                       |
| country     | String?      | Country                           |
| createdAt   | DateTime     | When record was created           |
| updatedAt   | DateTime     | When record was last updated      |

**Relations:**
- Many-to-one with User

## Academic Structure

### Department
Academic departments within the college.

| Field        | Type         | Description                       |
|--------------|--------------|-----------------------------------|
| id           | String (cuid) | Primary identifier               |
| name         | String       | Department name (unique)          |
| slug         | String       | URL-friendly name (unique)        |
| description  | String?      | Department description            |
| headFacultyId| String?      | ID of department head (faculty)   |
| createdAt    | DateTime     | When record was created           |
| updatedAt    | DateTime     | When record was last updated      |

**Relations:**
- One-to-many with FacultyProfile
- One-to-many with Program
- One-to-many with Course

### Program
Academic programs offered by departments.

| Field        | Type         | Description                       |
|--------------|--------------|-----------------------------------|
| id           | String (cuid) | Primary identifier               |
| name         | String       | Program name                      |
| slug         | String       | URL-friendly name (unique)        |
| departmentId | String       | Foreign key to Department         |
| degreeType   | String       | Type of degree (e.g., BS, MS, PhD)|
| duration     | Int          | Program duration in years         |
| description  | String?      | Program description               |
| imageUrl     | String?      | Program image URL                 |
| isActive     | Boolean      | Whether program is active         |
| createdAt    | DateTime     | When record was created           |
| updatedAt    | DateTime     | When record was last updated      |

**Relations:**
- Many-to-one with Department

### Course
Individual courses offered by departments.

| Field        | Type         | Description                       |
|--------------|--------------|-----------------------------------|
| id           | String (cuid) | Primary identifier               |
| code         | String       | Course code (unique)              |
| name         | String       | Course name                       |
| description  | String?      | Course description                |
| credits      | Int          | Number of credits                 |
| departmentId | String       | Foreign key to Department         |
| isActive     | Boolean      | Whether course is active          |
| createdAt    | DateTime     | When record was created           |
| updatedAt    | DateTime     | When record was last updated      |

**Relations:**
- Many-to-one with Department
- One-to-many with CourseClass

## Faculty Profiles

### FacultyProfile
Detailed profile for faculty members.

| Field          | Type         | Description                       |
|----------------|--------------|-----------------------------------|
| id             | String (cuid) | Primary identifier               |
| userId         | String       | Foreign key to User               |
| title          | String       | Academic title (Prof., Dr., etc.) |
| departmentId   | String       | Foreign key to Department         |
| officeLocation | String?      | Office location                   |
| websiteUrl     | String?      | Personal/professional website     |
| scholarId      | String?      | Google Scholar ID                 |
| bio            | String?      | Biography                         |
| isActive       | Boolean      | Whether profile is active         |
| createdAt      | DateTime     | When record was created           |
| updatedAt      | DateTime     | When record was last updated      |

**Relations:**
- Many-to-one with User
- Many-to-one with Department
- One-to-many with FacultyEducation
- One-to-many with FacultyResearchArea
- One-to-many with FacultyPublication
- One-to-many with FacultyTimeline
- One-to-many with FacultyCVDocument
- One-to-many with CourseClass
- One-to-many with OfficeHour
- One-to-many with ResearchProject
- One-to-many with FacultyIndustryExperience
- One-to-many with FacultySkill

### FacultyEducation
Educational background of faculty members.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| facultyId   | String       | Foreign key to FacultyProfile     |
| degree      | String       | Degree earned                     |
| institution | String       | Institution name                  |
| year        | Int?         | Year of completion                |
| field       | String?      | Field of study                    |
| createdAt   | DateTime     | When record was created           |

**Relations:**
- Many-to-one with FacultyProfile

### FacultyResearchArea
Research interests and specializations of faculty members.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| facultyId   | String       | Foreign key to FacultyProfile     |
| areaName    | String       | Research area name                |
| description | String?      | Description of research area      |
| createdAt   | DateTime     | When record was created           |

**Relations:**
- Many-to-one with FacultyProfile

### FacultyPublication
Academic publications by faculty members.

| Field         | Type         | Description                       |
|---------------|--------------|-----------------------------------|
| id            | String (cuid) | Primary identifier               |
| facultyId     | String       | Foreign key to FacultyProfile     |
| title         | String       | Publication title                 |
| authors       | String[]     | List of authors                   |
| journal       | String       | Journal/conference name           |
| year          | Int          | Publication year                  |
| citationCount | Int          | Number of citations               |
| link          | String?      | URL to publication                |
| abstract      | String?      | Publication abstract              |
| tags          | String[]     | Publication tags                  |
| createdAt     | DateTime     | When record was created           |
| updatedAt     | DateTime     | When record was last updated      |

**Relations:**
- Many-to-one with FacultyProfile

### FacultyTimeline
Significant events in a faculty member's career.

| Field       | Type             | Description                       |
|-------------|------------------|-----------------------------------|
| id          | String (cuid)    | Primary identifier                |
| facultyId   | String           | Foreign key to FacultyProfile     |
| year        | String           | Year of event                     |
| title       | String           | Event title                       |
| description | String           | Event description                 |
| type        | TimelineEventType| Event type (EDUCATION, POSITION, AWARD, PUBLICATION) |
| createdAt   | DateTime         | When record was created           |

**Relations:**
- Many-to-one with FacultyProfile

### FacultyCVDocument
CV and other professional documents for faculty members.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| facultyId   | String       | Foreign key to FacultyProfile     |
| title       | String       | Document title                    |
| fileType    | String       | File type (pdf, docx, etc.)       |
| fileSize    | String       | File size                         |
| fileName    | String       | Original file name                |
| fileUrl     | String       | URL to file                       |
| lastUpdated | DateTime     | When file was last updated        |
| createdAt   | DateTime     | When record was created           |

**Relations:**
- Many-to-one with FacultyProfile

### FacultyIndustryExperience
Industry work experience of faculty members.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| facultyId   | String       | Foreign key to FacultyProfile     |
| company     | String       | Company name                      |
| position    | String       | Job position/title                |
| startDate   | String       | Start date                        |
| endDate     | String?      | End date (null if ongoing)        |
| description | String?      | Job description                   |
| createdAt   | DateTime     | When record was created           |
| updatedAt   | DateTime     | When record was last updated      |

**Relations:**
- Many-to-one with FacultyProfile

### FacultySkill
Technical and professional skills of faculty members.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| facultyId   | String       | Foreign key to FacultyProfile     |
| skillName   | String       | Skill name                        |
| proficiency | String?      | Proficiency level                 |
| category    | String?      | Skill category                    |
| createdAt   | DateTime     | When record was created           |

**Relations:**
- Many-to-one with FacultyProfile

## Course Management

### CourseClass
Individual class sections of courses.

| Field          | Type         | Description                       |
|----------------|--------------|-----------------------------------|
| id             | String (cuid) | Primary identifier               |
| courseId       | String       | Foreign key to Course             |
| facultyId      | String       | Foreign key to FacultyProfile     |
| semester       | String       | Semester (Fall, Spring, etc.)     |
| year           | Int          | Academic year                     |
| section        | String?      | Section identifier                |
| enrollmentCount| Int          | Current enrollment count          |
| maxEnrollment  | Int          | Maximum enrollment capacity       |
| schedule       | Json         | Class schedule (days, time, location) |
| syllabusUrl    | String?      | URL to syllabus document          |
| description    | String?      | Class-specific description        |
| status         | String       | Class status (upcoming, current, past) |
| createdAt      | DateTime     | When record was created           |
| updatedAt      | DateTime     | When record was last updated      |

**Relations:**
- Many-to-one with Course
- Many-to-one with FacultyProfile

## Office Hours & Scheduling

### OfficeHour
Faculty office hours schedule.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| facultyId   | String       | Foreign key to FacultyProfile     |
| dayOfWeek   | Int          | Day of week (0=Sunday, 1=Monday)  |
| startTime   | String       | Start time (HH:MM format)         |
| endTime     | String       | End time (HH:MM format)           |
| location    | String       | Office location                   |
| notes       | String?      | Additional notes                  |
| isActive    | Boolean      | Whether office hour is active     |
| createdAt   | DateTime     | When record was created           |
| updatedAt   | DateTime     | When record was last updated      |

**Relations:**
- Many-to-one with FacultyProfile
- One-to-many with OfficeHourBooking

### OfficeHourBooking
Student bookings for faculty office hours.

| Field       | Type          | Description                       |
|-------------|---------------|-----------------------------------|
| id          | String (cuid) | Primary identifier                |
| officeHourId| String        | Foreign key to OfficeHour         |
| studentId   | String        | Foreign key to User (student)     |
| date        | DateTime      | Booking date                      |
| startTime   | String        | Start time (HH:MM format)         |
| endTime     | String        | End time (HH:MM format)           |
| topic       | String?       | Meeting topic                     |
| notes       | String?       | Additional notes                  |
| status      | BookingStatus | Status (PENDING, CONFIRMED, CANCELLED, COMPLETED) |
| createdAt   | DateTime      | When record was created           |
| updatedAt   | DateTime      | When record was last updated      |

**Relations:**
- Many-to-one with OfficeHour
- Many-to-one with User (student)

## Research Projects

### ResearchProject
Faculty research projects with student opportunities.

| Field       | Type          | Description                       |
|-------------|---------------|-----------------------------------|
| id          | String (cuid) | Primary identifier                |
| facultyId   | String        | Foreign key to FacultyProfile     |
| title       | String        | Project title                     |
| description | String        | Project description               |
| timeline    | String        | Project timeline                  |
| positions   | Int           | Number of available positions     |
| commitment  | String        | Time commitment (e.g., "10 hours/week") |
| isPaid      | Boolean       | Whether position is paid          |
| isCredited  | Boolean       | Whether academic credit is available |
| status      | ProjectStatus | Status (RECRUITING, ONGOING, COMPLETED, CANCELLED) |
| imageUrl    | String?       | Project image URL                 |
| createdAt   | DateTime      | When record was created           |
| updatedAt   | DateTime      | When record was last updated      |

**Relations:**
- Many-to-one with FacultyProfile
- One-to-many with ProjectRequirement
- Many-to-many with Tag through ProjectTag

### ProjectRequirement
Requirements for research project participation.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| projectId   | String       | Foreign key to ResearchProject    |
| requirement | String       | Requirement description           |
| createdAt   | DateTime     | When record was created           |

**Relations:**
- Many-to-one with ResearchProject

## Content Management

### Category
Content categories for posts.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| name        | String       | Category name (unique)            |
| slug        | String       | URL-friendly name (unique)        |
| description | String?      | Category description              |
| color       | String?      | Hex color code for UI             |
| createdAt   | DateTime     | When record was created           |
| updatedAt   | DateTime     | When record was last updated      |

**Relations:**
- One-to-many with Post

### Tag
Tags for posts and projects.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| name        | String       | Tag name (unique)                 |
| slug        | String       | URL-friendly name (unique)        |
| createdAt   | DateTime     | When record was created           |

**Relations:**
- Many-to-many with Post through PostTag
- Many-to-many with ResearchProject through ProjectTag

### Post
Blog or news content.

| Field       | Type         | Description                       |
|-------------|--------------|-----------------------------------|
| id          | String (cuid) | Primary identifier               |
| title       | String       | Post title                        |
| slug        | String       | URL-friendly title (unique)       |
| content     | String       | Post content                      |
| excerpt     | String?      | Short summary                     |
| authorId    | String       | Foreign key to User (author)      |
| categoryId  | String       | Foreign key to Category           |
| status      | PostStatus   | Status (DRAFT, PUBLISHED, ARCHIVED)|
| featured    | Boolean      | Whether post is featured          |
| imageUrl    | String?      | Featured image URL                |
| imageAlt    | String?      | Image alt text                    |
| viewCount   | Int          | Number of views                   |
| readingTime | String?      | Estimated reading time            |
| publishedAt | DateTime?    | When post was published           |
| createdAt   | DateTime     | When record was created           |
| updatedAt   | DateTime     | When record was last updated      |

**Relations:**
- Many-to-one with User (author)
- Many-to-one with Category
- Many-to-many with Tag through PostTag

## Entity Relationship Diagram

```
User 1--1 UserProfile
User 1--1 FacultyProfile (for faculty users)
User 1--* Post (as author)
User 1--* OfficeHourBooking (as student)

Department 1--* FacultyProfile
Department 1--* Program
Department 1--* Course

FacultyProfile 1--* FacultyEducation
FacultyProfile 1--* FacultyResearchArea
FacultyProfile 1--* FacultyPublication
FacultyProfile 1--* FacultyTimeline
FacultyProfile 1--* FacultyCVDocument
FacultyProfile 1--* CourseClass
FacultyProfile 1--* OfficeHour
FacultyProfile 1--* ResearchProject
FacultyProfile 1--* FacultyIndustryExperience
FacultyProfile 1--* FacultySkill

Course 1--* CourseClass

OfficeHour 1--* OfficeHourBooking

ResearchProject 1--* ProjectRequirement
ResearchProject *--* Tag (through ProjectTag)

Category 1--* Post
Post *--* Tag (through PostTag)
```

## Enums

### UserRole
- STUDENT
- FACULTY
- COLLEGE_ADMIN
- SYS_ADMIN

### UserStatus
- ACTIVE
- INACTIVE
- PENDING
- SUSPENDED

### PostStatus
- DRAFT
- PUBLISHED
- ARCHIVED

### ProjectStatus
- RECRUITING
- ONGOING
- COMPLETED
- CANCELLED

### BookingStatus
- PENDING
- CONFIRMED
- CANCELLED
- COMPLETED

### TimelineEventType
- EDUCATION
- POSITION
- AWARD
- PUBLICATION 