# Ullens College Website

A modern, full-featured college website built with Next.js, featuring comprehensive faculty management, student services, and administrative capabilities.

## 🌟 Overview

This is a sophisticated college website platform that provides a complete digital ecosystem for educational institutions. It includes faculty portals, administrative dashboards, student services, and a public-facing website with modern design and functionality.

## ✨ Key Features

### 🎓 Faculty Management
- **Comprehensive Faculty Profiles** - Detailed profiles with research areas, publications, and timelines
- **Office Hours Scheduling** - Students can book appointments with faculty
- **Research Project Management** - Faculty can post and manage research opportunities
- **Publication Tracking** - Academic publications with metadata
- **CV Document Management** - Upload and manage curriculum vitae
- **Education History** - Track academic background and achievements

### 👥 User Management
- **Multi-Role Authentication** - System Admin, College Admin, Faculty, and Student roles
- **Secure Authentication** - NextAuth.js with multiple provider support
- **User Profiles** - Extended profile information with avatars and contact details
- **Role-Based Access Control** - Granular permissions based on user roles

### 📚 Academic Structure
- **Department Management** - Organize faculty and programs by departments
- **Program Catalog** - Comprehensive degree program information
- **Course Management** - Course catalog with class scheduling
- **Research Areas** - Categorized research interests and expertise

### 🎨 Modern UI/UX
- **Responsive Design** - Optimized for desktop, tablet, and mobile
- **Dark/Light Theme** - User preference-based theming
- **Accessibility First** - ARIA attributes, keyboard navigation, screen reader support
- **Print-Friendly** - Dedicated print layouts for profiles and documents
- **Performance Optimized** - Lazy loading, image optimization, and efficient rendering

### 🔍 Advanced Search & Discovery
- **Faculty Search** - Advanced search with multiple filters
- **Research Opportunities** - Discover faculty research projects
- **Recently Viewed** - Track and display recently browsed content
- **Favorites System** - Bookmark faculty and content for quick access

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 15.2.4 with App Router
- **UI Library**: React 19
- **Styling**: Tailwind CSS with custom design system
- **Components**: Radix UI primitives
- **Animations**: Framer Motion
- **Icons**: Lucide React

### Backend
- **API**: Next.js API Routes
- **Authentication**: NextAuth.js
- **Database**: PostgreSQL
- **ORM**: Prisma
- **Validation**: Zod

### Development
- **Language**: TypeScript
- **Package Manager**: npm/pnpm
- **Linting**: ESLint
- **Formatting**: Prettier (via Next.js defaults)

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- PostgreSQL database
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd college-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Update your `.env` file:
   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/college_website_db"
   
   # NextAuth
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secret-key-here"
   ```

4. **Set up the database**
   ```bash
   # Generate Prisma client
   npm run db:generate
   
   # Push schema to database
   npm run db:push
   
   # Set up default data
   npm run db:setup
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

Visit `http://localhost:3000` to see the application running.

## 📖 Usage

### Default Accounts

After setup, you can log in with these default accounts:

| Role | Email | Password | Access |
|------|-------|----------|---------|
| System Admin | <EMAIL> | defaultpassword123 | Full system access |
| College Admin | <EMAIL> | defaultpassword123 | Content & faculty management |
| Faculty | <EMAIL> | defaultpassword123 | Faculty portal |
| Student | <EMAIL> | defaultpassword123 | Student features |

### Access Points

- **Main Website**: http://localhost:3000
- **Admin Panel**: http://localhost:3000/admin
- **Faculty Portal**: http://localhost:3000/faculty-portal
- **Authentication**: http://localhost:3000/auth/signin

## 🏗️ Project Structure

```
├── app/                    # Next.js App Router
│   ├── admin/             # Admin dashboard pages
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── faculty/           # Faculty directory
│   ├── faculty-portal/    # Faculty management portal
│   ├── programs/          # Academic programs
│   ├── research/          # Research showcase
│   └── ...               # Other pages
├── components/            # React components
│   ├── admin/            # Admin-specific components
│   ├── faculty/          # Faculty components
│   ├── landing/          # Homepage components
│   ├── ui/               # Reusable UI components
│   └── ...               # Other component categories
├── lib/                  # Utilities and services
│   ├── data/             # Data management
│   ├── services/         # API services
│   └── utils.ts          # Helper functions
├── prisma/               # Database schema and migrations
├── public/               # Static assets
├── scripts/              # Setup and utility scripts
└── styles/               # Global styles
```

## 🗄️ Database Schema

### Core Models

- **User Management**: Users, profiles, accounts, sessions
- **Faculty System**: Faculty profiles, education, research areas, publications
- **Academic Structure**: Departments, programs, courses
- **Content Management**: Posts, categories, tags
- **Scheduling**: Office hours, bookings, research projects

### Key Relationships

```mermaid
graph TD
    A[User] --> B[UserProfile]
    A --> C[FacultyProfile]
    C --> D[Department]
    C --> E[FacultyEducation]
    C --> F[FacultyResearchArea]
    C --> G[FacultyPublication]
    D --> H[Program]
    D --> I[Course]
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build            # Build for production
npm run start            # Start production server
npm run lint             # Run ESLint

# Database
npm run db:generate      # Generate Prisma client
npm run db:push          # Push schema to database
npm run db:migrate       # Run database migrations
npm run db:studio        # Open Prisma Studio
npm run db:setup         # Initialize with default data
npm run db:reset         # Reset and reinitialize database

# Testing
npm run test:auth        # Test authentication flow
```

### Adding New Features

1. **Database Changes**
   ```bash
   # Update schema in prisma/schema.prisma
   npm run db:push
   npm run db:generate
   ```

2. **API Routes**
   - Add routes in `app/api/`
   - Use Prisma client for database operations
   - Implement proper authentication checks

3. **UI Components**
   - Add reusable components in `components/ui/`
   - Use existing design system patterns
   - Follow accessibility guidelines

## 🔐 Authentication & Authorization

### User Roles

- **SYS_ADMIN**: Full system access, user management
- **COLLEGE_ADMIN**: Faculty and content management
- **FACULTY**: Personal profile and research management
- **STUDENT**: Profile management and service access

### Protected Routes

- Admin routes require `COLLEGE_ADMIN` or `SYS_ADMIN` role
- Faculty portal requires `FACULTY` role
- API routes include role-based authorization

## 🚀 Deployment

### Environment Variables

```env
# Production Database
DATABASE_URL="your-production-database-url"

# NextAuth Configuration
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="secure-random-string-for-production"

# Optional: Additional OAuth providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### Deployment Platforms

- **Vercel** (Recommended for Next.js)
- **Railway** (Database + App hosting)
- **Docker** (Container deployment)

### Pre-deployment Checklist

- [ ] Update all default passwords
- [ ] Set secure `NEXTAUTH_SECRET`
- [ ] Configure production database
- [ ] Set up SSL certificates
- [ ] Configure backup strategy
- [ ] Set up monitoring and logging

## 📊 Features in Detail

### Faculty Portal

- **Profile Management**: Complete faculty profile with photo, bio, contact info
- **Research Areas**: Categorized research interests and expertise
- **Publications**: Academic publications with DOI links and metadata
- **Timeline**: Career milestones and achievements
- **Office Hours**: Set availability and manage student bookings
- **Research Projects**: Post opportunities for student collaboration

### Admin Dashboard

- **User Management**: Create, edit, and manage user accounts
- **Content Management**: Publish news, announcements, and blog posts
- **Analytics**: Track user engagement and system usage
- **Department Management**: Organize academic structure
- **Settings**: Configure system-wide preferences

### Student Features

- **Faculty Discovery**: Browse and search faculty by expertise
- **Office Hours Booking**: Schedule appointments with faculty
- **Research Opportunities**: Find and apply for research projects
- **Program Information**: Explore academic programs and requirements

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Use existing UI components when possible
- Add tests for new functionality
- Update documentation for new features
- Follow the established code style

## 📝 Documentation

- **Database Setup**: See `DATABASE_SETUP.md`
- **Technical Improvements**: See `TECHNICAL-IMPROVEMENTS.md`
- **Faculty Portal Testing**: See `FACULTY_PORTAL_TESTING.md`
- **API Documentation**: Available in code comments and type definitions

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify `DATABASE_URL` format
   - Ensure PostgreSQL is running
   - Check network connectivity

2. **Authentication Issues**
   - Verify `NEXTAUTH_SECRET` is set
   - Check `NEXTAUTH_URL` matches your domain
   - Clear browser cookies

3. **Build Errors**
   - Run `npm run db:generate` after schema changes
   - Check TypeScript errors with `npm run lint`
   - Verify all environment variables are set

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Support

For support and questions:

- Create an issue in the repository
- Check existing documentation
- Review the troubleshooting guide

## 🔄 Changelog

See the git commit history for detailed changes and updates.

---

**Built with ❤️ for educational institutions** 