// Test script for faculty update API with authentication
const testFacultyUpdate = async () => {
  // Replace these values with valid ones from your environment
  const facultyId = 'cly8tkg0d00009qs8hwz7jjy1'; // Replace with a valid faculty ID
  const sessionCookie = process.env.SESSION_COOKIE || ''; // Get this from your browser when logged in as admin
  
  if (!sessionCookie) {
    console.error('No session cookie provided. Set SESSION_COOKIE environment variable.');
    process.exit(1);
  }
  
  try {
    // Fetch current faculty data
    console.log(`Fetching faculty with ID: ${facultyId}`);
    const getResponse = await fetch(`http://localhost:3000/api/admin/faculty/${facultyId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': sessionCookie
      },
    });
    
    const responseText = await getResponse.text();
    console.log('Raw response:', responseText);
    
    if (!getResponse.ok) {
      let errorMessage = 'Failed to fetch faculty';
      try {
        const error = JSON.parse(responseText);
        errorMessage = error.error || errorMessage;
      } catch (e) {
        // If parsing fails, use the raw text
        errorMessage = responseText || errorMessage;
      }
      throw new Error(errorMessage);
    }
    
    const faculty = JSON.parse(responseText);
    console.log('Current faculty data:', faculty);
    
    // Prepare update data
    const updateData = {
      name: faculty.user.name,
      email: faculty.user.email,
      title: faculty.title,
      departmentId: faculty.departmentId,
      officeLocation: faculty.officeLocation ? faculty.officeLocation + ' (Updated)' : 'New Office Location',
      websiteUrl: faculty.websiteUrl || '',
      scholarId: faculty.scholarId || '',
      bio: faculty.bio || 'Updated biography text',
      status: faculty.user.status,
    };
    
    console.log('Update data to be sent:', updateData);
    
    // Send update request
    console.log(`Sending PUT request to update faculty ID: ${facultyId}`);
    const updateResponse = await fetch(`http://localhost:3000/api/admin/faculty/${facultyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': sessionCookie
      },
      body: JSON.stringify(updateData),
    });
    
    const updateResponseText = await updateResponse.text();
    console.log('Raw update response:', updateResponseText);
    
    if (!updateResponse.ok) {
      let errorMessage = 'Failed to update faculty';
      try {
        const error = JSON.parse(updateResponseText);
        errorMessage = error.error || errorMessage;
        if (error.details) {
          console.error('Validation errors:', error.details);
        }
      } catch (e) {
        // If parsing fails, use the raw text
        errorMessage = updateResponseText || errorMessage;
      }
      throw new Error(errorMessage);
    }
    
    const responseData = JSON.parse(updateResponseText);
    console.log('Update successful:', responseData);
    
  } catch (error) {
    console.error('Error:', error.message);
  }
};

// Run the test
testFacultyUpdate(); 