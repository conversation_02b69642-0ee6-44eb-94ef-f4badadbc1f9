# Cache Management Guide

This document explains how to handle cache-related issues in the Next.js development environment.

## Common Cache Issues

### Webpack Cache Corruption
You might encounter errors like:
```
[webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory
```

### Chunk Loading Errors
```
ChunkLoadError: Loading chunk app/admin/layout failed.
```

## Solutions

### Quick Fix
For most cache issues, use our custom cache clearing script:

```bash
npm run dev:fresh
```

This command will:
1. Clear all Next.js and Node.js caches
2. Remove corrupted webpack cache files
3. Start a fresh development server

### Manual Cache Clearing
If you need to clear caches manually:

```bash
npm run clear-cache
```

Then start the development server:

```bash
npm run dev
```

### What Gets Cleared

The cache clearing script removes:
- `.next/` directory (Next.js build cache)
- `node_modules/.cache/` (Node.js cache)
- `tsconfig.tsbuildinfo` (TypeScript build info)
- Any webpack temporary files

## Prevention

### Best Practices
1. **Regular cache clearing**: Run `npm run dev:fresh` if you encounter any build issues
2. **After major changes**: Clear cache when switching branches or after major dependency updates
3. **Before deployment**: Always clear cache before building for production

### When to Clear Cache
- After updating dependencies
- When switching Git branches
- After modifying webpack or Next.js configuration
- When encountering unexplained build errors
- After system crashes or forced shutdowns

## Troubleshooting

### If Cache Clearing Doesn't Work
1. Stop the development server completely
2. Run `npm run clear-cache`
3. Restart your terminal/IDE
4. Run `npm run dev`

### Nuclear Option
If all else fails:
```bash
rm -rf .next node_modules/.cache tsconfig.tsbuildinfo
npm install
npm run dev
```

## Scripts Available

- `npm run clear-cache` - Clear all caches
- `npm run dev:fresh` - Clear cache and start fresh dev server
- `npm run dev` - Standard development server

## Technical Details

The cache clearing script (`scripts/clear-cache.js`) handles:
- Safe deletion of cache directories
- Error handling for locked files
- Detection of webpack temporary files
- Cross-platform compatibility

This ensures a clean development environment and resolves most cache-related issues automatically.
