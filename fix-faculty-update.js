/**
 * Faculty Update Fix Script
 * 
 * NOTE: Before using this script, try updating the faculty through the UI.
 * We've added a Debug Panel at the bottom of the Edit Faculty page that shows
 * form validation status and allows direct API testing.
 * 
 * The main issue with faculty updates was that the form validation was requiring a password
 * even when editing, which has now been fixed. You should be able to update faculty
 * information through the UI without any issues.
 * 
 * Only use this script if you're still having problems with the UI.
 */

// Fix script for faculty update issues
// This will directly update a faculty member's email through the API
const axios = require('axios');
const fs = require('fs');
const readline = require('readline');

// Get the session cookie from a file
// Create a session-cookie.txt file with your session cookie value
let sessionCookie = '';
try {
  if (fs.existsSync('./session-cookie.txt')) {
    sessionCookie = fs.readFileSync('./session-cookie.txt', 'utf8').trim();
  }
} catch (err) {
  console.error('Error reading session cookie file:', err);
}

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function promptForInput(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function updateFaculty() {
  try {
    // If no session cookie in file, prompt for it
    if (!sessionCookie) {
      console.log('No session cookie found in session-cookie.txt');
      sessionCookie = await promptForInput('Please paste your session cookie: ');
      
      // Save it for future use
      fs.writeFileSync('./session-cookie.txt', sessionCookie);
    }
    
    // Prompt for faculty ID
    const facultyId = await promptForInput('Enter faculty ID to update: ');
    
    // Fetch current faculty data
    console.log(`Fetching faculty with ID: ${facultyId}`);
    const getResponse = await axios.get(`http://localhost:3000/api/admin/faculty/${facultyId}`, {
      headers: {
        'Cookie': sessionCookie
      },
      validateStatus: () => true
    });
    
    if (getResponse.status !== 200) {
      console.error('Failed to fetch faculty:', getResponse.data);
      return;
    }
    
    const faculty = getResponse.data;
    console.log('Current faculty data:');
    console.log(`Name: ${faculty.user.name}`);
    console.log(`Email: ${faculty.user.email}`);
    console.log(`Title: ${faculty.title}`);
    console.log(`Department: ${faculty.department?.name || 'None'}`);
    
    // Prompt for new email
    const newEmail = await promptForInput('Enter new email (or press Enter to keep current): ');
    
    if (!newEmail) {
      console.log('No changes requested. Exiting.');
      rl.close();
      return;
    }
    
    // Prepare update data
    const updateData = {
      name: faculty.user.name,
      email: newEmail || faculty.user.email,
      title: faculty.title,
      departmentId: faculty.departmentId,
      status: faculty.user.status
    };
    
    console.log('Update data to be sent:', updateData);
    
    // Confirm before proceeding
    const confirm = await promptForInput('Proceed with update? (y/n): ');
    if (confirm.toLowerCase() !== 'y') {
      console.log('Update cancelled.');
      rl.close();
      return;
    }
    
    // Send update request
    console.log(`Sending PUT request to update faculty ID: ${facultyId}`);
    const updateResponse = await axios.put(
      `http://localhost:3000/api/admin/faculty/${facultyId}`,
      updateData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Cookie': sessionCookie
        },
        validateStatus: () => true
      }
    );
    
    console.log(`Response status: ${updateResponse.status}`);
    
    if (updateResponse.status >= 200 && updateResponse.status < 300) {
      console.log('Update successful!');
      console.log('Updated faculty data:', updateResponse.data);
    } else {
      console.error('Update failed:', updateResponse.data);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  } finally {
    rl.close();
  }
}

// Run the update function
updateFaculty(); 