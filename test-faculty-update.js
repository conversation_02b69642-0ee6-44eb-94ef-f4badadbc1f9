// Test script for faculty update API
const testFacultyUpdate = async () => {
  const facultyId = 'cly8tkg0d00009qs8hwz7jjy1'; // Replace with a valid faculty ID from your database
  
  try {
    // Fetch current faculty data
    const getResponse = await fetch(`http://localhost:3000/api/admin/faculty/${facultyId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!getResponse.ok) {
      const error = await getResponse.json();
      throw new Error(error.error || 'Failed to fetch faculty');
    }
    
    const faculty = await getResponse.json();
    console.log('Current faculty data:', faculty);
    
    // Prepare update data
    const updateData = {
      name: faculty.user.name,
      email: faculty.user.email,
      title: faculty.title,
      departmentId: faculty.departmentId,
      officeLocation: faculty.officeLocation || 'Updated Office Location', // Change this value
      status: faculty.user.status,
    };
    
    console.log('Update data to be sent:', updateData);
    
    // Send update request
    const updateResponse = await fetch(`http://localhost:3000/api/admin/faculty/${facultyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });
    
    const responseData = await updateResponse.json();
    
    if (!updateResponse.ok) {
      throw new Error(responseData.error || 'Failed to update faculty');
    }
    
    console.log('Update successful:', responseData);
    
  } catch (error) {
    console.error('Error:', error.message);
  }
};

// Run the test
testFacultyUpdate(); 